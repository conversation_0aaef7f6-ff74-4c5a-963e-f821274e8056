{"cells": [{"cell_type": "code", "execution_count": null, "id": "286a9124", "metadata": {}, "outputs": [], "source": ["import os\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "import pandas as pd\n", "from tree_sitter import Language, Parser\n", "import tree_sitter_java as tsjava\n", "\n", "# Neo4j direct driver for flexible Cypher\n", "from neo4j import GraphDatabase\n", "\n", "# LangChain + Google Gemini for LLM semantic extraction\n", "from langchain_community.document_loaders import TextLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter, Language as LC_Language\n", "from langchain_google_genai import ChatGoogleGenerativeAI\n", "from langchain_experimental.graph_transformers import LLMGraphTransformer\n", "\n", "# Your configurations\n", "BASE_PATH = Path(\"C:/Shaik/sample/java\")\n", "NEO4J_URI = \"bolt://localhost:7687\"\n", "NEO4J_USER = \"neo4j\"\n", "NEO4J_PASSWORD = \"Test@7889\"\n", "NEO4J_DB = \"astllm\"\n", "\n", "google_api_key = \"AIzaSyAZydcsmJuuWAP1UCcqUPcQguXIkPe5LyM\"\n", "\n"]}, {"cell_type": "code", "execution_count": 38, "id": "8a381a6c", "metadata": {}, "outputs": [], "source": ["llm = ChatGoogleGenerativeAI(\n", "    model=\"gemini-2.5-pro\",\n", "    temperature=0,\n", "    google_api_key=google_api_key\n", ")\n", "\n", "graph = Neo4jGraph(\n", "    url=NEO4J_URL,\n", "    username=NEO4J_USER,\n", "    password=NEO4J_PASS,\n", "    database=NEO4J_DB\n", ")\n", "\n", "JAVA_LANGUAGE = Language(tsjava.language())\n", "parser = Parser(JAVA_LANGUAGE)"]}, {"cell_type": "code", "execution_count": 47, "id": "2d948711", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Extracted 8 folders and 12 files.\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>source_node</th>\n", "      <th>source_type</th>\n", "      <th>destination_node</th>\n", "      <th>destination_type</th>\n", "      <th>relationship</th>\n", "      <th>file_path</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>java</td>\n", "      <td>Folder</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>java</td>\n", "      <td>Folder</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>java</td>\n", "      <td>Folder</td>\n", "      <td>configuration</td>\n", "      <td>Folder</td>\n", "      <td>CONTAINS</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>java</td>\n", "      <td>Folder</td>\n", "      <td>controller</td>\n", "      <td>Folder</td>\n", "      <td>CONTAINS</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>java</td>\n", "      <td>Folder</td>\n", "      <td>dto</td>\n", "      <td>Folder</td>\n", "      <td>CONTAINS</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  source_node source_type destination_node destination_type relationship  \\\n", "0        None        None             java           Folder         None   \n", "1        None        None             java           Folder         None   \n", "2        java      Folder    configuration           Folder     CONTAINS   \n", "3        java      Folder       controller           Folder     CONTAINS   \n", "4        java      Folder              dto           Folder     CONTAINS   \n", "\n", "  file_path  \n", "0      None  \n", "1      None  \n", "2      None  \n", "3      None  \n", "4      None  "]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["def extract_folder_file_hierarchy(base_path):\n", "    folder_records = []\n", "    file_records = []\n", "    base_path = os.path.abspath(base_path)\n", "    base_folder_name = os.path.basename(base_path)\n", "\n", "    folder_records.append({\n", "        \"source_node\": None,\n", "        \"source_type\": None,\n", "        \"destination_node\": base_folder_name,\n", "        \"destination_type\": \"Folder\",\n", "        \"relationship\": None,\n", "        \"file_path\": None\n", "    })\n", "\n", "    for root, dirs, files in os.walk(base_path):\n", "        rel_root = os.path.relpath(root, base_path)\n", "        if rel_root == \".\":\n", "            parent_folder = None\n", "            current_folder = base_folder_name\n", "        elif '/' not in rel_root and '\\\\' not in rel_root:\n", "            parent_folder = base_folder_name\n", "            current_folder = rel_root\n", "        else:\n", "            parent_folder = os.path.dirname(rel_root)\n", "            current_folder = os.path.basename(rel_root)\n", "\n", "        if current_folder:\n", "            folder_records.append({\n", "                \"source_node\": parent_folder,\n", "                \"source_type\": \"Folder\" if parent_folder else None,\n", "                \"destination_node\": current_folder,\n", "                \"destination_type\": \"Folder\",\n", "                \"relationship\": \"CONTAINS\" if parent_folder else None,\n", "                \"file_path\": None\n", "            })\n", "\n", "        for f in files:\n", "            if f.endswith(\".java\"):\n", "                file_path = os.path.join(root, f)\n", "                file_rel_path = os.path.relpath(file_path, base_path)\n", "                file_records.append({\n", "                    \"source_node\": current_folder,\n", "                    \"source_type\": \"Folder\",\n", "                    \"destination_node\": f,\n", "                    \"destination_type\": \"File\",\n", "                    \"relationship\": \"CONTAINS\",\n", "                    \"file_path\": file_rel_path\n", "                })\n", "\n", "    return folder_records, file_records\n", "\n", "folder_records, file_records = extract_folder_file_hierarchy(BASE_PATH)\n", "print(f\"✅ Extracted {len(folder_records)} folders and {len(file_records)} files.\")\n", "pd.DataFrame(folder_records + file_records).head()\n"]}, {"cell_type": "code", "execution_count": 39, "id": "8ee59f60", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Parsing with AST: 100%|██████████| 12/12 [00:00<00:00, 51.17it/s]\n"]}], "source": ["JAVA_LANGUAGE = Language(tsjava.language())\n", "parser = Parser(JAVA_LANGUAGE)\n", "\n", "lineage_records = []\n", "\n", "def extract_structure(node, source_code, file_path, folder_path, file_name):\n", "    def traverse(n, parent_class=None, parent_method=None):\n", "        if n.type == \"class_declaration\":\n", "            class_name = next((source_code[c.start_byte:c.end_byte].decode() for c in n.children if c.type == \"identifier\"), None)\n", "            if class_name:\n", "                lineage_records.append({\n", "                    \"source_node\": file_name,\n", "                    \"source_type\": \"File\",\n", "                    \"destination_node\": class_name,\n", "                    \"destination_type\": \"Class\",\n", "                    \"relationship\": \"DECLARES\",\n", "                    \"file_path\": file_path\n", "                })\n", "            for c in n.children:\n", "                traverse(c, parent_class=class_name)\n", "\n", "        elif n.type == \"method_declaration\":\n", "            method_name = next((source_code[c.start_byte:c.end_byte].decode() for c in n.children if c.type == \"identifier\"), None)\n", "            if method_name:\n", "                lineage_records.append({\n", "                    \"source_node\": parent_class,\n", "                    \"source_type\": \"Class\",\n", "                    \"destination_node\": method_name,\n", "                    \"destination_type\": \"Method\",\n", "                    \"relationship\": \"DECLARES\",\n", "                    \"file_path\": file_path\n", "                })\n", "            for c in n.children:\n", "                traverse(c, parent_class=parent_class, parent_method=method_name)\n", "\n", "        elif n.type == \"method_invocation\":\n", "            invoked_method = next((source_code[c.start_byte:c.end_byte].decode() for c in n.children if c.type == \"identifier\"), None)\n", "            if invoked_method and parent_method:\n", "                lineage_records.append({\n", "                    \"source_node\": parent_method,\n", "                    \"source_type\": \"Method\",\n", "                    \"destination_node\": invoked_method,\n", "                    \"destination_type\": \"Method\",\n", "                    \"relationship\": \"CALLS\",\n", "                    \"file_path\": file_path\n", "                })\n", "            for c in n.children:\n", "                traverse(c, parent_class=parent_class, parent_method=parent_method)\n", "\n", "        for c in n.children:\n", "            traverse(c, parent_class=parent_class, parent_method=parent_method)\n", "\n", "    traverse(node)\n", "\n", "for path in tqdm(list(BASE_PATH.rglob(\"*.java\")), desc=\"Parsing with AST\"):\n", "    source_code = open(path, encoding=\"utf-8\").read().encode(\"utf-8\")\n", "    tree = parser.parse(source_code)\n", "    file_name = path.name\n", "    folder_path = str(path.parent.relative_to(BASE_PATH))\n", "    file_path = str(path.relative_to(BASE_PATH))\n", "\n", "    # Folder → File relationship\n", "    lineage_records.append({\n", "        \"source_node\": folder_path if folder_path != '.' else 'root',\n", "        \"source_type\": \"Folder\",\n", "        \"destination_node\": file_name,\n", "        \"destination_type\": \"File\",\n", "        \"relationship\": \"CONTAINS\",\n", "        \"file_path\": file_path\n", "    })\n", "\n", "    extract_structure(tree.root_node, source_code, file_path, folder_path, file_name)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 40, "id": "f94b7fa0", "metadata": {}, "outputs": [], "source": ["df_ast = pd.DataFrame(lineage_records)\n"]}, {"cell_type": "code", "execution_count": 41, "id": "88489bf4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>source_node</th>\n", "      <th>source_type</th>\n", "      <th>destination_node</th>\n", "      <th>destination_type</th>\n", "      <th>relationship</th>\n", "      <th>file_path</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>388</th>\n", "      <td>createUserWithLoan</td>\n", "      <td>Method</td>\n", "      <td>dto</td>\n", "      <td>Method</td>\n", "      <td>CALLS</td>\n", "      <td>service\\UserService.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>150</th>\n", "      <td>storeProcDataSource</td>\n", "      <td>Method</td>\n", "      <td>props</td>\n", "      <td>Method</td>\n", "      <td>CALLS</td>\n", "      <td>configuration\\StoredProcDBConfig.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133</th>\n", "      <td>primaryDataSource</td>\n", "      <td>Method</td>\n", "      <td>ds</td>\n", "      <td>Method</td>\n", "      <td>CALLS</td>\n", "      <td>configuration\\StoredProcDBConfig.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>printDsInfo</td>\n", "      <td>Method</td>\n", "      <td>getURL</td>\n", "      <td>Method</td>\n", "      <td>CALLS</td>\n", "      <td>LoanAppApplication.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>59</th>\n", "      <td>printDsInfo</td>\n", "      <td>Method</td>\n", "      <td>getURL</td>\n", "      <td>Method</td>\n", "      <td>CALLS</td>\n", "      <td>LoanAppApplication.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>391</th>\n", "      <td>createUserWithLoan</td>\n", "      <td>Method</td>\n", "      <td>dto</td>\n", "      <td>Method</td>\n", "      <td>CALLS</td>\n", "      <td>service\\UserService.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>printDsInfo</td>\n", "      <td>Method</td>\n", "      <td>getMetaData</td>\n", "      <td>Method</td>\n", "      <td>CALLS</td>\n", "      <td>LoanAppApplication.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>389</th>\n", "      <td>createUserWithLoan</td>\n", "      <td>Method</td>\n", "      <td>loan</td>\n", "      <td>Method</td>\n", "      <td>CALLS</td>\n", "      <td>service\\UserService.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>363</th>\n", "      <td>callStoreProc</td>\n", "      <td>Method</td>\n", "      <td>storeProcTemplate</td>\n", "      <td>Method</td>\n", "      <td>CALLS</td>\n", "      <td>service\\StoreProcService.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>371</th>\n", "      <td>callStoreProc</td>\n", "      <td>Method</td>\n", "      <td>cs</td>\n", "      <td>Method</td>\n", "      <td>CALLS</td>\n", "      <td>service\\StoreProcService.java</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             source_node source_type   destination_node destination_type  \\\n", "388   createUserWithLoan      Method                dto           Method   \n", "150  storeProcDataSource      Method              props           Method   \n", "133    primaryDataSource      Method                 ds           Method   \n", "44           printDsInfo      Method             getURL           Method   \n", "59           printDsInfo      Method             getURL           Method   \n", "391   createUserWithLoan      Method                dto           Method   \n", "38           printDsInfo      Method        getMetaData           Method   \n", "389   createUserWithLoan      Method               loan           Method   \n", "363        callStoreProc      Method  storeProcTemplate           Method   \n", "371        callStoreProc      Method                 cs           Method   \n", "\n", "    relationship                              file_path  \n", "388        CALLS               service\\UserService.java  \n", "150        CALLS  configuration\\StoredProcDBConfig.java  \n", "133        CALLS  configuration\\StoredProcDBConfig.java  \n", "44         CALLS                LoanAppApplication.java  \n", "59         CALLS                LoanAppApplication.java  \n", "391        CALLS               service\\UserService.java  \n", "38         CALLS                LoanAppApplication.java  \n", "389        CALLS               service\\UserService.java  \n", "363        CALLS          service\\StoreProcService.java  \n", "371        CALLS          service\\StoreProcService.java  "]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["df_ast.sample(10)"]}, {"cell_type": "code", "execution_count": 49, "id": "fe1aca9d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Prepared 396 AST + folder structure records for Neo4j ingestion.\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>source_node</th>\n", "      <th>source_type</th>\n", "      <th>destination_node</th>\n", "      <th>destination_type</th>\n", "      <th>relationship</th>\n", "      <th>file_path</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>java</td>\n", "      <td>folder</td>\n", "      <td>configuration</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>java</td>\n", "      <td>folder</td>\n", "      <td>controller</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>java</td>\n", "      <td>folder</td>\n", "      <td>dto</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>java</td>\n", "      <td>folder</td>\n", "      <td>model</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>java</td>\n", "      <td>folder</td>\n", "      <td>repository</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  source_node source_type destination_node destination_type relationship  \\\n", "2        java      folder    configuration           folder     contains   \n", "3        java      folder       controller           folder     contains   \n", "4        java      folder              dto           folder     contains   \n", "5        java      folder            model           folder     contains   \n", "6        java      folder       repository           folder     contains   \n", "\n", "  file_path  \n", "2      None  \n", "3      None  \n", "4      None  \n", "5      None  \n", "6      None  "]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "df_combined_ast = pd.concat([df_folders, df_files, df_ast], ignore_index=True)\n", "\n", "# Clean\n", "df_combined_ast = df_combined_ast.dropna(subset=[\"source_node\", \"destination_node\", \"source_type\", \"destination_type\", \"relationship\"])\n", "for col in [\"source_node\", \"destination_node\", \"source_type\", \"destination_type\", \"relationship\"]:\n", "    df_combined_ast[col] = df_combined_ast[col].astype(str).str.strip().str.lower()\n", "\n", "print(f\"✅ Prepared {len(df_combined_ast)} AST + folder structure records for Neo4j ingestion.\")\n", "df_combined_ast.head()\n"]}, {"cell_type": "code", "execution_count": 42, "id": "b7d2d9f2", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["LLM extraction: 100%|██████████| 14/14 [07:26<00:00, 31.90s/it]\n"]}], "source": ["java_docs = []\n", "for path in BASE_PATH.rglob(\"*.java\"):\n", "    loader = TextLoader(str(path))\n", "    java_docs.extend(loader.load())\n", "\n", "splitter = RecursiveCharacterTextSplitter.from_language(\n", "    language=LC_Language.JAVA,\n", "    chunk_size=2000,\n", "    chunk_overlap=200\n", ")\n", "split_docs = []\n", "for doc in java_docs:\n", "    chunks = splitter.split_documents([doc])\n", "    for idx, chunk in enumerate(chunks):\n", "        chunk.metadata[\"chunk_index\"] = idx\n", "        chunk.metadata[\"source\"] = doc.metadata[\"source\"]\n", "        split_docs.append(chunk)\n", "\n", "\n", "def build_prompt(file_path):\n", "    file_name = os.path.basename(file_path)\n", "    return f\"\"\"\n", "You are a **code lineage extraction engine**. From this Java chunk, extract triples:\n", "\n", "[SourceLabel]:SourceName -[RELATION]-> [TargetLabel]:TargetName\n", "\n", "Example:\n", "File:MyFile.java -[DECLARES]-> Class:UserController\n", "\n", "Include:\n", "- API calls (CALLS_API)\n", "- Table usage (READS_FROM, WRITES_TO)\n", "\n", "Return triples only.\n", "\"\"\"\n", "\n", "all_llm_lineage = []\n", "\n", "for chunk in tqdm(split_docs, desc=\"LLM extraction\"):\n", "    transformer = LLMGraphTransformer(\n", "        llm=llm,\n", "        additional_instructions=build_prompt(chunk.metadata[\"source\"]),\n", "        allowed_nodes=[\"File\", \"Class\", \"Function\", \"API\", \"Table\", \"Column\"],\n", "        allowed_relationships=[\n", "            (\"File\", \"DECLARES\", \"Class\"),\n", "            (\"Class\", \"DECLARES\", \"Function\"),\n", "            (\"Function\", \"CALLS\", \"Function\"),\n", "            (\"Function\", \"CALLS_API\", \"API\"),\n", "            (\"Function\", \"READS_FROM\", \"Table\"),\n", "            (\"Function\", \"WRITES_TO\", \"Table\")\n", "        ],\n", "        strict_mode=True\n", "    )\n", "    graph_docs = transformer.convert_to_graph_documents([chunk])\n", "    for gd in graph_docs:\n", "        for rel in gd.relationships:\n", "            all_llm_lineage.append({\n", "                \"source_node\": rel.source.id,\n", "                \"source_type\": rel.source.type,\n", "                \"destination_node\": rel.target.id,\n", "                \"destination_type\": rel.target.type,\n", "                \"relationship\": rel.type,\n", "                \"file_path\": chunk.metadata[\"source\"]\n", "            })\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 43, "id": "6d19415d", "metadata": {}, "outputs": [], "source": ["df_llm = pd.DataFrame(all_llm_lineage)\n"]}, {"cell_type": "code", "execution_count": 44, "id": "1df2765d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>source_node</th>\n", "      <th>source_type</th>\n", "      <th>destination_node</th>\n", "      <th>destination_type</th>\n", "      <th>relationship</th>\n", "      <th>file_path</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Loanappapplication.Java</td>\n", "      <td>File</td>\n", "      <td>Loanappapplication</td>\n", "      <td>Class</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\java\\LoanAppApplication.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Loanappapplication</td>\n", "      <td>Class</td>\n", "      <td>Printdsinfo</td>\n", "      <td>Function</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\java\\LoanAppApplication.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Loanappapplication</td>\n", "      <td>Class</td>\n", "      <td>Main</td>\n", "      <td>Function</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\java\\LoanAppApplication.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Main</td>\n", "      <td>Function</td>\n", "      <td>Springapplication.Run</td>\n", "      <td>Api</td>\n", "      <td>CALLS_API</td>\n", "      <td>C:\\Shaik\\sample\\java\\LoanAppApplication.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Printdsinfo</td>\n", "      <td>Function</td>\n", "      <td>System.Out.Println</td>\n", "      <td>Api</td>\n", "      <td>CALLS_API</td>\n", "      <td>C:\\Shaik\\sample\\java\\LoanAppApplication.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Primarydatasourceproperties.Java</td>\n", "      <td>File</td>\n", "      <td>Primarydatasourceproperties</td>\n", "      <td>Class</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\java\\configuration\\PrimaryData...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Primarydatasourceproperties</td>\n", "      <td>Class</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Function</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\java\\configuration\\PrimaryData...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Primarydatasourceproperties</td>\n", "      <td>Class</td>\n", "      <td>Seturl</td>\n", "      <td>Function</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\java\\configuration\\PrimaryData...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Primarydatasourceproperties</td>\n", "      <td>Class</td>\n", "      <td>Getusername</td>\n", "      <td>Function</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\java\\configuration\\PrimaryData...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Primarydatasourceproperties</td>\n", "      <td>Class</td>\n", "      <td>Setusername</td>\n", "      <td>Function</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\java\\configuration\\PrimaryData...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        source_node source_type             destination_node  \\\n", "0           Loanappapplication.Java        File           Loanappapplication   \n", "1                Loanappapplication       Class                  Printdsinfo   \n", "2                Loanappapplication       Class                         Main   \n", "3                              Main    Function        Springapplication.Run   \n", "4                       Printdsinfo    Function           System.Out.Println   \n", "5  Primarydatasourceproperties.Java        File  Primarydatasourceproperties   \n", "6       Primarydatasourceproperties       Class                       Geturl   \n", "7       Primarydatasourceproperties       Class                       Seturl   \n", "8       Primarydatasourceproperties       Class                  Getusername   \n", "9       Primarydatasourceproperties       Class                  Setusername   \n", "\n", "  destination_type relationship  \\\n", "0            Class     DECLARES   \n", "1         Function     DECLARES   \n", "2         Function     DECLARES   \n", "3              Api    CALLS_API   \n", "4              Api    CALLS_API   \n", "5            Class     DECLARES   \n", "6         Function     DECLARES   \n", "7         Function     DECLARES   \n", "8         Function     DECLARES   \n", "9         Function     DECLARES   \n", "\n", "                                           file_path  \n", "0       C:\\Shaik\\sample\\java\\LoanAppApplication.java  \n", "1       C:\\Shaik\\sample\\java\\LoanAppApplication.java  \n", "2       C:\\Shaik\\sample\\java\\LoanAppApplication.java  \n", "3       C:\\Shaik\\sample\\java\\LoanAppApplication.java  \n", "4       C:\\Shaik\\sample\\java\\LoanAppApplication.java  \n", "5  C:\\Shaik\\sample\\java\\configuration\\PrimaryData...  \n", "6  C:\\Shaik\\sample\\java\\configuration\\PrimaryData...  \n", "7  C:\\Shaik\\sample\\java\\configuration\\PrimaryData...  \n", "8  C:\\Shaik\\sample\\java\\configuration\\PrimaryData...  \n", "9  C:\\Shaik\\sample\\java\\configuration\\PrimaryData...  "]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["df_llm.head(10)"]}, {"cell_type": "code", "execution_count": null, "id": "d198ced5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5e88fc85", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>source_node</th>\n", "      <th>source_type</th>\n", "      <th>destination_node</th>\n", "      <th>destination_type</th>\n", "      <th>relationship</th>\n", "      <th>file_path</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>java</td>\n", "      <td>folder</td>\n", "      <td>configuration</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>java</td>\n", "      <td>folder</td>\n", "      <td>controller</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>java</td>\n", "      <td>folder</td>\n", "      <td>dto</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>java</td>\n", "      <td>folder</td>\n", "      <td>model</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>java</td>\n", "      <td>folder</td>\n", "      <td>repository</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>java</td>\n", "      <td>folder</td>\n", "      <td>service</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>java</td>\n", "      <td>folder</td>\n", "      <td>loanappapplication.java</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>LoanAppApplication.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>configuration</td>\n", "      <td>folder</td>\n", "      <td>primarydatasourceproperties.java</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>configuration\\PrimaryDataSourceProperties.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>configuration</td>\n", "      <td>folder</td>\n", "      <td>seconddatasourceproperties.java</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>configuration\\SecondDataSourceProperties.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>configuration</td>\n", "      <td>folder</td>\n", "      <td>storedprocdbconfig.java</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>configuration\\StoredProcDBConfig.java</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     source_node source_type                  destination_node  \\\n", "0           java      folder                     configuration   \n", "1           java      folder                        controller   \n", "2           java      folder                               dto   \n", "3           java      folder                             model   \n", "4           java      folder                        repository   \n", "5           java      folder                           service   \n", "6           java      folder           loanappapplication.java   \n", "7  configuration      folder  primarydatasourceproperties.java   \n", "8  configuration      folder   seconddatasourceproperties.java   \n", "9  configuration      folder           storedprocdbconfig.java   \n", "\n", "  destination_type relationship  \\\n", "0           folder     contains   \n", "1           folder     contains   \n", "2           folder     contains   \n", "3           folder     contains   \n", "4           folder     contains   \n", "5           folder     contains   \n", "6             file     contains   \n", "7             file     contains   \n", "8             file     contains   \n", "9             file     contains   \n", "\n", "                                        file_path  \n", "0                                            None  \n", "1                                            None  \n", "2                                            None  \n", "3                                            None  \n", "4                                            None  \n", "5                                            None  \n", "6                         LoanAppApplication.java  \n", "7  configuration\\PrimaryDataSourceProperties.java  \n", "8   configuration\\SecondDataSourceProperties.java  \n", "9           configuration\\StoredProcDBConfig.java  "]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["df_combined = pd.concat([df_combined_ast, df_llm], ignore_index=True)\n", "df_combined.drop_duplicates(inplace=True)\n", "\n", "df_combined.head(10)\n"]}, {"cell_type": "code", "execution_count": 57, "id": "624880aa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 12 duplicates removed. 197 unique rows to ingest.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Pushing to Neo4j: 100%|██████████| 197/197 [00:03<00:00, 57.18it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Neo4j ingestion complete.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["from neo4j import GraphDatabase\n", "from tqdm import tqdm\n", "\n", "# Clean and normalize columns (just in case)\n", "for col in [\"source_node\", \"destination_node\", \"source_type\", \"destination_type\", \"relationship\"]:\n", "    df_combined[col] = df_combined[col].astype(str).str.strip().str.lower()\n", "\n", "# Drop duplicate rows based on all key columns\n", "df_unique = df_combined.drop_duplicates(\n", "    subset=[\"source_node\", \"source_type\", \"destination_node\", \"destination_type\", \"relationship\"]\n", ").reset_index(drop=True)\n", "\n", "print(f\"✅ {len(df_combined) - len(df_unique)} duplicates removed. {len(df_unique)} unique rows to ingest.\")\n", "\n", "# Neo4j driver setup\n", "driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))\n", "\n", "with driver.session(database=\"astllm\") as session:\n", "    for idx, row in tqdm(df_unique.iterrows(), total=len(df_unique), desc=\"Pushing to Neo4j\"):\n", "        try:\n", "            session.run(f\"\"\"\n", "                MERGE (s:{row['source_type']} {{name: $source_node}})\n", "                MERGE (t:{row['destination_type']} {{name: $destination_node}})\n", "                MERGE (s)-[r:{row['relationship']}]->(t)\n", "            \"\"\", {\n", "                \"source_node\": row[\"source_node\"],\n", "                \"destination_node\": row[\"destination_node\"]\n", "            })\n", "        except Exception as e:\n", "            print(f\"Error at row {idx}: {e}\")\n", "\n", "print(\"✅ Neo4j ingestion complete.\")\n"]}, {"cell_type": "code", "execution_count": 56, "id": "06dc3d3b", "metadata": {}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["# Clean up Neo4j database\n", "graph.query(\"MATCH (n) DETACH DELETE n\")"]}, {"cell_type": "code", "execution_count": null, "id": "bd0ecd10", "metadata": {}, "outputs": [], "source": ["\n", "\n", "filtered_llm = df_llm_lineage[df_llm_lineage[\"source_node\"].str.contains(\n", "    \"com.morganstanley.loanapp.repository.loandetailsrepository\",\n", "    case=False, na=False\n", ")]\n", "\n", "\n", "\n", "print(f\"Found {len(filtered_llm)} LLM lineage rows for userrepository.java:\")\n", "display(filtered_llm)\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}