{"cells": [{"cell_type": "code", "execution_count": null, "id": "stage1_configuration", "metadata": {}, "outputs": [], "source": ["# ========== COMBINED DATA LINEAGE PIPELINE V11 ==========\n", "# Combines complete pipeline from final_v9.ipynb with advanced features from final_v10.ipynb\n", "# Base: final_v9.ipynb (complete working pipeline)\n", "# Enhanced with: advanced prompts, memory optimizations, and variable context tracking from final_v10.ipynb\n", "\n", "# STAGE 1: CONFIGURATION & INITIALIZATION\n", "import os\n", "import json\n", "import re\n", "import uuid\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "import pandas as pd\n", "from collections import defaultdict\n", "from concurrent.futures import ThreadPoolExecutor, as_completed\n", "import threading\n", "import time\n", "from tenacity import retry, stop_after_attempt, wait_exponential\n", "\n", "# Tree-sitter for AST parsing\n", "from tree_sitter import Language, Parser\n", "import tree_sitter_java as tsjava\n", "\n", "# LangChain components\n", "from langchain_community.document_loaders import TextLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter, Language as LC_Language\n", "from langchain_experimental.graph_transformers import LLMGraphTransformer\n", "from langchain_community.graphs import Neo4jGraph\n", "from langchain_openai import AzureChatOpenAI\n", "from langchain_core.prompts import PromptTemplate\n", "from langchain.schema import Document\n", "\n", "# Configuration\n", "BASE_PATH = Path(r\"C:/Shaik/sample/ServicesBolt\")\n", "\n", "# Neo4j Configuration\n", "NEO4J_URI = \"bolt://localhost:7687\"\n", "NEO4J_USER = \"neo4j\"\n", "NEO4J_PASSWORD = \"Test@7889\"\n", "NEO4J_DB = \"servicesbolt\"\n", "\n", "# Initialize connections\n", "graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)\n", "JAVA_LANGUAGE = Language(tsjava.language())\n", "parser = Parser(JAVA_LANGUAGE)\n", "\n", "# Azure OpenAI Configuration\n", "llm = AzureChatOpenAI(\n", "    api_key=\"********************************\",\n", "    azure_endpoint=\"https://azureopenaibrsc.openai.azure.com/\",\n", "    azure_deployment=\"gpt-4o\",\n", "    api_version=\"2024-12-01-preview\"\n", ")\n", "\n", "# Loop variables to avoid in transformation analysis only\n", "LOOP_VARIABLES = {\n", "    'i', 'j', 'k', 'l', 'm', 'n', 'x', 'y', 'z',\n", "    'index', 'idx', 'counter', 'count'\n", "}\n", "\n", "# Enhanced comment removal function (from v10)\n", "def remove_java_comments(source_code):\n", "    \"\"\"Remove all types of Java comments to avoid false positives in pattern matching\"\"\"\n", "    # Remove single-line comments\n", "    source_code = re.sub(r'//.*$', '', source_code, flags=re.MULTILINE)\n", "    # Remove multi-line comments including /** */ blocks\n", "    source_code = re.sub(r'/\\*.*?\\*/', '', source_code, flags=re.DOTALL)\n", "    return source_code\n", "\n", "# Long-term memory storage (enhanced from v10)\n", "MEMORY_FILE = \"servicesbolt_memory_v11.json\"\n", "memory_lock = threading.Lock()\n", "\n", "def load_memory():\n", "    \"\"\"Load long-term memory from disk\"\"\"\n", "    try:\n", "        with open(MEMORY_FILE, 'r', encoding='utf-8') as f:\n", "            data = json.load(f)\n", "            \n", "            # Convert lists back to sets where needed\n", "            def convert_from_json(obj):\n", "                if isinstance(obj, dict):\n", "                    result = {}\n", "                    for k, v in obj.items():\n", "                        if k == 'validated_edges' and isinstance(v, list):\n", "                            result[k] = set(v)\n", "                        else:\n", "                            result[k] = convert_from_json(v)\n", "                    return result\n", "                elif isinstance(obj, list):\n", "                    return [convert_from_json(item) for item in obj]\n", "                else:\n", "                    return obj\n", "            \n", "            return convert_from_json(data)\n", "            \n", "    except FileNotFoundError:\n", "        # Return enhanced default memory structure (from v10)\n", "        return {\n", "            'class_registry': {},\n", "            'validated_edges': set(),\n", "            'variable_flows': {},\n", "            'method_signatures': {},\n", "            'transformation_cache': {},\n", "            'variable_contexts': {}  # Enhanced variable context tracking\n", "        }\n", "    except json.JSONDecodeError as e:\n", "        print(f\"⚠️ Error loading JSON memory file: {e}\")\n", "        return {\n", "            'class_registry': {},\n", "            'validated_edges': set(),\n", "            'variable_flows': {},\n", "            'method_signatures': {},\n", "            'transformation_cache': {},\n", "            'variable_contexts': {}  # Enhanced variable context tracking\n", "        }\n", "\n", "def save_memory(memory):\n", "    \"\"\"Save long-term memory to disk\"\"\"\n", "    with memory_lock:\n", "        try:\n", "            memory_copy = memory.copy()\n", "            \n", "            # Convert sets to lists for JSON serialization\n", "            def convert_for_json(obj):\n", "                if isinstance(obj, set):\n", "                    return list(obj)\n", "                elif isinstance(obj, dict):\n", "                    return {k: convert_for_json(v) for k, v in obj.items()}\n", "                elif isinstance(obj, list):\n", "                    return [convert_for_json(item) for item in obj]\n", "                else:\n", "                    return obj\n", "            \n", "            memory_copy = convert_for_json(memory_copy)\n", "            \n", "            with open(MEMORY_FILE, 'w', encoding='utf-8') as f:\n", "                json.dump(memory_copy, f, indent=2, ensure_ascii=False)\n", "        except Exception as e:\n", "            print(f\"⚠️ Error saving memory to JSON: {e}\")\n", "\n", "# Initialize memory\n", "memory = load_memory()\n", "\n", "# Clear Neo4j database\n", "graph.query(\"MATCH (n) DETACH DELETE n\")\n", "print(\"✅ Stage 1 Complete: Configuration loaded and Neo4j cleared\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ========== RETRY LOGIC FOR RATE LIMITING (from v10) ==========\n", "@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=60))\n", "def process_with_retry(transformer, doc):\n", "    \"\"\"Process LLM request with retry logic for rate limiting\"\"\"\n", "    try:\n", "        return transformer.convert_to_graph_documents([doc])\n", "    except Exception as e:\n", "        error_str = str(e).lower()\n", "        if \"429\" in error_str or \"rate limit\" in error_str or \"quota\" in error_str:\n", "            print(f\"🔄 Rate limit hit, retrying after delay...\")\n", "            raise  # This will trigger the retry\n", "        else:\n", "            print(f\"❌ Non-rate-limit error: {e}\")\n", "            raise  # Re-raise non-rate-limit errors\n", "\n", "print(\"✅ Rate limiting retry logic loaded\")"]}, {"cell_type": "code", "execution_count": null, "id": "utility_functions", "metadata": {}, "outputs": [], "source": ["# ========== IMPROVED UTILITY FUNCTIONS (from v9 base) ==========\n", "\n", "def to_pascal_case(text):\n", "    \"\"\"Convert text to PascalCase with improved handling\"\"\"\n", "    if not text:\n", "        return text\n", "    \n", "    # Remove file extensions first\n", "    text = re.sub(r'\\.(java|class)$', '', text, flags=re.IGNORECASE)\n", "    \n", "    # Handle file paths - extract just the filename\n", "    if '/' in text or '\\\\' in text:\n", "        text = os.path.basename(text)\n", "        text = re.sub(r'\\.(java|class)$', '', text, flags=re.IGNORECASE)\n", "    \n", "    # If already in PascalCase, return as is\n", "    if re.match(r'^[A-Z][a-zA-Z0-9]*$', text):\n", "        return text\n", "    \n", "    # Handle camelCase to PascalCase conversion\n", "    if re.match(r'^[a-z][a-zA-Z0-9]*$', text):\n", "        return text[0].upper() + text[1:]\n", "    \n", "    # Split on common delimiters and capitalize each part\n", "    parts = re.split(r'[_\\-\\s]+', text)\n", "    result = ''\n", "    for part in parts:\n", "        if part:\n", "            result += part[0].upper() + part[1:].lower() if len(part) > 1 else part.upper()\n", "    \n", "    return result if result else text\n", "\n", "def extract_clean_name(full_name, name_type):\n", "    \"\"\"Extract clean name from potentially concatenated strings\"\"\"\n", "    if not full_name:\n", "        return full_name\n", "    \n", "    # Remove common prefixes\n", "    prefixes = ['method:', 'class:', 'variable:', 'field:', 'table:', 'endpoint:']\n", "    for prefix in prefixes:\n", "        if full_name.lower().startswith(prefix):\n", "            full_name = full_name[len(prefix):]\n", "    \n", "    # Remove file extensions EXCEPT for file type\n", "    if name_type.lower() != 'file':\n", "        full_name = re.sub(r'\\.(java|class)$', '', full_name, flags=re.IGNORECASE)\n", "    \n", "    # Handle file.class patterns - extract only class name\n", "    if '.' in full_name and name_type.lower() in ['class', 'interface']:\n", "        parts = full_name.split('.')\n", "        full_name = parts[-1] if parts[-1] else parts[-2] if len(parts) > 1 else full_name\n", "    \n", "    # Handle classname:method or classname.method patterns\n", "    if name_type.lower() == 'method':\n", "        if ':' in full_name:\n", "            parts = full_name.split(':')\n", "            full_name = parts[-1] if parts[-1] else parts[-2] if len(parts) > 1 else full_name\n", "        elif '.' in full_name:\n", "            parts = full_name.split('.')\n", "            full_name = parts[-1] if parts[-1] else parts[-2] if len(parts) > 1 else full_name\n", "    \n", "    # Apply PascalCase for classes, methods, files, folders\n", "    if name_type.lower() in ['class', 'interface', 'method', 'file', 'folder']:\n", "        return to_pascal_case(full_name)\n", "    \n", "    # For variables, keep original name\n", "    if name_type.lower() == 'variable':\n", "        if '.' in full_name:\n", "            return full_name.split('.')[-1]\n", "        return full_name\n", "    \n", "    # For tables, apply PascalCase\n", "    if name_type.lower() == 'table':\n", "        return to_pascal_case(full_name)\n", "    \n", "    return full_name\n", "\n", "def is_loop_variable(var_name):\n", "    \"\"\"Check if variable is a loop variable to avoid in transformations\"\"\"\n", "    if not var_name:\n", "        return True\n", "    var_lower = var_name.lower().strip()\n", "    return var_lower in LOOP_VARIABLES or len(var_lower) <= 1\n", "\n", "def get_variable_context(var_name, method_name=None, class_name=None):\n", "    \"\"\"Get context for a variable (method or class where it's defined)\"\"\"\n", "    if method_name:\n", "        return extract_clean_name(method_name, 'method')\n", "    elif class_name:\n", "        return extract_clean_name(class_name, 'class')\n", "    return None\n", "\n", "print(\"✅ Improved utility functions loaded\")"]}, {"cell_type": "code", "execution_count": null, "id": "variable_registry", "metadata": {}, "outputs": [], "source": ["# ========== ENHANCED VARIABLE METADATA REGISTRY (from v10) ==========\n", "\n", "class ImprovedVariableRegistry:\n", "    \"\"\"Enhanced registry to track variables with proper context separation\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.variables = {}  # var_id -> metadata\n", "        self.name_to_id = {}  # (var_name, context) -> var_id\n", "        self.chunk_memory = {}  # chunk_id -> variables seen\n", "        \n", "    def register_variable(self, var_name, context, chunk_id, context_info):\n", "        \"\"\"Register a variable with unique ID and context metadata\"\"\"\n", "        # Clean variable name\n", "        clean_var_name = extract_clean_name(var_name, 'variable')\n", "        clean_context = extract_clean_name(context, context_info.get('context_type', 'method'))\n", "        \n", "        # Create unique key\n", "        var_key = (clean_var_name, clean_context)\n", "        \n", "        if var_key in self.name_to_id:\n", "            var_id = self.name_to_id[var_key]\n", "            self.variables[var_id]['chunks'].add(chunk_id)\n", "            self.variables[var_id]['contexts'].append(context_info)\n", "        else:\n", "            var_id = f\"var_{uuid.uuid4().hex[:8]}\"\n", "            self.name_to_id[var_key] = var_id\n", "            self.variables[var_id] = {\n", "                'variable_name': clean_var_name,\n", "                'context_name': clean_context,\n", "                'context_type': context_info.get('context_type', 'method'),\n", "                'chunks': {chunk_id},\n", "                'contexts': [context_info],\n", "                'declared_in': chunk_id if context_info.get('action') == 'declared' else None,\n", "                'modifications': [],\n", "                'usages': [],\n", "                'data_type': context_info.get('data_type'),\n", "                'lineage_path': []\n", "            }\n", "        \n", "        if chunk_id not in self.chunk_memory:\n", "            self.chunk_memory[chunk_id] = set()\n", "        self.chunk_memory[chunk_id].add(var_id)\n", "        \n", "        return var_id\n", "    \n", "    def get_variable_for_neo4j(self, var_id):\n", "        \"\"\"Get variable data formatted for Neo4j\"\"\"\n", "        if var_id in self.variables:\n", "            var_data = self.variables[var_id]\n", "            return {\n", "                'name': var_data['variable_name'],\n", "                'context': var_data['context_name'],\n", "                'context_type': var_data['context_type'],\n", "                'full_context': f\"{var_data['context_name']}.{var_data['variable_name']}\"\n", "            }\n", "        return None\n", "\n", "# Enhanced variable context tracking functions (from v10)\n", "def create_unique_variable_name(var_name, context_name, context_type='method'):\n", "    \"\"\"Create unique variable name with context for proper relationship mapping\"\"\"\n", "    if not var_name or not context_name:\n", "        return var_name\n", "    \n", "    clean_var = extract_clean_name(var_name, 'variable')\n", "    clean_context = extract_clean_name(context_name, context_type)\n", "    \n", "    return f\"{clean_context}.{clean_var}\"\n", "\n", "def is_method_name(name, class_registry):\n", "    \"\"\"Check if a name is actually a method name to prevent method-variable confusion\"\"\"\n", "    if not name:\n", "        return False\n", "    \n", "    if name.endswith('()'):\n", "        return True\n", "    \n", "    for class_name, class_info in class_registry.items():\n", "        if 'source_code' in class_info:\n", "            method_pattern = rf'\\b(public|private|protected)\\s+\\w+\\s+{re.escape(name)}\\s*\\('\n", "            if re.search(method_pattern, class_info['source_code']):\n", "                return True\n", "    \n", "    return False\n", "\n", "# Initialize improved variable registry\n", "variable_registry = ImprovedVariableRegistry()\n", "\n", "print(\"✅ Enhanced Variable Registry initialized\")"]}, {"cell_type": "code", "execution_count": null, "id": "stage2_folder_file_hierarchy", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 2: IMPROVED FOLDER-FILE HIERARCHY ==========\n", "\n", "def extract_folder_file_hierarchy():\n", "    \"\"\"Extract and normalize folder-file relationships with improved naming\"\"\"\n", "    relationships = []\n", "    base_folder = to_pascal_case(BASE_PATH.name)\n", "\n", "    for root, dirs, files in os.walk(BASE_PATH):\n", "        current_path = Path(root)\n", "        rel_path = current_path.relative_to(BASE_PATH)\n", "\n", "        # Determine current folder name and its parent\n", "        if rel_path != Path('.'):\n", "            folder_name = to_pascal_case(current_path.name)\n", "            parent_rel_path = current_path.parent.relative_to(BASE_PATH)\n", "            parent_name = base_folder if parent_rel_path == Path('.') else to_pascal_case(current_path.parent.name)\n", "\n", "            relationships.append({\n", "                'source_node': parent_name,\n", "                'source_type': 'Folder',\n", "                'destination_node': folder_name,\n", "                'destination_type': 'Folder',\n", "                'relationship': 'CONTAINS'\n", "            })\n", "            current_folder_name = folder_name\n", "        else:\n", "            current_folder_name = base_folder\n", "\n", "        # Process files inside the folder\n", "        for file in files:\n", "            if file.lower().endswith(\".java\"):\n", "                file_name = extract_clean_name(file, 'file')\n", "                relationships.append({\n", "                    'source_node': current_folder_name,\n", "                    'source_type': 'Folder',\n", "                    'destination_node': file_name,\n", "                    'destination_type': 'File',\n", "                    'relationship': 'CONTAINS',\n", "                    'file_path': str(current_path / file)\n", "                })\n", "\n", "    return relationships\n", "\n", "# Execute Stage 2\n", "folder_file_relationships = extract_folder_file_hierarchy()\n", "df_hierarchy = pd.DataFrame(folder_file_relationships)\n", "\n", "# Store Stage 2 results in memory\n", "memory['stage_2_results'] = {\n", "    'relationships': len(df_hierarchy),\n", "    'folders': len([r for r in folder_file_relationships if r['destination_type'] == 'Folder']),\n", "    'files': len([r for r in folder_file_relationships if r['destination_type'] == 'File'])\n", "}\n", "\n", "# Add validated edges to prevent duplicates\n", "for _, row in df_hierarchy.iterrows():\n", "    edge_key = f\"{row['source_node']}-{row['relationship']}-{row['destination_node']}\"\n", "    memory['validated_edges'].add(edge_key)\n", "save_memory(memory)\n", "\n", "print(f\"✅ Stage 2 Complete: {len(df_hierarchy)} folder/file relationships extracted\")\n", "print(f\"📁 Folders: {memory['stage_2_results']['folders']}, Files: {memory['stage_2_results']['files']}\")"]}, {"cell_type": "code", "execution_count": null, "id": "stage2b_ast_file_class", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 2B: AST-BASED FILE-CLASS RELATIONSHIPS ==========\n", "\n", "def extract_file_class_relationships():\n", "    \"\"\"Extract file-class relationships using AST parsing\"\"\"\n", "    relationships = []\n", "    \n", "    for root, dirs, files in os.walk(BASE_PATH):\n", "        for file in files:\n", "            if file.lower().endswith(\".java\"):\n", "                file_path = Path(root) / file\n", "                file_name = extract_clean_name(file, 'file')\n", "                \n", "                try:\n", "                    with open(file_path, 'r', encoding='utf-8') as f:\n", "                        content = f.read()\n", "                    \n", "                    # Parse with tree-sitter\n", "                    tree = parser.parse(bytes(content, 'utf8'))\n", "                    \n", "                    # Extract classes and interfaces\n", "                    def extract_classes_from_node(node):\n", "                        classes = []\n", "                        if node.type in ['class_declaration', 'interface_declaration']:\n", "                            name_node = None\n", "                            for child in node.children:\n", "                                if child.type == 'identifier':\n", "                                    name_node = child\n", "                                    break\n", "                            if name_node:\n", "                                class_name = content[name_node.start_byte:name_node.end_byte]\n", "                                class_type = 'Class' if node.type == 'class_declaration' else 'Interface'\n", "                                classes.append((to_pascal_case(class_name), class_type))\n", "                        \n", "                        for child in node.children:\n", "                            classes.extend(extract_classes_from_node(child))\n", "                        \n", "                        return classes\n", "                    \n", "                    classes = extract_classes_from_node(tree.root_node)\n", "                    \n", "                    for class_name, class_type in classes:\n", "                        relationships.append({\n", "                            'source_node': file_name,\n", "                            'source_type': 'File',\n", "                            'destination_node': class_name,\n", "                            'destination_type': class_type,\n", "                            'relationship': 'DECLARES',\n", "                            'file_path': str(file_path)\n", "                        })\n", "                        \n", "                except Exception as e:\n", "                    print(f\"⚠️ Error processing {file_path}: {e}\")\n", "                    continue\n", "    \n", "    return relationships\n", "\n", "# Execute Stage 2B\n", "file_class_relationships = extract_file_class_relationships()\n", "df_file_class = pd.DataFrame(file_class_relationships)\n", "\n", "# Store Stage 2B results in memory\n", "memory['stage_2b_results'] = {\n", "    'relationships': len(df_file_class),\n", "    'classes': len([r for r in file_class_relationships if r['destination_type'] == 'Class']),\n", "    'interfaces': len([r for r in file_class_relationships if r['destination_type'] == 'Interface'])\n", "}\n", "\n", "# Add validated edges\n", "for _, row in df_file_class.iterrows():\n", "    edge_key = f\"{row['source_node']}-{row['relationship']}-{row['destination_node']}\"\n", "    memory['validated_edges'].add(edge_key)\n", "save_memory(memory)\n", "\n", "print(f\"✅ Stage 2B Complete: {len(df_file_class)} file-class relationships extracted\")\n", "print(f\"🏗️ Classes: {memory['stage_2b_results']['classes']}, Interfaces: {memory['stage_2b_results']['interfaces']}\")"]}, {"cell_type": "code", "execution_count": null, "id": "stage3_enhanced_class_registry", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 3: ENHANCED CLASS REGISTRY & ANALYSIS ==========\n", "\n", "def build_enhanced_class_registry():\n", "    \"\"\"Build comprehensive class registry with enhanced metadata\"\"\"\n", "    class_registry = {}\n", "    \n", "    for root, dirs, files in os.walk(BASE_PATH):\n", "        for file in files:\n", "            if file.lower().endswith(\".java\"):\n", "                file_path = Path(root) / file\n", "                \n", "                try:\n", "                    with open(file_path, 'r', encoding='utf-8') as f:\n", "                        content = f.read()\n", "                    \n", "                    # Remove comments to avoid false positives (enhanced from v10)\n", "                    clean_content = remove_java_comments(content)\n", "                    \n", "                    # Parse with tree-sitter\n", "                    tree = parser.parse(bytes(content, 'utf8'))\n", "                    \n", "                    def extract_class_info(node, content):\n", "                        classes = []\n", "                        if node.type in ['class_declaration', 'interface_declaration']:\n", "                            class_info = {\n", "                                'type': 'class' if node.type == 'class_declaration' else 'interface',\n", "                                'file_path': str(file_path),\n", "                                'source_code': content[node.start_byte:node.end_byte],\n", "                                'methods': [],\n", "                                'fields': [],\n", "                                'annotations': [],\n", "                                'extends': None,\n", "                                'implements': []\n", "                            }\n", "                            \n", "                            # Extract class name\n", "                            for child in node.children:\n", "                                if child.type == 'identifier':\n", "                                    class_name = content[child.start_byte:child.end_byte]\n", "                                    class_info['name'] = to_pascal_case(class_name)\n", "                                    break\n", "                            \n", "                            # Extract methods, fields, annotations\n", "                            for child in node.children:\n", "                                if child.type == 'class_body':\n", "                                    for body_child in child.children:\n", "                                        if body_child.type == 'method_declaration':\n", "                                            method_name = None\n", "                                            for method_child in body_child.children:\n", "                                                if method_child.type == 'identifier':\n", "                                                    method_name = content[method_child.start_byte:method_child.end_byte]\n", "                                                    break\n", "                                            if method_name:\n", "                                                class_info['methods'].append(to_pascal_case(method_name))\n", "                                        \n", "                                        elif body_child.type == 'field_declaration':\n", "                                            for field_child in body_child.children:\n", "                                                if field_child.type == 'variable_declarator':\n", "                                                    for var_child in field_child.children:\n", "                                                        if var_child.type == 'identifier':\n", "                                                            field_name = content[var_child.start_byte:var_child.end_byte]\n", "                                                            class_info['fields'].append(field_name)\n", "                                                            break\n", "                            \n", "                            # Detect annotations (enhanced from v10)\n", "                            annotation_patterns = [\n", "                                r'@RestController', r'@Controller', r'@Service', r'@Repository',\n", "                                r'@Entity', r'@Table', r'@Component', r'@Configuration'\n", "                            ]\n", "                            for pattern in annotation_patterns:\n", "                                if re.search(pattern, class_info['source_code']):\n", "                                    class_info['annotations'].append(pattern[1:])  # Remove @\n", "                            \n", "                            if 'name' in class_info:\n", "                                classes.append((class_info['name'], class_info))\n", "                        \n", "                        for child in node.children:\n", "                            classes.extend(extract_class_info(child, content))\n", "                        \n", "                        return classes\n", "                    \n", "                    classes = extract_class_info(tree.root_node, content)\n", "                    \n", "                    for class_name, class_info in classes:\n", "                        class_registry[class_name] = class_info\n", "                        \n", "                except Exception as e:\n", "                    print(f\"⚠️ Error processing {file_path}: {e}\")\n", "                    continue\n", "    \n", "    return class_registry\n", "\n", "# Execute Stage 3\n", "class_registry = build_enhanced_class_registry()\n", "memory['class_registry'] = class_registry\n", "\n", "# Store Stage 3 results\n", "memory['stage_3_results'] = {\n", "    'total_classes': len(class_registry),\n", "    'controllers': len([c for c in class_registry.values() if 'RestController' in c.get('annotations', []) or 'Controller' in c.get('annotations', [])]),\n", "    'services': len([c for c in class_registry.values() if 'Service' in c.get('annotations', [])]),\n", "    'entities': len([c for c in class_registry.values() if 'Entity' in c.get('annotations', [])]),\n", "    'repositories': len([c for c in class_registry.values() if 'Repository' in c.get('annotations', [])])\n", "}\n", "\n", "save_memory(memory)\n", "\n", "print(f\"✅ Stage 3 Complete: {len(class_registry)} classes registered\")\n", "print(f\"🎯 Controllers: {memory['stage_3_results']['controllers']}, Services: {memory['stage_3_results']['services']}\")\n", "print(f\"📊 Entities: {memory['stage_3_results']['entities']}, Repositories: {memory['stage_3_results']['repositories']}\")"]}, {"cell_type": "code", "execution_count": null, "id": "stage3b_ast_extraction", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 3B: COMPREHENSIVE AST EXTRACTION ==========\n", "\n", "def extract_comprehensive_ast_data():\n", "    \"\"\"Extract comprehensive AST data for all Java files\"\"\"\n", "    ast_data = []\n", "    \n", "    for root, dirs, files in os.walk(BASE_PATH):\n", "        for file in files:\n", "            if file.lower().endswith(\".java\"):\n", "                file_path = Path(root) / file\n", "                file_name = extract_clean_name(file, 'file')\n", "                \n", "                try:\n", "                    with open(file_path, 'r', encoding='utf-8') as f:\n", "                        content = f.read()\n", "                    \n", "                    # Parse with tree-sitter\n", "                    tree = parser.parse(bytes(content, 'utf8'))\n", "                    \n", "                    def extract_ast_relationships(node, parent_name=None, parent_type=None):\n", "                        relationships = []\n", "                        \n", "                        if node.type in ['class_declaration', 'interface_declaration']:\n", "                            # Extract class/interface name\n", "                            class_name = None\n", "                            for child in node.children:\n", "                                if child.type == 'identifier':\n", "                                    class_name = to_pascal_case(content[child.start_byte:child.end_byte])\n", "                                    break\n", "                            \n", "                            if class_name:\n", "                                # File declares class/interface\n", "                                relationships.append({\n", "                                    'source_node': file_name,\n", "                                    'source_type': 'File',\n", "                                    'destination_node': class_name,\n", "                                    'destination_type': 'Class' if node.type == 'class_declaration' else 'Interface',\n", "                                    'relationship': 'DECLARES',\n", "                                    'file_path': str(file_path)\n", "                                })\n", "                                \n", "                                # Process class body\n", "                                for child in node.children:\n", "                                    if child.type == 'class_body':\n", "                                        relationships.extend(extract_ast_relationships(child, class_name, 'Class'))\n", "                        \n", "                        elif node.type == 'method_declaration' and parent_name:\n", "                            # Extract method name\n", "                            method_name = None\n", "                            for child in node.children:\n", "                                if child.type == 'identifier':\n", "                                    method_name = to_pascal_case(content[child.start_byte:child.end_byte])\n", "                                    break\n", "                            \n", "                            if method_name:\n", "                                # Class contains method\n", "                                relationships.append({\n", "                                    'source_node': parent_name,\n", "                                    'source_type': parent_type,\n", "                                    'destination_node': method_name,\n", "                                    'destination_type': 'Method',\n", "                                    'relationship': 'CONTAINS',\n", "                                    'file_path': str(file_path)\n", "                                })\n", "                                \n", "                                # Process method body for variables\n", "                                for child in node.children:\n", "                                    if child.type == 'block':\n", "                                        relationships.extend(extract_ast_relationships(child, method_name, 'Method'))\n", "                        \n", "                        elif node.type == 'field_declaration' and parent_name:\n", "                            # Extract field variables\n", "                            for child in node.children:\n", "                                if child.type == 'variable_declarator':\n", "                                    for var_child in child.children:\n", "                                        if var_child.type == 'identifier':\n", "                                            var_name = content[var_child.start_byte:var_child.end_byte]\n", "                                            # Keep all variables for basic structure extraction\n", "                                            relationships.append({\n", "                                                    'source_node': parent_name,\n", "                                                    'source_type': parent_type,\n", "                                                    'destination_node': var_name,\n", "                                                    'destination_type': 'Variable',\n", "                                                    'relationship': 'HAS_FIELD',\n", "                                                    'file_path': str(file_path)\n", "                                                })\n", "                        \n", "                        elif node.type == 'local_variable_declaration' and parent_name:\n", "                            # Extract local variables\n", "                            for child in node.children:\n", "                                if child.type == 'variable_declarator':\n", "                                    for var_child in child.children:\n", "                                        if var_child.type == 'identifier':\n", "                                            var_name = content[var_child.start_byte:var_child.end_byte]\n", "                                            # Keep all variables for basic structure extraction\n", "                                            relationships.append({\n", "                                                    'source_node': parent_name,\n", "                                                    'source_type': parent_type,\n", "                                                    'destination_node': var_name,\n", "                                                    'destination_type': 'Variable',\n", "                                                    'relationship': 'DECLARES',\n", "                                                    'file_path': str(file_path)\n", "                                                })\n", "                        \n", "                        # Recursively process children\n", "                        for child in node.children:\n", "                            relationships.extend(extract_ast_relationships(child, parent_name, parent_type))\n", "                        \n", "                        return relationships\n", "                    \n", "                    file_relationships = extract_ast_relationships(tree.root_node)\n", "                    ast_data.extend(file_relationships)\n", "                    \n", "                except Exception as e:\n", "                    print(f\"⚠️ Error processing AST for {file_path}: {e}\")\n", "                    continue\n", "    \n", "    return ast_data\n", "\n", "# Execute Stage 3B\n", "ast_relationships = extract_comprehensive_ast_data()\n", "df_ast = pd.DataFrame(ast_relationships)\n", "\n", "# Store Stage 3B results\n", "memory['stage_3b_results'] = {\n", "    'total_relationships': len(df_ast),\n", "    'file_class': len([r for r in ast_relationships if r['relationship'] == 'DECLARES' and r['source_type'] == 'File']),\n", "    'class_method': len([r for r in ast_relationships if r['relationship'] == 'CONTAINS' and r['destination_type'] == 'Method']),\n", "    'class_field': len([r for r in ast_relationships if r['relationship'] == 'HAS_FIELD']),\n", "    'method_variable': len([r for r in ast_relationships if r['relationship'] == 'DECLARES' and r['source_type'] == 'Method'])\n", "}\n", "\n", "# Add validated edges\n", "for _, row in df_ast.iterrows():\n", "    edge_key = f\"{row['source_node']}-{row['relationship']}-{row['destination_node']}\"\n", "    memory['validated_edges'].add(edge_key)\n", "\n", "save_memory(memory)\n", "\n", "print(f\"✅ Stage 3B Complete: {len(df_ast)} AST relationships extracted\")\n", "print(f\"🔗 File→Class: {memory['stage_3b_results']['file_class']}, Class→Method: {memory['stage_3b_results']['class_method']}\")\n", "print(f\"📝 Class→Field: {memory['stage_3b_results']['class_field']}, Method→Variable: {memory['stage_3b_results']['method_variable']}\")"]}, {"cell_type": "code", "execution_count": null, "id": "enhanced_prompts_v10", "metadata": {}, "outputs": [], "source": ["# ========== ENHANCED PROMPTS FROM V10 ==========\n", "\n", "def escape_braces_for_langchain(text):\n", "    \"\"\"Escape curly braces in Java code to prevent LangChain template variable conflicts\"\"\"\n", "    if not text:\n", "        return text\n", "    return text.replace('{', '{{').replace('}', '}}')\n", "\n", "def build_consolidated_stage4b_prompt(file_path, ast_df, class_registry):\n", "    \"\"\"Build consolidated Stage 4B prompt for file-class-method-variable relationships (from v10)\"\"\"\n", "    \n", "    # Extract file name from path\n", "    file_name = to_pascal_case(Path(file_path).stem) if file_path else 'UnknownFile'\n", "    \n", "    # Get class registry info for this file\n", "    file_classes = [name for name, info in class_registry.items() \n", "                   if info.get('file_path') == file_path]\n", "    \n", "    # Build endpoint context\n", "    endpoint_context = ''\n", "    controllers = [c for c in file_classes if 'RestController' in class_registry[c].get('annotations', [])]\n", "    if controllers:\n", "        endpoint_context = f\"\\nREST Controllers in {file_name}: {', '.join(controllers)}\"\n", "    \n", "    # Build database context\n", "    db_entities = [c for c in file_classes if 'Entity' in class_registry[c].get('annotations', [])]\n", "    db_context = ''\n", "    if db_entities:\n", "        db_list = [f\"- {entity} (Entity)\" for entity in db_entities]\n", "        db_context = f\"\\nDatabase Entities in {file_name}:\\n\" + \"\\n\".join(db_list)\n", "    \n", "    # Build structural-only prompt (Stage 4B focuses on code structure)\n", "    prompt = f\"\"\"\n", "You are a Java code structure extraction engine. Extract ONLY structural relationships from this Java file.\n", "\n", "CURRENT FILE: {file_name}\n", "{endpoint_context}\n", "{db_context}\n", "\n", "EXTRACT STRUCTURAL RELATIONSHIPS ONLY:\n", "\n", "1. FILE-CLASS RELATIONSHIPS:\n", "   - File -[DECLARES]-> Class (detect all classes/interfaces in file)\n", "   - File -[DECLARES]-> Interface (detect all interfaces in file)\n", "\n", "2. CLASS-<PERSON>TH<PERSON> RELATIONSHIPS:\n", "   - Class -[CONTAINS]-> Method (all methods in classes)\n", "   - Interface -[CONTAINS]-> Method (all methods in interfaces)\n", "\n", "3. CLASS-VARIABLE RELATIONSHIPS:\n", "   - Class -[HAS_FIELD]-> Variable (class-level fields only)\n", "   - Method -[DECLARES]-> Variable (method parameters and local variables)\n", "\n", "4. INHERITANCE & IMPLEMENTATION:\n", "   - Class -[EXTENDS]-> Class (class inheritance)\n", "   - Class -[IMPLEMENTS]-> Interface (interface implementation)\n", "   - Method -[CALLS]-> Method (method invocations)\n", "\n", "5. BUSINESS RELATIONSHIPS:\n", "   - Class -[DECLARES]-> Endpoint (for @RestController classes)\n", "   - Class -[MAPS_TO]-> Table (for @Entity classes)\n", "\n", "NOTE: Data flow relationships (PRODUCES, FLOWS_TO, TRANSFORMS_TO, USES) are handled in Stage 5B.\n", "\n", "3. NAMING RULES:\n", "   - Use PascalCase for Files, Classes, Methods (e.g., DateUtil, ConvertDtoToEntity)\n", "   - Use original names for Variables (e.g., orderDto, entityList)\n", "   - Remove prefixes like 'method:', 'class:', 'variable:'\n", "   - For endpoints: use descriptive names like 'GetOrdersEndpoint'\n", "   - For tables: use PascalCase table names\n", "\n", "4. VARIABLE FILTERING:\n", "   - EXCLUDE only loop variables: i, j, k, l, m, n, x, y, z, index, idx, counter, count\n", "   - INCLUDE all meaningful variables: DTOs, entities, service calls, business data, temp variables with business meaning\n", "\n", "5. CONTEXT TRACKING:\n", "   - Global variables (class fields): context = class name\n", "   - Local variables (method variables): context = method name\n", "\n", "Extract relationships in format:\n", "[SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName\n", "\n", "Return ONLY the relationship triples, no explanations.\n", "\"\"\"\n", "    return prompt.replace(\"{\", \"{{\").replace(\"}\", \"}}\")\n", "\n", "def smart_chunk_strategy(file_path, content):\n", "    \"\"\"Smart chunking: whole file if <1000 lines, language chunks if larger\"\"\"\n", "    lines = content.count('\\n') + 1\n", "    escaped_content = escape_braces_for_langchain(content)\n", "    \n", "    if lines <= 1000:\n", "        # Small file: process as single chunk\n", "        return [Document(\n", "            page_content=escaped_content,\n", "            metadata={'source': str(file_path), 'chunk_type': 'whole_file'}\n", "        )]\n", "    else:\n", "        # Large file: use language-aware chunking\n", "        text_splitter = RecursiveCharacterTextSplitter.from_language(\n", "            language=LC_Language.JAVA,\n", "            chunk_size=2000,\n", "            chunk_overlap=200\n", "        )\n", "        \n", "        docs = text_splitter.create_documents(\n", "            [escaped_content],\n", "            metadatas=[{'source': str(file_path), 'chunk_type': 'language_chunk'}]\n", "        )\n", "        \n", "        return docs\n", "\n", "def build_first_llm_structure_prompt(file_path, ast_context):\n", "    \"\"\"Build prompt for first LLM: basic structure extraction from file chunks + AST\"\"\"\n", "    \n", "    file_name = to_pascal_case(Path(file_path).stem) if file_path else 'UnknownFile'\n", "    \n", "    prompt = f\"\"\"\n", "You are a Java code structure extraction engine. Extract ONLY basic structural relationships from this Java file chunk.\n", "\n", "CURRENT FILE: {file_name}\n", "\n", "AST CONTEXT PROVIDED:\n", "{ast_context}\n", "\n", "EXTRACT BASIC STRUCTURAL RELATIONSHIPS ONLY:\n", "\n", "1. FILE-CLASS RELATIONSHIPS:\n", "   - File -[DECLARES]-> Class (detect all classes in file)\n", "   - File -[DECLARES]-> Interface (detect all interfaces in file)\n", "\n", "2. CLASS-<PERSON>TH<PERSON> RELATIONSHIPS:\n", "   - Class -[CONTAINS]-> Method (all methods in classes)\n", "   - Interface -[CONTAINS]-> Method (all methods in interfaces)\n", "\n", "3. VARIABLE DECLARATIONS:\n", "   - Class -[HAS_FIELD]-> Variable (class-level fields)\n", "   - Method -[DECLARES]-> Variable (method parameters and local variables)\n", "\n", "4. BASIC INHERITANCE:\n", "   - Class -[EXTENDS]-> Class (class inheritance)\n", "   - Class -[IMPLEMENTS]-> Interface (interface implementation)\n", "\n", "NAMING RULES:\n", "- Use PascalCase for Files, Classes, Methods\n", "- Use original names for Variables\n", "- Include ALL variables (we'll filter later)\n", "\n", "Extract relationships in format:\n", "[SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName\n", "\n", "Return ONLY the relationship triples, no explanations.\n", "\"\"\"\n", "    return prompt.replace(\"{\", \"{{\").replace(\"}\", \"}}\")\n", "\n", "\n", "print(\"✅ Enhanced prompts and chunking strategy loaded from v10\")"]}, {"cell_type": "code", "execution_count": null, "id": "stage4b_improved_llm_processing", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 4 FUNCTIONS: LLM PROCESSING FUNCTIONS ==========\n", "\n", "def run_first_llm_structure_extraction():\n", "    \"\"\"First LLM: Extract basic structure from file chunks + AST data\"\"\"\n", "    \n", "    # Prepare documents with AST context for processing\n", "    smart_docs = []\n", "    \n", "    for root, dirs, files in os.walk(BASE_PATH):\n", "        for file in files:\n", "            if file.lower().endswith(\".java\"):\n", "                file_path = Path(root) / file\n", "                \n", "                try:\n", "                    with open(file_path, 'r', encoding='utf-8') as f:\n", "                        content = f.read()\n", "                    \n", "                    # Apply smart chunking strategy\n", "                    # Build AST context for this file\n", "                    ast_context = \"AST Relationships for this file:\\n\"\n", "                    if not df_ast.empty:\n", "                        file_ast = df_ast[df_ast['file_path'] == str(file_path)]\n", "                        for _, row in file_ast.iterrows():\n", "                            ast_context += f\"- {row['source_node']} -[{row['relationship']}]-> {row['destination_node']}\\n\"\n", "                    \n", "                    file_docs = smart_chunk_strategy(file_path, content)\n", "                    \n", "                    # Add AST context to each document\n", "                    for doc in file_docs:\n", "                        doc.metadata['ast_context'] = ast_context\n", "                    \n", "                    smart_docs.extend(file_docs)\n", "                    \n", "                except Exception as e:\n", "                    print(f\"⚠️ Error reading {file_path}: {e}\")\n", "                    continue\n", "    \n", "    print(f\"📄 Prepared {len(smart_docs)} smart document chunks with AST context\")\n", "    \n", "    # Process with first LLM for basic structure extraction\n", "    all_basic_relationships = []\n", "    \n", "    for doc_info in tqdm(smart_docs, desc='🤖 First LLM: Basic Structure Extraction'):\n", "        file_path = doc_info.metadata.get('source')\n", "        \n", "        ast_context = doc_info.metadata.get('ast_context', '')\n", "        \n", "        # Use first LLM prompt for basic structure extraction\n", "        structure_prompt = build_first_llm_structure_prompt(file_path, ast_context)\n", "        \n", "        # Configure transformer with all allowed relationships\n", "        transformer = LLMGraphTransformer(\n", "            llm=llm,\n", "            additional_instructions=structure_prompt,\n", "            allowed_nodes=['File', 'Class', 'Interface', 'Method', 'Variable', 'Table', 'Endpoint'],\n", "            allowed_relationships=[\n", "                # Structural relationships only - Stage 4B focuses on code structure\n", "                ('File', 'DECLARES', 'Class'),\n", "                ('File', 'DECLARES', 'Interface'),\n", "                ('Class', 'CONTAINS', 'Method'),\n", "                ('Interface', 'CONTAINS', 'Method'),\n", "                ('Class', 'HAS_FIELD', 'Variable'),\n", "                ('Method', 'DECLARES', 'Variable'),\n", "                ('Class', 'EXTENDS', 'Class'),\n", "                ('Class', 'IMPLEMENTS', 'Interface'),\n", "                ('Method', 'CALLS', 'Method'),\n", "                ('Class', 'DECLARES', 'Endpoint'),\n", "                ('Class', 'MAPS_TO', 'Table')\n", "            ],\n", "            strict_mode=False,\n", "            node_properties=False,\n", "            relationship_properties=False\n", "        )\n", "        \n", "        try:\n", "            # Use retry logic for rate limiting (from v10)\n", "            graph_docs = process_with_retry(transformer, doc_info)\n", "            \n", "            if graph_docs:\n", "                for graph_doc in graph_docs:\n", "                    # Extract relationships with enhanced validation\n", "                    for relationship in graph_doc.relationships:\n", "                        source_name = extract_clean_name(relationship.source.id, relationship.source.type)\n", "                        dest_name = extract_clean_name(relationship.target.id, relationship.target.type)\n", "                        \n", "                        # Enhanced filtering (from v10)\n", "                        # Keep all variables for basic structure extraction (1st LLM stage)\n", "                        # Prevent method-variable confusion\n", "                        if relationship.target.type == 'Variable' and is_method_name(dest_name, class_registry):\n", "                            continue\n", "                        \n", "                        # Create edge key for deduplication\n", "                        edge_key = f\"{source_name}-{relationship.type}-{dest_name}\"\n", "                        \n", "                        if edge_key not in memory['validated_edges']:\n", "                            all_basic_relationships.append({\n", "                                'source_node': source_name,\n", "                                'source_type': relationship.source.type,\n", "                                'destination_node': dest_name,\n", "                                'destination_type': relationship.target.type,\n", "                                'relationship': relationship.type,\n", "                                'file_path': file_path\n", "                            })\n", "                            memory['validated_edges'].add(edge_key)\n", "                            \n", "        except Exception as e:\n", "            print(f\"⚠️ Error processing {file_path}: {e}\")\n", "            continue\n", "    \n", "    print(f\"✅ First LLM: Extracted {len(all_basic_relationships)} basic structural relationships\")\n", "    return pd.DataFrame(all_basic_relationships)\n"]}, {"cell_type": "code", "execution_count": null, "id": "stage4_basic_structure_extraction", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 4: BASIC STRUCTURE EXTRACTION ==========\n", "# Extract basic structural relationships using first LLM\n", "\n", "print(\"🚀 Starting Stage 4: Basic Structure Extraction...\")\n", "df_basic_structure = run_first_llm_structure_extraction()\n", "\n", "# Store Stage 4 results\n", "memory['stage_4_results'] = {\n", "    'basic_structure_count': len(df_basic_structure),\n", "    'relationship_types': df_basic_structure['relationship'].value_counts().to_dict() if not df_basic_structure.empty else {}\n", "}\n", "save_memory(memory)\n", "\n", "print(f\"✅ Stage 4 Complete: {len(df_basic_structure)} basic structural relationships extracted\")\n", "if not df_basic_structure.empty:\n", "    print(f\"📈 Relationship types: {memory['stage_4_results']['relationship_types']}\")"]}, {"cell_type": "code", "execution_count": null, "id": "stage5b_advanced_transformation", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 5 FUNCTIONS: ADVANCED TRANSFORMATION ANALYSIS ==========\n", "\n", "def run_advanced_transformation_analysis():\n", "    \"\"\"Run advanced transformation analysis with enhanced prompts from v10\"\"\"\n", "    \n", "    if not class_registry:\n", "        print(\"❌ No class registry found. Run previous stages first.\")\n", "        return pd.DataFrame()\n", "    \n", "    print(f\"📊 Processing {len(class_registry)} classes for advanced transformations\")\n", "    \n", "    # Enhanced LLM prompt for variable transformations (from v10)\n", "    enhanced_prompt = \"\"\"\n", "You are an advanced Java data flow analysis engine specializing in variable transformations and method-level data flows.\n", "\n", "CRITICAL TRANSFORMATION PATTERNS TO CAPTURE:\n", "\n", "1. METHOD VARIABLE PRODUCTION:\n", "   - When a variable is assigned from a method call: Method -[PRODUCES]-> Variable\n", "   - Example: String result = service.processData(input) → processData -[PRODUCES]-> result\n", "\n", "2. COLLECTION OPERATIONS:\n", "   - When collections are updated: Variable -[FLOWS_TO]-> CollectionEntry\n", "   - map.put(key, value) → value -[FLOWS_TO]-> CollectionEntry:map.key\n", "   - list.add(item) → item -[FLOWS_TO]-> CollectionEntry:list\n", "\n", "3. VARI<PERSON>LE TRANSFORMATIONS INSIDE METHODS:\n", "   - Data type conversions: Variable -[TRANSFORMS_TO]-> Variable\n", "   - String operations: originalStr -[TRANSFORMS_TO]-> processedStr\n", "   - Object mapping: dto -[TRANSFORMS_TO]-> entity\n", "\n", "4. COMPLEX FLOWS:\n", "   - Method chaining: var1 -[FLOWS_TO]-> method1 -[PRODUCES]-> var2 -[FLOWS_TO]-> method2\n", "   - Conditional assignments: condition ? var1 : var2 -[FLOWS_TO]-> result\n", "   - Loop variable updates: loopVar -[TRANSFORMS_TO]-> updatedLoopVar\n", "\n", "5. RETURN VALUE FLOWS:\n", "   - Method return values: Variable -[RETURNS_DATA_TO]-> Variable\n", "   - Return transformations: processedData -[RETURNS_DATA_TO]-> methodResult\n", "\n", "FOCUS ON CAPTURING TRA<PERSON><PERSON>ORMATIONS THAT OCCUR INSIDE METHOD BODIES, NOT JUST METHOD SIGNATURES.\n", "\n", "Output format: source_node|relationship|destination_node|source_type|destination_type\n", "\"\"\"\n", "    \n", "    all_relationships = []\n", "    \n", "    # Process classes in batches for memory efficiency (from v10)\n", "    class_names = list(class_registry.keys())\n", "    batch_size = 5  # Smaller batches for detailed analysis\n", "    \n", "    for i in range(0, len(class_names), batch_size):\n", "        batch_classes = class_names[i:i+batch_size]\n", "        print(f\"🔄 Processing batch {i//batch_size + 1}/{(len(class_names) + batch_size - 1)//batch_size}\")\n", "        \n", "        for class_name in batch_classes:\n", "            class_info = class_registry[class_name]\n", "            \n", "            # Build enhanced context using variable registry (from v10)\n", "            context_info = f\"\"\"\n", "Class: {class_name}\n", "File: {class_info.get('file_path', 'Unknown')}\n", "\n", "Variable Context from Registry:\n", "\"\"\"\n", "            \n", "            # Add variable context information\n", "            variable_contexts = memory.get('variable_contexts', {})\n", "            for var_name, var_info in variable_contexts.items():\n", "                if var_info.get('context') == class_name or class_name in var_name:\n", "                    context_info += f\"- {var_name}: {var_info.get('context_type', 'unknown')} scope in {var_info.get('context', 'unknown')}\\n\"\n", "            \n", "            # Add method signatures for better understanding\n", "            method_sigs = memory.get('method_signatures', {})\n", "            if class_name in method_sigs:\n", "                context_info += f\"\\nMethod Signatures:\\n\"\n", "                for method, sig in method_sigs[class_name].items():\n", "                    context_info += f\"- {method}: {sig}\\n\"\n", "            \n", "            # Add source code for analysis\n", "            source_code = class_info.get('source_code', '')\n", "            if source_code:\n", "                context_info += f\"\\nSource Code:\\n{source_code[:2000]}...\"  # Limit for token efficiency\n", "            \n", "            try:\n", "                # Create document for LLM processing\n", "                doc = Document(\n", "                    page_content=f\"Analyze this Java class for variable transformations:\\n\\n{context_info}\",\n", "                    metadata={'class_name': class_name}\n", "                )\n", "                \n", "                # Configure transformer for transformation relationships\n", "                transformer = LLMGraphTransformer(\n", "                    llm=llm,\n", "                    additional_instructions=enhanced_prompt,\n", "                    allowed_nodes=['Method', 'Variable', 'CollectionEntry'],\n", "                    allowed_relationships=[\n", "                        ('Method', 'PRODUCES', 'Variable'),\n", "                        ('Variable', 'FLOWS_TO', 'CollectionEntry'),\n", "                        ('Variable', 'TRANSFORMS_TO', 'Variable'),\n", "                        ('Variable', 'FLOWS_TO', 'Method'),\n", "                        ('Variable', 'RETURNS_DATA_TO', 'Variable')\n", "                    ],\n", "                    strict_mode=False,\n", "                    node_properties=False,\n", "                    relationship_properties=False\n", "                )\n", "                \n", "                # Process with retry logic\n", "                graph_docs = process_with_retry(transformer, doc)\n", "                \n", "                if graph_docs:\n", "                    for graph_doc in graph_docs:\n", "                        for relationship in graph_doc.relationships:\n", "                            source_name = extract_clean_name(relationship.source.id, relationship.source.type)\n", "                            dest_name = extract_clean_name(relationship.target.id, relationship.target.type)\n", "                            \n", "                            # Filter only loop variables in transformation analysis\n", "                            if relationship.source.type == 'Variable' and is_loop_variable(source_name):\n", "                                continue\n", "                            if relationship.target.type == 'Variable' and is_loop_variable(dest_name):\n", "                                continue\n", "                            \n", "                            edge_key = f\"{source_name}-{relationship.type}-{dest_name}\"\n", "                            \n", "                            if edge_key not in memory['validated_edges']:\n", "                                all_relationships.append({\n", "                                    'source_node': source_name,\n", "                                    'source_type': relationship.source.type,\n", "                                    'destination_node': dest_name,\n", "                                    'destination_type': relationship.target.type,\n", "                                    'relationship': relationship.type,\n", "                                    'class_context': class_name\n", "                                })\n", "                                memory['validated_edges'].add(edge_key)\n", "                                \n", "            except Exception as e:\n", "                print(f\"⚠️ Error processing transformations for {class_name}: {e}\")\n", "                continue\n", "    \n", "    return pd.DataFrame(all_relationships)\n", "\n", "# Execute Stage 5: Advanced Transformation Analysis\n", "print(\"🚀 Starting Stage 5: Advanced Transformation Analysis...\")\n", "df_transformations = run_advanced_transformation_analysis()\n", "\n", "# Combine basic structure and transformations\n", "print(\"🔗 Combining basic structure and transformation relationships...\")\n", "df_llm_combined = pd.concat([df_basic_structure, df_transformations], ignore_index=True)\n", "\n", "# Store Stage 5 results\n", "memory['stage_5_results'] = {\n", "    'basic_structure_count': len(df_basic_structure),\n", "    'transformation_count': len(df_transformations),\n", "    'total_llm_relationships': len(df_llm_combined),\n", "    'produces': len(df_transformations[df_transformations['relationship'] == 'PRODUCES']) if not df_transformations.empty else 0,\n", "    'transforms_to': len(df_transformations[df_transformations['relationship'] == 'TRANSFORMS_TO']) if not df_transformations.empty else 0,\n", "    'flows_to': len(df_transformations[df_transformations['relationship'] == 'FLOWS_TO']) if not df_transformations.empty else 0,\n", "    'returns_data_to': len(df_transformations[df_transformations['relationship'] == 'RETURNS_DATA_TO']) if not df_transformations.empty else 0\n", "}\n", "\n", "save_memory(memory)\n", "\n", "print(f\"✅ Stage 5 Complete: {len(df_transformations)} transformation relationships extracted\")\n", "print(f\"📊 Combined LLM Results: {len(df_basic_structure)} basic + {len(df_transformations)} transformations = {len(df_llm_combined)} total\")\n", "print(f\"🏭 Produces: {memory['stage_5_results']['produces']}, Transforms: {memory['stage_5_results']['transforms_to']}\")\n", "print(f\"🌊 Flows: {memory['stage_5_results']['flows_to']}, Returns: {memory['stage_5_results']['returns_data_to']}\")"]}, {"cell_type": "code", "execution_count": null, "id": "stage6_consolidation_neo4j", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 6: CONSOLIDATION AND NEO4J UPLOAD ==========\n", "\n", "def consolidate_and_upload_to_neo4j():\n", "    \"\"\"Consolidate all relationships and upload to Neo4j with enhanced validation\"\"\"\n", "    \n", "    # Combine all relationship DataFrames\n", "    all_dataframes = []\n", "    \n", "    if not df_hierarchy.empty:\n", "        all_dataframes.append(df_hierarchy[['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship']])\n", "        print(f\"📁 Added {len(df_hierarchy)} folder/file relationships\")\n", "    \n", "    if not df_file_class.empty:\n", "        all_dataframes.append(df_file_class[['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship']])\n", "        print(f\"🏗️ Added {len(df_file_class)} file/class relationships\")\n", "    \n", "    if not df_ast.empty:\n", "        all_dataframes.append(df_ast[['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship']])\n", "        print(f\"🌳 Added {len(df_ast)} AST relationships\")\n", "    \n", "    if not df_llm_combined.empty:\n", "        all_dataframes.append(df_llm_combined[['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship']])\n", "        print(f\"🤖 Added {len(df_llm_combined)} combined LLM relationships (basic structure + transformations)\")\n", "    \n", "    if not all_dataframes:\n", "        print(\"❌ No data to consolidate\")\n", "        return pd.DataFrame()\n", "    \n", "    # Consolidate all relationships\n", "    df_consolidated = pd.concat(all_dataframes, ignore_index=True)\n", "    \n", "    # Enhanced deduplication with validation\n", "    print(f\"🔍 Deduplicating {len(df_consolidated)} total relationships...\")\n", "    \n", "    # Remove exact duplicates\n", "    df_consolidated = df_consolidated.drop_duplicates()\n", "    \n", "    # Remove invalid relationships (empty nodes)\n", "    df_consolidated = df_consolidated[\n", "        (df_consolidated['source_node'].notna()) & \n", "        (df_consolidated['destination_node'].notna()) &\n", "        (df_consolidated['source_node'] != '') & \n", "        (df_consolidated['destination_node'] != '')\n", "    ]\n", "    \n", "    # Remove self-references (node pointing to itself)\n", "    df_consolidated = df_consolidated[\n", "        df_consolidated['source_node'] != df_consolidated['destination_node']\n", "    ]\n", "    \n", "    print(f\"✅ Final consolidated dataset: {len(df_consolidated)} unique relationships\")\n", "    \n", "    # Upload to Neo4j in batches\n", "    batch_size = 100\n", "    total_batches = (len(df_consolidated) + batch_size - 1) // batch_size\n", "    \n", "    print(f\"📤 Uploading to Neo4j in {total_batches} batches...\")\n", "    \n", "    for i in range(0, len(df_consolidated), batch_size):\n", "        batch_df = df_consolidated.iloc[i:i+batch_size]\n", "        batch_num = i // batch_size + 1\n", "        \n", "        try:\n", "            # Create nodes and relationships\n", "            for _, row in batch_df.iterrows():\n", "                # Create source node\n", "                graph.query(\n", "                    f\"MERGE (s:{row['source_type']} {{name: $source_name}})\",\n", "                    {'source_name': row['source_node']}\n", "                )\n", "                \n", "                # Create destination node\n", "                graph.query(\n", "                    f\"MERGE (d:{row['destination_type']} {{name: $dest_name}})\",\n", "                    {'dest_name': row['destination_node']}\n", "                )\n", "                \n", "                # Create relationship\n", "                graph.query(\n", "                    f\"MATCH (s:{row['source_type']} {{name: $source_name}}) \"\n", "                    f\"MATCH (d:{row['destination_type']} {{name: $dest_name}}) \"\n", "                    f\"MERGE (s)-[:{row['relationship']}]->(d)\",\n", "                    {\n", "                        'source_name': row['source_node'],\n", "                        'dest_name': row['destination_node']\n", "                    }\n", "                )\n", "            \n", "            print(f\"✅ Batch {batch_num}/{total_batches} uploaded successfully\")\n", "            \n", "        except Exception as e:\n", "            print(f\"❌ Error uploading batch {batch_num}: {e}\")\n", "            continue\n", "    \n", "    return df_consolidated\n", "\n", "# Execute Stage 6: Consolidation and Neo4j Upload\n", "print(\"🚀 Starting Stage 6: Consolidation and Neo4j Upload...\")\n", "df_final = consolidate_and_upload_to_neo4j()\n", "\n", "# Store Stage 6 results\n", "memory['stage_6_results'] = {\n", "    'total_final_relationships': len(df_final),\n", "    'unique_nodes': len(set(df_final['source_node'].tolist() + df_final['destination_node'].tolist())),\n", "    'relationship_types': df_final['relationship'].nunique(),\n", "    'node_types': len(set(df_final['source_type'].tolist() + df_final['destination_type'].tolist()))\n", "}\n", "\n", "save_memory(memory)\n", "\n", "print(f\"✅ Stage 6 Complete: {len(df_final)} relationships uploaded to Neo4j\")\n", "print(f\"🎯 Unique Nodes: {memory['stage_6_results']['unique_nodes']}, Relationship Types: {memory['stage_6_results']['relationship_types']}\")\n", "print(f\"📊 Node Types: {memory['stage_6_results']['node_types']}\")"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 4}