{"cells": [{"cell_type": "code", "execution_count": 12, "id": "71ce5a7f", "metadata": {}, "outputs": [], "source": ["import os\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "import pandas as pd\n", "import re\n", "from collections import defaultdict\n", "\n", "from tree_sitter import Language, Parser\n", "import tree_sitter_java as tsjava\n", "\n", "from langchain_community.document_loaders import TextLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter, Language as LC_Language\n", "from langchain_google_genai import ChatGoogleGenerativeAI\n", "from langchain_experimental.graph_transformers import LLMGraphTransformer\n", "from langchain_community.graphs import Neo4jGraph\n", "\n", "BASE_PATH = Path(\"C:\\Shaik\\sample\\java\")\n", "NEO4J_URI = \"bolt://localhost:7687\"\n", "NEO4J_USER = \"neo4j\"\n", "NEO4J_PASSWORD = \"Test@7889\"\n", "NEO4J_DB = \"loanapp\"\n", "GOOGLE_API_KEY = \"AIzaSyASelgh7zLlDIpbpNyJkgkimlM_ODU86dY\"\n", "\n", "graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)\n", "JAVA_LANGUAGE = Language(tsjava.language())\n", "parser = Parser(JAVA_LANGUAGE)\n", "\n", "llm = ChatGoogleGenerativeAI(\n", "    model=\"gemini-2.5-pro\",\n", "    temperature=0,\n", "    google_api_key=GOOGLE_API_KEY\n", ")\n"]}, {"cell_type": "code", "execution_count": 3, "id": "f265ecfc", "metadata": {}, "outputs": [], "source": ["def extract_folder_file_hierarchy(base_path):\n", "    folder_records, file_records = [], []\n", "    base_path = os.path.abspath(base_path)\n", "    base_folder_name = os.path.basename(base_path)\n", "    processed_folders = set()\n", "    \n", "    for root, dirs, files in os.walk(base_path):\n", "        rel_root = os.path.relpath(root, base_path)\n", "        parent_folder = base_folder_name if rel_root == \".\" else os.path.dirname(rel_root) or base_folder_name\n", "        current_folder = base_folder_name if rel_root == \".\" else os.path.basename(rel_root)\n", "        \n", "        folder_key = f\"{parent_folder}->{current_folder}\"\n", "        if folder_key not in processed_folders and parent_folder != current_folder:\n", "            folder_records.append({\n", "                \"source_node\": parent_folder,\n", "                \"source_type\": \"folder\",\n", "                \"destination_node\": current_folder,\n", "                \"destination_type\": \"folder\",\n", "                \"relationship\": \"contains\",\n", "                \"file_path\": None\n", "            })\n", "            processed_folders.add(folder_key)\n", "        \n", "        for f in files:\n", "            if f.endswith(\".java\"):\n", "                file_rel_path = os.path.relpath(os.path.join(root, f), base_path)\n", "                file_records.append({\n", "                    \"source_node\": current_folder,\n", "                    \"source_type\": \"folder\",\n", "                    \"destination_node\": f,\n", "                    \"destination_type\": \"file\",\n", "                    \"relationship\": \"contains\",\n", "                    \"file_path\": file_rel_path\n", "                })\n", "    return folder_records, file_records\n", "\n", "folder_records, file_records = extract_folder_file_hierarchy(BASE_PATH)\n", "df_folders = pd.DataFrame(folder_records)\n", "df_files = pd.DataFrame(file_records)\n"]}, {"cell_type": "code", "execution_count": 4, "id": "80615f97", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>source_node</th>\n", "      <th>source_type</th>\n", "      <th>destination_node</th>\n", "      <th>destination_type</th>\n", "      <th>relationship</th>\n", "      <th>file_path</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>java</td>\n", "      <td>folder</td>\n", "      <td>configuration</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>java</td>\n", "      <td>folder</td>\n", "      <td>controller</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>java</td>\n", "      <td>folder</td>\n", "      <td>dto</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>java</td>\n", "      <td>folder</td>\n", "      <td>model</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>java</td>\n", "      <td>folder</td>\n", "      <td>repository</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>java</td>\n", "      <td>folder</td>\n", "      <td>service</td>\n", "      <td>folder</td>\n", "      <td>contains</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  source_node source_type destination_node destination_type relationship  \\\n", "0        java      folder    configuration           folder     contains   \n", "1        java      folder       controller           folder     contains   \n", "2        java      folder              dto           folder     contains   \n", "3        java      folder            model           folder     contains   \n", "4        java      folder       repository           folder     contains   \n", "5        java      folder          service           folder     contains   \n", "\n", "  file_path  \n", "0      None  \n", "1      None  \n", "2      None  \n", "3      None  \n", "4      None  \n", "5      None  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df_folders"]}, {"cell_type": "code", "execution_count": 5, "id": "300dd08d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>source_node</th>\n", "      <th>source_type</th>\n", "      <th>destination_node</th>\n", "      <th>destination_type</th>\n", "      <th>relationship</th>\n", "      <th>file_path</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>java</td>\n", "      <td>folder</td>\n", "      <td>LoanAppApplication.java</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>LoanAppApplication.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>configuration</td>\n", "      <td>folder</td>\n", "      <td>PrimaryDataSourceProperties.java</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>configuration\\PrimaryDataSourceProperties.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>configuration</td>\n", "      <td>folder</td>\n", "      <td>SecondDataSourceProperties.java</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>configuration\\SecondDataSourceProperties.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>configuration</td>\n", "      <td>folder</td>\n", "      <td>StoredProcDBConfig.java</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>configuration\\StoredProcDBConfig.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>controller</td>\n", "      <td>folder</td>\n", "      <td>UserController.java</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>controller\\UserController.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>dto</td>\n", "      <td>folder</td>\n", "      <td>UserLoanRequestDTO.java</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>dto\\UserLoanRequestDTO.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>model</td>\n", "      <td>folder</td>\n", "      <td>LoanDetails.java</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>model\\LoanDetails.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>model</td>\n", "      <td>folder</td>\n", "      <td>User.java</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>model\\User.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>repository</td>\n", "      <td>folder</td>\n", "      <td>LoanDetailsRepository.java</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>repository\\LoanDetailsRepository.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>repository</td>\n", "      <td>folder</td>\n", "      <td>UserRepository.java</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>repository\\UserRepository.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>service</td>\n", "      <td>folder</td>\n", "      <td>StoreProcService.java</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>service\\StoreProcService.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>service</td>\n", "      <td>folder</td>\n", "      <td>UserService.java</td>\n", "      <td>file</td>\n", "      <td>contains</td>\n", "      <td>service\\UserService.java</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      source_node source_type                  destination_node  \\\n", "0            java      folder           LoanAppApplication.java   \n", "1   configuration      folder  PrimaryDataSourceProperties.java   \n", "2   configuration      folder   SecondDataSourceProperties.java   \n", "3   configuration      folder           StoredProcDBConfig.java   \n", "4      controller      folder               UserController.java   \n", "5             dto      folder           UserLoanRequestDTO.java   \n", "6           model      folder                  LoanDetails.java   \n", "7           model      folder                         User.java   \n", "8      repository      folder        LoanDetailsRepository.java   \n", "9      repository      folder               UserRepository.java   \n", "10        service      folder             StoreProcService.java   \n", "11        service      folder                  UserService.java   \n", "\n", "   destination_type relationship  \\\n", "0              file     contains   \n", "1              file     contains   \n", "2              file     contains   \n", "3              file     contains   \n", "4              file     contains   \n", "5              file     contains   \n", "6              file     contains   \n", "7              file     contains   \n", "8              file     contains   \n", "9              file     contains   \n", "10             file     contains   \n", "11             file     contains   \n", "\n", "                                         file_path  \n", "0                          LoanAppApplication.java  \n", "1   configuration\\PrimaryDataSourceProperties.java  \n", "2    configuration\\SecondDataSourceProperties.java  \n", "3            configuration\\StoredProcDBConfig.java  \n", "4                   controller\\UserController.java  \n", "5                      dto\\UserLoanRequestDTO.java  \n", "6                           model\\LoanDetails.java  \n", "7                                  model\\User.java  \n", "8            repository\\LoanDetailsRepository.java  \n", "9                   repository\\UserRepository.java  \n", "10                   service\\StoreProcService.java  \n", "11                        service\\UserService.java  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df_files"]}, {"cell_type": "code", "execution_count": 6, "id": "f3099afc", "metadata": {}, "outputs": [], "source": ["def read_source_code(file_path):\n", "    with open(file_path, \"r\", encoding=\"utf-8\") as f:\n", "        return f.read().encode(\"utf-8\")\n", "\n", "def extract_package_and_imports(source_code_str):\n", "    package_pattern = r'package\\s+([\\w\\.]+);'\n", "    import_pattern = r'import\\s+([\\w\\.]+);'\n", "    package_match = re.search(package_pattern, source_code_str)\n", "    package_name = package_match.group(1) if package_match else None\n", "    import_matches = re.findall(import_pattern, source_code_str)\n", "    return package_name, import_matches\n", "\n", "def build_class_registry():\n", "    class_registry = {}\n", "    for root, _, files in os.walk(BASE_PATH):\n", "        for file in files:\n", "            if file.endswith(\".java\"):\n", "                file_path = os.path.join(root, file)\n", "                with open(file_path, 'r', encoding='utf-8') as f:\n", "                    source_code_str = f.read()\n", "                package_name, imports = extract_package_and_imports(source_code_str)\n", "                class_name = file.replace('.java', '')\n", "                fqcn = f\"{package_name}.{class_name}\" if package_name else class_name\n", "                class_registry[class_name] = {\n", "                    'fqcn': fqcn,\n", "                    'package': package_name,\n", "                    'file_path': file_path,\n", "                    'imports': imports\n", "                }\n", "    return class_registry\n", "\n", "class_registry = build_class_registry()\n"]}, {"cell_type": "code", "execution_count": 7, "id": "aa41ed4d", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'LoanAppApplication': {'fqcn': 'com.morganstanley.loanApp.LoanAppApplication',\n", "  'package': 'com.morganstanley.loanApp',\n", "  'file_path': 'C:\\\\Shaik\\\\sample\\\\java\\\\LoanAppApplication.java',\n", "  'imports': ['jakarta.annotation.PostConstruct',\n", "   'org.springframework.beans.factory.annotation.Autowired',\n", "   'org.springframework.beans.factory.annotation.Qualifier',\n", "   'org.springframework.boot.SpringApplication',\n", "   'org.springframework.boot.autoconfigure.SpringBootApplication',\n", "   'org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration',\n", "   'org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration',\n", "   'org.springframework.boot.context.properties.EnableConfigurationProperties',\n", "   'org.springframework.context.ConfigurableApplicationContext',\n", "   'org.springframework.core.env.Environment',\n", "   'javax.sql.DataSource',\n", "   'java.sql.SQLException']},\n", " 'PrimaryDataSourceProperties': {'fqcn': 'com.morganstanley.loanApp.configuration.PrimaryDataSourceProperties',\n", "  'package': 'com.morganstanley.loanApp.configuration',\n", "  'file_path': 'C:\\\\Shaik\\\\sample\\\\java\\\\configuration\\\\PrimaryDataSourceProperties.java',\n", "  'imports': ['jakarta.annotation.PostConstruct',\n", "   'org.springframework.boot.context.properties.ConfigurationProperties',\n", "   'org.springframework.stereotype.Component']},\n", " 'SecondDataSourceProperties': {'fqcn': 'com.morganstanley.loanApp.configuration.SecondDataSourceProperties',\n", "  'package': 'com.morganstanley.loanApp.configuration',\n", "  'file_path': 'C:\\\\Shaik\\\\sample\\\\java\\\\configuration\\\\SecondDataSourceProperties.java',\n", "  'imports': ['jakarta.annotation.PostConstruct',\n", "   'org.springframework.boot.context.properties.ConfigurationProperties',\n", "   'org.springframework.stereotype.Component']},\n", " 'StoredProcDBConfig': {'fqcn': 'com.morganstanley.loanApp.configuration.StoredProcDBConfig',\n", "  'package': 'com.morganstanley.loanApp.configuration',\n", "  'file_path': 'C:\\\\Shaik\\\\sample\\\\java\\\\configuration\\\\StoredProcDBConfig.java',\n", "  'imports': ['javax.sql.DataSource',\n", "   'com.zaxxer.hikari.HikariDataSource',\n", "   'org.springframework.beans.factory.annotation.Qualifier',\n", "   'org.springframework.boot.context.properties.ConfigurationProperties',\n", "   'org.springframework.boot.jdbc.DataSourceBuilder',\n", "   'org.springframework.context.annotation.Bean',\n", "   'org.springframework.context.annotation.Configuration',\n", "   'org.springframework.context.annotation.Primary',\n", "   'org.springframework.jdbc.core.JdbcTemplate']},\n", " 'UserController': {'fqcn': 'com.morganstanley.loanApp.controller.UserController',\n", "  'package': 'com.morganstanley.loanApp.controller',\n", "  'file_path': 'C:\\\\Shaik\\\\sample\\\\java\\\\controller\\\\UserController.java',\n", "  'imports': ['com.morganstanley.loanApp.dto.UserLoanRequestDTO',\n", "   'com.morganstanley.loanApp.model.User',\n", "   'com.morganstanley.loanApp.service.StoreProcService',\n", "   'com.morganstanley.loanApp.service.UserService',\n", "   'jakarta.validation.Valid',\n", "   'org.springframework.beans.factory.annotation.Autowired',\n", "   'org.springframework.http.HttpStatus',\n", "   'org.springframework.http.ResponseEntity',\n", "   'org.springframework.web.bind.annotation.PostMapping',\n", "   'org.springframework.web.bind.annotation.RequestBody',\n", "   'org.springframework.web.bind.annotation.RequestMapping',\n", "   'org.springframework.web.bind.annotation.RestController',\n", "   'java.util.ArrayList',\n", "   'java.util.List']},\n", " 'UserLoanRequestDTO': {'fqcn': 'com.morganstanley.loanApp.dto.UserLoanRequestDTO',\n", "  'package': 'com.morganstanley.loanApp.dto',\n", "  'file_path': 'C:\\\\Shaik\\\\sample\\\\java\\\\dto\\\\UserLoanRequestDTO.java',\n", "  'imports': ['jakarta.validation.constraints.Email',\n", "   'jakarta.validation.constraints.NotBlank',\n", "   'jakarta.validation.constraints.NotNull',\n", "   'jakarta.validation.constraints.Positive',\n", "   'lombok.Data',\n", "   'lombok.Getter',\n", "   'lombok.Setter']},\n", " 'LoanDetails': {'fqcn': 'com.morganstanley.loanApp.model.LoanDetails',\n", "  'package': 'com.morganstanley.loanApp.model',\n", "  'file_path': 'C:\\\\Shaik\\\\sample\\\\java\\\\model\\\\LoanDetails.java',\n", "  'imports': ['lombok.Data']},\n", " 'User': {'fqcn': 'com.morganstanley.loanApp.model.User',\n", "  'package': 'com.morganstanley.loanApp.model',\n", "  'file_path': 'C:\\\\Shaik\\\\sample\\\\java\\\\model\\\\User.java',\n", "  'imports': ['lombok.Data']},\n", " 'LoanDetailsRepository': {'fqcn': 'com.morganstanley.loanApp.repository.LoanDetailsRepository',\n", "  'package': 'com.morganstanley.loanApp.repository',\n", "  'file_path': 'C:\\\\Shaik\\\\sample\\\\java\\\\repository\\\\LoanDetailsRepository.java',\n", "  'imports': ['com.morganstanley.loanApp.model.LoanDetails',\n", "   'org.springframework.data.jpa.repository.JpaRepository']},\n", " 'UserRepository': {'fqcn': 'com.morganstanley.loanApp.repository.UserRepository',\n", "  'package': 'com.morganstanley.loanApp.repository',\n", "  'file_path': 'C:\\\\Shaik\\\\sample\\\\java\\\\repository\\\\UserRepository.java',\n", "  'imports': ['com.morganstanley.loanApp.model.User',\n", "   'org.springframework.data.jpa.repository.JpaRepository']},\n", " 'StoreProcService': {'fqcn': 'com.morganstanley.loanApp.service.StoreProcService',\n", "  'package': 'com.morganstanley.loanApp.service',\n", "  'file_path': 'C:\\\\Shaik\\\\sample\\\\java\\\\service\\\\StoreProcService.java',\n", "  'imports': ['org.springframework.beans.factory.annotation.Autowired',\n", "   'org.springframework.beans.factory.annotation.Qualifier',\n", "   'org.springframework.jdbc.core.JdbcTemplate',\n", "   'org.springframework.stereotype.Service',\n", "   'java.sql.CallableStatement',\n", "   'java.sql.Connection',\n", "   'java.sql.Types']},\n", " 'UserService': {'fqcn': 'com.morganstanley.loanApp.service.UserService',\n", "  'package': 'com.morganstanley.loanApp.service',\n", "  'file_path': 'C:\\\\Shaik\\\\sample\\\\java\\\\service\\\\UserService.java',\n", "  'imports': ['com.morganstanley.loanApp.dto.UserLoanRequestDTO',\n", "   'com.morganstanley.loanApp.model.LoanDetails',\n", "   'com.morganstanley.loanApp.model.User',\n", "   'com.morganstanley.loanApp.repository.LoanDetailsRepository',\n", "   'com.morganstanley.loanApp.repository.UserRepository',\n", "   'org.springframework.beans.factory.annotation.Autowired',\n", "   'org.springframework.stereotype.Service']}}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["class_registry"]}, {"cell_type": "code", "execution_count": 8, "id": "c6c7d2b5", "metadata": {}, "outputs": [], "source": ["def extract_enhanced_ast_structure(node, source_code, file_path, class_registry):\n", "    records = []\n", "    current_class, current_method = None, None\n", "    file_name = os.path.basename(file_path)\n", "    source_code_str = source_code.decode('utf-8')\n", "    package_name, imports = extract_package_and_imports(source_code_str)\n", "\n", "    def resolve_class_reference(class_ref):\n", "        if class_ref in class_registry:\n", "            return class_ref\n", "        for simple_name, info in class_registry.items():\n", "            if info['fqcn'] == class_ref or class_ref.endswith('.' + simple_name):\n", "                return simple_name\n", "        if '.' in class_ref:\n", "            return class_ref.split('.')[-1]\n", "        return class_ref\n", "\n", "    def traverse(n):\n", "        nonlocal current_class, current_method\n", "        if n.type == \"class_declaration\":\n", "            for child in n.children:\n", "                if child.type == \"identifier\":\n", "                    class_name = source_code[child.start_byte:child.end_byte].decode().strip()\n", "                    canonical_class_name = resolve_class_reference(class_name)\n", "                    records.append({\n", "                        \"source_node\": file_name,\n", "                        \"source_type\": \"file\",\n", "                        \"destination_node\": canonical_class_name,\n", "                        \"destination_type\": \"class\",\n", "                        \"relationship\": \"declares\",\n", "                        \"file_path\": file_path\n", "                    })\n", "                    current_class = canonical_class_name\n", "            for child in n.children:\n", "                traverse(child)\n", "            current_class = None\n", "        elif n.type == \"method_declaration\":\n", "            for child in n.children:\n", "                if child.type == \"identifier\":\n", "                    method_name = source_code[child.start_byte:child.end_byte].decode().strip()\n", "                    if current_class:\n", "                        records.append({\n", "                            \"source_node\": current_class,\n", "                            \"source_type\": \"class\",\n", "                            \"destination_node\": method_name,\n", "                            \"destination_type\": \"method\",\n", "                            \"relationship\": \"declares\",\n", "                            \"file_path\": file_path\n", "                        })\n", "                    current_method = method_name\n", "            for child in n.children:\n", "                traverse(child)\n", "            current_method = None\n", "        else:\n", "            for child in n.children:\n", "                traverse(child)\n", "    traverse(node)\n", "    return records\n", "\n", "ast_records = []\n", "for root, _, files in os.walk(BASE_PATH):\n", "    for file in files:\n", "        if file.endswith(\".java\"):\n", "            file_path = os.path.join(root, file)\n", "            source_code = read_source_code(file_path)\n", "            tree = parser.parse(source_code)\n", "            ast_records.extend(extract_enhanced_ast_structure(tree.root_node, source_code, file_path, class_registry))\n", "\n", "df_ast = pd.DataFrame(ast_records)\n"]}, {"cell_type": "code", "execution_count": 9, "id": "78233b4f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>source_node</th>\n", "      <th>source_type</th>\n", "      <th>destination_node</th>\n", "      <th>destination_type</th>\n", "      <th>relationship</th>\n", "      <th>file_path</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>SecondDataSourceProperties</td>\n", "      <td>class</td>\n", "      <td>setDriverClassName</td>\n", "      <td>method</td>\n", "      <td>declares</td>\n", "      <td>C:\\Shaik\\sample\\java\\configuration\\SecondDataS...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>SecondDataSourceProperties</td>\n", "      <td>class</td>\n", "      <td>getDriverClassName</td>\n", "      <td>method</td>\n", "      <td>declares</td>\n", "      <td>C:\\Shaik\\sample\\java\\configuration\\SecondDataS...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>StoreProcService</td>\n", "      <td>class</td>\n", "      <td>callStoreProc</td>\n", "      <td>method</td>\n", "      <td>declares</td>\n", "      <td>C:\\Shaik\\sample\\java\\service\\StoreProcService....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>UserService</td>\n", "      <td>class</td>\n", "      <td>createUserWithLoan</td>\n", "      <td>method</td>\n", "      <td>declares</td>\n", "      <td>C:\\Shaik\\sample\\java\\service\\UserService.java</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>SecondDataSourceProperties</td>\n", "      <td>class</td>\n", "      <td>printProps</td>\n", "      <td>method</td>\n", "      <td>declares</td>\n", "      <td>C:\\Shaik\\sample\\java\\configuration\\SecondDataS...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>PrimaryDataSourceProperties</td>\n", "      <td>class</td>\n", "      <td>setUrl</td>\n", "      <td>method</td>\n", "      <td>declares</td>\n", "      <td>C:\\Shaik\\sample\\java\\configuration\\PrimaryData...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>StoreProcService.java</td>\n", "      <td>file</td>\n", "      <td>StoreProcService</td>\n", "      <td>class</td>\n", "      <td>declares</td>\n", "      <td>C:\\Shaik\\sample\\java\\service\\StoreProcService....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>PrimaryDataSourceProperties</td>\n", "      <td>class</td>\n", "      <td>getPassword</td>\n", "      <td>method</td>\n", "      <td>declares</td>\n", "      <td>C:\\Shaik\\sample\\java\\configuration\\PrimaryData...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>UserController</td>\n", "      <td>class</td>\n", "      <td>createUserWithLoan</td>\n", "      <td>method</td>\n", "      <td>declares</td>\n", "      <td>C:\\Shaik\\sample\\java\\controller\\UserController...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>PrimaryDataSourceProperties</td>\n", "      <td>class</td>\n", "      <td>getUsername</td>\n", "      <td>method</td>\n", "      <td>declares</td>\n", "      <td>C:\\Shaik\\sample\\java\\configuration\\PrimaryData...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                    source_node source_type    destination_node  \\\n", "21   SecondDataSourceProperties       class  setDriverClassName   \n", "20   SecondDataSourceProperties       class  getDriverClassName   \n", "43             StoreProcService       class       callStoreProc   \n", "45                  UserService       class  createUserWithLoan   \n", "22   SecondDataSourceProperties       class          printProps   \n", "5   PrimaryDataSourceProperties       class              setUrl   \n", "42        StoreProcService.java        file    StoreProcService   \n", "8   PrimaryDataSourceProperties       class         getPassword   \n", "28               UserController       class  createUserWithLoan   \n", "6   PrimaryDataSourceProperties       class         getUsername   \n", "\n", "   destination_type relationship  \\\n", "21           method     declares   \n", "20           method     declares   \n", "43           method     declares   \n", "45           method     declares   \n", "22           method     declares   \n", "5            method     declares   \n", "42            class     declares   \n", "8            method     declares   \n", "28           method     declares   \n", "6            method     declares   \n", "\n", "                                            file_path  \n", "21  C:\\Shaik\\sample\\java\\configuration\\SecondDataS...  \n", "20  C:\\Shaik\\sample\\java\\configuration\\SecondDataS...  \n", "43  C:\\Shaik\\sample\\java\\service\\StoreProcService....  \n", "45      C:\\Shaik\\sample\\java\\service\\UserService.java  \n", "22  C:\\Shaik\\sample\\java\\configuration\\SecondDataS...  \n", "5   C:\\Shaik\\sample\\java\\configuration\\PrimaryData...  \n", "42  C:\\Shaik\\sample\\java\\service\\StoreProcService....  \n", "8   C:\\Shaik\\sample\\java\\configuration\\PrimaryData...  \n", "28  C:\\Shaik\\sample\\java\\controller\\UserController...  \n", "6   C:\\Shaik\\sample\\java\\configuration\\PrimaryData...  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df_ast.sample(10)"]}, {"cell_type": "code", "execution_count": 10, "id": "ef5b267a", "metadata": {}, "outputs": [], "source": ["splitter = RecursiveCharacterTextSplitter.from_language(\n", "    language=LC_Language.JAVA,\n", "    chunk_size=4000,\n", "    chunk_overlap=200\n", ")\n", "\n", "java_docs, split_docs = [], []\n", "for root, _, files in os.walk(BASE_PATH):\n", "    for file in files:\n", "        if file.endswith(\".java\"):\n", "            loader = TextLoader(os.path.join(root, file))\n", "            java_docs.extend(loader.load())\n", "\n", "for doc in java_docs:\n", "    split_docs.extend(splitter.split_documents([doc]))\n", "\n", "def build_enhanced_system_prompt(file_path, ast_df, class_registry):\n", "    ast_subset = ast_df[ast_df[\"file_path\"] == file_path] if len(ast_df) > 0 else pd.DataFrame()\n", "    ast_context = \"\"\n", "    for _, row in ast_subset.iterrows():\n", "        ast_context += f\"{row['source_type']}:{row['source_node']} -[{row['relationship']}]-> {row['destination_type']}:{row['destination_node']}\\n\"\n", "    registry_context = \"Known Classes:\\n\"\n", "    for class_name, info in class_registry.items():\n", "        registry_context += f\"- {class_name} (FQCN: {info['fqcn']})\\n\"\n", "    prompt = f\"\"\"\n", "You are a Java code lineage extraction engine. Your task is to extract relationships between code entities.\n", "\n", "CONTEXT:\n", "{registry_context}\n", "\n", "AST RELATIONSHIPS:\n", "{ast_context}\n", "\n", "RULES:\n", "1. Use SIMPLE class names only\n", "2. Method calls should link to actual method names\n", "3. Extract structural relationships\n", "4. Avoid duplicates\n", "5. Create 'uses' for dependency injections\n", "6. Extract cross-class method calls\n", "\n", "Extract triples in format:\n", "[SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName\n", "\n", "Return ONLY the triples, no explanations.\n", "\"\"\"\n", "    return prompt"]}, {"cell_type": "code", "execution_count": 13, "id": "17a316b6", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Stage 3: Enhanced LLM Extraction: 100%|██████████| 12/12 [05:24<00:00, 27.01s/it]\n"]}], "source": ["\n", "\n", "all_llm_lineage = []\n", "for chunk in tqdm(split_docs, desc=\"Stage 3: Enhanced LLM Extraction\"):\n", "    file_path = chunk.metadata.get(\"source\")\n", "    system_prompt = build_enhanced_system_prompt(file_path, df_ast, class_registry)\n", "    transformer = LLMGraphTransformer(\n", "        llm=llm,\n", "        additional_instructions=system_prompt,\n", "        allowed_nodes=[\"file\", \"class\", \"method\", \"variable\", \"table\"],\n", "        allowed_relationships=[\n", "            (\"file\", \"declares\", \"class\"),\n", "            (\"class\", \"declares\", \"method\"),\n", "            (\"method\", \"calls\", \"method\"),\n", "            (\"class\", \"has_field\", \"variable\"),\n", "            (\"method\", \"uses\", \"variable\"),\n", "            (\"class\", \"uses\", \"class\"),\n", "            (\"method\", \"reads_from\", \"table\"),\n", "            (\"method\", \"writes_to\", \"table\")\n", "        ],\n", "        strict_mode=True,\n", "        node_properties=False,\n", "        relationship_properties=False,\n", "    )\n", "    try:\n", "        graph_docs = transformer.convert_to_graph_documents([chunk])\n", "        for gd in graph_docs:\n", "            for rel in gd.relationships:\n", "                s_node = rel.source.id.strip().lower()\n", "                s_type = rel.source.type.strip().lower()\n", "                t_node = rel.target.id.strip().lower()\n", "                t_type = rel.target.type.strip().lower()\n", "                rel_type = rel.type.strip().lower()\n", "\n", "                def normalize_entity(entity_name, entity_type):\n", "                    if entity_type in [\"class\", \"method\"]:\n", "                        if '.' in entity_name:\n", "                            entity_name = entity_name.split('.')[-1]\n", "                        entity_name = re.sub(r'\\.(java|class)$', '', entity_name)\n", "                        if entity_type == \"class\":\n", "                            for class_name in class_registry.keys():\n", "                                if entity_name.lower() == class_name.lower():\n", "                                    return class_name.lower()\n", "                    return entity_name\n", "\n", "                s_node = normalize_entity(s_node, s_type)\n", "                t_node = normalize_entity(t_node, t_type)\n", "\n", "                if s_node == t_node and s_type == t_type:\n", "                    continue\n", "                if not s_node or not t_node:\n", "                    continue\n", "\n", "                all_llm_lineage.append({\n", "                    \"source_node\": s_node,\n", "                    \"source_type\": s_type,\n", "                    \"destination_node\": t_node,\n", "                    \"destination_type\": t_type,\n", "                    \"relationship\": rel_type,\n", "                    \"file_path\": file_path\n", "                })\n", "    except:\n", "        continue\n", "\n", "df_llm_lineage = pd.DataFrame(all_llm_lineage)\n"]}, {"cell_type": "code", "execution_count": 14, "id": "aec83127", "metadata": {}, "outputs": [], "source": ["for df in [df_folders, df_files, df_llm_lineage]:\n", "    for col in [\"source_node\", \"destination_node\", \"source_type\", \"destination_type\", \"relationship\"]:\n", "        if col in df.columns:\n", "            df[col] = df[col].astype(str).str.strip().str.lower()\n", "\n", "df_combined = pd.concat([df_folders, df_files, df_llm_lineage], ignore_index=True)\n", "df_combined.drop_duplicates(\n", "    subset=[\"source_node\", \"source_type\", \"destination_node\", \"destination_type\", \"relationship\"],\n", "    inplace=True\n", ")\n", "df_combined = df_combined[\n", "    ~((df_combined['source_node'] == df_combined['destination_node']) &\n", "      (df_combined['source_type'] == df_combined['destination_type']))\n", "]\n", "df_combined = df_combined[\n", "    (df_combined['source_node'].notna()) & (df_combined['destination_node'].notna()) &\n", "    (df_combined['source_node'] != '') & (df_combined['destination_node'] != '') &\n", "    (df_combined['source_node'] != 'none') & (df_combined['destination_node'] != 'none')\n", "]\n"]}, {"cell_type": "code", "execution_count": 15, "id": "b0d7d0ee", "metadata": {}, "outputs": [], "source": ["# ==============================\n", "# Stage 6: File vs Class Correction\n", "# ==============================\n", "\n", "# Create a corrected dataframe for insertion\n", "df_corrected = df_combined.copy()\n", "\n", "# Fix File and Class naming for consistency\n", "for idx, row in df_corrected.iterrows():\n", "    source_node = str(row[\"source_node\"]).strip()\n", "    dest_node = str(row[\"destination_node\"]).strip()\n", "    source_type = str(row[\"source_type\"]).strip().lower()\n", "    dest_type = str(row[\"destination_type\"]).strip().lower()\n", "\n", "    # --- File Node Correction ---\n", "    if source_type == \"file\":\n", "        match = df_files[df_files[\"destination_node\"].str.replace(\".java\", \"\", case=False) == source_node.replace(\".java\", \"\")]\n", "        if not match.empty:\n", "            df_corrected.at[idx, \"source_node\"] = match.iloc[0][\"destination_node\"]\n", "\n", "    if dest_type == \"file\":\n", "        match = df_files[df_files[\"destination_node\"].str.replace(\".java\", \"\", case=False) == dest_node.replace(\".java\", \"\")]\n", "        if not match.empty:\n", "            df_corrected.at[idx, \"destination_node\"] = match.iloc[0][\"destination_node\"]\n", "\n", "    # --- Class Node Correction ---\n", "    if source_type == \"class\":\n", "        clean_name = source_node.replace(\".java\", \"\")\n", "        for class_name in class_registry.keys():\n", "            if class_name.lower() == clean_name.lower():\n", "                df_corrected.at[idx, \"source_node\"] = class_name\n", "                break\n", "\n", "    if dest_type == \"class\":\n", "        clean_name = dest_node.replace(\".java\", \"\")\n", "        for class_name in class_registry.keys():\n", "            if class_name.lower() == clean_name.lower():\n", "                df_corrected.at[idx, \"destination_node\"] = class_name\n", "                break\n"]}, {"cell_type": "code", "execution_count": 16, "id": "a5b3a43b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Final Neo4j Insertion: 100%|██████████| 125/125 [00:02<00:00, 62.36it/s]"]}, {"name": "stdout", "output_type": "stream", "text": [" Final consistent data pushed to Neo4j.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["\n", "graph.query(\"MATCH (n) DETACH DELETE n\")\n", "\n", "# Insert clean corrected data\n", "for idx, row in tqdm(df_corrected.iterrows(), total=len(df_corrected), desc=\"Final Neo4j Insertion\"):\n", "    try:\n", "        source_node = str(row[\"source_node\"]).strip()\n", "        dest_node = str(row[\"destination_node\"]).strip()\n", "        source_type = str(row[\"source_type\"]).strip().capitalize()\n", "        dest_type = str(row[\"destination_type\"]).strip().capitalize()\n", "        relationship = str(row[\"relationship\"]).strip().upper()\n", "\n", "        if not all([source_node, dest_node, source_type, dest_type, relationship]):\n", "            continue\n", "\n", "        query = f\"\"\"\n", "        MERGE (s:{source_type} {{name: $source_node}})\n", "        MERGE (t:{dest_type} {{name: $destination_node}})\n", "        MERGE (s)-[:{relationship}]->(t)\n", "        \"\"\"\n", "        graph.query(query, {\n", "            \"source_node\": source_node,\n", "            \"destination_node\": dest_node\n", "        })\n", "    except Exception as e:\n", "        pass  # Optionally collect failed rows for review\n", "\n", "print(\" Final consistent data pushed to Neo4j.\")\n"]}, {"cell_type": "code", "execution_count": 17, "id": "1584370e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total Nodes in Neo4j: 76\n", "Total Relationships in Neo4j: 125\n"]}], "source": ["def validate_graph_statistics():\n", "    node_count = graph.query(\"MATCH (n) RETURN count(n) as count\")[0][\"count\"]\n", "    rel_count = graph.query(\"MATCH ()-[r]->() RETURN count(r) as count\")[0][\"count\"]\n", "\n", "    print(f\"Total Nodes in Neo4j: {node_count}\")\n", "    print(f\"Total Relationships in Neo4j: {rel_count}\")\n", "\n", "validate_graph_statistics()"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}