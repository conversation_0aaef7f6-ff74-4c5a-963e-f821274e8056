{"cells": [{"cell_type": "code", "execution_count": 41, "id": "4d79baec", "metadata": {}, "outputs": [], "source": ["NEO4J_URI=\"neo4j://127.0.0.1:7687\"\n", "NEO4J_USERNAME=\"neo4j\"\n", "NEO4J_PASSWORD=\"Test@7889\"\n", "NEO4J_DATABASE=\"test\""]}, {"cell_type": "code", "execution_count": 42, "id": "b0733378", "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ[\"NEO4J_URI\"]=NEO4J_URI\n", "os.environ[\"NEO4J_USERNAME\"]=NEO4J_USERNAME\n", "os.environ[\"NEO4J_PASSWORD\"]=NEO4J_PASSWORD\n", "os.environ[\"NEO4J_DATABASE\"]=NEO4J_DATABASE"]}, {"cell_type": "code", "execution_count": 43, "id": "938d7592", "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "from langchain_community.graphs import Neo4jGraph\n", "\n", "\n", "graph=Neo4jGraph(\n", "    url=NEO4J_URI,\n", "    username=NEO4J_USERNAME,\n", "    password=NEO4J_PASSWORD,\n", "    database=NEO4J_DATABASE\n", ")"]}, {"cell_type": "code", "execution_count": 58, "id": "b09b645a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Folder and File hierarchy with CONTAINS relationships pushed to Neo4j.\n"]}], "source": ["\n", "base_path = Path(\"C:/Shaik/sample/java\")\n", "\n", "# Collect created nodes to avoid duplicates in Neo4j\n", "created_folders = set()\n", "\n", "for file_path in base_path.rglob(\"*.java\"):\n", "    relative_path = file_path.relative_to(base_path)\n", "    parts = relative_path.parts  # ('com', 'morganstanley', 'loanapp', 'controller', 'Foo.java')\n", "    \n", "    parent_folder = str(base_path.name)  # \"java\"\n", "    parent_folder_path = \"\"  # building incremental path\n", "    \n", "    # Create top-level \"java\" folder node if not created\n", "    if parent_folder not in created_folders:\n", "        graph.query(\n", "            \"MERGE (f:Folder {name: $name})\",\n", "            {\"name\": parent_folder}\n", "        )\n", "        created_folders.add(parent_folder)\n", "\n", "    # Create intermediate folder nodes and CONTAINS relationships\n", "    for part in parts[:-1]:\n", "        parent_folder_path = str(Path(parent_folder_path) / part) if parent_folder_path else part\n", "        if parent_folder_path not in created_folders:\n", "            # Create the folder node\n", "            graph.query(\n", "                \"MERGE (f:Folder {name: $name})\",\n", "                {\"name\": parent_folder_path}\n", "            )\n", "            created_folders.add(parent_folder_path)\n", "        \n", "        # Create CONTAINS relationship from parent_folder to this folder\n", "        graph.query(\n", "            \"\"\"\n", "            MATCH (p:Folder {name: $parent_name}), (c:Folder {name: $child_name})\n", "            MERGE (p)-[:CONTAINS]->(c)\n", "            \"\"\",\n", "            {\"parent_name\": parent_folder, \"child_name\": parent_folder_path}\n", "        )\n", "        parent_folder = parent_folder_path\n", "\n", "    # Finally, create the File node\n", "    file_name = parts[-1]\n", "    graph.query(\n", "        \"MERGE (f:File {name: $name})\",\n", "        {\"name\": file_name}\n", "    )\n", "\n", "    # Create CONTAINS relationship between the last folder and the file\n", "    graph.query(\n", "        \"\"\"\n", "        MATCH (p:Folder {name: $parent_name}), (f:File {name: $file_name})\n", "        MERGE (p)-[:CONTAINS]->(f)\n", "        \"\"\",\n", "        {\"parent_name\": parent_folder, \"file_name\": file_name}\n", "    )\n", "\n", "print(\"✅ Folder and File hierarchy with CONTAINS relationships pushed to Neo4j.\")\n"]}, {"cell_type": "code", "execution_count": 8, "id": "c3a4223e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Shaik\\sample\\venv\\Lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["\n", "# !pip install langchain langchain-community langchain-google-genai neo4j\n", "\n", "from pathlib import Path\n", "from langchain_community.document_loaders import TextLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter, Language\n", "from langchain_google_genai import ChatGoogleGenerativeAI\n", "from langchain_experimental.graph_transformers import LLMGraphTransformer\n", "from langchain_community.graphs import Neo4jGraph\n"]}, {"cell_type": "code", "execution_count": 45, "id": "2f1082f7", "metadata": {}, "outputs": [], "source": ["java_docs = []\n", "for path in Path(\"java\").rglob(\"*.java\"):\n", "    loader = TextLoader(str(path))\n", "    loaded_docs = loader.load()\n", "    for doc in loaded_docs:\n", "        doc.metadata[\"language\"] = \"java\"\n", "        doc.metadata[\"source\"] = str(path)\n", "    java_docs.extend(loaded_docs)"]}, {"cell_type": "code", "execution_count": 46, "id": "8aaa1f11", "metadata": {}, "outputs": [], "source": ["splitter_java = RecursiveCharacterTextSplitter.from_language(\n", "    language=Language.JAVA,\n", "    chunk_size=2000,\n", "    chunk_overlap=200\n", ")"]}, {"cell_type": "code", "execution_count": 47, "id": "b847c619", "metadata": {}, "outputs": [], "source": ["split_docs = []\n", "for doc in java_docs:\n", "    chunks = splitter_java.split_documents([doc])\n", "    for idx, chunk in enumerate(chunks):\n", "        chunk.metadata[\"chunk_index\"] = idx\n", "        chunk.metadata[\"language\"] = \"java\"\n", "        chunk.metadata[\"source\"] = doc.metadata[\"source\"]\n", "        split_docs.append(chunk)"]}, {"cell_type": "code", "execution_count": 48, "id": "0657920f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total number of chunks: 14\n", "\n", "--- Chunk 1 ---\n", "Metadata: {'source': 'java\\\\LoanAppApplication.java', 'language': 'java', 'chunk_index': 0}\n", "package com.morganstanley.loanApp;\n", "\n", "import jakarta.annotation.PostConstruct;\n", "import org.springframework.beans.factory.annotation.Autowired;\n", "import org.springframework.beans.factory.annotation.Qualifier;\n", "import org.springframework.boot.SpringApplication;\n", "import org.springframework.boot.autoconfigure.SpringBootApplication;\n", "import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguratio \n", "...\n", "\n", "--- Chunk 2 ---\n", "Metadata: {'source': 'java\\\\configuration\\\\PrimaryDataSourceProperties.java', 'language': 'java', 'chunk_index': 0}\n", "package com.morganstanley.loanApp.configuration;\n", "\n", "import jakarta.annotation.PostConstruct;\n", "import org.springframework.boot.context.properties.ConfigurationProperties;\n", "import org.springframework.stereotype.Component;\n", "\n", "@Component\n", "@ConfigurationProperties(prefix = \"spring.datasource\")\n", "public class PrimaryDataSourceProperties {\n", "    public String getUrl() {\n", "        return url;\n", "    }\n", "\n", "    public void se \n", "...\n", "\n", "--- Chunk 3 ---\n", "Metadata: {'source': 'java\\\\configuration\\\\SecondDataSourceProperties.java', 'language': 'java', 'chunk_index': 0}\n", "package com.morganstanley.loanApp.configuration;\n", "\n", "import jakarta.annotation.PostConstruct;\n", "import org.springframework.boot.context.properties.ConfigurationProperties;\n", "import org.springframework.stereotype.Component;\n", "\n", "@Component\n", "@ConfigurationProperties(prefix = \"sp.second-datasource\")\n", "public class SecondDataSourceProperties {\n", "    public String getUrl() {\n", "        return url;\n", "    }\n", "\n", "    public void  \n", "...\n"]}], "source": ["print(f\"Total number of chunks: {len(split_docs)}\")\n", "for i, d in enumerate(split_docs[:3], 1):\n", "    print(f\"\\n--- Chunk {i} ---\")\n", "    print(\"Metadata:\", d.metadata)\n", "    print(d.page_content[:400], \"\\n...\")\n"]}, {"cell_type": "code", "execution_count": 49, "id": "e0000677", "metadata": {}, "outputs": [], "source": ["google_api_key = \"AIzaSyDY2qPx7-wQk90KjsCnnFgc88Qzb1lV5b0\""]}, {"cell_type": "code", "execution_count": 50, "id": "b943e339", "metadata": {}, "outputs": [], "source": ["from langchain_google_genai import ChatGoogleGenerativeAI\n", "llm = ChatGoogleGenerativeAI(\n", "    model=\"gemini-2.5-pro\",\n", "    temperature=0,\n", "    google_api_key=google_api_key)"]}, {"cell_type": "code", "execution_count": 52, "id": "cab440fd", "metadata": {}, "outputs": [], "source": ["\n", "system_prompt = \"\"\"\n", "You are a **code and data lineage analysis engine**. From the provided Python or Java code chunk, extract **structured graph triples** in the format:\n", "\n", "[SourceNodeLabel]:SourceNodeName -[RELATION]-> [TargetNodeLabel]:TargetNodeName\n", "\n", "## Context:\n", "- Use the explicit Folder and File info provided above each code chunk.\n", "- Ignore package statements when building Folder nodes.\n", "- If database table names, column names, or API endpoints are detected, include them as nodes for lineage.\n", "- If functions or classes are reading from or writing to database tables or making API calls, capture them in the lineage relationships.\n", "\n", "## Layers to capture:\n", "- Folder -> File\n", "- File -> Class\n", "- Class -> Function/Method\n", "- Function/Method -> Function/Method (CALLS)\n", "- Class -> Class (INHERITS)\n", "- Function/Method -> Table (READS_FROM / WRITES_TO)\n", "- Function/Method -> API (CALLS_API)\n", "\n", "## Node Types:\n", "- Folder\n", "- File\n", "- Class\n", "- Function\n", "- Table\n", "- Column (if explicit)\n", "- API\n", "\n", "## Relationship Types:\n", "- CONTAINS\n", "- DECLARES\n", "- CALLS\n", "- INHERITS\n", "- READS_FROM\n", "- WRITES_TO\n", "- CALLS_API\n", "\n", "## Examples:\n", "\n", "Folder:test/java -[CONTAINS]-> File:SecondDataSourceProperties.java\n", "File:SecondDataSourceProperties.java -[DECLARES]-> Class:SecondDataSourceProperties\n", "Class:SecondDataSourceProperties -[DECLARES]-> Function:getUrl\n", "Function:load_user_data -[READS_FROM]-> Table:users\n", "Function:save_user_data -[WRITES_TO]-> Table:users\n", "Function:fetch_from_service -[CALLS_API]-> API:getUserProfile\n", "\n", "## Instructions:\n", "- Return **only these triples, nothing else**.\n", "- If unsure, prefer to omit rather than hallucinate.\n", "- If code chunk does not involve data sources, return structural triples only.\n", "\"\"\"\n", "\n"]}, {"cell_type": "code", "execution_count": 53, "id": "ffd17b92", "metadata": {}, "outputs": [], "source": ["\n", "transformer = LLMGraphTransformer(\n", "    llm=llm,\n", "    additional_instructions=system_prompt,\n", "    allowed_nodes=[\"Folder\", \"File\", \"Class\", \"Function\", \"Table\", \"Column\", \"API\"],\n", "    allowed_relationships=[\n", "        (\"Folder\", \"CONTAINS\", \"File\"),\n", "        (\"File\", \"DECLARES\", \"Class\"),\n", "        (\"Class\", \"DECLARES\", \"Function\"),\n", "        (\"Function\", \"CALLS\", \"Function\"),\n", "        (\"Class\", \"INHERITS\", \"Class\"),\n", "        (\"Function\", \"READS_FROM\", \"Table\"),\n", "        (\"Function\", \"WRITES_TO\", \"Table\"),\n", "        (\"Function\", \"CALLS_API\", \"API\")\n", "    ],\n", "    strict_mode=True,\n", "    node_properties=False,\n", "    relationship_properties=False\n", ")\n"]}, {"cell_type": "code", "execution_count": 54, "id": "52a40d59", "metadata": {}, "outputs": [], "source": ["graph_documents = transformer.convert_to_graph_documents(split_docs)\n"]}, {"cell_type": "code", "execution_count": 59, "id": "7256b665", "metadata": {}, "outputs": [{"data": {"text/plain": ["[GraphDocument(nodes=[Node(id='Com/Morganstanley/Loanapp', type='Folder', properties={}), Node(id='Loanappapplication.Java', type='File', properties={}), Node(id='Loanappapplication', type='Class', properties={}), Node(id='Printdsinfo', type='Function', properties={}), Node(id='Main', type='Function', properties={}), Node(id='Springapplication.Run', type='Function', properties={})], relationships=[Relationship(source=Node(id='Com/Morganstanley/Loanapp', type='Folder', properties={}), target=Node(id='Loanappapplication.Java', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='Loanappapplication.Java', type='File', properties={}), target=Node(id='Loanappapplication', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Loanappapplication', type='Class', properties={}), target=Node(id='Printdsinfo', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Loanappapplication', type='Class', properties={}), target=Node(id='Main', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Main', type='Function', properties={}), target=Node(id='Springapplication.Run', type='Function', properties={}), type='CALLS', properties={})], source=Document(metadata={'source': 'java\\\\LoanAppApplication.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp;\\n\\nimport jakarta.annotation.PostConstruct;\\nimport org.springframework.beans.factory.annotation.Autowired;\\nimport org.springframework.beans.factory.annotation.Qualifier;\\nimport org.springframework.boot.SpringApplication;\\nimport org.springframework.boot.autoconfigure.SpringBootApplication;\\nimport org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;\\nimport org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;\\nimport org.springframework.boot.context.properties.EnableConfigurationProperties;\\nimport org.springframework.context.ConfigurableApplicationContext;\\nimport org.springframework.core.env.Environment;\\n\\nimport javax.sql.DataSource;\\nimport java.sql.SQLException;\\n\\n@SpringBootApplication\\n@EnableConfigurationProperties\\npublic class LoanAppApplication {\\n\\n\\t@Autowired\\n\\t@Qualifier(\"dataSource\")\\n\\tprivate DataSource dataSource; // primary\\n\\n\\t@Autowired\\n\\t@Qualifier(\"storeProcDataSource\")\\n\\tprivate DataSource secondDataSource;\\n\\n\\n\\t@PostConstruct\\n\\tpublic void printDsInfo() throws SQLException {\\n\\t\\tSystem.out.println(\"Primary DataSource connection URL: \" + dataSource.getConnection().getMetaData().getURL());\\n\\t\\tSystem.out.println(\"Secondary DataSource connection URL: \" + secondDataSource.getConnection().getMetaData().getURL());\\n\\t}\\n\\tpublic static void main(String[] args) {\\n\\t\\t SpringApplication.run(LoanAppApplication.class, args);\\n\\n\\t}\\n\\n}')),\n", " GraphDocument(nodes=[Node(id='Com/Morganstanley/Loanapp/Configuration', type='Folder', properties={}), Node(id='Primarydatasourceproperties.Java', type='File', properties={}), Node(id='Primarydatasourceproperties', type='Class', properties={}), Node(id='Geturl', type='Function', properties={}), Node(id='Seturl', type='Function', properties={}), Node(id='Getusername', type='Function', properties={}), Node(id='Setusername', type='Function', properties={}), Node(id='Getpassword', type='Function', properties={}), Node(id='Setpassword', type='Function', properties={}), Node(id='Getdriverclassname', type='Function', properties={}), Node(id='Setdriverclassname', type='Function', properties={}), Node(id='Printprops', type='Function', properties={})], relationships=[Relationship(source=Node(id='Com/Morganstanley/Loanapp/Configuration', type='Folder', properties={}), target=Node(id='Primarydatasourceproperties.Java', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='Primarydatasourceproperties.Java', type='File', properties={}), target=Node(id='Primarydatasourceproperties', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Primarydatasourceproperties', type='Class', properties={}), target=Node(id='Geturl', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Primarydatasourceproperties', type='Class', properties={}), target=Node(id='Seturl', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Primarydatasourceproperties', type='Class', properties={}), target=Node(id='Getusername', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Primarydatasourceproperties', type='Class', properties={}), target=Node(id='Setusername', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Primarydatasourceproperties', type='Class', properties={}), target=Node(id='Getpassword', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Primarydatasourceproperties', type='Class', properties={}), target=Node(id='Setpassword', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Primarydatasourceproperties', type='Class', properties={}), target=Node(id='Getdriverclassname', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Primarydatasourceproperties', type='Class', properties={}), target=Node(id='Setdriverclassname', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Primarydatasourceproperties', type='Class', properties={}), target=Node(id='Printprops', type='Function', properties={}), type='DECLARES', properties={})], source=Document(metadata={'source': 'java\\\\configuration\\\\PrimaryDataSourceProperties.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp.configuration;\\n\\nimport jakarta.annotation.PostConstruct;\\nimport org.springframework.boot.context.properties.ConfigurationProperties;\\nimport org.springframework.stereotype.Component;\\n\\n@Component\\n@ConfigurationProperties(prefix = \"spring.datasource\")\\npublic class PrimaryDataSourceProperties {\\n    public String getUrl() {\\n        return url;\\n    }\\n\\n    public void setUrl(String url) {\\n        this.url = url;\\n    }\\n\\n    public String getUsername() {\\n        return username;\\n    }\\n\\n    public void setUsername(String username) {\\n        this.username = username;\\n    }\\n\\n    public String getPassword() {\\n        return password;\\n    }\\n\\n    public void setPassword(String password) {\\n        this.password = password;\\n    }\\n\\n    public String getDriverClassName() {\\n        return driverClassName;\\n    }\\n\\n    public void setDriverClassName(String driverClassName) {\\n        this.driverClassName = driverClassName;\\n    }\\n\\n    private String url;\\n    private String username;\\n    private String password;\\n    private String driverClassName;\\n    @PostConstruct\\n    public void printProps() {\\n        System.out.println(\"Primary DS Props:\");\\n        System.out.println(\"URL: \" + url);\\n        System.out.println(\"Username: \" + username);\\n        System.out.println(\"Driver: \" + driverClassName);\\n    }\\n\\n\\n}')),\n", " GraphDocument(nodes=[Node(id='Com/Morganstanley/Loanapp/Configuration', type='Folder', properties={}), Node(id='Seconddatasourceproperties.Java', type='File', properties={}), Node(id='Seconddatasourceproperties', type='Class', properties={}), Node(id='Geturl', type='Function', properties={}), Node(id='Seturl', type='Function', properties={}), Node(id='Getusername', type='Function', properties={}), Node(id='Setusername', type='Function', properties={}), Node(id='Getpassword', type='Function', properties={}), Node(id='Setpassword', type='Function', properties={}), Node(id='Getdriverclassname', type='Function', properties={}), Node(id='Setdriverclassname', type='Function', properties={}), Node(id='Printprops', type='Function', properties={})], relationships=[Relationship(source=Node(id='Com/Morganstanley/Loanapp/Configuration', type='Folder', properties={}), target=Node(id='Seconddatasourceproperties.Java', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='Seconddatasourceproperties.Java', type='File', properties={}), target=Node(id='Seconddatasourceproperties', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Geturl', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Seturl', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Getusername', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Setusername', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Getpassword', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Setpassword', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Getdriverclassname', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Setdriverclassname', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Printprops', type='Function', properties={}), type='DECLARES', properties={})], source=Document(metadata={'source': 'java\\\\configuration\\\\SecondDataSourceProperties.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp.configuration;\\n\\nimport jakarta.annotation.PostConstruct;\\nimport org.springframework.boot.context.properties.ConfigurationProperties;\\nimport org.springframework.stereotype.Component;\\n\\n@Component\\n@ConfigurationProperties(prefix = \"sp.second-datasource\")\\npublic class SecondDataSourceProperties {\\n    public String getUrl() {\\n        return url;\\n    }\\n\\n    public void setUrl(String url) {\\n        this.url = url;\\n    }\\n\\n    public String getUsername() {\\n        return username;\\n    }\\n\\n    public void setUsername(String username) {\\n        this.username = username;\\n    }\\n\\n    public String getPassword() {\\n        return password;\\n    }\\n\\n    public void setPassword(String password) {\\n        this.password = password;\\n    }\\n\\n    public String getDriverClassName() {\\n        return driverClassName;\\n    }\\n\\n    public void setDriverClassName(String driverClassName) {\\n        this.driverClassName = driverClassName;\\n    }\\n\\n    private String url;\\n    private String username;\\n    private String password;\\n    private String driverClassName;\\n    @PostConstruct\\n    public void printProps() {\\n        System.out.println(\"Secondary DS Props:\");\\n        System.out.println(\"URL: \" + url);\\n        System.out.println(\"Username: \" + username);\\n        System.out.println(\"Driver: \" + driverClassName);\\n    }\\n\\n\\n}')),\n", " GraphDocument(nodes=[Node(id='Com/Morganstanley/Loanapp/Configuration', type='Folder', properties={}), Node(id='Storedprocdbconfig.Java', type='File', properties={}), Node(id='Storedprocdbconfig', type='Class', properties={}), Node(id='Primarydatasource', type='Function', properties={}), Node(id='Storeprocdatasource', type='Function', properties={}), Node(id='Storeprocjdbctemplate', type='Function', properties={})], relationships=[Relationship(source=Node(id='Com/Morganstanley/Loanapp/Configuration', type='Folder', properties={}), target=Node(id='Storedprocdbconfig.Java', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='Storedprocdbconfig.Java', type='File', properties={}), target=Node(id='Storedprocdbconfig', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Storedprocdbconfig', type='Class', properties={}), target=Node(id='Primarydatasource', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Storedprocdbconfig', type='Class', properties={}), target=Node(id='Storeprocdatasource', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Storedprocdbconfig', type='Class', properties={}), target=Node(id='Storeprocjdbctemplate', type='Function', properties={}), type='DECLARES', properties={})], source=Document(metadata={'source': 'java\\\\configuration\\\\StoredProcDBConfig.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp.configuration;\\nimport javax.sql.DataSource;\\n\\nimport com.zaxxer.hikari.HikariDataSource;\\nimport org.springframework.beans.factory.annotation.Qualifier;\\nimport org.springframework.boot.context.properties.ConfigurationProperties;\\nimport org.springframework.boot.jdbc.DataSourceBuilder;\\nimport org.springframework.context.annotation.Bean;\\nimport org.springframework.context.annotation.Configuration;\\nimport org.springframework.context.annotation.Primary;\\nimport org.springframework.jdbc.core.JdbcTemplate;\\n\\n@Configuration\\npublic class StoredProcDBConfig {\\n    @Bean\\n    @Qualifier(\"dataSource\")\\n    @Primary\\n    public DataSource primaryDataSource(PrimaryDataSourceProperties props) {\\n        HikariDataSource ds = new HikariDataSource();\\n        ds.setJdbcUrl(props.getUrl());\\n        ds.setUsername(props.getUsername());\\n        ds.setPassword(props.getPassword());\\n        ds.setDriverClassName(props.getDriverClassName());\\n\\n        System.out.println(\">>> Primary DataSource initialized with URL: \" + props.getUrl());\\n\\n        return ds;\\n    }\\n\\n    @Bean(name = \"storeProcDataSource\")\\n    public DataSource storeProcDataSource(SecondDataSourceProperties props) {\\n        HikariDataSource ds = new HikariDataSource();\\n        ds.setJdbcUrl(props.getUrl());\\n        ds.setUsername(props.getUsername());\\n        ds.setPassword(props.getPassword());\\n        ds.setDriverClassName(props.getDriverClassName());\\n\\n        System.out.println(\">>> Second DataSource initialized with URL: \" + props.getUrl());\\n\\n        return ds;\\n    }\\n\\n    @Bean\\n    public JdbcTemplate storeProcJdbcTemplate(@Qualifier(\"storeProcDataSource\") DataSource ds) {\\n        return new JdbcTemplate(ds);\\n    }\\n}')),\n", " GraphDocument(nodes=[Node(id='Com/Morganstanley/Loanapp/Controller', type='Folder', properties={}), Node(id='Loancontroller.Java', type='File', properties={}), Node(id='Loancontroller', type='Class', properties={})], relationships=[Relationship(source=Node(id='Com/Morganstanley/Loanapp/Controller', type='Folder', properties={}), target=Node(id='Loancontroller.Java', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='Loancontroller.Java', type='File', properties={}), target=Node(id='Loancontroller', type='Class', properties={}), type='DECLARES', properties={})], source=Document(metadata={'source': 'java\\\\controller\\\\UserController.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp.controller;\\n\\nimport com.morganstanley.loanApp.dto.UserLoanRequestDTO;\\nimport com.morganstanley.loanApp.model.User;\\n\\nimport com.morganstanley.loanApp.service.StoreProcService;\\nimport com.morganstanley.loanApp.service.UserService;\\nimport jakarta.validation.Valid;\\nimport org.springframework.beans.factory.annotation.Autowired;\\nimport org.springframework.http.HttpStatus;\\nimport org.springframework.http.ResponseEntity;\\nimport org.springframework.web.bind.annotation.PostMapping;\\nimport org.springframework.web.bind.annotation.RequestBody;\\nimport org.springframework.web.bind.annotation.RequestMapping;\\nimport org.springframework.web.bind.annotation.RestController;\\n\\nimport java.io.*;\\nimport java.util.ArrayList;\\nimport java.util.List;\\n\\n@RestController\\n@RequestMapping(\"/loanApp\")')),\n", " GraphDocument(nodes=[Node(id='Usercontroller', type='Class', properties={}), Node(id='Userservice', type='Class', properties={}), Node(id='Storeprocservice', type='Class', properties={}), Node(id='Usercontroller.Createuserwithloan', type='Function', properties={}), Node(id='Usercontroller.Saveuserloanfromfile', type='Function', properties={}), Node(id='Userservice.Createuserwithloan', type='Function', properties={}), Node(id='/Createloan', type='Api', properties={}), Node(id='/Save-From-File', type='Api', properties={})], relationships=[Relationship(source=Node(id='Usercontroller', type='Class', properties={}), target=Node(id='Usercontroller.Createuserwithloan', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Usercontroller', type='Class', properties={}), target=Node(id='Usercontroller.Saveuserloanfromfile', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Userservice', type='Class', properties={}), target=Node(id='Userservice.Createuserwithloan', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Usercontroller.Createuserwithloan', type='Function', properties={}), target=Node(id='Userservice.Createuserwithloan', type='Function', properties={}), type='CALLS', properties={}), Relationship(source=Node(id='Usercontroller.Saveuserloanfromfile', type='Function', properties={}), target=Node(id='Userservice.Createuserwithloan', type='Function', properties={}), type='CALLS', properties={}), Relationship(source=Node(id='Usercontroller.Createuserwithloan', type='Function', properties={}), target=Node(id='/Createloan', type='Api', properties={}), type='CALLS_API', properties={}), Relationship(source=Node(id='Usercontroller.Saveuserloanfromfile', type='Function', properties={}), target=Node(id='/Save-From-File', type='Api', properties={}), type='CALLS_API', properties={})], source=Document(metadata={'source': 'java\\\\controller\\\\UserController.java', 'language': 'java', 'chunk_index': 1}, page_content='public class UserController {\\n    @Autowired\\n    private UserService userService;\\n\\n    @Autowired\\n    private StoreProcService storeProcService;\\n\\n    @PostMapping(\"/createLoan\")\\n    public ResponseEntity<User> createUserWithLoan(@Valid @RequestBody UserLoanRequestDTO dto) {\\n        User savedUser = userService.createUserWithLoan(dto);\\n        return new ResponseEntity<>(savedUser, HttpStatus.CREATED);\\n    }\\n\\n    @PostMapping(\"/save-from-file\")\\n    public ResponseEntity<String> saveUserLoanFromFile(){\\n        try (InputStream is = getClass().getClassLoader().getResourceAsStream(\"usersData.txt\");\\n             BufferedReader reader = new BufferedReader(new InputStreamReader(is))) {\\n\\n            if (is == null) {\\n                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)\\n                        .body(\"Data file not found\");\\n            }\\n\\n            String line ;\\n            while ((line = reader.readLine()) != null) {\\n                String[] parts = line.split(\",\");\\n                if (parts.length != 4) {\\n                    return ResponseEntity.badRequest()\\n                            .body(\"Invalid file format\");\\n                }\\n\\n\\n                UserLoanRequestDTO userDTO = new UserLoanRequestDTO();\\n                userDTO.setName(parts[0].trim());\\n                userDTO.setEmail(parts[1].trim());\\n                userDTO.setLoanType(parts[2].trim());\\n                userDTO.setAmount(Double.valueOf(parts[3].trim()));\\n\\n                userService.createUserWithLoan(userDTO);\\n            }\\n            return ResponseEntity.ok(\"Loan application saved from flat file\");\\n\\n        } catch (Exception e) {\\n            e.printStackTrace();\\n            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)\\n                    .body(\"Error processing flat file\");\\n        }\\n    }')),\n", " GraphDocument(nodes=[Node(id='Readcsvandcallstoreproc', type='Function', properties={}), Node(id='/Processstoreprocfromcsv', type='Api', properties={}), Node(id='Callstoreproc', type='Function', properties={})], relationships=[Relationship(source=Node(id='Readcsvandcallstoreproc', type='Function', properties={}), target=Node(id='/Processstoreprocfromcsv', type='Api', properties={}), type='CALLS_API', properties={}), Relationship(source=Node(id='Readcsvandcallstoreproc', type='Function', properties={}), target=Node(id='Callstoreproc', type='Function', properties={}), type='CALLS', properties={})], source=Document(metadata={'source': 'java\\\\controller\\\\UserController.java', 'language': 'java', 'chunk_index': 2}, page_content='@PostMapping(\"/processStoreProcFromCSV\")\\n    public ResponseEntity<List<String>> readCSVAndCallStoreProc(){\\n        List<String> outputMessages = new ArrayList<>();\\n\\n        try (InputStream is = getClass().getClassLoader().getResourceAsStream(\"branchesData.csv\");\\n             BufferedReader reader = new BufferedReader(new InputStreamReader(is))) {\\n            String line;\\n            boolean isFirstLine = true;\\n\\n            while ((line = reader.readLine()) != null) {\\n                if (isFirstLine) {\\n                    isFirstLine = false; // skip header\\n                    continue;\\n                }\\n\\n                String[] parts = line.split(\",\");\\n                if (parts.length != 2) continue;\\n\\n                String branchName = parts[0].trim();\\n                String branchAddress = parts[1].trim();\\n\\n                Integer result = storeProcService.callStoreProc(branchName, branchAddress);\\n                outputMessages.add(\"Branch Id for \" + branchName + \" is: \" + result);\\n            }\\n\\n            return ResponseEntity.ok(outputMessages);\\n        } catch (IOException | NumberFormatException ex) {\\n            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)\\n                    .body(List.of(\"Error reading file or parsing data: \" + ex.getMessage()));\\n        }\\n    }\\n\\n\\n}')),\n", " GraphDocument(nodes=[Node(id='Com/Morganstanley/Loanapp/Dto', type='Folder', properties={}), Node(id='Userloanrequestdto.Java', type='File', properties={}), Node(id='Userloanrequestdto', type='Class', properties={}), Node(id='Getname', type='Function', properties={}), Node(id='Setname', type='Function', properties={}), Node(id='Getemail', type='Function', properties={}), Node(id='Setemail', type='Function', properties={}), Node(id='Getloantype', type='Function', properties={}), Node(id='Setloantype', type='Function', properties={}), Node(id='Getamount', type='Function', properties={}), Node(id='Setamount', type='Function', properties={})], relationships=[Relationship(source=Node(id='Com/Morganstanley/Loanapp/Dto', type='Folder', properties={}), target=Node(id='Userloanrequestdto.Java', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='Userloanrequestdto.Java', type='File', properties={}), target=Node(id='Userloanrequestdto', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Userloanrequestdto', type='Class', properties={}), target=Node(id='Getname', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Userloanrequestdto', type='Class', properties={}), target=Node(id='Setname', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Userloanrequestdto', type='Class', properties={}), target=Node(id='Getemail', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Userloanrequestdto', type='Class', properties={}), target=Node(id='Setemail', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Userloanrequestdto', type='Class', properties={}), target=Node(id='Getloantype', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Userloanrequestdto', type='Class', properties={}), target=Node(id='Setloantype', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Userloanrequestdto', type='Class', properties={}), target=Node(id='Getamount', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Userloanrequestdto', type='Class', properties={}), target=Node(id='Setamount', type='Function', properties={}), type='DECLARES', properties={})], source=Document(metadata={'source': 'java\\\\dto\\\\UserLoanRequestDTO.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp.dto;\\n\\nimport jakarta.validation.constraints.Email;\\nimport jakarta.validation.constraints.NotBlank;\\nimport jakarta.validation.constraints.NotNull;\\nimport jakarta.validation.constraints.Positive;\\nimport lombok.Data;\\nimport lombok.Getter;\\nimport lombok.Setter;\\n\\n\\n\\npublic class UserLoanRequestDTO {\\n\\n    public String getName() {\\n        return name;\\n    }\\n\\n    public void setName(String name) {\\n        this.name = name;\\n    }\\n\\n    public String getEmail() {\\n        return email;\\n    }\\n\\n    public void setEmail(String email) {\\n        this.email = email;\\n    }\\n\\n    public String getLoanType() {\\n        return loanType;\\n    }\\n\\n    public void setLoanType(String loanType) {\\n        this.loanType = loanType;\\n    }\\n\\n    public Double getAmount() {\\n        return amount;\\n    }\\n\\n    public void setAmount(Double amount) {\\n        this.amount = amount;\\n    }\\n\\n    @NotBlank(message = \"Name is required\")\\n    private String name;\\n\\n    @Email(message = \"Email should be valid\")\\n    @NotBlank(message = \"Email is required\")\\n    private String email;\\n\\n    @NotBlank(message = \"Loan type is required\")\\n    private String loanType;\\n\\n    @NotNull(message = \"Loan amount is required\")\\n    @Positive(message = \"Loan amount must be positive\")\\n    private Double amount;\\n}')),\n", " GraphDocument(nodes=[Node(id='Com/Morganstanley/Loanapp/Model', type='Folder', properties={}), Node(id='Loandetails.Java', type='File', properties={}), Node(id='Loandetails', type='Class', properties={}), Node(id='Loan_Details', type='Table', properties={})], relationships=[Relationship(source=Node(id='Com/Morganstanley/Loanapp/Model', type='Folder', properties={}), target=Node(id='Loandetails.Java', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='Loandetails.Java', type='File', properties={}), target=Node(id='Loandetails', type='Class', properties={}), type='DECLARES', properties={})], source=Document(metadata={'source': 'java\\\\model\\\\LoanDetails.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp.model;\\n\\nimport jakarta.persistence.*;\\nimport lombok.Data;\\n\\n@Entity\\n@Table(name = \"loan_details\")\\n@Data\\npublic class LoanDetails {\\n\\n    @Id\\n    @GeneratedValue(strategy = GenerationType.IDENTITY)\\n    private Long id;\\n\\n    private String loanType;\\n    private Double amount;\\n\\n    @Column(name = \"user_id\")\\n    private Long userId;\\n\\n\\n\\n}')),\n", " GraphDocument(nodes=[Node(id='Com/Morganstanley/Loanapp/Model', type='Folder', properties={}), Node(id='User.Java', type='File', properties={}), Node(id='User', type='Class', properties={}), Node(id='Users', type='Table', properties={})], relationships=[Relationship(source=Node(id='Com/Morganstanley/Loanapp/Model', type='Folder', properties={}), target=Node(id='User.Java', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='User.Java', type='File', properties={}), target=Node(id='User', type='Class', properties={}), type='DECLARES', properties={})], source=Document(metadata={'source': 'java\\\\model\\\\User.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp.model;\\n\\nimport jakarta.persistence.*;\\nimport lombok.Data;\\n\\n@Entity\\n@Table(name = \"users\")\\n@Data\\npublic class User {\\n\\n    @Id\\n    @GeneratedValue(strategy = GenerationType.IDENTITY)\\n    private Long id;\\n\\n    private String name;\\n    private String email;\\n}')),\n", " GraphDocument(nodes=[Node(id='Com/Morganstanley/Loanapp/Repository', type='Folder', properties={}), Node(id='Loandetailsrepository.Java', type='File', properties={}), Node(id='Loandetailsrepository', type='Class', properties={}), Node(id='Jparepository', type='Class', properties={})], relationships=[Relationship(source=Node(id='Com/Morganstanley/Loanapp/Repository', type='Folder', properties={}), target=Node(id='Loandetailsrepository.Java', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='Loandetailsrepository.Java', type='File', properties={}), target=Node(id='Loandetailsrepository', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Loandetailsrepository', type='Class', properties={}), target=Node(id='Jparepository', type='Class', properties={}), type='INHERITS', properties={})], source=Document(metadata={'source': 'java\\\\repository\\\\LoanDetailsRepository.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp.repository;\\n\\nimport com.morganstanley.loanApp.model.LoanDetails;\\nimport org.springframework.data.jpa.repository.JpaRepository;\\n\\npublic interface LoanDetailsRepository extends JpaRepository<LoanDetails,Long> {\\n}')),\n", " GraphDocument(nodes=[Node(id='Com/Morganstanley/Loanapp/Repository', type='Folder', properties={}), Node(id='Userrepository.Java', type='File', properties={}), Node(id='Userrepository', type='Class', properties={}), Node(id='Jparepository', type='Class', properties={}), Node(id='User', type='Table', properties={})], relationships=[Relationship(source=Node(id='Com/Morganstanley/Loanapp/Repository', type='Folder', properties={}), target=Node(id='Userrepository.Java', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='Userrepository.Java', type='File', properties={}), target=Node(id='Userrepository', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Userrepository', type='Class', properties={}), target=Node(id='Jparepository', type='Class', properties={}), type='INHERITS', properties={})], source=Document(metadata={'source': 'java\\\\repository\\\\UserRepository.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp.repository;\\n\\nimport com.morganstanley.loanApp.model.User;\\nimport org.springframework.data.jpa.repository.JpaRepository;\\n\\n\\npublic interface UserRepository extends JpaRepository<User, Long> {\\n}')),\n", " GraphDocument(nodes=[Node(id='Com/Morganstanley/Loanapp/Service', type='Folder', properties={}), Node(id='Storeprocservice.Java', type='File', properties={}), Node(id='Storeprocservice', type='Class', properties={}), Node(id='Callstoreproc', type='Function', properties={}), Node(id='Sp_Insert_Branch', type='Table', properties={})], relationships=[Relationship(source=Node(id='Com/Morganstanley/Loanapp/Service', type='Folder', properties={}), target=Node(id='Storeprocservice.Java', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='Storeprocservice.Java', type='File', properties={}), target=Node(id='Storeprocservice', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Storeprocservice', type='Class', properties={}), target=Node(id='Callstoreproc', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Callstoreproc', type='Function', properties={}), target=Node(id='Sp_Insert_Branch', type='Table', properties={}), type='WRITES_TO', properties={})], source=Document(metadata={'source': 'java\\\\service\\\\StoreProcService.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp.service;\\n\\nimport org.springframework.beans.factory.annotation.Autowired;\\nimport org.springframework.beans.factory.annotation.Qualifier;\\nimport org.springframework.jdbc.core.JdbcTemplate;\\nimport org.springframework.stereotype.Service;\\n\\nimport java.sql.CallableStatement;\\nimport java.sql.Connection;\\nimport java.sql.Types;\\n\\n@Service\\npublic class StoreProcService {\\n\\n\\n    @Autowired\\n    @Qualifier(\"storeProcJdbcTemplate\")\\n    private JdbcTemplate storeProcTemplate;\\n\\n\\n\\n\\n    public Integer callStoreProc(String branchName, String branchAddress) {\\n\\n        return storeProcTemplate.execute((Connection conn) -> {\\n            CallableStatement cs = conn.prepareCall(\"{call sp_insert_branch(?, ?, ?)}\");\\n            cs.setString(1, branchName);\\n            cs.setString(2, branchAddress);\\n            cs.registerOutParameter(3, Types.INTEGER);\\n            cs.execute();\\n            return cs.getInt(3);\\n        });\\n    }\\n}')),\n", " GraphDocument(nodes=[Node(id='Com/Morganstanley/Loanapp/Service', type='Folder', properties={}), Node(id='Userservice.Java', type='File', properties={}), Node(id='Userservice', type='Class', properties={}), Node(id='Createuserwithloan', type='Function', properties={}), Node(id='User', type='Table', properties={}), Node(id='Loandetails', type='Table', properties={})], relationships=[Relationship(source=Node(id='Com/Morganstanley/Loanapp/Service', type='Folder', properties={}), target=Node(id='Userservice.Java', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='Userservice.Java', type='File', properties={}), target=Node(id='Userservice', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Userservice', type='Class', properties={}), target=Node(id='Createuserwithloan', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Createuserwithloan', type='Function', properties={}), target=Node(id='User', type='Table', properties={}), type='WRITES_TO', properties={}), Relationship(source=Node(id='Createuserwithloan', type='Function', properties={}), target=Node(id='Loandetails', type='Table', properties={}), type='WRITES_TO', properties={})], source=Document(metadata={'source': 'java\\\\service\\\\UserService.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp.service;\\n\\nimport com.morganstanley.loanApp.dto.UserLoanRequestDTO;\\nimport com.morganstanley.loanApp.model.LoanDetails;\\nimport com.morganstanley.loanApp.model.User;\\nimport com.morganstanley.loanApp.repository.LoanDetailsRepository;\\nimport com.morganstanley.loanApp.repository.UserRepository;\\nimport org.springframework.beans.factory.annotation.Autowired;\\nimport org.springframework.stereotype.Service;\\n\\n@Service\\npublic class UserService {\\n    @Autowired\\n    private UserRepository userRepository;\\n\\n    @Autowired\\n    private LoanDetailsRepository loanDetailsRepository;\\n\\n    public User createUserWithLoan(UserLoanRequestDTO dto) {\\n        User user = new User();\\n        user.setName(dto.getName());\\n        user.setEmail(dto.getEmail());\\n        user = userRepository.save(user);\\n\\n        LoanDetails loan = new LoanDetails();\\n        loan.setLoanType(dto.getLoanType());\\n        loan.setAmount(dto.getAmount());\\n        loan.setUserId(user.getId());\\n        loanDetailsRepository.save(loan);\\n\\n        return user;\\n    }\\n}'))]"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["graph_documents"]}, {"cell_type": "code", "execution_count": 61, "id": "a4798904", "metadata": {}, "outputs": [], "source": ["\n", "# 1️⃣ Normalize helper\n", "def normalize_name(name: str) -> str:\n", "    return name.lower().strip()"]}, {"cell_type": "code", "execution_count": 62, "id": "45a41737", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ LLM graph documents merged into Neo4j with case-insensitive deduplication.\n"]}], "source": ["# 2️⃣ Merge LLM-extracted graph documents with case-insensitive handling\n", "for gd in graph_documents:\n", "    for node in gd.nodes:\n", "        label = node.type\n", "        props = node.properties or {}\n", "        props[\"id\"] = normalize_name(node.id)\n", "\n", "        # Check existence (case-insensitive)\n", "        exists = graph.query(\n", "            f\"\"\"\n", "            MATCH (n:{label})\n", "            WHERE toLower(n.id) = $id\n", "            RETURN n\n", "            \"\"\",\n", "            {\"id\": props[\"id\"]}\n", "        )\n", "        if not exists:\n", "            props_cypher = \", \".join(f\"{k}: ${k}\" for k in props.keys())\n", "            graph.query(\n", "                f\"\"\"\n", "                CREATE (n:{label} {{ {props_cypher} }})\n", "                \"\"\",\n", "                props\n", "            )\n", "\n", "    for rel in gd.relationships:\n", "        source_label = rel.source.type\n", "        source_id = normalize_name(rel.source.id)\n", "        target_label = rel.target.type\n", "        target_id = normalize_name(rel.target.id)\n", "        rel_type = rel.type\n", "\n", "        # Ensure source and target exist, then create relationship\n", "        graph.query(\n", "            f\"\"\"\n", "            MERGE (src:{source_label} {{id: $source_id}})\n", "            MERGE (tgt:{target_label} {{id: $target_id}})\n", "            MERGE (src)-[r:{rel_type}]->(tgt)\n", "            \"\"\",\n", "            {\"source_id\": source_id, \"target_id\": target_id}\n", "        )\n", "\n", "print(\"✅ LLM graph documents merged into Neo4j with case-insensitive deduplication.\")"]}, {"cell_type": "code", "execution_count": 63, "id": "9103c055", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Orphan File nodes repaired and connected with CONTAINS relationships (case-insensitive).\n"]}], "source": ["# 3️⃣ Repair missing CONTAINS relationships for orphan Files\n", "orphan_files = graph.query(\"\"\"\n", "MATCH (f:File)\n", "WHERE NOT ( (:Folder)-[:CONTAINS]->(f) )\n", "RETURN f.id as file_id\n", "\"\"\")\n", "\n", "for record in orphan_files:\n", "    file_id = record[\"file_id\"]\n", "    normalized_file = normalize_name(file_id)\n", "\n", "    # Infer folder from filename heuristics\n", "    # Example: \"usercontroller.java\" -> folder = \"controller\"\n", "    possible_folder = None\n", "    if normalized_file.endswith(\".java\"):\n", "        parts = normalized_file.split(\".\")\n", "        if len(parts) > 2:\n", "            possible_folder = parts[-2]  # crude fallback\n", "\n", "    if possible_folder:\n", "        # Check for folder existence using case-insensitive matching\n", "        folder_exists = graph.query(\"\"\"\n", "        MATCH (folder:Folder)\n", "        WHERE toLower(folder.name) = $folder\n", "        RETURN folder\n", "        \"\"\", {\"folder\": possible_folder})\n", "\n", "        if folder_exists:\n", "            graph.query(\"\"\"\n", "            MATCH (folder:Folder)\n", "            WHERE toLower(folder.name) = $folder\n", "            MATCH (file:File)\n", "            WHERE toLower(file.id) = $file_id\n", "            MERGE (folder)-[:CONTAINS]->(file)\n", "            \"\"\", {\"folder\": possible_folder, \"file_id\": normalized_file})\n", "\n", "print(\"✅ Orphan File nodes repaired and connected with CONTAINS relationships (case-insensitive).\")\n"]}, {"cell_type": "code", "execution_count": 17, "id": "47bb3948", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Code structure and data lineage triples pushed to Neo4j successfully.\n"]}], "source": ["graph.add_graph_documents(graph_documents)\n", "\n", "print(\"✅ Code structure and data lineage triples pushed to Neo4j successfully.\")\n"]}, {"cell_type": "code", "execution_count": 64, "id": "60d473dc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ All nodes and relationships deleted from Neo4j.\n"]}], "source": ["graph.query(\"MATCH (n) DETACH DELETE n\")\n", "print(\"✅ All nodes and relationships deleted from Neo4j.\")"]}, {"cell_type": "code", "execution_count": null, "id": "fb93e6a9", "metadata": {}, "outputs": [], "source": ["import pickle\n", "\n", "with open(\"graph_documents.pkl\", \"wb\") as f:\n", "    pickle.dump(graph_documents, f)"]}, {"cell_type": "code", "execution_count": null, "id": "b74b3bbd", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}