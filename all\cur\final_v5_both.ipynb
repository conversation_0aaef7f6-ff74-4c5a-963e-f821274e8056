{"cells": [{"cell_type": "code", "execution_count": 8, "id": "ec072c28", "metadata": {}, "outputs": [], "source": ["import os\n", "from pathlib import Path\n", "import pandas as pd\n", "from tqdm import tqdm\n", "from tree_sitter import Language, Parser\n", "import tree_sitter_java as tsjava\n", "\n", "from langchain_community.document_loaders import TextLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter, Language as LC_Language\n", "\n", "from langchain.vectorstores import FAISS\n", "from langchain.embeddings import OpenAIEmbeddings\n", "\n", "from langchain_google_genai import ChatGoogleGenerativeAI\n", "from langchain_google_genai import GoogleGenerativeAIEmbeddings\n", "from langchain_experimental.graph_transformers import LLMGraphTransformer\n", "\n", "from langchain_community.graphs import Neo4jGraph\n", "\n", "# -----------------------------------\n", "# CONFIG\n", "BASE_PATH = Path(\"C:/Shaik/sample/java\")\n", "NEO4J_URI = \"bolt://localhost:7687\"\n", "NEO4J_USER = \"neo4j\"\n", "NEO4J_PASSWORD = \"Test@7889\"\n", "NEO4J_DB = \"astllm3\"\n", "google_api_key = \"AIzaSyDlwEbSYmqOoIGbh-Upn2ZGXR3nwAfxcQ0\""]}, {"cell_type": "code", "execution_count": 2, "id": "4c4ebbff", "metadata": {}, "outputs": [], "source": ["\n", "\n", "# -----------------------------------\n", "# Stage 1: Extract Folder & File hierarchy (save CSV)\n", "\n", "def extract_folder_file_hierarchy(base_path):\n", "    folder_records = []\n", "    file_records = []\n", "    base_path = os.path.abspath(base_path)\n", "    base_folder_name = os.path.basename(base_path)\n", "\n", "    folder_records.append({\n", "        \"source_node\": None,\n", "        \"source_type\": None,\n", "        \"destination_node\": base_folder_name,\n", "        \"destination_type\": \"Folder\",\n", "        \"relationship\": None,\n", "        \"file_path\": None\n", "    })\n", "\n", "    for root, dirs, files in os.walk(base_path):\n", "        rel_root = os.path.relpath(root, base_path)\n", "        if rel_root == \".\":\n", "            parent_folder = None\n", "            current_folder = base_folder_name\n", "        elif '/' not in rel_root and '\\\\' not in rel_root:\n", "            parent_folder = base_folder_name\n", "            current_folder = rel_root\n", "        else:\n", "            parent_folder = os.path.dirname(rel_root)\n", "            current_folder = os.path.basename(rel_root)\n", "\n", "        if current_folder:\n", "            folder_records.append({\n", "                \"source_node\": parent_folder,\n", "                \"source_type\": \"Folder\" if parent_folder else None,\n", "                \"destination_node\": current_folder,\n", "                \"destination_type\": \"Folder\",\n", "                \"relationship\": \"CONTAINS\" if parent_folder else None,\n", "                \"file_path\": None\n", "            })\n", "\n", "        for f in files:\n", "            if f.endswith(\".java\"):\n", "                file_path = os.path.join(root, f)\n", "                file_rel_path = os.path.relpath(file_path, base_path)\n", "                file_records.append({\n", "                    \"source_node\": current_folder,\n", "                    \"source_type\": \"Folder\",\n", "                    \"destination_node\": f,\n", "                    \"destination_type\": \"File\",\n", "                    \"relationship\": \"CONTAINS\",\n", "                    \"file_path\": file_rel_path\n", "                })\n", "\n", "    return folder_records, file_records\n", "\n", "folder_records, file_records = extract_folder_file_hierarchy(BASE_PATH)"]}, {"cell_type": "code", "execution_count": 3, "id": "81b72422", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Stage 1: Extracted 8 folders and 12 files, saved to CSV.\n"]}], "source": ["\n", "\n", "df_folders = pd.DataFrame(folder_records)\n", "df_files = pd.DataFrame(file_records)\n", "\n", "\n", "print(f\"Stage 1: Extracted {len(df_folders)} folders and {len(df_files)} files, saved to CSV.\")"]}, {"cell_type": "code", "execution_count": 4, "id": "2b0aff5f", "metadata": {}, "outputs": [], "source": ["\n", "# -----------------------------------\n", "# Stage 2: AST extraction for LLM context (save CSV)\n", "\n", "JAVA_LANGUAGE = Language(tsjava.language())\n", "parser = Parser(JAVA_LANGUAGE)\n", "\n", "def read_source_code(file_path):\n", "    with open(file_path, \"r\", encoding=\"utf-8\") as f:\n", "        return f.read().encode(\"utf-8\")\n", "\n", "def extract_ast_structure(node, source_code, file_path):\n", "    records = []\n", "    current_class = None\n", "    current_method = None\n", "\n", "    def traverse(n):\n", "        nonlocal current_class, current_method\n", "        if n.type == \"class_declaration\":\n", "            for child in n.children:\n", "                if child.type == \"identifier\":\n", "                    class_name = source_code[child.start_byte:child.end_byte].decode()\n", "                    records.append({\n", "                        \"source_node\": os.path.basename(file_path),\n", "                        \"source_type\": \"File\",\n", "                        \"destination_node\": class_name,\n", "                        \"destination_type\": \"Class\",\n", "                        \"relationship\": \"DECLARES\",\n", "                        \"file_path\": file_path\n", "                    })\n", "                    current_class = class_name\n", "            for child in n.children:\n", "                traverse(child)\n", "            current_class = None\n", "\n", "        elif n.type == \"method_declaration\":\n", "            for child in n.children:\n", "                if child.type == \"identifier\":\n", "                    method_name = source_code[child.start_byte:child.end_byte].decode()\n", "                    records.append({\n", "                        \"source_node\": current_class,\n", "                        \"source_type\": \"Class\",\n", "                        \"destination_node\": method_name,\n", "                        \"destination_type\": \"Method\",\n", "                        \"relationship\": \"DECLARES\",\n", "                        \"file_path\": file_path\n", "                    })\n", "                    current_method = method_name\n", "            for child in n.children:\n", "                traverse(child)\n", "            current_method = None\n", "\n", "        elif n.type == \"method_invocation\":\n", "            for child in n.children:\n", "                if child.type == \"identifier\":\n", "                    called_method = source_code[child.start_byte:child.end_byte].decode()\n", "                    records.append({\n", "                        \"source_node\": current_method,\n", "                        \"source_type\": \"Method\",\n", "                        \"destination_node\": called_method,\n", "                        \"destination_type\": \"Method\",\n", "                        \"relationship\": \"CALLS\",\n", "                        \"file_path\": file_path\n", "                    })\n", "            for child in n.children:\n", "                traverse(child)\n", "\n", "        elif n.type == \"variable_declarator\":\n", "            for child in n.children:\n", "                if child.type == \"identifier\":\n", "                    var_name = source_code[child.start_byte:child.end_byte].decode()\n", "                    source = current_method or current_class or os.path.basename(file_path)\n", "                    source_type = \"Method\" if current_method else \"Class\" if current_class else \"File\"\n", "                    rel = \"USES\" if current_method else \"HAS_FIELD\" if current_class else \"DECLARES\"\n", "                    records.append({\n", "                        \"source_node\": source,\n", "                        \"source_type\": source_type,\n", "                        \"destination_node\": var_name,\n", "                        \"destination_type\": \"Variable\",\n", "                        \"relationship\": rel,\n", "                        \"file_path\": file_path\n", "                    })\n", "            for child in n.children:\n", "                traverse(child)\n", "        else:\n", "            for child in n.children:\n", "                traverse(child)\n", "\n", "    traverse(node)\n", "    return records"]}, {"cell_type": "code", "execution_count": 9, "id": "18fc3da4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Stage 2: Extracted 244 AST triples for context, saved to CSV.\n"]}], "source": ["ast_records = []\n", "for root, dirs, files in os.walk(BASE_PATH):\n", "    for file in files:\n", "        if file.endswith(\".java\"):\n", "            file_path = os.path.join(root, file)\n", "            source_code = read_source_code(file_path)\n", "            tree = parser.parse(source_code)\n", "            ast_records.extend(extract_ast_structure(tree.root_node, source_code, file_path))\n", "\n", "df_ast = pd.DataFrame(ast_records)\n", "\n", "print(f\"Stage 2: Extracted {len(df_ast)} AST triples for context, saved to CSV.\")"]}, {"cell_type": "code", "execution_count": 10, "id": "766833d5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Stage 3: Split 12 Java files into 14 chunks.\n"]}], "source": ["\n", "# ------------------------------\n", "# Stage 3: Split Java files into chunks and embed for vector DB\n", "\n", "splitter = RecursiveCharacterTextSplitter.from_language(\n", "    language=LC_Language.JAVA,\n", "    chunk_size=2000,\n", "    chunk_overlap=200\n", ")\n", "\n", "# Load Java files\n", "java_docs = []\n", "for root, dirs, files in os.walk(BASE_PATH):\n", "    for file in files:\n", "        if file.endswith(\".java\"):\n", "            loader = TextLoader(os.path.join(root, file))\n", "            java_docs.extend(loader.load())\n", "\n", "# Split documents once\n", "split_docs = []\n", "for doc in java_docs:\n", "    split_docs.extend(splitter.split_documents([doc]))\n", "\n", "print(f\"Stage 3: Split {len(java_docs)} Java files into {len(split_docs)} chunks.\")"]}, {"cell_type": "code", "execution_count": 11, "id": "eca94d24", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Stage 3: Saved FAISS vector store locally with Gemini embeddings.\n"]}], "source": ["# Embed chunks with Gemini embeddings\n", "embeddings = GoogleGenerativeAIEmbeddings(\n", "    model=\"models/gemini-embedding-exp-03-07\",\n", "    google_api_key=google_api_key\n", ")\n", "\n", "texts = [chunk.page_content for chunk in split_docs]\n", "\n", "faiss_index = FAISS.from_texts(\n", "    texts,\n", "    embeddings,\n", "    metadatas=[chunk.metadata for chunk in split_docs]\n", ")\n", "\n", "faiss_index.save_local(\"faiss_index\")\n", "print(\"Stage 3: Saved FAISS vector store locally with Gemini embeddings.\")"]}, {"cell_type": "code", "execution_count": 12, "id": "06f915e9", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_12520\\4231329552.py:10: LangChainDeprecationWarning: The class `Neo4jGraph` was deprecated in LangChain 0.3.8 and will be removed in 1.0. An updated version of the class exists in the :class:`~langchain-neo4j package and should be used instead. To use it run `pip install -U :class:`~langchain-neo4j` and import as `from :class:`~langchain_neo4j import Neo4jGraph``.\n", "  graph = Neo4jGraph(\n"]}], "source": ["# ------------------------------\n", "# Stage 4: LLM extraction with vector retrieval + AST context\n", "\n", "llm = ChatGoogleGenerativeAI(\n", "    model=\"gemini-2.5-pro\",\n", "    temperature=0,\n", "    google_api_key=google_api_key\n", ")\n", "\n", "graph = Neo4jGraph(\n", "    url=NEO4J_URI,\n", "    username=NEO4J_USER,\n", "    password=NEO4J_PASSWORD,\n", "    database=NEO4J_DB\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "974d7299", "metadata": {}, "outputs": [], "source": ["# Escape curly braces in Java code chunk and related_text, ast_context\n", "\n", "def escape_braces(text: str) -> str:\n", "    return text.replace(\"{\", \"{{\").replace(\"}\", \"}}\")\n", "\n", "def build_system_prompt(file_path, ast_df, related_chunks):\n", "    # AST context filtered by file\n", "    ast_subset = ast_df[ast_df[\"file_path\"] == file_path]\n", "    ast_context = \"\"\n", "    for _, row in ast_subset.iterrows():\n", "        ast_context += f\"{row['source_type'].capitalize()}:{row['source_node']} -[{row['relationship'].upper()}]-> {row['destination_type'].capitalize()}:{row['destination_node']}\\n\"\n", "\n", "    # Related chunks concatenated (including the first)\n", "    related_text = \"\\n\\n---\\n\\n\".join([chunk.page_content for chunk in related_chunks])\n", "\n", "    # Escape all curly braces to avoid formatting errors\n", "    escaped_chunk = escape_braces(related_chunks[5].page_content)\n", "    escaped_related_text = escape_braces(related_text)\n", "    escaped_ast_context = escape_braces(ast_context)\n", "\n", "    prompt = f\"\"\"\n", "You are a code lineage extraction engine.\n", "\n", "Given this Java code chunk:\n", "{escaped_chunk}\n", "\n", "And these related code chunks (from the same or related files):\n", "{escaped_related_text}\n", "\n", "And this AST context for the file:\n", "{escaped_ast_context}\n", "\n", "Extract lineage triples in the format:\n", "[SourceNodeType]:SourceNodeName -[RELATIONSHIP]-> [TargetNodeType]:TargetNodeName\n", "\n", "Ignore trivial variables and utility methods.\n", "Focus on meaningful business logic relationships.\n", "Return triples only without explanations.\n", "\"\"\"\n", "    return prompt\n"]}, {"cell_type": "code", "execution_count": 14, "id": "0a64b681", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["LLM Extraction per chunk: 100%|██████████| 14/14 [11:00<00:00, 47.18s/it]\n"]}], "source": ["\n", "\n", "all_llm_lineage = []\n", "\n", "for chunk in tqdm(split_docs, desc=\"LLM Extraction per chunk\"):\n", "    query_vector = embeddings.embed_query(chunk.page_content)\n", "    related_chunks = faiss_index.similarity_search_by_vector(query_vector, k=3)\n", "    file_path = chunk.metadata.get(\"source\", \"\")\n", "\n", "    system_prompt = build_system_prompt(file_path, df_ast, related_chunks)\n", "\n", "    transformer = LLMGraphTransformer(\n", "        llm=llm,\n", "        additional_instructions=system_prompt,\n", "        allowed_nodes=[\"File\", \"Class\", \"Method\", \"Variable\", \"Table\", \"API\"],\n", "        allowed_relationships=[\n", "            (\"File\", \"DECLARES\", \"Class\"),\n", "            (\"Class\", \"DECLARES\", \"Method\"),\n", "            (\"Method\", \"CALLS\", \"Method\"),\n", "            (\"Class\", \"HAS_FIELD\", \"Variable\"),\n", "            (\"Method\", \"USES\", \"Variable\"),\n", "            (\"Method\", \"READS_FROM\", \"Table\"),\n", "            (\"Method\", \"WRITES_TO\", \"Table\"),\n", "            (\"Method\", \"CALLS_API\", \"API\"),\n", "            (\"Variable\", \"DERIVES_FROM\", \"Variable\"),\n", "            (\"Variable\", \"WRITES_TO\", \"Table\"),\n", "            (\"Variable\", \"READS_FROM\", \"Table\"),\n", "        ],\n", "        strict_mode=True,\n", "        node_properties=False,\n", "        relationship_properties=False,\n", "    )\n", "\n", "    graph_docs = transformer.convert_to_graph_documents([chunk])\n", "\n", "    for gd in graph_docs:\n", "        for rel in gd.relationships:\n", "            all_llm_lineage.append({\n", "                \"source_node\": rel.source.id.strip().lower() if rel.source.id else None,\n", "                \"source_type\": rel.source.type.strip().lower() if rel.source.type else None,\n", "                \"destination_node\": rel.target.id.strip().lower() if rel.target.id else None,\n", "                \"destination_type\": rel.target.type.strip().lower() if rel.target.type else None,\n", "                \"relationship\": rel.type.strip().lower() if rel.type else None,\n", "                \"file_path\": file_path\n", "            })"]}, {"cell_type": "code", "execution_count": 19, "id": "09179223", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Stage 4: Extracted 125 LLM lineage triples, saved to CSV.\n"]}], "source": ["\n", "df_llm_lineage = pd.DataFrame(all_llm_lineage).dropna().drop_duplicates()\n", "\n", "print(f\"Stage 4: Extracted {len(df_llm_lineage)} LLM lineage triples, saved to CSV.\")\n", "\n", "# ------------------------------\n", "# Final Stage: Combine and push all lineage to Neo4j\n", "\n", "df_folders_files = pd.concat([df_folders, df_files], ignore_index=True)\n", "df_all = pd.concat([df_folders_files, df_llm_lineage], ignore_index=True)\n", "df_all.to_csv(\"stage4_final.csv\", index=False)"]}, {"cell_type": "code", "execution_count": 20, "id": "d9cbcd3d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Filtered to 143 valid lineage rows before Neo4j ingestion.\n"]}], "source": ["# Remove invalid rows before ingestion\n", "invalid_values = [\"none\", \"nan\", \"\"]\n", "\n", "df_all_cleaned = df_all[\n", "    df_all[\"source_node\"].notna() &\n", "    df_all[\"destination_node\"].notna() &\n", "    ~df_all[\"source_node\"].isin(invalid_values) &\n", "    ~df_all[\"destination_node\"].isin(invalid_values)\n", "].copy()\n", "\n", "print(f\"✅ Filtered to {len(df_all_cleaned)} valid lineage rows before Neo4j ingestion.\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "5ae74d41", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'graph' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 2\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# Clear existing Neo4j graph\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m \u001b[43mgraph\u001b[49m.query(\u001b[33m\"\u001b[39m\u001b[33mMATCH (n) DETACH DELETE n\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[31mNameError\u001b[39m: name 'graph' is not defined"]}], "source": ["# Clear existing Neo4j graph\n", "graph.query(\"MATCH (n) DETACH DELETE n\")"]}, {"cell_type": "code", "execution_count": 25, "id": "1e19ff64", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Pushing lineage to Neo4j:   0%|          | 0/143 [00:00<?, ?it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["Pushing lineage to Neo4j: 100%|██████████| 143/143 [00:01<00:00, 83.53it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Completed Neo4j ingestion without 'none' or 'nan' nodes.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["for idx, row in tqdm(df_all_cleaned.iterrows(), total=len(df_all_cleaned), desc=\"Pushing lineage to Neo4j\"):\n", "    try:\n", "        source_label = row[\"source_type\"].capitalize() if row[\"source_type\"] else \"Unknown\"\n", "        dest_label = row[\"destination_type\"].capitalize() if row[\"destination_type\"] else \"Unknown\"\n", "        rel_type = row[\"relationship\"].upper() if row[\"relationship\"] else \"RELATED\"\n", "\n", "        graph.query(\n", "            f\"\"\"\n", "            MERGE (s:{source_label} {{name: $source_node}})\n", "            MERGE (t:{dest_label} {{name: $destination_node}})\n", "            MERGE (s)-[:{rel_type}]->(t)\n", "            \"\"\",\n", "            {\n", "                \"source_node\": row[\"source_node\"],\n", "                \"destination_node\": row[\"destination_node\"]\n", "            }\n", "        )\n", "    except Exception as e:\n", "        print(f\"Error pushing row {idx}: {e}\")\n", "\n", "print(\"✅ Completed Neo4j ingestion without 'none' or 'nan' nodes.\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "01cbfa4e", "metadata": {}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}