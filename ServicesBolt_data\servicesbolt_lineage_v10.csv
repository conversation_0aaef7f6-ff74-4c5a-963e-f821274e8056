source_node,source_type,destination_node,destination_type,relationship
ServicesBolt,Folder,Application,File,CONTAINS
ServicesBolt,Folder,TriggerCollector,File,CONTAINS
ServicesBolt,Folder,Api,Folder,CONTAINS
Api,Folder,ALMConfigController,File,CONTAINS
Api,Folder,AlmController,File,CONTAINS
ServicesBolt,Folder,Request,Folder,CONTAINS
Request,Folder,ALMConfigReq,File,CONTAINS
Request,Folder,ALMToolReq,File,CONTAINS
ServicesBolt,Folder,Response,Folder,CONTAINS
Response,Folder,Authentication,File,CONTAINS
Response,Folder,DataResponse,File,CONTAINS
ServicesBolt,Folder,Service,Folder,CONTAINS
Service,Folder,ALMConfigService,File,CONTAINS
Service,Folder,ALMConfigServiceImplementation,File,CONTAINS
Service,Folder,AlmService,File,CONTAINS
Service,Folder,ALMServiceImplementation,File,CONTAINS
Service,Folder,ConfigurationSettingService,File,CONTAINS
Service,Folder,ConfigurationSettingServiceImplementation,File,CONTAINS
ServicesBolt,Folder,Util,Folder,CONTAINS
Util,Folder,DateUtil,File,CONTAINS
Application,File,Application,Class,DECLARES
TriggerCollector,File,TriggerCollector,Class,DECLARES
ALMConfigController,File,ALMConfigController,Class,DECLARES
AlmController,File,AlmController,Class,DECLARES
ALMConfigReq,File,ALMConfigReq,Class,DECLARES
ALMToolReq,File,ALMToolReq,Class,DECLARES
DataResponse,File,DataResponse,Class,DECLARES
ALMConfigServiceImplementation,File,ALMConfigServiceImplementation,Class,DECLARES
ALMServiceImplementation,File,ALMServiceImplementation,Class,DECLARES
ALMServiceImplementation,File,TransitionComparator,Class,DECLARES
ALMServiceImplementation,File,SprintComparatort,Class,DECLARES
ConfigurationSettingServiceImplementation,File,ConfigurationSettingServiceImplementation,Class,DECLARES
DateUtil,File,DateUtil,Class,DECLARES
Application,Class,configure,Method,DECLARES
Application,Class,passwordEncoder,Method,DECLARES
Application,Class,main,Method,DECLARES
TriggerCollector,Class,Logger,Variable,HAS_FIELD
TriggerCollector,Class,Ctx,Variable,HAS_FIELD
TriggerCollector,Class,getDataFromTools,Method,DECLARES
getDataFromTools,Method,Ctx,Variable,USES
getDataFromTools,Method,Portfolio,Variable,USES
getDataFromTools,Method,Scheduler,Variable,USES
getDataFromTools,Method,Jobkey,Variable,USES
getDataFromTools,Method,Job,Variable,USES
getDataFromTools,Method,Joba,Variable,USES
getDataFromTools,Method,Trigger,Variable,USES
ALMConfigController,Class,Log,Variable,HAS_FIELD
ALMConfigController,Class,Almconfigservice,Variable,HAS_FIELD
ALMConfigController,Class,saveALMConfig,Method,DECLARES
ALMConfigController,Class,retrieveList,Method,DECLARES
ALMConfigController,Class,retrieveALMConfig,Method,DECLARES
AlmController,Class,Service,Variable,HAS_FIELD
AlmController,Class,storyAgeing,Method,DECLARES
AlmController,Class,groomingTable,Method,DECLARES
AlmController,Class,delDuplicate,Method,DECLARES
AlmController,Class,getSprintProgressHome,Method,DECLARES
AlmController,Class,defectInsightData,Method,DECLARES
AlmController,Class,defectTrendAndClassification,Method,DECLARES
AlmController,Class,defectClassification,Method,DECLARES
AlmController,Class,getIssueBrakeUp,Method,DECLARES
AlmController,Class,getStoryProgress,Method,DECLARES
AlmController,Class,getDefectsSummaryHome,Method,DECLARES
AlmController,Class,getTaskRiskStoryPoint,Method,DECLARES
AlmController,Class,burndownCalculation,Method,DECLARES
AlmController,Class,getProductionSlippage,Method,DECLARES
AlmController,Class,getDefectDensity,Method,DECLARES
AlmController,Class,getDefectBacklog,Method,DECLARES
AlmController,Class,getDefectPareto,Method,DECLARES
AlmController,Class,getActiveSprints,Method,DECLARES
AlmController,Class,delAllIsues,Method,DECLARES
AlmController,Class,getMetricsDatas,Method,DECLARES
AlmController,Class,getAllTransitions,Method,DECLARES
AlmController,Class,getProjectMetrics,Method,DECLARES
AlmController,Class,getChangesItems,Method,DECLARES
AlmController,Class,getTransitionsData,Method,DECLARES
AlmController,Class,getIterationData,Method,DECLARES
AlmController,Class,getEffortData,Method,DECLARES
AlmController,Class,getProjectDetials,Method,DECLARES
AlmController,Class,getCurrentProjectDetials,Method,DECLARES
AlmController,Class,getCurrentIter,Method,DECLARES
AlmController,Class,getIterations,Method,DECLARES
AlmController,Class,getDefectCount,Method,DECLARES
AlmController,Class,getRelease,Method,DECLARES
AlmController,Class,getUnReleaseData,Method,DECLARES
getUnReleaseData,Method,Response,Variable,USES
AlmController,Class,getDefects,Method,DECLARES
getDefects,Method,Response,Variable,USES
AlmController,Class,getSlaData,Method,DECLARES
AlmController,Class,getAssigneeIssues,Method,DECLARES
AlmController,Class,getDateIterations,Method,DECLARES
AlmController,Class,getProdDefects,Method,DECLARES
getProdDefects,Method,Response,Variable,USES
AlmController,Class,getAlmType,Method,DECLARES
getAlmType,Method,Almtype,Variable,USES
getAlmType,Method,Config,Variable,USES
getAlmType,Method,Metric,Variable,USES
getAlmType,Method,Configuration1,Variable,USES
getAlmType,Method,Metric1,Variable,USES
AlmController,Class,getVelocityChart,Method,DECLARES
AlmController,Class,getIssueHierarchy,Method,DECLARES
getIssueHierarchy,Method,Response,Variable,USES
AlmController,Class,getComponentWiseIssueHierarchy,Method,DECLARES
getComponentWiseIssueHierarchy,Method,Response,Variable,USES
AlmController,Class,getComponentWiseVelocityChart,Method,DECLARES
getComponentWiseVelocityChart,Method,Resp,Variable,USES
AlmController,Class,getComponontWiseSprintWiseStories,Method,DECLARES
getComponontWiseSprintWiseStories,Method,Response,Variable,USES
AlmController,Class,getComponents,Method,DECLARES
getComponents,Method,Response,Variable,USES
AlmController,Class,updateComponent,Method,DECLARES
AlmController,Class,saveEngScore,Method,DECLARES
AlmController,Class,getFeatureMetrics,Method,DECLARES
ALMConfigReq,Class,Storyname,Variable,HAS_FIELD
ALMConfigReq,Class,Priorityname,Variable,HAS_FIELD
ALMConfigReq,Class,Projectname,Variable,HAS_FIELD
ALMConfigReq,Class,Defectname,Variable,HAS_FIELD
ALMConfigReq,Class,Releasename,Variable,HAS_FIELD
ALMConfigReq,Class,Taskname,Variable,HAS_FIELD
ALMConfigReq,Class,Closestate,Variable,HAS_FIELD
ALMConfigReq,Class,Newstate,Variable,HAS_FIELD
ALMConfigReq,Class,Progressstate,Variable,HAS_FIELD
ALMConfigReq,Class,Criticalpriority,Variable,HAS_FIELD
ALMConfigReq,Class,Highpriority,Variable,HAS_FIELD
ALMConfigReq,Class,Medpriority,Variable,HAS_FIELD
ALMConfigReq,Class,Lowpriority,Variable,HAS_FIELD
ALMConfigReq,Class,Tracksset,Variable,HAS_FIELD
ALMConfigReq,Class,Rejectionphase,Variable,HAS_FIELD
ALMConfigReq,Class,Reopenphase,Variable,HAS_FIELD
ALMConfigReq,Class,Testingphase,Variable,HAS_FIELD
ALMConfigReq,Class,Productionphase,Variable,HAS_FIELD
ALMConfigReq,Class,Personhours,Variable,HAS_FIELD
ALMConfigReq,Class,Timezone,Variable,HAS_FIELD
ALMConfigReq,Class,Velocityfields,Variable,HAS_FIELD
ALMConfigReq,Class,Environment,Variable,HAS_FIELD
ALMConfigReq,Class,Safeenabled,Variable,HAS_FIELD
ALMConfigReq,Class,Ccrlabel,Variable,HAS_FIELD
ALMConfigReq,Class,Cycletimestates,Variable,HAS_FIELD
ALMConfigReq,Class,Throughputstates,Variable,HAS_FIELD
ALMConfigReq,Class,Firstsprint,Variable,HAS_FIELD
ALMConfigReq,Class,Trendtype,Variable,HAS_FIELD
ALMConfigReq,Class,getCcrLabel,Method,DECLARES
ALMConfigReq,Class,setCcrLabel,Method,DECLARES
ALMConfigReq,Class,getCycleTimeStates,Method,DECLARES
ALMConfigReq,Class,setCycleTimeStates,Method,DECLARES
ALMConfigReq,Class,getThroughputStates,Method,DECLARES
ALMConfigReq,Class,setThroughputStates,Method,DECLARES
ALMConfigReq,Class,getRejectionPhase,Method,DECLARES
ALMConfigReq,Class,setRejectionPhase,Method,DECLARES
ALMConfigReq,Class,getReopenPhase,Method,DECLARES
ALMConfigReq,Class,setReopenPhase,Method,DECLARES
ALMConfigReq,Class,getTestingPhase,Method,DECLARES
ALMConfigReq,Class,setTestingPhase,Method,DECLARES
ALMConfigReq,Class,getProductionPhase,Method,DECLARES
ALMConfigReq,Class,setProductionPhase,Method,DECLARES
ALMConfigReq,Class,getStoryName,Method,DECLARES
ALMConfigReq,Class,getPriorityName,Method,DECLARES
ALMConfigReq,Class,setPriorityName,Method,DECLARES
ALMConfigReq,Class,getTaskName,Method,DECLARES
ALMConfigReq,Class,setTaskName,Method,DECLARES
ALMConfigReq,Class,getReleaseName,Method,DECLARES
ALMConfigReq,Class,setReleaseName,Method,DECLARES
ALMConfigReq,Class,getCloseState,Method,DECLARES
ALMConfigReq,Class,setCloseState,Method,DECLARES
ALMConfigReq,Class,getCriticalPriority,Method,DECLARES
ALMConfigReq,Class,setCriticalPriority,Method,DECLARES
ALMConfigReq,Class,getHighPriority,Method,DECLARES
ALMConfigReq,Class,setHighPriority,Method,DECLARES
ALMConfigReq,Class,getMedPriority,Method,DECLARES
ALMConfigReq,Class,setMedPriority,Method,DECLARES
ALMConfigReq,Class,getLowPriority,Method,DECLARES
ALMConfigReq,Class,setLowPriority,Method,DECLARES
ALMConfigReq,Class,setStoryName,Method,DECLARES
ALMConfigReq,Class,getProjectName,Method,DECLARES
ALMConfigReq,Class,setProjectName,Method,DECLARES
ALMConfigReq,Class,getDefectName,Method,DECLARES
ALMConfigReq,Class,setDefectName,Method,DECLARES
ALMConfigReq,Class,getTrendType,Method,DECLARES
ALMConfigReq,Class,setTrendType,Method,DECLARES
ALMConfigReq,Class,getTracksSet,Method,DECLARES
ALMConfigReq,Class,setTracksSet,Method,DECLARES
ALMConfigReq,Class,getNewState,Method,DECLARES
ALMConfigReq,Class,setNewState,Method,DECLARES
ALMConfigReq,Class,getProgressState,Method,DECLARES
ALMConfigReq,Class,setProgressState,Method,DECLARES
ALMConfigReq,Class,toDetailsAddSetting,Method,DECLARES
ALMConfigReq,Class,getPersonHours,Method,DECLARES
ALMConfigReq,Class,setPersonHours,Method,DECLARES
ALMConfigReq,Class,getTimeZone,Method,DECLARES
ALMConfigReq,Class,setTimeZone,Method,DECLARES
ALMConfigReq,Class,getVelocityFields,Method,DECLARES
ALMConfigReq,Class,setVelocityFields,Method,DECLARES
ALMConfigReq,Class,getEnvironment,Method,DECLARES
ALMConfigReq,Class,setEnvironment,Method,DECLARES
ALMConfigReq,Class,isSafeEnabled,Method,DECLARES
ALMConfigReq,Class,setSafeEnabled,Method,DECLARES
ALMConfigReq,Class,getFirstSprint,Method,DECLARES
ALMConfigReq,Class,setFirstSprint,Method,DECLARES
ALMToolReq,Class,Almtype,Variable,HAS_FIELD
ALMToolReq,Class,getAlmType,Method,DECLARES
ALMToolReq,Class,setAlmType,Method,DECLARES
setAlmType,Method,Almtype,Variable,USES
Authentication,Class,Authntication,Variable,HAS_FIELD
Authentication,Class,Authenticationofservice,Method,DECLARES
Auth,Class,Response,Variable,HAS_FIELD
Auth,Class,Authenticationstatus,Variable,HAS_FIELD
Auth,Class,Authenticate,Method,DECLARES
DataResponse,Class,Lastupdated,Variable,HAS_FIELD
DataResponse,Class,getResult,Method,DECLARES
DataResponse,Class,getLastUpdated,Method,DECLARES
Almconfigservice,Interface,saveALMConfig,Method,DECLARES
Almconfigservice,Interface,retrieveALMConfig,Method,DECLARES
ALMConfigServiceImplementation,Class,saveALMConfig,Method,DECLARES
ALMConfigServiceImplementation,Class,retrieveALMConfig,Method,DECLARES
ALMConfigServiceImplementation,Class,Almconfigrepo,Variable,HAS_FIELD
ALMConfigServiceImplementation,Class,Log,Variable,HAS_FIELD
retrieveALMConfig,Method,Lastupdate,Variable,USES
Almservice,Interface,getMetricDetails,Method,DECLARES
Almservice,Interface,getAllMetrics,Method,DECLARES
Almservice,Interface,getChangesItems,Method,DECLARES
Almservice,Interface,getTransitionsData,Method,DECLARES
Almservice,Interface,getIterationData,Method,DECLARES
Almservice,Interface,getEffortData,Method,DECLARES
Almservice,Interface,getProjectDetails,Method,DECLARES
Almservice,Interface,getDefectCounts,Method,DECLARES
Almservice,Interface,getCrtItr,Method,DECLARES
Almservice,Interface,getRelease,Method,DECLARES
Almservice,Interface,getUnReleaseData,Method,DECLARES
Almservice,Interface,getDefects,Method,DECLARES
Almservice,Interface,getSlaData,Method,DECLARES
Almservice,Interface,getAssigneeIssues,Method,DECLARES
Almservice,Interface,getDateIterations,Method,DECLARES
Almservice,Interface,getProdDefects,Method,DECLARES
Almservice,Interface,delDuplicate,Method,DECLARES
Almservice,Interface,getCurrentProjectDetails,Method,DECLARES
Almservice,Interface,delAllIssues,Method,DECLARES
Almservice,Interface,getAllTransitions,Method,DECLARES
Almservice,Interface,getComponentVelocity,Method,DECLARES
Almservice,Interface,getComponentsSprint,Method,DECLARES
Almservice,Interface,getIssueHierarchy,Method,DECLARES
Almservice,Interface,getComponentWiseIssueHierarchy,Method,DECLARES
Almservice,Interface,getComponents,Method,DECLARES
Almservice,Interface,updateComponentsOfTaskandSubtask,Method,DECLARES
Almservice,Interface,getFeatureMetrics,Method,DECLARES
Almservice,Interface,getSprintProgressHome,Method,DECLARES
Almservice,Interface,getDefectsSummaryHome,Method,DECLARES
Almservice,Interface,getTaskRisk,Method,DECLARES
Almservice,Interface,getActiveSprints,Method,DECLARES
Almservice,Interface,getIssueBrakeUp,Method,DECLARES
Almservice,Interface,getStoryProgress,Method,DECLARES
Almservice,Interface,burndownCalculation,Method,DECLARES
Almservice,Interface,getDefectInsightData,Method,DECLARES
Almservice,Interface,defectParetoCalculation,Method,DECLARES
Almservice,Interface,getProductionSlippage,Method,DECLARES
Almservice,Interface,getDefectDensity,Method,DECLARES
Almservice,Interface,getDefectBacklog,Method,DECLARES
Almservice,Interface,getDefectTrendAndClassification,Method,DECLARES
Almservice,Interface,getStoryAgeingData,Method,DECLARES
Almservice,Interface,getGroomingTable,Method,DECLARES
Almservice,Interface,getAllIterations,Method,DECLARES
Almservice,Interface,getDefectClassification,Method,DECLARES
Almservice,Interface,saveEngScore,Method,DECLARES
Almservice,Interface,getComponentVelocityChart,Method,DECLARES
Almservice,Interface,getComponentsSprintStories,Method,DECLARES
Almservice,Interface,getIssueHierarchyChart,Method,DECLARES
Almservice,Interface,getComponentWiseIssueHierarchyChart,Method,DECLARES
Almservice,Interface,getComponentsChart,Method,DECLARES
ALMServiceImplementation,Class,Transitionrepo,Variable,HAS_FIELD
ALMServiceImplementation,Class,Efforthistoryrepo,Variable,HAS_FIELD
TransitionComparator,Class,compare,Method,DECLARES
compare,Method,T1,Variable,USES
compare,Method,T2,Variable,USES
SprintComparatort,Class,compare,Method,DECLARES
compare,Method,S1,Variable,USES
compare,Method,S2,Variable,USES
ALMServiceImplementation,Class,getDateIterations,Method,DECLARES
getDateIterations,Method,Response,Variable,USES
getDateIterations,Method,Query,Variable,USES
ALMServiceImplementation,Class,getMetricDetails,Method,DECLARES
ALMServiceImplementation,Class,getAllMetrics,Method,DECLARES
ALMServiceImplementation,Class,getChangesItems,Method,DECLARES
ALMServiceImplementation,Class,getTransitionsData,Method,DECLARES
ALMServiceImplementation,Class,getIterationData,Method,DECLARES
ALMServiceImplementation,Class,getEffortData,Method,DECLARES
ALMServiceImplementation,Class,getProjectDetails,Method,DECLARES
ALMServiceImplementation,Class,getFeatureMetrics,Method,DECLARES
ALMServiceImplementation,Class,populateAuthor,Method,DECLARES
ALMServiceImplementation,Class,getCrtItr,Method,DECLARES
ALMServiceImplementation,Class,getDefectCounts,Method,DECLARES
ALMServiceImplementation,Class,getRelease,Method,DECLARES
ALMServiceImplementation,Class,getUnReleaseData,Method,DECLARES
ALMServiceImplementation,Class,getDefects,Method,DECLARES
ALMServiceImplementation,Class,getProdDefects,Method,DECLARES
ALMServiceImplementation,Class,getSlaData,Method,DECLARES
ALMServiceImplementation,Class,getAssigneeIssues,Method,DECLARES
ALMServiceImplementation,Class,delDuplicate,Method,DECLARES
ALMServiceImplementation,Class,getCurrentProjectDetails,Method,DECLARES
ALMServiceImplementation,Class,delAllIssues,Method,DECLARES
ALMServiceImplementation,Class,getAllTransitions,Method,DECLARES
ALMServiceImplementation,Class,getComponentVelocity,Method,DECLARES
ALMServiceImplementation,Class,getComponentsSprint,Method,DECLARES
ALMServiceImplementation,Class,getIssueHierarchy,Method,DECLARES
ALMServiceImplementation,Class,getComponentList,Method,DECLARES
ALMServiceImplementation,Class,getComponentWiseIssueHierarchy,Method,DECLARES
ALMServiceImplementation,Class,getComponents,Method,DECLARES
ALMServiceImplementation,Class,getSprintWiseStories,Method,DECLARES
ALMServiceImplementation,Class,filterTrans,Method,DECLARES
ALMServiceImplementation,Class,getVelocityChart,Method,DECLARES
ALMServiceImplementation,Class,callSP,Method,DECLARES
ALMServiceImplementation,Class,calcClosedSP,Method,DECLARES
ALMServiceImplementation,Class,storyLoop,Method,DECLARES
ALMServiceImplementation,Class,storyLoopRefined,Method,DECLARES
ALMServiceImplementation,Class,updateComponentsOfTaskandSubtask,Method,DECLARES
ALMServiceImplementation,Class,getSprintProgressHome,Method,DECLARES
ALMServiceImplementation,Class,getDefectsSummaryHome,Method,DECLARES
ALMServiceImplementation,Class,getTaskRisk,Method,DECLARES
ALMServiceImplementation,Class,getActiveSprints,Method,DECLARES
ALMServiceImplementation,Class,getIssueBrakeUp,Method,DECLARES
ALMServiceImplementation,Class,getStoryProgress,Method,DECLARES
ALMServiceImplementation,Class,burndownCalculation,Method,DECLARES
ALMServiceImplementation,Class,getDefectInsightData,Method,DECLARES
ALMServiceImplementation,Class,defectParetoCalculation,Method,DECLARES
ALMServiceImplementation,Class,getProductionSlippage,Method,DECLARES
ALMServiceImplementation,Class,getDefectDensity,Method,DECLARES
ALMServiceImplementation,Class,getDefectBacklog,Method,DECLARES
ALMServiceImplementation,Class,getDefectTrendAndClassification,Method,DECLARES
ALMServiceImplementation,Class,getDefectClassification,Method,DECLARES
ALMServiceImplementation,Class,getStoryAgeingData,Method,DECLARES
ALMServiceImplementation,Class,getGroomingTable,Method,DECLARES
ALMServiceImplementation,Class,getAllIterations,Method,DECLARES
ALMServiceImplementation,Class,getAlmType,Method,DECLARES
ALMServiceImplementation,Class,saveEngScore,Method,DECLARES
ALMServiceImplementation,Class,getComponentVelocityChart,Method,DECLARES
ALMServiceImplementation,Class,getComponentsSprintStories,Method,DECLARES
ALMServiceImplementation,Class,getIssueHierarchyChart,Method,DECLARES
ALMServiceImplementation,Class,getComponentWiseIssueHierarchyChart,Method,DECLARES
ALMServiceImplementation,Class,getComponentsChart,Method,DECLARES
ALMServiceImplementation,Class,Changehisortyrepo,Variable,HAS_FIELD
ALMServiceImplementation,Class,Authorrepo,Variable,HAS_FIELD
ALMServiceImplementation,Class,Projectrepo,Variable,HAS_FIELD
ALMServiceImplementation,Class,Releaserepo,Variable,HAS_FIELD
ALMServiceImplementation,Class,Iterationrepo,Variable,HAS_FIELD
ALMServiceImplementation,Class,Metricrepo,Variable,HAS_FIELD
ALMServiceImplementation,Class,Almconfigrepo,Variable,HAS_FIELD
ALMServiceImplementation,Class,Agg,Variable,HAS_FIELD
ALMServiceImplementation,Class,Mongotemplate,Variable,HAS_FIELD
ALMServiceImplementation,Class,Workingbacklog,Variable,HAS_FIELD
ALMServiceImplementation,Class,Workingsprints,Variable,HAS_FIELD
ALMServiceImplementation,Class,Velocityfields,Variable,HAS_FIELD
ALMServiceImplementation,Class,Closestates,Variable,HAS_FIELD
ALMServiceImplementation,Class,Tasknames,Variable,HAS_FIELD
ALMServiceImplementation,Class,Almconfig,Variable,HAS_FIELD
ALMServiceImplementation,Class,Vlist,Variable,HAS_FIELD
ALMServiceImplementation,Class,Issuelist,Variable,HAS_FIELD
ALMServiceImplementation,Class,Widarr,Variable,HAS_FIELD
ALMServiceImplementation,Class,Tempsprefined,Variable,HAS_FIELD
ALMServiceImplementation,Class,Tempspremoved,Variable,HAS_FIELD
ALMServiceImplementation,Class,Finalstoriescommited,Variable,HAS_FIELD
ALMServiceImplementation,Class,Storiescompleted,Variable,HAS_FIELD
ALMServiceImplementation,Class,Defetcscompleted,Variable,HAS_FIELD
ALMServiceImplementation,Class,Refinedissulist,Variable,HAS_FIELD
ALMServiceImplementation,Class,Removedissulist,Variable,HAS_FIELD
ALMServiceImplementation,Class,Logger,Variable,HAS_FIELD
ALMServiceImplementation,Class,Chartservice,Variable,HAS_FIELD
Configurationsettingservice,Interface,getConfig,Method,DECLARES
Configurationsettingservice,Interface,addConfig,Method,DECLARES
Configurationsettingservice,Interface,deleteConfig,Method,DECLARES
Configurationsettingservice,Interface,deleteAllCollections,Method,DECLARES
Configurationsettingservice,Interface,deleteProject,Method,DECLARES
Configurationsettingservice,Interface,getConfigProject,Method,DECLARES
ConfigurationSettingServiceImplementation,Class,Configurationsettingrepository,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,Almservice,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,Buildrepo,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,Buildfailurepatternrepo,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,Codecoveragerepo,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,Codequalityrep,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,Healthrepo,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,Scmrepo,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,Almconfigrepo,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,Chartconfigrepo,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,Goalsettingrepo,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,Portfoliorepo,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,Projecthealthrepo,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,Userassociation,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,Log,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,getConfig,Method,DECLARES
getConfig,Method,Lastupdated,Variable,USES
ConfigurationSettingServiceImplementation,Class,addConfig,Method,DECLARES
addConfig,Method,Date,Variable,USES
addConfig,Method,Timestamp,Variable,USES
ConfigurationSettingServiceImplementation,Class,deleteConfig,Method,DECLARES
ConfigurationSettingServiceImplementation,Class,deleteAllCollections,Method,DECLARES
deleteAllCollections,Method,Failurepattern,Variable,USES
deleteAllCollections,Method,Failurepatternobj,Variable,USES
ConfigurationSettingServiceImplementation,Class,deleteProject,Method,DECLARES
ConfigurationSettingServiceImplementation,Class,getConfigProject,Method,DECLARES
getConfigProject,Method,Config,Variable,USES
DateUtil,Class,getDateInFormat,Method,DECLARES
getDateInFormat,Method,Pattern,Variable,USES
getDateInFormat,Method,Format,Variable,USES
getDateInFormat,Method,Simpledateformat,Variable,USES
DateUtil,Class,getLastWeekWorkingDateRange,Method,DECLARES
getLastWeekWorkingDateRange,Method,Date,Variable,USES
getLastWeekWorkingDateRange,Method,Start,Variable,USES
getLastWeekWorkingDateRange,Method,End,Variable,USES
Application,Class,Post:/Api,Endpoint,EXPOSES
main,Method,Post:/Api,Endpoint,MAPS_TO
main,Method,configure,Method,CALLS
main,Method,Run,Method,CALLS
main,Method,getDataFromTools,Method,CALLS
passwordEncoder,Method,Bcryptpasswordencoder,Variable,PRODUCES
Getprojectname(),Method,Jobkey,Variable,FLOWS_TO
Getcronexpression(),Method,Trigger,Variable,FLOWS_TO
Getschedulerenabled(),Method,Scheduler,Variable,FLOWS_TO
Jobkey,Variable,Trigger,Variable,FLOWS_TO
Jobkey,Variable,Job,Variable,TRANSFORMS_TO
Job,Variable,Joba,Variable,FLOWS_TO
Joba,Variable,Jobdatamap,Variable,TRANSFORMS_TO
Trigger,Variable,Scheduler,Variable,FLOWS_TO
Findall(),Method,Portfolioconfig,Table,READS_FROM
getDataFromTools,Method,Getcontext,Method,CALLS
getDataFromTools,Method,Getbean,Method,CALLS
getDataFromTools,Method,Findall(),Method,CALLS
getDataFromTools,Method,Checkexists,Method,CALLS
getDataFromTools,Method,Deletejob,Method,CALLS
getDataFromTools,Method,Newjob,Method,CALLS
getDataFromTools,Method,Newtrigger,Method,CALLS
getDataFromTools,Method,Schedulejob,Method,CALLS
getDataFromTools,Method,Start,Method,CALLS
getDataFromTools,Method,Stdschedulerfactory.Getscheduler,Externalservice,INVOKES
Req,Variable,saveALMConfig,Method,FLOWS_TO
Req,Variable,toDetailsAddSetting,Method,CALLS
toDetailsAddSetting,Method,Detailsaddsetting,Variable,TRANSFORMS_TO
Detailsaddsetting,Variable,saveALMConfig,Method,FLOWS_TO
saveALMConfig,Method,Almconfiguration,Variable,PRODUCES
Almconfiguration,Variable,Body,Variable,FLOWS_TO
saveALMConfig,Method,Almconfiguration,Table,WRITES_TO
retrieveALMConfig,Method,Almconfiguration,Table,READS_FROM
ALMConfigController,Class,Post:/Almconfig,Endpoint,EXPOSES
ALMConfigController,Class,Get:/Almconfigdetails,Endpoint,EXPOSES
ALMConfigController,Class,Get:/Almconfigdetailsconfig,Endpoint,EXPOSES
saveALMConfig,Method,Post:/Almconfig,Endpoint,MAPS_TO
retrieveList,Method,Get:/Almconfigdetails,Endpoint,MAPS_TO
retrieveALMConfig,Method,Get:/Almconfigdetailsconfig,Endpoint,MAPS_TO
Post:/Almconfig,Endpoint,Req,Variable,ACCEPTS
Post:/Almconfig,Endpoint,Almconfiguration,Variable,RETURNS
Get:/Almconfigdetails,Endpoint,Dataresponse,Variable,RETURNS
Get:/Almconfigdetailsconfig,Endpoint,Dataresponse,Variable,RETURNS
saveALMConfig,Method,toDetailsAddSetting,Method,CALLS
retrieveList,Method,retrieveALMConfig,Method,CALLS
saveALMConfig,Method,Almconfigservice.Savealmconfig,Externalservice,INVOKES
retrieveList,Method,Almconfigservice.Retrievealmconfig,Externalservice,INVOKES
retrieveALMConfig,Method,Almconfigservice.Retrievealmconfig,Externalservice,INVOKES
Projname,Variable,"Getstoryageingdata(projname,Almtype)",Method,FLOWS_TO
Almtype,Variable,"Getstoryageingdata(projname,Almtype)",Method,FLOWS_TO
Storyname,Variable,getStoryName,Method,FLOWS_TO
Storyname,Variable,setStoryName,Method,FLOWS_TO
Priorityname,Variable,getPriorityName,Method,FLOWS_TO
Priorityname,Variable,setPriorityName,Method,FLOWS_TO
Taskname,Variable,getTaskName,Method,FLOWS_TO
Taskname,Variable,setTaskName,Method,FLOWS_TO
Releasename,Variable,getReleaseName,Method,FLOWS_TO
Releasename,Variable,setReleaseName,Method,FLOWS_TO
Closestate,Variable,getCloseState,Method,FLOWS_TO
Closestate,Variable,setCloseState,Method,FLOWS_TO
Criticalpriority,Variable,getCriticalPriority,Method,FLOWS_TO
Criticalpriority,Variable,setCriticalPriority,Method,FLOWS_TO
Highpriority,Variable,getHighPriority,Method,FLOWS_TO
Highpriority,Variable,setHighPriority,Method,FLOWS_TO
Medpriority,Variable,getMedPriority,Method,FLOWS_TO
Medpriority,Variable,setMedPriority,Method,FLOWS_TO
Lowpriority,Variable,getLowPriority,Method,FLOWS_TO
Lowpriority,Variable,setLowPriority,Method,FLOWS_TO
Projectname,Variable,getProjectName,Method,FLOWS_TO
Projectname,Variable,setProjectName,Method,FLOWS_TO
Defectname,Variable,getDefectName,Method,FLOWS_TO
Defectname,Variable,setDefectName,Method,FLOWS_TO
Trendtype,Variable,getTrendType,Method,FLOWS_TO
Trendtype,Variable,setTrendType,Method,FLOWS_TO
Tracksset,Variable,getTracksSet,Method,FLOWS_TO
Tracksset,Variable,setTracksSet,Method,FLOWS_TO
Newstate,Variable,getNewState,Method,FLOWS_TO
Newstate,Variable,setNewState,Method,FLOWS_TO
Progressstate,Variable,getProgressState,Method,FLOWS_TO
Progressstate,Variable,setProgressState,Method,FLOWS_TO
Personhours,Variable,getPersonHours,Method,FLOWS_TO
Personhours,Variable,setPersonHours,Method,FLOWS_TO
Timezone,Variable,getTimeZone,Method,FLOWS_TO
Timezone,Variable,setTimeZone,Method,FLOWS_TO
Velocityfields,Variable,getVelocityFields,Method,FLOWS_TO
Velocityfields,Variable,setVelocityFields,Method,FLOWS_TO
Environment,Variable,getEnvironment,Method,FLOWS_TO
Environment,Variable,setEnvironment,Method,FLOWS_TO
Safeenabled,Variable,isSafeEnabled,Method,FLOWS_TO
Safeenabled,Variable,setSafeEnabled,Method,FLOWS_TO
Firstsprint,Variable,getFirstSprint,Method,FLOWS_TO
Firstsprint,Variable,setFirstSprint,Method,FLOWS_TO
Rejectionphase,Variable,getRejectionPhase,Method,FLOWS_TO
Rejectionphase,Variable,setRejectionPhase,Method,FLOWS_TO
Reopenphase,Variable,getReopenPhase,Method,FLOWS_TO
Reopenphase,Variable,setReopenPhase,Method,FLOWS_TO
Testingphase,Variable,getTestingPhase,Method,FLOWS_TO
Testingphase,Variable,setTestingPhase,Method,FLOWS_TO
Productionphase,Variable,getProductionPhase,Method,FLOWS_TO
Productionphase,Variable,setProductionPhase,Method,FLOWS_TO
Ccrlabel,Variable,getCcrLabel,Method,FLOWS_TO
Ccrlabel,Variable,setCcrLabel,Method,FLOWS_TO
Cycletimestates,Variable,getCycleTimeStates,Method,FLOWS_TO
Cycletimestates,Variable,setCycleTimeStates,Method,FLOWS_TO
Throughputstates,Variable,getThroughputStates,Method,FLOWS_TO
Throughputstates,Variable,setThroughputStates,Method,FLOWS_TO
toDetailsAddSetting,Method,Details,Variable,PRODUCES
toDetailsAddSetting,Method,Copyproperties,Method,CALLS
Req,Variable,Details,Variable,TRANSFORMS_TO
getAlmType,Method,Almtype,Variable,PRODUCES
Username,Variable,"Basic(Username, Password)",Variable,FLOWS_TO
Password,Variable,"Basic(Username, Password)",Variable,FLOWS_TO
Feature,Variable,Register(Feature),Variable,FLOWS_TO
Client,Variable,Webtarget,Variable,PRODUCES
Webtarget,Variable,Invocationbuilder,Variable,PRODUCES
Invocationbuilder,Variable,Get(),Variable,CALLS
Get(),Variable,Response,Variable,FLOWS_TO
Authentication,Class,Get:/Api/Tfsbuild,Endpoint,EXPOSES
Authenticate,Method,Get:/Api/Tfsbuild,Endpoint,MAPS_TO
Get:/Api/Tfsbuild,Endpoint,Username,Variable,ACCEPTS
Get:/Api/Tfsbuild,Endpoint,Password,Variable,ACCEPTS
Get:/Api/Tfsbuild,Endpoint,Response,Variable,RETURNS
Authenticate,Method,Newclient(),Method,CALLS
Authenticate,Method,"Basic(username,Password)",Method,CALLS
Authenticate,Method,Register(feature),Method,CALLS
Authenticate,Method,)),Method,CALLS
Authenticate,Method,ApplicationJson),Method,CALLS
Authenticate,Method,Get(),Method,CALLS
Authenticate,Method,Http://Localhost:8081/Api/Tfsbuild,Externalservice,INVOKES
Lastupdated,Variable,getLastUpdated,Method,FLOWS_TO
getLastUpdated,Method,Lastupdated,Variable,PRODUCES
DataResponse,Class,N/A,Endpoint,EXPOSES
Almconfiguration,Variable,saveALMConfig,Method,FLOWS_TO
String Projectname,Variable,retrieveALMConfig,Method,FLOWS_TO
retrieveALMConfig,Method,Dataresponse<Almconfiguration>,Variable,PRODUCES
Almconfigservice,Class,Post:/Api/Alm/Config,Endpoint,EXPOSES
Almconfigservice,Class,Get:/Api/Alm/Config/{Projectname},Endpoint,EXPOSES
saveALMConfig,Method,Post:/Api/Alm/Config,Endpoint,MAPS_TO
retrieveALMConfig,Method,Get:/Api/Alm/Config/{Projectname},Endpoint,MAPS_TO
Post:/Api/Alm/Config,Endpoint,Almconfiguration,Variable,ACCEPTS
Post:/Api/Alm/Config,Endpoint,Almconfiguration,Variable,RETURNS
Get:/Api/Alm/Config/{Projectname},Endpoint,String Projectname,Variable,ACCEPTS
Get:/Api/Alm/Config/{Projectname},Endpoint,Dataresponse<Almconfiguration>,Variable,RETURNS
Req,Variable,Getprojectname()),Variable,FLOWS_TO
Req,Variable,Save(Req),Variable,FLOWS_TO
Save(Req),Variable,Almconfiguration,Variable,TRANSFORMS_TO
Projectname,Variable,Findbyprojectname(Projectname),Variable,FLOWS_TO
Lastupdate,Variable,"Dataresponse<Almconfiguration>(Result, Lastupdate)",Variable,FLOWS_TO
saveALMConfig,Method,Almconfiguration,Table,READS_FROM
ALMConfigServiceImplementation,Class,Post:/Api/Almconfig,Endpoint,EXPOSES
ALMConfigServiceImplementation,Class,Get:/Api/Almconfig/{Projectname},Endpoint,EXPOSES
saveALMConfig,Method,Post:/Api/Almconfig,Endpoint,MAPS_TO
Post:/Api/Almconfig,Endpoint,Req,Variable,ACCEPTS
Post:/Api/Almconfig,Endpoint,Almconfiguration,Variable,RETURNS
retrieveALMConfig,Method,Get:/Api/Almconfig/{Projectname},Endpoint,MAPS_TO
Get:/Api/Almconfig/{Projectname},Endpoint,Projectname,Variable,ACCEPTS
Get:/Api/Almconfig/{Projectname},Endpoint,Dataresponse<Almconfiguration>,Variable,RETURNS
saveALMConfig,Method,Findbyprojectname,Method,CALLS
saveALMConfig,Method,Deletebyprojectname,Method,CALLS
saveALMConfig,Method,Save,Method,CALLS
retrieveALMConfig,Method,Findbyprojectname,Method,CALLS
getMetricDetails,Method,Monogoutmetrics,Variable,PRODUCES
getAllMetrics,Method,List<Monogoutmetrics>,Variable,PRODUCES
getChangesItems,Method,List<Changehistorymodel>,Variable,PRODUCES
getTransitionsData,Method,List<Transitionmodel>,Variable,PRODUCES
getIterationData,Method,Iterationoutmodel,Variable,PRODUCES
getEffortData,Method,List<Efforthistorymodel>,Variable,PRODUCES
getProjectDetails,Method,List<Iterationoutmodel>,Variable,PRODUCES
getDefectCounts,Method,Projectmodel,Variable,PRODUCES
getRelease,Method,Releasedetails,Variable,PRODUCES
getUnReleaseData,Method,List<Metricsmodel>,Variable,PRODUCES
getCurrentProjectDetails,Method,"Map<Integer, List<Iterationoutmodel>>",Variable,PRODUCES
getAllTransitions,Method,List<Transitionmodel>,Variable,PRODUCES
getComponentVelocity,Method,List<Componentvelocitylist>,Variable,PRODUCES
getComponentsSprint,Method,List<Componentsprintwisestories>,Variable,PRODUCES
Almservice,Class,Interface,Endpoint,EXPOSES
Agg,Variable,Mongoaggregate,Variable,FLOWS_TO
Mongotemplate(),Method,Mongotemplate,Variable,PRODUCES
Mixedcasemetricsconst),Method,Response,Variable,PRODUCES
Covertedtempdatemetrics(),Variable,Convertedtempmetrics,Variable,TRANSFORMS_TO
getMetricDetails,Method,Metrics,Table,READS_FROM
Req,Variable,Configurationsetting,Variable,FLOWS_TO
Configurationsetting,Variable,addConfig,Method,FLOWS_TO
Configurationsetting,Variable,deleteConfig,Method,FLOWS_TO
Projectname,Variable,deleteAllCollections,Method,FLOWS_TO
Projectname,Variable,deleteProject,Method,FLOWS_TO
Pname,Variable,getConfigProject,Method,FLOWS_TO
Configurationsettingservice,Class,Get:/Api/Config,Endpoint,EXPOSES
Configurationsettingservice,Class,Post:/Api/Config,Endpoint,EXPOSES
Configurationsettingservice,Class,Delete:/Api/Config,Endpoint,EXPOSES
Configurationsettingservice,Class,Delete:/Api/Collections,Endpoint,EXPOSES
Configurationsettingservice,Class,Delete:/Api/Project,Endpoint,EXPOSES
Configurationsettingservice,Class,Get:/Api/Project,Endpoint,EXPOSES
getConfig,Method,Get:/Api/Config,Endpoint,MAPS_TO
addConfig,Method,Post:/Api/Config,Endpoint,MAPS_TO
deleteConfig,Method,Delete:/Api/Config,Endpoint,MAPS_TO
deleteAllCollections,Method,Delete:/Api/Collections,Endpoint,MAPS_TO
deleteProject,Method,Delete:/Api/Project,Endpoint,MAPS_TO
getConfigProject,Method,Get:/Api/Project,Endpoint,MAPS_TO
Get:/Api/Config,Endpoint,Dataresponse,Variable,RETURNS
Post:/Api/Config,Endpoint,Req,Variable,ACCEPTS
Post:/Api/Config,Endpoint,Configurationsetting,Variable,RETURNS
Delete:/Api/Config,Endpoint,Configurationsetting,Variable,ACCEPTS
Delete:/Api/Collections,Endpoint,Projectname,Variable,ACCEPTS
Delete:/Api/Project,Endpoint,Projectname,Variable,ACCEPTS
Get:/Api/Project,Endpoint,Pname,Variable,ACCEPTS
Get:/Api/Project,Endpoint,Configurationsetting,Variable,RETURNS
Lastupdated,Variable,Dataresponse,Variable,FLOWS_TO
Req,Variable,Save,Method,FLOWS_TO
Failurepattern,Variable,Failurepatternobj,Variable,FLOWS_TO
Failurepatternobj,Variable,Save,Method,FLOWS_TO
Projectname,Variable,delAllIssues,Method,FLOWS_TO
Projectname,Variable,Findbyname,Method,FLOWS_TO
Projectname,Variable,Findbyprojectname,Method,FLOWS_TO
Projectname,Variable,Findbypname,Method,FLOWS_TO
Config,Variable,Getmetrics,Method,FLOWS_TO
Format,Variable,Pattern,Variable,FLOWS_TO
Dateinput,Variable,Format(),Variable,FLOWS_TO
Pattern,Variable,Simpledateformat,Variable,TRANSFORMS_TO
Format(),Variable,Return,Variable,PRODUCES
Date,Variable,Settime(),Variable,FLOWS_TO
Start,Variable,Getdateinformat(),Method,FLOWS_TO
End,Variable,Getdateinformat(),Method,FLOWS_TO
Getdateinformat(),Method,"Map[""Start""]",Variable,PRODUCES
Getdateinformat(),Method,"Map[""End""]",Variable,PRODUCES
Getlastweekworkingdaterange(),Method,Getdateinformat(),Method,CALLS
DateUtil,Class,.,Endpoint,EXPOSES
