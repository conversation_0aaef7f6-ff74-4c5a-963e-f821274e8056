package com.bolt.dashboard.util;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.TreeMap;
import java.util.stream.Collector;
import java.util.stream.Collectors;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ALMConfiguration;
import com.bolt.dashboard.core.model.ComponentVelocityList;
import com.bolt.dashboard.core.model.DefectInsightData;
import com.bolt.dashboard.core.model.IssueList;
import com.bolt.dashboard.core.model.IterationModel;
import com.bolt.dashboard.core.model.IterationOutModel;
import com.bolt.dashboard.core.model.MetricAgeData;
import com.bolt.dashboard.core.model.MetricsModel;
import com.bolt.dashboard.core.model.MonogOutMetrics;
import com.bolt.dashboard.core.model.ScoreCardSprintData;
import com.bolt.dashboard.core.model.TransitionModel;
import com.bolt.dashboard.core.repository.ALMConfigRepo;
import com.bolt.dashboard.core.repository.MetricRepo;
import com.bolt.dashboard.core.repository.ProjectIterationRepo;

public class DefectCalculations {
	AnnotationConfigApplicationContext ctx = DataConfig.getContext();
	CommonFunctions commonFunc = new CommonFunctions();
	MetricRepo metricRepo = null;
	private static final Logger LOGGER = LogManager.getLogger(DefectCalculations.class);
	ALMConfiguration almConfiguration = null;
	List<MetricsModel> allBugs = null;
	List<IterationOutModel> allIterations = null;
	List<IterationOutModel> allIterationsAndBacklog = null;
	List<String> closeStates = null;
	boolean meanTimeCalcFlag = true;
	ALMConfigRepo almConfigRepo = null;
	double totalRem = 0, totalSpent = 0;
	List<MetricsModel> totMetrics;
	List<MetricsModel> filterMetricsData, filterStroyData;
	private long currentDate = new DateTime().getMillis();
	private Long firstSprintStartDate;
	MongoTemplate mongo = null;

	/* added while sprint defect trend calculations like in velocity */
	private ArrayList<String> wIdArr;
	private List<IssueList> issueList = null;
	private ArrayList<IterationOutModel> workingBacklog;
	private ArrayList<IterationOutModel> workingSprints;
	private List<String> workItemArr2 = null;
	private List<IssueList> refinedIssuList = null;
	HashMap<String, List<IssueList>> sprintAndDefectsAdded;
	HashMap<String, List<IssueList>> sprintAndDefectsClosed; 
	
	public DefectCalculations() {
		if (mongo == null) {
			try {
				mongo = DataConfig.getInstance().mongoTemplate();
			} catch (Exception e) {
				LOGGER.error("Mongo error");
			}
		}
		if (almConfigRepo == null)
			almConfigRepo = ctx.getBean(ALMConfigRepo.class);

	}

	private void getInititialDetails(String projectName, String almType) {
		metricRepo = ctx.getBean(MetricRepo.class);

		almConfiguration = almConfigRepo.findByProjectName(projectName).get(0);
		closeStates = Arrays.asList(almConfiguration.getCloseState());
//	     allBugs = metricRepo.findByPNameAndType(projectName, "Bug");

		try {
			mongo = DataConfig.getInstance().mongoTemplate();
		} catch (Exception e) {
			LOGGER.error("Mongo error");
		}
		Query q = new Query();
		q.addCriteria(Criteria.where("pName").is(projectName).and("sName").nin("BACKLOG", "Backlog", "backlog",
				"FUTURE", "Future", "future", "BackLog"));
		allIterations = mongo.find(q, IterationOutModel.class);
		allIterations = allIterations.stream().sorted(Comparator.comparing(IterationOutModel::getStDate))
						.collect(Collectors.toList());
		q = new Query();
		q.addCriteria(Criteria.where("pName").is(projectName).and("sName").nin("FUTURE", "Future", "future"));
		allIterationsAndBacklog = mongo.find(q, IterationOutModel.class, "Author");
		allIterationsAndBacklog = allIterationsAndBacklog.stream()
				.sorted(Comparator.comparing(IterationOutModel::getStDate,Comparator.nullsLast(Long::compareTo)))
				.collect(Collectors.toList());
	}

	public Map<String, List<DefectInsightData>> calculateDefectInsightDataComponent(String projName, String almType,
			boolean compFlag) {
		Map<String, List<DefectInsightData>> defectDataComponent = new HashMap<String, List<DefectInsightData>>();

		getInititialDetails(projName, almType);
		defectDataComponent.put("ALL", calculateDefectInsightData(projName, almType, allIterationsAndBacklog));

		if (compFlag) {

			ArrayList<String> componentList = CommonFunctions.getComponentList(allIterationsAndBacklog);
			Collections.sort(componentList);

			for (String component : componentList) {
				List<IterationOutModel> tempAuthorList = new ArrayList<IterationOutModel>();
				for (IterationOutModel auth : allIterationsAndBacklog) {

					IterationOutModel author = new IterationOutModel();
					BeanUtils.copyProperties(auth, author);

					List<MonogOutMetrics> metrics = new ArrayList<MonogOutMetrics>();
					if (auth.getMetrics() != null)
						for (MonogOutMetrics m : auth.getMetrics()) {
							if (m.getComponents() != null && m.getComponents().get(0) != null
									&& m.getComponents().get(0).equals(component)) {

								metrics.add(m);

							}
						}
					if (metrics.size() > 0) {
						author.setMetrics(metrics);
						tempAuthorList.add(author);
					}

				}
				Collections.sort(tempAuthorList, new SprintComparatort());
				defectDataComponent.put(component, calculateDefectInsightData(projName, almType, tempAuthorList));
			}
		}

		return defectDataComponent;
	}

	public List<DefectInsightData> calculateDefectInsightData(String projName, String almType,
			List<IterationOutModel> iterations) {
		

		closeStates = Arrays.asList(almConfiguration.getCloseState());
		long currentTime = new DateTime().getMillis();
		// List<Map<String, List<DefectInsightData>>> defectDataList = new
		// ArrayList<Map<String,List<DefectInsightData>>>();

		List<DefectInsightData> defectInsightList = new ArrayList<DefectInsightData>();
		Map<String, DefectInsightData> defectInsightMap = new HashMap<String, DefectInsightData>();
		List<Map<String, String>> meanTimeFix = new ArrayList<Map<String, String>>();

		if (almConfiguration.getFixedState() == null || almConfiguration.getFixedState().equals("")) {
			meanTimeCalcFlag = false;
		}
		for (IterationOutModel iter : iterations) {
			if (iter.getMetrics() != null) {
				List<MonogOutMetrics> bugs = iter.getMetrics().stream()
						.filter(m -> m.getType().equals(almConfiguration.getDefectName())).collect(Collectors.toList());
				Map<String, List<MonogOutMetrics>> groupedByPriority = bugs.stream()
						.collect(Collectors.groupingBy(MonogOutMetrics::getPriority));
				DefectInsightData temp = null;
				List<Map<String, String>> averageDefectWaitTimetemp = null;
				Map<String, String> meanTimePriorTemp = null;

				for (Entry<String, List<MonogOutMetrics>> issuePriority : groupedByPriority.entrySet()) {

					Optional<Map<String, String>> meanTimeOptional = Optional.empty();
					meanTimeOptional = meanTimeFix.stream()
							.filter(m -> (m.get("Priority")).equals(issuePriority.getKey())).findFirst();
					if (meanTimeOptional.isPresent()) {
						meanTimePriorTemp = meanTimeOptional.get();
					} else {
						meanTimePriorTemp = new HashMap<String, String>();
						meanTimePriorTemp.put("Priority", issuePriority.getKey());
						meanTimeFix.add(meanTimePriorTemp);
					}
					long age = 0;
					if (defectInsightMap.containsKey(issuePriority.getKey())) {
						temp = defectInsightMap.get(issuePriority.getKey());
						averageDefectWaitTimetemp = temp.getAverageDefectWaitTime();
					} else {
						temp = new DefectInsightData();
						temp.setMetricData(new ArrayList<MetricAgeData>());
						defectInsightList.add(temp);
						temp.setPriority(issuePriority.getKey());
						averageDefectWaitTimetemp = new ArrayList<Map<String, String>>();
						temp.setAverageDefectWaitTime(averageDefectWaitTimetemp);
						defectInsightMap.put(issuePriority.getKey(), temp);
						temp.setMeanTimeToFix(0);
						temp.setMeanTimeToResolveDefects(0);
						temp.setMeanTimeFixCount(0);
						temp.setMeanTimeResolveCount(0);

					}

					for (MonogOutMetrics metric : issuePriority.getValue()) {
						
						MetricAgeData defectMetric = new MetricAgeData();
						temp.getMetricData().add(defectMetric);
						String status = "Open";
						if (closeStates.indexOf(metric.getState()) > -1) {
							long time = metric.getResDate();
							if (time == 0) {
								time = metric.getUpdatedDate();
							}
							age = time - metric.getCreateDate();
							status = "Closed";
						} else {

							age = currentTime - metric.getCreateDate();
						}
						List<TransitionModel> transitions = metric.getTransitions();
                        long timeForDuplicate=0;
						boolean duplicateState=false;
                        boolean firstTimeFlag = true;
						boolean addDefectTimeFlag = false;
						Map<String, String> averageDefectMap = null;
						List<Map<String, String>> defectWorkFlowList = new ArrayList<Map<String, String>>();
						defectMetric.setDefectWorkFlow(defectWorkFlowList);
						if (transitions != null) {
							List<TransitionModel> sortedTrans = transitions.stream()
							        .sorted(Comparator.comparingLong(TransitionModel::getMdfDate))
							        .collect(Collectors.toList());
							// MeanTime Fix
							meanTimeCalculation(transitions, temp, almConfiguration);

							for (int index = 0; index <= sortedTrans.size() - 1; index++) {
								firstTimeFlag = true;
								addDefectTimeFlag = false;
								TransitionModel trans = sortedTrans.get(index);
								if (closeStates.indexOf(trans.getCrState()) <= -1) {
									addDefectTimeFlag = true;
								}
								Optional<Map<String, String>> averageDefectMapOptional = Optional.empty();
								if (index != 0) {
									averageDefectMapOptional = averageDefectWaitTimetemp.stream()
											.filter(m -> (m.get("State")).equals(trans.getCrState())).findFirst();
									if (averageDefectMapOptional.isPresent()) {
										averageDefectMap = averageDefectMapOptional.get();
										firstTimeFlag = false;
									} else {
										averageDefectMap = new HashMap<String, String>();

									}
									if (closeStates.indexOf(trans.getCrState()) <= -1) {
										addDefectTimeFlag = true;
									}
									if (addDefectTimeFlag) {
										if (firstTimeFlag) {
											averageDefectMap.put("State", trans.getCrState());
											averageDefectMap.put("Count", "1");

											averageDefectMap.put("Wait Time", String.valueOf(trans.getWaitTime()));
											if (issuePriority.getKey().equals("Blocker")
													&& trans.getCrState().equals("In Progress")) {
												LOGGER.info(
														"Wid " + metric.getwId() + " Wait Time " + trans.getWaitTime());
											}
											averageDefectWaitTimetemp.add(averageDefectMap);
										} else {
											long value = trans.getWaitTime()
													+ Long.parseLong(averageDefectMap.get("Wait Time"));
											if (issuePriority.getKey().equals("Blocker")
													&& trans.getCrState().equals("In Progress")) {
												LOGGER.info(
														"Wid " + metric.getwId() + " Wait Time " + trans.getWaitTime());
											}
											averageDefectMap.put("Wait Time", String.valueOf(value));
											value = 1 + Long.parseLong(averageDefectMap.get("Count"));
											averageDefectMap.put("Count", String.valueOf(value));
										}
									}
								}
								if (index == 0) {
									// Calculation for Average Defect Time
									// For Index 0 have to use differnt state to filter for Average DefectTime

									averageDefectMapOptional = averageDefectWaitTimetemp.stream()
											.filter(m -> (m.get("State")).equals(trans.getFrmState())).findFirst();
									if (averageDefectMapOptional.isPresent()) {
										averageDefectMap = averageDefectMapOptional.get();
										firstTimeFlag = false;
									} else {
										averageDefectMap = new HashMap<String, String>();
									}

									if (closeStates.indexOf(trans.getFrmState()) <= -1) {
										addDefectTimeFlag = true;
									}
									if (addDefectTimeFlag) {
										if (firstTimeFlag) {
											averageDefectMap.put("State", trans.getFrmState());
											averageDefectMap.put("Wait Time",
													String.valueOf(trans.getPreStateWaitTime()));
											if (issuePriority.getKey().equals("Blocker")
													&& trans.getFrmState().equals("In Progress")) {
												LOGGER.info("Wid " + metric.getwId() + " Wait Time "
														+ trans.getPreStateWaitTime());
											}
											averageDefectWaitTimetemp.add(averageDefectMap);
											averageDefectMap.put("Count", "1");
										} else {
											long value = trans.getPreStateWaitTime()
													+ Long.parseLong(averageDefectMap.get("Wait Time"));
											averageDefectMap.put("Wait Time", String.valueOf(value));
											if (issuePriority.getKey().equals("Blocker")
													&& trans.getFrmState().equals("In Progress")) {
												LOGGER.info("Wid " + metric.getwId() + " Wait Time "
														+ trans.getPreStateWaitTime());
											}
											value = 1 + Long.parseLong(averageDefectMap.get("Count"));
											averageDefectMap.put("Count", String.valueOf(value));
										}

									}
                                   
									 if(!trans.getFrmState().equals(trans.getCrState())) {
											pushStateFlowObject(trans.getFrmState(), commonFunc
													.convertMilisToDisplayValuesDefect(trans.getPreStateWaitTime(), 24),
													defectMetric, status);
											
		                                    }
									if (index == transitions.size()) {
										pushStateFlowObject(trans.getCrState(), "Present State", defectMetric, status);
									}
									if(!trans.getFrmState().equals(trans.getCrState())) {
										if(index==transitions.size()-1 || !(trans.getCrState().equals(sortedTrans.get(index+1).getCrState()))) {
									pushStateFlowObject(trans.getCrState(),
											commonFunc.convertMilisToDisplayValuesDefect(trans.getWaitTime(), 24),
											defectMetric, status);
										}else {
											duplicateState=true;
											timeForDuplicate=trans.getWaitTime();
										}
									}else {
                                    	long milis=trans.getPreStateWaitTime()+trans.getWaitTime();
                                    	pushStateFlowObject(trans.getFrmState(), commonFunc
    											.convertMilisToDisplayValuesDefect(milis, 24),
    											defectMetric, status);
                                    }

								} else if (index == transitions.size() - 1) {
									pushStateFlowObject(trans.getCrState(), "Present State", defectMetric, status);
								} else {
									long tempTime=trans.getWaitTime();
                                    if(duplicateState) {
                                    	
                                    	tempTime=tempTime+timeForDuplicate;
                                    	if(trans.getCrState().equals(sortedTrans.get(index+1).getCrState())) {
                                    		timeForDuplicate=tempTime;
                                    	}else {
                                    		duplicateState=false;
                                    		timeForDuplicate=0;
                                    	pushStateFlowObject(trans.getCrState(),
    											commonFunc.convertMilisToDisplayValuesDefect(tempTime, 24),
    											defectMetric, status);
                                    	}
                                    }else {
                                    	pushStateFlowObject(trans.getCrState(),
    											commonFunc.convertMilisToDisplayValuesDefect(tempTime, 24),
    											defectMetric, status);
                                    }
									
								}

							}

						} else {

							// Average Defect Wait Time
							if (closeStates.indexOf(metric.getState()) <= -1) {
								Optional<Map<String, String>> averageDefectMapOptional = averageDefectWaitTimetemp
										.stream().filter(m -> (m.get("State")).equals(metric.getState())).findFirst();
								if (averageDefectMapOptional.isPresent()) {
									averageDefectMap = averageDefectMapOptional.get();
									firstTimeFlag = false;
								} else {
									averageDefectMap = new HashMap<String, String>();

								}
								if (firstTimeFlag) {
									averageDefectMap.put("State", metric.getState());
									averageDefectMap.put("Wait Time", String.valueOf(metric.getWaitTime()));
									averageDefectMap.put("Count", "1");
									if (issuePriority.getKey().equals("Blocker")
											&& metric.getState().equals("In Progress")) {
										LOGGER.info("Wid " + metric.getwId() + " Wait Time " + metric.getWaitTime());
									}
									averageDefectWaitTimetemp.add(averageDefectMap);
								} else {
									long value = metric.getWaitTime()
											+ Long.parseLong(averageDefectMap.get("Wait Time"));
									if (issuePriority.getKey().equals("Blocker")
											&& metric.getState().equals("In Progress")) {
										LOGGER.info("Wid " + metric.getwId() + " Wait Time " + metric.getWaitTime());
									}
									averageDefectMap.put("Wait Time", String.valueOf(value));
									value = 1 + Long.parseLong(averageDefectMap.get("Count"));
									averageDefectMap.put("Count", String.valueOf(value));
								}

							}

							pushStateFlowObject(metric.getState(),
									commonFunc.convertMilisToDisplayValuesDefect(age, 24), defectMetric, status);
						}
						defectMetric.setAgeDisplay(commonFunc.convertMilisToDisplayValuesDefect(age, 24));
						defectMetric.setAgeInDays((int) commonFunc.toDaysString(age, 24));
						defectMetric.setAgeInMilis(age);
						defectMetric.setId(metric.getwId());
						defectMetric.setStatus(status);
						defectMetric.setCreateDate(metric.getCreateDate());
						defectMetric.setPriority(issuePriority.getKey());
					}

				}

			}
		}

//		for (Map<String, String> meanTime : meanTimeFix) {
//			defectDataComponent.get("ALL");
//		}
		return defectInsightList;
	}

	private void meanTimeCalculation(List<TransitionModel> transitions, DefectInsightData temp,
			ALMConfiguration almConfiguration) {
		
		List<String> closeState = Arrays.asList(almConfiguration.getCloseState());

		if (transitions.size() > 0) {
			long createTime = transitions.get(0).getCreateTime();
			long fixTime = 0;
			boolean closeFlag = false;
			long closeTime = 0;
			for (TransitionModel trans : transitions) {
				if (meanTimeCalcFlag && trans.getCrState().equals(almConfiguration.getFixedState())) {
					fixTime = trans.getMdfDate();
				}

				if (closeState.indexOf(trans.getCrState()) > -1 && !closeFlag) {
					closeFlag = true;
					closeTime = trans.getMdfDate();
				}
			}
			long val = 0;
			if (meanTimeCalcFlag) {

				if (fixTime != 0) {
					val = fixTime - createTime;
					temp.setMeanTimeToFix(temp.getMeanTimeToFix() + val);
					temp.setMeanTimeFixCount(temp.getMeanTimeFixCount() + 1);
				}

			}
			if (closeTime != 0) {
				val = closeTime - createTime;
				temp.setMeanTimeResolveCount(temp.getMeanTimeResolveCount() + 1);
				temp.setMeanTimeToResolveDefects(temp.getMeanTimeToResolveDefects() + val);
			}

		}

	}

	private void pushStateFlowObject(String state, String value, MetricAgeData defectMetric, String status) {
		
		Map<String, String> stateFlowMap = new HashMap<String, String>();
		stateFlowMap.put("name", state);
		stateFlowMap.put("value", value);
		stateFlowMap.put("Status", String.valueOf(false));
		if (status.equals("Closed")) {
			stateFlowMap.put("Status", String.valueOf(true));
		}
		defectMetric.getDefectWorkFlow().add(stateFlowMap);
	}

	public Map componentSprintDefectTrend(String projectName, String almType, boolean componentBased) {
		Map responseMap = new HashMap();
		getInititialDetails(projectName, almType);
		List<String> closedStates = Arrays.asList(almConfiguration.getCloseState());
		responseMap.put("ALL", sprintDefectTrend(closedStates, allIterationsAndBacklog));

		if (componentBased) {

			ArrayList<String> componentList = CommonFunctions.getComponentList(allIterationsAndBacklog);
			Collections.sort(componentList);

			for (String component : componentList) {
				List<IterationOutModel> tempAuthorList = new ArrayList<IterationOutModel>();
				for (IterationOutModel auth : allIterationsAndBacklog) {

					IterationOutModel author = new IterationOutModel();
					BeanUtils.copyProperties(auth, author);

					List<MonogOutMetrics> metrics = new ArrayList<MonogOutMetrics>();
					if (auth.getMetrics() != null)
						for (MonogOutMetrics m : auth.getMetrics()) {
							if (m.getComponents() != null && m.getComponents().get(0) != null
									&& m.getComponents().get(0).equals(component)) {

								metrics.add(m);

							}
						}
					if (metrics.size() > 0) {
						author.setMetrics(metrics);
						tempAuthorList.add(author);
					}

				}
				Collections.sort(tempAuthorList, new SprintComparatort());
				responseMap.put(component, sprintDefectTrend(closedStates, tempAuthorList));
			}
		}

		return responseMap;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public Map sprintDefectTrend(List<String> closedStates, List<IterationOutModel> iterationData) {

		List<Integer> assignedBugs = new ArrayList<Integer>();
		List<Integer> closedBugs = new ArrayList<Integer>();
		List<Integer> rejectedBugs = new ArrayList<Integer>();
		List<Integer> reopenedBugs = new ArrayList<Integer>();
		List<String> category = new ArrayList<String>();
		calcVelocity(iterationData);
		List<IterationOutModel> filteredSprints = new ArrayList<IterationOutModel>();
		for (IterationOutModel auth : iterationData) {
			if (null != auth.getState() && auth.getStDate() != 0 && !auth.getState().toLowerCase().contains("future")
					&& !auth.getState().toLowerCase().contains("backlog") && auth.getsId() != 0) {
				filteredSprints.add(auth);

			}
		}
		

		Map sprintTrend = new HashMap();
		List<Map> defectClassification = new ArrayList<Map>();
		for (IterationOutModel iter : filteredSprints) {

			if (iter.getMetrics() != null && iter.getMetrics().size() > 0) {

				List<MonogOutMetrics> tempAssigned = iter.getMetrics().stream().filter((ele) -> {
					return ele.getType().equalsIgnoreCase(almConfiguration.getDefectName());
				}).collect(Collectors.toList());



				List<MonogOutMetrics> tempClosed = tempAssigned.stream().filter((ele) -> {
					if (closedStates.indexOf(ele.getState()) > -1) {
						return true;
					} else {
						return false;
					}
				}).collect(Collectors.toList());

				List<MonogOutMetrics> tempRejected = new ArrayList<MonogOutMetrics>();
				if (almConfiguration.getRejectionPhase() != null && almConfiguration.getRejectionPhase().length > 0) {
					List rejectionStates = Arrays.asList(almConfiguration.getRejectionPhase());
					tempRejected = tempAssigned.stream().filter((ele) -> {
						if (rejectionStates.indexOf(ele.getState()) > -1) {
							return true;
						} else {
							return false;
						}
					}).collect(Collectors.toList());
				}

				List<MonogOutMetrics> tempReopen = new ArrayList<MonogOutMetrics>();
				if (almConfiguration.getRejectionPhase() != null && almConfiguration.getRejectionPhase().length > 0) {
					List reopenStates = Arrays.asList(almConfiguration.getReopenPhase());
					tempReopen = tempAssigned.stream().filter((ele) -> {
						if (reopenStates.indexOf(ele.getState()) > -1) {
							return true;
						} else {
							return false;
						}
					}).collect(Collectors.toList());
				}
				
				
				if(sprintAndDefectsAdded.get(iter.getsName()) !=null) {
					assignedBugs.add(sprintAndDefectsAdded.get(iter.getsName()).size());
					closedBugs.add(sprintAndDefectsClosed.get(iter.getsName()).size());
					
					for (IssueList ele : sprintAndDefectsAdded.get(iter.getsName())) {
						Map issue = new HashMap();
						issue.put("wId", ele.getwId());
						issue.put("sName", ele.getsName());
						issue.put("state", ele.getState());
						issue.put("assignee", ele.getAssignee());
						issue.put("severity", ele.getSeverity());
						issue.put("priority", ele.getPriority());
						if (closedStates.indexOf(ele.getState()) > -1) {
							issue.put("isClosed", true);
						} else {
							issue.put("isClosed", false);
						}
						defectClassification.add(issue);
					}
				}else {
					assignedBugs.add(0);
					closedBugs.add(0);
				}
				

				rejectedBugs.add(tempRejected.size());
				reopenedBugs.add(tempReopen.size());
				category.add(iter.getsName());
			} else {
				assignedBugs.add(0);
				closedBugs.add(0);
				rejectedBugs.add(0);
				reopenedBugs.add(0);
				category.add(iter.getsName());
			}
		}
		sprintTrend.put("assignedBugs", assignedBugs);
		sprintTrend.put("closedBugs", closedBugs);
		if (almConfiguration.getRejectionPhase() != null)
			sprintTrend.put("rejectedBugs", rejectedBugs);
		if (almConfiguration.getReopenPhase() != null)
			sprintTrend.put("reopenedBugs", reopenedBugs);
		sprintTrend.put("category", category);

		Map item = new HashMap();
		item.put("sprintTrend", sprintTrend);
		item.put("defectClassification", defectClassification);

		return item;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public Map defectClassification(String projectName, String almType) {
		getInititialDetails(projectName, almType);
		List<String> closedStates = Arrays.asList(almConfiguration.getCloseState());

		Map responseMap = new HashMap();
		List<Map> defectClassification = new ArrayList<Map>();
		for (IterationOutModel iter : allIterationsAndBacklog) {

			if (iter.getMetrics() != null && iter.getMetrics().size() > 0) {

				List<MonogOutMetrics> tempAssigned = iter.getMetrics().stream().filter((ele) -> {
					return ele.getType().equalsIgnoreCase(almConfiguration.getDefectName());
				}).collect(Collectors.toList());

				for (MonogOutMetrics ele : tempAssigned) {
					Map issue = new HashMap();
					issue.put("wId", ele.getwId());
					issue.put("sName", ele.getsName());
					issue.put("state", ele.getState());
					issue.put("assignee", ele.getAssgnTo());
					issue.put("severity", ele.getSeverity());
					issue.put("priority", ele.getPriority());
					if (closedStates.indexOf(ele.getState()) > -1) {
						issue.put("isClosed", true);
					} else {
						issue.put("isClosed", false);
					}
					defectClassification.add(issue);
				}
			}
		}

		Map item = new HashMap();
		item.put("defectClassification", defectClassification);
		responseMap.put("ALL", item);
		return responseMap;
	}

	public Map<String, List<DefectParetoModel>> getParetoDataComp(String projName, String almType,
			boolean componentBased) {
		Map<String, List<DefectParetoModel>> componentWiseData = new HashMap<String, List<DefectParetoModel>>();
		almConfiguration = almConfigRepo.findByProjectName(projName).get(0);
		ProjectIterationRepo authorRepo = ctx.getBean(ProjectIterationRepo.class);
		List<IterationOutModel> authorAllData = authorRepo.findByPNameAndPAlmType(projName, almType);
		componentWiseData.put("ALL", getParetoData(authorAllData));

		if (componentBased) {

			ArrayList<String> componentList = CommonFunctions.getComponentList(authorAllData);
			Collections.sort(componentList);

			for (String component : componentList) {
				List<IterationOutModel> tempAuthorList = new ArrayList<IterationOutModel>();
				for (IterationOutModel auth : authorAllData) {

					IterationOutModel author = new IterationOutModel();
					BeanUtils.copyProperties(auth, author);

					List<MonogOutMetrics> metrics = new ArrayList<MonogOutMetrics>();
					if (auth.getMetrics() != null)
						for (MonogOutMetrics m : auth.getMetrics()) {
							if (m.getComponents() != null && m.getComponents().get(0) != null
									&& m.getComponents().get(0).equals(component)) {

								metrics.add(m);

							}
						}
					if (metrics.size() > 0) {
						author.setMetrics(metrics);
						tempAuthorList.add(author);
					}

				}
				Collections.sort(tempAuthorList, new SprintComparatort());
				componentWiseData.put(component, getParetoData(tempAuthorList));
			}
		}

		return componentWiseData;
	}

	int sum = 0;

	public List<DefectParetoModel> getParetoData(List<IterationOutModel> authorAllData) {
		String[] paretoType = { "environment", "priority", "state", "assgnTo" };
		sum = 0;
		List<DefectParetoModel> paretoChartType = new ArrayList<DefectParetoModel>();
		for (String property : paretoType) {
//			property = "environment";
			if (property.equals("severity")) {
				property = almConfiguration.getPriorityName();
			}
			List<MonogOutMetrics> metricData = getMetricsData(authorAllData, "All", almConfiguration);
			Map<String, List<MonogOutMetrics>> groupedDataByModule = groupDataForPareto(metricData, property);
			String text = property;
			List<Integer[]> moduleDefect = new ArrayList<Integer[]>();
			List<String[]> moduleCategory = new ArrayList<String[]>();
			List<Integer> moduleCumulative = new ArrayList<Integer>();

			Map<String, List<MonogOutMetrics>> sortable = new HashMap<String, List<MonogOutMetrics>>();
			if (groupedDataByModule != null) {
				ValueComparator cmp = new ValueComparator(groupedDataByModule);
				Map<String, List<MonogOutMetrics>> sortedAscendingMap = groupedDataByModule.entrySet().stream()
						.sorted(Map.Entry.comparingByValue(cmp)).collect(Collectors.toMap(Map.Entry::getKey,
								Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));

				// groupedDataByModule.entrySet().stream().forEach(List<MonogOutMetrics> m);
//                .sorted(Map.Entry.comparingByValue((o1, o2) -> Integer.compare(o1.size(), o2.size)).map(Map.Entry::getKey)));

				sortedAscendingMap.forEach((key, value) -> {
					if (key.toString().equals("undefined")) {
						key = "Unassigned";
					}
					if (value.size() > 0) {
						Integer[] temp1 = new Integer[1];
						String[] temp2 = new String[1];
						temp1[0] = value.size();
						temp2[0] = key;
						moduleDefect.add(temp1);
						sum = sum + value.size();
						moduleCategory.add(temp2);
						moduleCumulative.add(sum);
					}

				});

				DefectParetoModel pareto = new DefectParetoModel();
				pareto.setModuleCategory(moduleCategory);
				pareto.setModuleCumulative(moduleCumulative);
				pareto.setModuleDefect(moduleDefect);
				pareto.setSum(sum);
				if (property.equals("environment"))
					text = "Environment";
				else if (property.equals("priority"))
					text = "Priority";
				else if (property.equals("state"))
					text = "Status";
				else if (property.equals("assgnTo"))
					text = "Assignee";
				pareto.setType(text);
				paretoChartType.add(pareto);
			}
		}
//	        for (var module in scope.groupedDataByModule) {
//	            sortable.push([module, scope.groupedDataByModule[module]]);
//	            sortable.sort(function (a, b) {
//	                return b[1].length - a[1].length;
//	            });
//	        }
//	       for(sortable, function (element, index) {
//	            if (element[0] === "undefined") {
//	                element[0] = "Unassigned";
//	            }
//	            var value = _.values(sortable)[index];
//	            if (value[1].length != 0) {
//	                moduleDefect.push([value[1].length]);
//	                sum = sum + value[1].length;
//	                moduleCategory.push([element[0]]);
//	                moduleCumulative.push(sum);
//	            }
//	        });
//	        drawParetoChart(moduleCategory, moduleDefect, moduleCumulative, sum, text);

		return paretoChartType;
	}

	class ValueComparator implements Comparator<List<MonogOutMetrics>> {
		Map<String, List<MonogOutMetrics>> base;

		public ValueComparator() {

		}

		public ValueComparator(Map<String, List<MonogOutMetrics>> groupedDataByModule) {
			this.base = groupedDataByModule;
		}

		@Override
		public int compare(List<MonogOutMetrics> arg0, List<MonogOutMetrics> arg1) {
			
			return arg1.size() - arg0.size();
		}
	}

	private List<MonogOutMetrics> getMetricsData(List<IterationOutModel> dataSource, String resolved,
			ALMConfiguration almConfiguration) {

		List<MonogOutMetrics> metricsSource = new ArrayList<MonogOutMetrics>();
		for (IterationOutModel e : dataSource) {
			if (e.getMetrics() != null)
				for (MonogOutMetrics m : e.getMetrics()) {

					if (resolved.equals("All") && almConfiguration.getDefectName().equals(m.getType())
							&& !Arrays.asList(almConfiguration.getCloseState()).contains(m.getState())) {

						metricsSource.add(m);
					} else if (resolved.equals("All") && almConfiguration.getDefectName().equals(m.getType())) {
						metricsSource.add(m);
					}

				}
		}
		;
		return metricsSource;

	}

	public Map<String, List<MonogOutMetrics>> groupDataForPareto(List<MonogOutMetrics> bugsdata, String propertyName) {
		Map<String, List<MonogOutMetrics>> groupedDataByModule = null;
		try {

			if (propertyName.equals("environment"))
				try {
					groupedDataByModule = bugsdata.stream().collect(Collectors.groupingBy(w -> w.getEnvironment()));
				} catch (Exception e) {
					LOGGER.error("groupedDataByModule environment is null");
				}

			else if (propertyName.equals("priority"))
				groupedDataByModule = bugsdata.stream().collect(Collectors.groupingBy(w -> w.getPriority()));
			else if (propertyName.equals("state"))
				groupedDataByModule = bugsdata.stream().collect(Collectors.groupingBy(w -> w.getState()));
			else if (propertyName.equals("assgnTo"))
				groupedDataByModule = bugsdata.stream()
						.collect(Collectors.groupingBy(w -> w.getAssgnTo() != null ? w.getAssgnTo() : "unAssigned"));
		} catch (Exception e) {
			LOGGER.error("groupedDataByModule error");
		}

//        		_(bugsdata).sortBy(propertyName).groupBy(function (item) {
//            return item[propertyName];
//        }).forEach(function (group) {
//            return group;
//        });
		return groupedDataByModule;
	}

	public Map<String, List<DefectProductionSlippage>> getDefectProducationSlippageComp(String projName, String almType,
			boolean componentBased) {
		Map<String, List<DefectProductionSlippage>> componentWiseData = new HashMap<String, List<DefectProductionSlippage>>();
		List<IterationOutModel> authorProdData = null;
		List<MonogOutMetrics> totalBugsTable = new ArrayList<MonogOutMetrics>();
		almConfiguration = almConfigRepo.findByProjectName(projName).get(0);
		Query q = new Query();
		q.addCriteria(Criteria.where("pName").is(projName).and("sName").nin("BACKLOG", "Backlog", "backlog", "BackLog")
				.and("state").nin("FUTURE", "Future", "future"));
		authorProdData = mongo.find(q, IterationOutModel.class, "Author");
		authorProdData = authorProdData.stream()
				.sorted(Comparator.comparing(IterationOutModel::getStDate,Comparator.nullsLast(Long::compareTo)))
				.collect(Collectors.toList());
		// filter of total bug from metrics
		Query q1 = new Query();
		q1.addCriteria(Criteria.where("pName").is(projName).and("type").in(almConfiguration.getDefectName())
				.and("state").nin("FUTURE", "Future", "future"));
		q1.with(new Sort(Sort.Direction.ASC, "sName"));
		totalBugsTable = totalBugsTable.stream()
				.sorted(Comparator.comparing(MonogOutMetrics::getsName,Comparator.nullsLast(String::compareTo)))
				.collect(Collectors.toList());
		componentWiseData.put("ALL", getDefectProducationSlippage(authorProdData, totalBugsTable));

		if (componentBased) {

			ArrayList<String> componentList = CommonFunctions.getComponentList(authorProdData);
			Collections.sort(componentList);

			for (String component : componentList) {
				List<IterationOutModel> tempAuthorList = new ArrayList<IterationOutModel>();
				for (IterationOutModel auth : authorProdData) {

					IterationOutModel author = new IterationOutModel();
					BeanUtils.copyProperties(auth, author);

					List<MonogOutMetrics> metrics = new ArrayList<MonogOutMetrics>();
					if (auth.getMetrics() != null)
						for (MonogOutMetrics m : auth.getMetrics()) {
							if (m.getComponents() != null && m.getComponents().get(0) != null
									&& m.getComponents().get(0).equals(component)) {

								metrics.add(m);

							}
						}
					if (metrics.size() > 0) {
						author.setMetrics(metrics);
						tempAuthorList.add(author);
					}

				}
				Collections.sort(tempAuthorList, new SprintComparatort());

				List<MonogOutMetrics> tempMetric = new ArrayList<MonogOutMetrics>();
				for (MonogOutMetrics m : totalBugsTable) {
					if (m.getComponents() != null && m.getComponents().get(0) != null
							&& m.getComponents().get(0).equals(component)) {

						tempMetric.add(m);

					}
				}
//					if (tempMetric.size() > 0) {
//						
//					}
				componentWiseData.put(component, getDefectProducationSlippage(tempAuthorList, tempMetric));

			}

		}

		return componentWiseData;
	}

	public List<DefectProductionSlippage> getDefectProducationSlippage(List<IterationOutModel> authorProdData,
			List<MonogOutMetrics> totalBugsTable) {

		DecimalFormat df = new DecimalFormat("###.##");
		float prodCount = 0;
		float testCount = 0;
		float totalCount = 0;

		List<Double> productionSlippage = new ArrayList<Double>();
		List<Double> testEffective = new ArrayList<Double>();

		List<String> categorySlippage = new ArrayList<String>();
		String[] category = {}, rejectBugs = {}, reopnedBugs = {};
		int storyPoints = 0;
		int size = authorProdData.size();
		for (int index = 0; index < size; index++) {
			IterationOutModel element = authorProdData.get(index);

			categorySlippage.add(element.getsName());
			long startDatePresentBugs = element.getStDate();
			long endDatePresentBugs = element.getEndDate();
			long startDateNextBugs, endDateNextBugs;
			if (index < size - 1) {
				startDateNextBugs = authorProdData.get(index + 1).getStDate();
				endDateNextBugs = authorProdData.get(index + 1).getEndDate();
			} else {
				startDateNextBugs = authorProdData.get(index).getEndDate();
				endDateNextBugs = currentDate;
			}
// filtering only story	 - discarding the piece for no use
//			List<MonogOutMetrics> story = element.getMetrics().stream().filter(i -> i.getType().equals("Story"))
//					.collect(Collectors.toList());
//			for (MonogOutMetrics element1 : story) {
//
//				List<Long> storyKeySet = new ArrayList<Long>();
//				if (element1.getStoryPoints() != null && element1.getResDate() > 0) {
//					for (Entry<Long, Double> k : element1.getStoryPoints().entrySet())
//
//						if (element1.getStoryPoints().containsKey(k)) {
//							storyKeySet.add(k.getKey());
//						}
//				}
//				Collections.sort(storyKeySet);
//				if (storyKeySet.size() > 0)
//					storyPoints += element1.getStoryPoints().get(storyKeySet.get(storyKeySet.size() - 1));
//			}

			if (almConfiguration.getTestingPhase() != null) {
				prodCount = totalBugsTable.stream()
						.filter(o -> o.getCreateDate() > endDatePresentBugs && o.getCreateDate() < endDateNextBugs
								&& !(Arrays.asList(almConfiguration.getTestingPhase()).contains(o.getWhenFound())))
						.collect(Collectors.toList()).size();

				testCount = totalBugsTable.stream()
						.filter(o -> o.getCreateDate() > startDatePresentBugs && o.getCreateDate() < startDateNextBugs
								&& Arrays.asList(almConfiguration.getTestingPhase()).contains(o.getWhenFound()))
						.collect(Collectors.toList()).size();

			}
//			testCount = totalBugsTable.stream()
//					.filter(o -> o.getCreateDate() > startDatePresentBugs && o.getCreateDate() < startDateNextBugs)
//					.collect(Collectors.toList()).size();

			if (index > size) {
				prodCount = 0;
				testCount = 0;
				totalCount = 0;
			}

			if ((testCount + prodCount) != 0) {
				double val = ((prodCount / (testCount + prodCount)) * 100);
				productionSlippage.add(Double.valueOf(df.format(val)));
				double valtest = ((testCount / (testCount + prodCount)) * 100);
				testEffective.add(Double.valueOf(df.format(valtest)));
			} else {
				productionSlippage.add(0.0);
				testEffective.add(100.0);
			}
		}
		List<DefectProductionSlippage> defProd = new ArrayList<DefectProductionSlippage>();
		DefectProductionSlippage prodSlippage = new DefectProductionSlippage();
		prodSlippage.setCategory(categorySlippage);
		prodSlippage.setTestEffective(testEffective);
		prodSlippage.setProductionSlippage(productionSlippage);
		defProd.add(prodSlippage);

		return defProd;

	}

	public Map<String, List<DefectDensity>> getDefectDensityComp(String projName, String almType,
			boolean componentBased) {
		Map<String, List<DefectDensity>> componentWiseData = new HashMap<String, List<DefectDensity>>();
		almConfiguration = almConfigRepo.findByProjectName(projName).get(0);
		Query q = new Query();
		q.addCriteria(Criteria.where("pName").is(projName).and("sName").nin("BACKLOG", "Backlog", "backlog", "BackLog")
				.and("state").nin("FUTURE", "Future", "future"));
		List<IterationOutModel> activeSprints = mongo.find(q, IterationOutModel.class, "Author");
		activeSprints = activeSprints.stream()
		.sorted(Comparator.comparing(IterationOutModel::getStDate,Comparator.nullsLast(Long::compareTo)))
		.collect(Collectors.toList());
		componentWiseData.put("ALL", getDefectDensity(activeSprints));

		if (componentBased) {

			ArrayList<String> componentList = CommonFunctions.getComponentList(activeSprints);
			Collections.sort(componentList);

			for (String component : componentList) {
				List<IterationOutModel> tempAuthorList = new ArrayList<IterationOutModel>();
				for (IterationOutModel auth : activeSprints) {

					IterationOutModel author = new IterationOutModel();
					BeanUtils.copyProperties(auth, author);

					List<MonogOutMetrics> metrics = new ArrayList<MonogOutMetrics>();
					if (auth.getMetrics() != null)
						for (MonogOutMetrics m : auth.getMetrics()) {
							if (m.getComponents() != null && m.getComponents().get(0) != null
									&& m.getComponents().get(0).equals(component)) {

								metrics.add(m);

							}
						}
					if (metrics.size() > 0) {
						author.setMetrics(metrics);
						tempAuthorList.add(author);
					}

				}
				Collections.sort(tempAuthorList, new SprintComparatort());
				componentWiseData.put(component, getDefectDensity(tempAuthorList));
			}
		}
		return componentWiseData;
	}

	public List<DefectDensity> getDefectDensity(List<IterationOutModel> activeSprints) {
		DecimalFormat df = new DecimalFormat("###.##");

		List<Double> sprintDefectDensity = new ArrayList<Double>();
		List<String> category = new ArrayList<String>();
		List<Double> spCompleted = new ArrayList<Double>();
		List<Double> defectsAdded = new ArrayList<Double>();

		List<MonogOutMetrics> sprintsData = new ArrayList<MonogOutMetrics>();
		

		firstSprintStartDate = activeSprints.get(0).getStDate();
		if(almConfiguration.getFirstSprint() !=null) {
			for(IterationOutModel sprint:activeSprints) {
				if(sprint.getsName().equalsIgnoreCase(almConfiguration.getFirstSprint())) {
					firstSprintStartDate = sprint.getStDate();
				}
			}
			
		}
		for (IterationOutModel m : activeSprints) {
			if (m.getMetrics() != null)
				sprintsData.addAll(m.getMetrics());
		}
		int size = activeSprints.size();
		double totalSp=0;
		for (int i = 0; i < size; i++) {

			List<MonogOutMetrics> completeSp = new ArrayList<MonogOutMetrics>();
			List<MonogOutMetrics> defects = new ArrayList<MonogOutMetrics>();

			IterationOutModel e = activeSprints.get(i);
			if (e.getsName().contains("Sprint 4.0.2.0-4")) {
				LOGGER.info("");
			}
			if (i != size - 1) {
				completeSp = getMerics(sprintsData, "Story", activeSprints.get(i + 1).getStDate(),e.getStDate());
				defects = getMerics(sprintsData, almConfiguration.getDefectName(),
						activeSprints.get(i + 1).getStDate(),e.getStDate());
			} else {
				completeSp = getMerics(sprintsData, "Story", e.getEndDate(),e.getStDate());
				defects = getMerics(sprintsData, almConfiguration.getDefectName(), e.getEndDate(),e.getStDate());
			}
			category.add(e.getsName());
			double storypoint = getStorypoint(completeSp);
			totalSp = totalSp+storypoint;
			if (totalSp == 0) {
				sprintDefectDensity.add(0.0);
			} else {
				double valtest = (defects.size() / totalSp * 100) / 100;
				sprintDefectDensity.add(Double.valueOf(df.format(valtest)));
			}
			spCompleted.add(totalSp);
			defectsAdded.add((double) defects.size());
		}
		LOGGER.info(sprintDefectDensity.size());
		List<DefectDensity> defDensity = new ArrayList<DefectDensity>();
		DefectDensity density = new DefectDensity();
		density.setCategory(category);
		density.setDefectDenisty(sprintDefectDensity);
		density.setSpCompleted(spCompleted);
		density.setDefectsAdded(defectsAdded);

		defDensity.add(density);

		// componentWiseData.put("ALL", defDensity);

//		        var lineData = [{
//		            name: "Defect Density"
//		            , data: sprintDefectDensity
//		                    }];
//		        var pagination = {
//		            val: true
//		            , count: 5
//		        , };
//		        commonCharts.drawLineChart('#defectStory', '', 'Value', lineData, yaxis, pagination);

		return defDensity;
	}

	public List<MonogOutMetrics> getMerics(List<MonogOutMetrics> metricsdata, String issue, long nextSp,long currentSprint) {
		List<MonogOutMetrics> cStory = new ArrayList<MonogOutMetrics>();
		if (issue.equals("Story")) {
			cStory = metricsdata.stream().filter(m -> (m.getType().equals(issue)
					&& m.getCreateDate() >= firstSprintStartDate && m.getResDate() >= currentSprint &&  m.getResDate()<= nextSp && m.getCreateDate() <= nextSp))
					.collect(Collectors.toList());

		} else {
			cStory = metricsdata.stream().filter(m -> (m.getType().equals(issue)
					&& m.getCreateDate() >= firstSprintStartDate && m.getCreateDate() <= nextSp))
					.collect(Collectors.toList());

		}
		return cStory;
	}

	public double getStorypoint(List<MonogOutMetrics> storydata) {
		int stpoint = 0;
		for (MonogOutMetrics s : storydata) {
			if (s.getStoryPoints() != null) {

				Map<Long, Double> spData = s.getStoryPoints();
				TreeMap<Long, Double> sorted = new TreeMap<>();
				// Copy all data from hashMap into TreeMap
				sorted.putAll(spData);

				stpoint += ((Double) sorted.values().toArray()[sorted.size() - 1]);
			}

		}

		return stpoint;
	}

	public Map<String, DefectBacklog> getDefectBacklogComponent(String projName, String almType,
			boolean componentBased) {
		getInititialDetails(projName, almType);
		almConfiguration = almConfigRepo.findByProjectName(projName).get(0);
		Map<String, DefectBacklog> componentWiseData = new HashMap<String, DefectBacklog>();

		Query q1 = new Query();
		q1.addCriteria(Criteria.where("pName").is(projName).and("type").in(almConfiguration.getDefectName())
				.and("state").nin("FUTURE", "Future", "future"));
		q1.with(new Sort(Sort.Direction.ASC, "sName"));
		List<MonogOutMetrics> totalBugsTable = new ArrayList<MonogOutMetrics>();
		// getting all Bugs
		totalBugsTable = mongo.find(q1, MonogOutMetrics.class, "Metrics");

		componentWiseData.put("ALL", getDefectBacklog(totalBugsTable));

		if (componentBased) {

			ArrayList<String> componentList = CommonFunctions.getComponentList(allIterationsAndBacklog);
			Collections.sort(componentList);

			for (String component : componentList) {
				List<MonogOutMetrics> tempMetric = new ArrayList<MonogOutMetrics>();
				for (MonogOutMetrics m : totalBugsTable) {
					if (m.getComponents() != null && m.getComponents().get(0) != null
							&& m.getComponents().get(0).equals(component)) {

						tempMetric.add(m);

					}
				}
//					if (tempMetric.size() > 0) {
//						
//					}

				componentWiseData.put(component, getDefectBacklog(tempMetric));
			}

		}

		return componentWiseData;
	}

	public DefectBacklog getDefectBacklog(List<MonogOutMetrics> totalBugsTable) {
		// Map<String, DefectBacklog> componentWiseData = new HashMap<String,
		// DefectBacklog>();
		int duration = 30;// days of time period
		boolean severityFlag = false, priorityFlag = false;
		String flagVal;

		List<MonogOutMetrics> totalDefectData = new ArrayList<MonogOutMetrics>();
		SimpleDateFormat format1 = new SimpleDateFormat("MM-dd-yyyy");
		List<DefectSeverity> defectsBySeverity = new ArrayList<DefectSeverity>();
		List<Long> durationDays = new ArrayList<Long>();
		List<Integer[]> newOpenBugs = new ArrayList<Integer[]>();
		List<String> newCategory = new ArrayList<String>();

		if (almConfiguration.getPriorityName() != null) {
			if (almConfiguration.getPriorityName().equals("priority")) {
				priorityFlag = true;
			} else {
				severityFlag = true;
			}
		} else {
			priorityFlag = true;
		}

		final boolean isPriority = priorityFlag;
		if (Constant.prodTypeNewDef.equals("Production") && almConfiguration.getProductionPhase() != null
				&& almConfiguration.getProductionPhase().length > 0) {

			totalDefectData = totalBugsTable.stream()
					.filter(o -> ((Arrays.asList(almConfiguration.getProductionPhase()).contains(o.getWhenFound()))
							&& isPriority ? o.getPriority() != null : o.getSeverity() != null))
					.collect(Collectors.toList());
		} else if (almConfiguration.getProductionPhase() != null && almConfiguration.getProductionPhase().length > 0) {

			totalDefectData = totalBugsTable.stream()
					.filter(o -> ((!Arrays.asList(almConfiguration.getProductionPhase()).contains(o.getWhenFound()))
							&& isPriority ? o.getPriority() != null : o.getSeverity() != null))
					.collect(Collectors.toList());
		} else {

			totalDefectData = totalBugsTable.stream()
					.filter(o -> (isPriority ? o.getPriority() != null : o.getSeverity() != null))
					.collect(Collectors.toList());
		}
		// Get start date of the first bug created
		if (totalBugsTable.size() > 0) {
			long firstBugDate = totalDefectData.get(0).getCreateDate();
			int size = totalDefectData.size();
			for (int i = 0; i < size - 1; i++) {

				if (firstBugDate > totalDefectData.get(i + 1).getCreateDate()) {
					firstBugDate = totalDefectData.get(i + 1).getCreateDate();
				}
				// firstBugDate = firstBugDate < totalDefectData.get(i + 1).getCreateDate()
				// ?firstBugDate: totalDefectData.get(i + 1).getCreateDate() ;
//                     

			}
			List<String> severities = new ArrayList<String>();
			Map<String, List<MonogOutMetrics>> bySeverity = null;
			if (severityFlag != false) {
				flagVal = "severity";
				bySeverity = totalDefectData.stream().collect(Collectors.groupingBy(w -> w.getSeverity()));
			} else {
				flagVal = "priority";
				bySeverity = totalDefectData.stream().collect(Collectors.groupingBy(w -> w.getPriority()));
			}

			bySeverity.forEach((key, value) -> {
				if (!key.equals(" ")) {
					severities.add(key);
				}
			});
			// till is ok

			// Create an array of weekly based timestamps
			Calendar startDate = Calendar.getInstance();
			startDate.setTimeInMillis(firstBugDate);
//                  long startDate = moment(firstBugDate);
//                  var endDate = moment();

			Date today = new Date();
			Calendar endDate = new GregorianCalendar();
			endDate.setTime(today);
//                  cal.add(Calendar.DAY_OF_MONTH, -30);
			while (endDate.compareTo(startDate) > -1) {
				long date = startDate.getTimeInMillis();
				durationDays.add(date);
				startDate.add(Calendar.DAY_OF_MONTH, 30);
//                      startDate.add(duration, 'days');
			}
			// durationDays.add(endDate.getTimeInMillis());
			final int durationSize = durationDays.size();
			for (int i = 0; i < durationSize; i++) {
				long o = durationDays.get(i);

				List<MonogOutMetrics> bugsCreated = new ArrayList<MonogOutMetrics>();
				final int index = i;

				if (i == durationSize - 1) {
					bugsCreated = totalDefectData.stream()
							.filter(item -> item.getCreateDate() < endDate.getTimeInMillis())
							.collect(Collectors.toList());
				} else {
					bugsCreated = totalDefectData.stream()
							.filter(item -> item.getCreateDate() < durationDays.get(index + 1))
							.collect(Collectors.toList());
				}
				List<MonogOutMetrics> allOpen = bugsCreated.stream()
						.filter(item -> (item.getResDate() > o
								&& Arrays.asList(almConfiguration.getCloseState()).contains(item.getState()))
								|| !Arrays.asList(almConfiguration.getCloseState()).contains(item.getState()))
						.collect(Collectors.toList());

				Integer[] temp1 = new Integer[1];

				temp1[0] = allOpen.size();
				newOpenBugs.add(temp1);
				startDate.setTimeInMillis(o);
				Date date = startDate.getTime();

				String date1 = format1.format(date);
				newCategory.add(date1);
				for (String element : severities) {
					int tempClosedBySeverity = 0;
					Integer[] temp2 = new Integer[1];

					if (severityFlag != false)
						tempClosedBySeverity = allOpen.stream().filter(item -> item.getSeverity().equals(element))
								.collect(Collectors.toList()).size();
					else
						tempClosedBySeverity = allOpen.stream().filter(item -> item.getPriority().equals(element))
								.collect(Collectors.toList()).size();
					DefectSeverity defSev = new DefectSeverity();
					defSev.setName(element);
					temp2[0] = tempClosedBySeverity;
					defSev.setData(temp2);
					defectsBySeverity.add(defSev);

				}
			}
		}
		Map<String, List<DefectSeverity>> groupedData = defectsBySeverity.stream()
				.collect(Collectors.groupingBy(DefectSeverity::getName));

//          var groupedData = group(defectsBySeverity, 'name');
		Map<String, List<Integer[]>> seriesNew = new HashMap<String, List<Integer[]>>();

		seriesNew.put("All", newOpenBugs);
		groupedData.forEach((key, value) -> {
			if (!key.equals(" ")) {
				List<Integer[]> data = new ArrayList<Integer[]>();
				for (DefectSeverity def : value) {
					data.add(def.data);
				}
				LOGGER.info("" + key + " " + data.size());
				seriesNew.put("" + key, data);
			}
		});

		DefectBacklog defBacklog = new DefectBacklog();
		defBacklog.setCategory(newCategory);
		defBacklog.setData(seriesNew);

		// componentWiseData.put("ALL", defBacklog);
		return defBacklog;
	}

	class SprintComparatort implements Comparator {

		@Override
		public int compare(Object o1, Object o2) {
			IterationOutModel s1 = (IterationOutModel) o1;
			IterationOutModel s2 = (IterationOutModel) o2;

			if (s1.getStDate() != null && s2.getStDate() != null) {
				if (s1.getStDate().equals(s2.getStDate()))
					return 0;
				else if (s1.getStDate() > s2.getStDate())
					return 1;
				else
					return -1;
			} else if (s1.getStDate() == null) {
				return 1;
			} else {
				return -1;
			}

		}
	}
	
	
	public double callSP(String flag, IterationOutModel sp) {
		double tempSp = 0;
		
		issueList = new ArrayList<IssueList>();

		long date;
		if (flag.equals("start")) {
			date = sp.getStDate();
		} else {
			if (sp.getState().toLowerCase().contains("active")) {
				date = new Date().getTime();
			} else {

				date = sp.getEndDate();

			}

		}

		for (IterationOutModel it : workingBacklog) {

			ArrayList<String> keys, dateMapValues;
			ArrayList<Long> dateMapKeys=new ArrayList<Long>();
			ArrayList<Long> storyPointMapKeys=new ArrayList<Long>();
			Map storyPointMap;
			Map<Long, String> dateMap;
			if (it.getMetrics() != null) {

				for (MonogOutMetrics ele : it.getMetrics()) {
					
					if(ele.getwId().equals("NTSBW-2782") && sp.getsName().equalsIgnoreCase("NP PI-4.2")) {
						System.out.println("Issue Comes");
					}
					
					Collections.sort(dateMapKeys);
					if (ele.getAllocatedDate() != null && almConfiguration.getDefectName().equalsIgnoreCase(ele.getType())
							&& ele.getStoryPoints() != null && (workItemArr2.indexOf(ele.getwId()) < 0)) {
						dateMap = ele.getAllocatedDate();
						dateMapKeys = new ArrayList<Long>(ele.getAllocatedDate().keySet());
						storyPointMapKeys = new ArrayList<Long>(ele.getStoryPoints().keySet());
						storyPointMap = ele.getStoryPoints();
						Collections.sort(storyPointMapKeys);
						for (int i = 0; i < dateMapKeys.size(); i++) {
							List<String> dateValues = Arrays.asList(dateMap.get(dateMapKeys.get(i)).split(","));
							if (i == dateMapKeys.size() - 1 && dateMapKeys.get(i) < date && (dateValues.contains(String.valueOf(sp.getsId())))) {
								refinedIssues(true, sp, issueList, ele, date, dateMapKeys.get(i));
								
							} else if ((dateMapKeys.get(i) < date) && (dateValues.contains(String.valueOf(sp.getsId())))
									&& (dateMapKeys.get(i + 1) > date)) {
								refinedIssues(false, sp, issueList, ele, date, dateMapKeys.get(i));
							
							}

						}
					}

				}
			}

		}
		return tempSp;
	}
	

	private double calcClosedDefects(IterationOutModel sp) {
		double tempSp = 0;
		issueList = new ArrayList<IssueList>();

		long date;

		if (sp.getState().toLowerCase().contains("active")) {
			date = new Date().getTime();
		} else {
			if (sp.getCompletedDate() != 0) {
				date = sp.getCompletedDate();
			} else {
				date = sp.getEndDate();
			}
		}

		wIdArr = new ArrayList<String>();
		for (IterationOutModel it : workingBacklog) {

			ArrayList<String> keys;
			ArrayList<Long> dateMapKeys, storyPointMapKeys;
			Map storyPointMap;
			Map<Long, String> dateMap;

			if (it.getMetrics() != null) {

				for (MonogOutMetrics ele : it.getMetrics()) {

					if (ele.getsId() != 0) {
						if (ele.getAllocatedDate() != null && (almConfiguration.getDefectName().equalsIgnoreCase(ele.getType()))
								&& ele.getStoryPoints() != null) {
							dateMap = ele.getAllocatedDate();
							dateMapKeys = new ArrayList<Long>(ele.getAllocatedDate().keySet());
							Collections.sort(dateMapKeys);

							storyPointMapKeys = new ArrayList<Long>(ele.getStoryPoints().keySet());
							storyPointMap = ele.getStoryPoints();

							Collections.sort(storyPointMapKeys);

							for (int i = 0; i < dateMapKeys.size(); i++) {
								List<String> dateValues = Arrays.asList(dateMap.get(dateMapKeys.get(i)).split(","));
								if (i == dateMapKeys.size() - 1 && dateMapKeys.get(i) < date
										&& dateValues.contains(String.valueOf(sp.getsId()))) {
									String wid = ele.getwId();
									TransitionModel trans = filterTrans(ele.getTransitions(), date);
									if (closeStates.indexOf(trans.getCrState()) > -1 && trans.getMdfDate() < date
											&& trans.getMdfDate() > sp.getStDate()
											&& trans.getMdfDate() > dateMapKeys.get(i)
											&& wIdArr.indexOf(ele.getwId()) <= -1) {
										wIdArr.add(ele.getwId());
										long alloc = 0;
										
										IssueList issue = new IssueList();
										issue.setwId(ele.getwId());
										issue.setAssignee(ele.getAssgnTo());
										issue.setState(ele.getState());
										issue.setType(ele.getType());
										issue.setAllocatedDate(ele.getAllocatedDate());
										issue.setTransitions(ele.getTransitions());
										issue.setCreatedDate(ele.getCreateDate());
										issue.setsName(sp.getsName());
										issue.setPriority(ele.getPriority());
										issue.setSeverity(ele.getSeverity());
										issueList.add(issue);

									}

								} else if (dateMapKeys.get(i) < date && dateValues.indexOf(sp.getsId() + "") > -1
										&& dateMapKeys.get(i + 1) > date) {
									String wid = ele.getwId();
									TransitionModel trans = filterTrans(ele.getTransitions(), date);
									if (closeStates.indexOf(trans.getCrState()) > -1 && trans.getMdfDate() < date
											&& trans.getMdfDate() > sp.getStDate()
											&& trans.getMdfDate() > dateMapKeys.get(i)
											&& wIdArr.indexOf(ele.getwId()) <= -1) {

										wIdArr.add(ele.getwId());
										
										IssueList issue = new IssueList();
										issue.setwId(ele.getwId());
										issue.setAssignee(ele.getAssgnTo());
										issue.setState(ele.getState());
										issue.setType(ele.getType());
										issue.setAllocatedDate(ele.getAllocatedDate());
										issue.setTransitions(ele.getTransitions());
										issue.setCreatedDate(ele.getCreateDate());
										issue.setsName(sp.getsName());
										issue.setPriority(ele.getPriority());
										issue.setSeverity(ele.getSeverity());
										issueList.add(issue);
										
									}

								}

							}
						}
					}
				}
			}
		}
		return tempSp;
	}
	
	private List<ScoreCardSprintData> calcVelocity(List<IterationOutModel> iterations) {
		sprintAndDefectsAdded = new HashMap<String, List<IssueList>>();
		sprintAndDefectsClosed = new HashMap<String, List<IssueList>>();

		ArrayList<String> workItemArr = new ArrayList<String>();
		workingBacklog = new ArrayList<IterationOutModel>();
		workingSprints = new ArrayList<IterationOutModel>();
		

		for (IterationOutModel auth : iterations) {
			if (null == auth.getState() || (null != auth.getState() && !auth.getState().toLowerCase().contains("future"))) {
				workingBacklog.add(auth);
			}
		}

		for (IterationOutModel auth : iterations) {
			if (null != auth.getState() && auth.getStDate() != 0 && !auth.getState().toLowerCase().contains("future")
					&& !auth.getState().toLowerCase().contains("backlog") && auth.getsId() != 0) {
				workingSprints.add(auth);
				workingSprints.sort(Comparator.comparing(IterationOutModel::getStDate));

			}
		}
		
		for (int i = workingSprints.size()- 30 >= 0 ? workingSprints.size()-30:0; i < workingSprints.size() ; i++) {
			double totalStoryPoints = 0, totalCompletedStoryPoints = 0, totalSPAfter = 0, totalSpRefined = 0,
					totalSPremoved = 0, currentSprintBugs = 0, capacityPerSprint = 0, actualEffort = 0, tempSp = 0;


			workItemArr2 = new ArrayList<String>();
			
			if(workingSprints.get(i).getsName().equals("Sprint 96 Jan 07 - Jan 20")) {
				System.out.println("Stop");
			}


			refinedIssuList = new ArrayList<>();
			
			callSP("start", workingSprints.get(i));

			callSP("end", workingSprints.get(i));
			
			
			calcClosedDefects(workingSprints.get(i));
			
			sprintAndDefectsAdded.put(workingSprints.get(i).getsName(), refinedIssuList);
			sprintAndDefectsClosed.put(workingSprints.get(i).getsName(), issueList);

		}

		return null;

	}
	
	
	private void refinedIssues(boolean isRefined,  IterationOutModel it,
			List<IssueList> issueList, MonogOutMetrics m, long date, Long allocateDate) {
	
			long endDate;
			List<TransitionModel> tr = m.getTransitions();
			if (tr != null) {
				tr.sort(Comparator.comparing(TransitionModel::getMdfDate));
			}
			boolean checkRemoved = false;
		
				if (it.getState().toLowerCase().contains("active")) {
					endDate = new Date().getTime();
				} else {
					if (it.getCompletedDate() != 0) {
						endDate = it.getCompletedDate();
					} else {
						endDate = it.getEndDate();
					}
				}

			List<String> checkRemoveResult = null;
			
			
			if(!isRefined) {
				checkRemoveResult = checkRemoved(it, m, allocateDate, endDate);
			}
			
			if((checkRemoveResult !=null && checkRemoveResult.get(0).equals("false")) || checkRemoveResult ==null) {
				if(closeStates.indexOf("Withdrawn") > -1) {
					checkRemoveResult = checkWithdrawn(it, m, allocateDate, endDate, date);
				}
			}
			
				
			if(checkRemoveResult !=null) {
				checkRemoved = checkRemoveResult.get(0).equals("false") ? false : true;
			}
			
			

			if (!checkRemoved) {
				IssueList issue = new IssueList();
				issue.setwId(m.getwId());
				issue.setAssignee(m.getAssgnTo());
				issue.setState(m.getState());
				issue.setType(m.getType());
				issue.setAllocatedDate(m.getAllocatedDate());
				issue.setTransitions(m.getTransitions());
				issue.setCreatedDate(m.getCreateDate());
				issue.setsName(it.getsName());
				issue.setPriority(m.getPriority());
				issue.setSeverity(m.getSeverity());
				refinedIssuList.add(issue);
			}
			workItemArr2.add(m.getwId());
		



	}

	private List<String> checkRemoved(IterationOutModel it, MonogOutMetrics m, Long allocateDate, long endDate) {
		
		Map<Long, String> dateMap = m.getAllocatedDate();
		ArrayList<Long> dateMapKeys = new ArrayList<Long>(m.getAllocatedDate().keySet());
		List<String> result = new ArrayList<String>();
		result.add("false");

		Collections.sort(dateMapKeys);
		int index = dateMapKeys.indexOf(allocateDate);

		for (int i = index + 1; i < dateMapKeys.size(); i++) {
			// String Sprints[]=dateMap.get(dateMapKeys.get(i))
			List<String> dateValues = Arrays.asList(dateMap.get(dateMapKeys.get(i)).split(","));
			if (dateMapKeys.get(i) < endDate && !(dateValues.contains(String.valueOf(it.getsId())))) {
				result.add(0, "true");
				result.add(1, String.valueOf(dateMapKeys.get(i)));
				return result;
			}

		}
		return result;
	}
	
	private List<String> checkWithdrawn(IterationOutModel it, MonogOutMetrics m, Long allocateDate, long endDate,long startDate){
		
		List<String> result = new ArrayList<String>();
		List<TransitionModel> transitions= m.getTransitions();
		if(transitions !=null) {
			transitions.sort(Comparator.comparing(TransitionModel::getMdfDate));
			transitions = transitions.stream().filter((ele)->{
				if(ele.getMdfDate() <=endDate) {
					return true;
				}else {
					return false;
				}
			}).collect(Collectors.toList());
			int index=-1;
			for(int i=0;i<transitions.size();i++) {
				if(transitions.get(i).getCrState().equalsIgnoreCase("Withdrawn")) {
					index=i;
				}
			}
			
			if(transitions.size() >0 && index == transitions.size()-1) {
				result.add(0, "true");
				result.add(1, String.valueOf(transitions.get(index).getMdfDate()));
			}else {
				result.add("false");
			}
		}else {
			result.add("false");
		}
		
		return result;
	}
	TransitionModel filterTrans(List<TransitionModel> tr, long date) {
		List<TransitionModel> newTr = new ArrayList<TransitionModel>();
		if (null != tr) {
			for (int i = 0; i < tr.size(); i++) {
				if (tr.get(i).getMdfDate() < date) {
					newTr.add(tr.get(i));
				}
			}
		}

		if (newTr.size() > 0) {
			return newTr.get(newTr.size() - 1);
		} else {
			return new TransitionModel();
		}
	}
	
	
}
