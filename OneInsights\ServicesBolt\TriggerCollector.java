package com.bolt.dashboard;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.quartz.CronScheduleBuilder;
import org.quartz.JobBuilder;
import org.quartz.JobDetail;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.Trigger;
import org.quartz.TriggerBuilder;
import org.quartz.impl.StdSchedulerFactory;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.stereotype.Component;

import com.bolt.dashboard.core.ProjectCollector;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.PortfolioConfig;
import com.bolt.dashboard.core.repository.PortfolioConfigRepo;
import com.bolt.dashboard.jira.ALMClientImplementation;

@Component
public class TriggerCollector {
	private static final Logger LOGGER = LogManager.getLogger(ALMClientImplementation.class);
	AnnotationConfigApplicationContext ctx=null;
	public void getDataFromTools(Boolean flag) throws SchedulerException {
		ctx = DataConfig.getContext();
		PortfolioConfigRepo portfolio = ctx.getBean(PortfolioConfigRepo.class);
		List<PortfolioConfig> data = portfolio.findAll();
		Scheduler scheduler = new StdSchedulerFactory().getScheduler();
		for (PortfolioConfig config : data) {
			if (config.getProjectName() != null && config.getCronExpression() != null && config.getSchedulerEnabled()!=null && config.getSchedulerEnabled()) {
				String jobkey = config.getProjectName();
				JobKey job = new JobKey(jobkey, "BOLT_SCHEDULER");
				
				// Use this Part to delete and rerun, If Already scheduler is Running
				if (scheduler.checkExists(job)){
				    scheduler.deleteJob(job);
				    LOGGER.info("Deleted the job...!");
				}else {
					LOGGER.info("else the job...!");
				}
				JobDetail jobA = JobBuilder.newJob(ProjectCollector.class).withIdentity(job).build();

				jobA.getJobDataMap().put("projectName", config.getProjectName());

				Trigger trigger = TriggerBuilder.newTrigger().withIdentity(jobkey, "BOLT_SCHEDULER")
						.withSchedule(CronScheduleBuilder.cronSchedule(config.getCronExpression())).build();

				scheduler.scheduleJob(jobA, trigger);

			}

		}

		scheduler.start();
	}
}
