package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ChangeHistoryModel;
import com.bolt.dashboard.core.model.ComponentBurnDown;
import com.bolt.dashboard.core.model.ComponentGroomingTable;
import com.bolt.dashboard.core.model.ComponentIssueBreakup;
import com.bolt.dashboard.core.model.ComponentSprintWiseStories;
import com.bolt.dashboard.core.model.ComponentStoryAgeing;
import com.bolt.dashboard.core.model.ComponentStoryProgress;
import com.bolt.dashboard.core.model.ComponentTaskRisk;
import com.bolt.dashboard.core.model.ComponentVelocityList;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.model.DefectInsightData;
import com.bolt.dashboard.core.model.EffortHistoryModel;
import com.bolt.dashboard.core.model.IterationModel;
import com.bolt.dashboard.core.model.IterationOutModel;
import com.bolt.dashboard.core.model.MetricAgeData;
import com.bolt.dashboard.core.model.MetricsModel;
import com.bolt.dashboard.core.model.MonogOutMetrics;
import com.bolt.dashboard.core.model.ProjectModel;
import com.bolt.dashboard.core.model.ReleaseDetails;
import com.bolt.dashboard.core.model.TransitionModel;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.jira.ChartCalculations;
import com.bolt.dashboard.service.AlmService;
import com.bolt.dashboard.util.BurnDownDataSprint;
import com.bolt.dashboard.util.DefectBacklog;
import com.bolt.dashboard.util.DefectDensity;
import com.bolt.dashboard.util.DefectParetoModel;
import com.bolt.dashboard.util.DefectProductionSlippage;
import com.bolt.dashboard.util.StoryProgressSprintwise;
import com.bolt.dashboard.util.TaskRiskSprint;

@RestController
public class AlmController {
	
	private AlmService service;

	@Autowired
	public AlmController(AlmService service) {
		this.service = service;
	}

	/*
	 * public static void main(String[] args) { ApplicationContext ctx =
	 * DataConfig.getContext(); service = new
	 * ALMServiceImplementation(ctx.getBean(TransitionRepo.class),
	 * ctx.getBean(ProjectIterationRepo.class),
	 * ctx.getBean(EffortHistoryRepo.class),
	 * ctx.getBean(ChangeHisortyRepo.class), ctx.getBean(ProjectRepo.class),
	 * ctx.getBean(ReleaseRepo.class)); new
	 * AlmController(service).getSlaData("RDC", "JIRA", 1524156068000L); }
	 */
	
	@RequestMapping(value = "/storyAgeing", method = GET, produces = APPLICATION_JSON_VALUE)
	public  List<ComponentStoryAgeing>  storyAgeing(@RequestParam("pName") String projName,@RequestParam("almType") String almType){
		 
		return this.service.getStoryAgeingData(projName,almType);
	}
	
	@RequestMapping(value = "/groomingTable", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<ComponentGroomingTable> groomingTable(@RequestParam("pName") String projName,@RequestParam("almType") String almType){
		 
		return this.service.getGroomingTable(projName,almType);
	}
	
	@RequestMapping(value = "/delDuplicate", method =RequestMethod.GET)
	public String delDuplicate(@RequestParam("pName") String projName) {
		return service.delDuplicate(projName);
	}
	
	@RequestMapping(value = "/sprintProgressHome", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<Map<String,List<String>>> getSprintProgressHome(@RequestParam("pName") String projName,@RequestParam("almType") String almType){
		return this.service.getSprintProgressHome(projName,almType);
	}
	@RequestMapping(value = "/defectInsightData", method = GET, produces = APPLICATION_JSON_VALUE)
	public Map<String, List<DefectInsightData>>  defectInsightData(@RequestParam("pName") String projName,@RequestParam("almType") String almType,
			@RequestParam("componentFlag") boolean componentFlag){
		 
		return this.service.getDefectInsightData(projName,almType,componentFlag);
	}
	
	@RequestMapping(value = "/defectTrendAndClassification", method = GET, produces = APPLICATION_JSON_VALUE)
	public Map  defectTrendAndClassification(@RequestParam("pName") String projName,@RequestParam("almType") String almType,@RequestParam("componentBasedFlag") boolean componentBased){
		 
		return this.service.getDefectTrendAndClassification(projName,almType,componentBased);
	}
	
	@RequestMapping(value = "/defectClassification", method = GET, produces = APPLICATION_JSON_VALUE)
	public Map  defectClassification(@RequestParam("pName") String projName,@RequestParam("almType") String almType,@RequestParam("componentBasedFlag") boolean componentBased){
		 
		return this.service.getDefectClassification(projName,almType);
	}
	
	@RequestMapping(value = "/issueBrakeUp", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<ComponentIssueBreakup> getIssueBrakeUp(@RequestParam("pName") String projName,@RequestParam("almType") String almType){
		
		return this.service.getIssueBrakeUp(projName,almType);
	}
	
	@RequestMapping(value = "/getStoryProgress", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<ComponentStoryProgress> getStoryProgress(@RequestParam("pName") String projName,@RequestParam("almType") String almType){
		
		return service.getStoryProgress(projName,almType);
	}
	
	@RequestMapping(value = "/defectsSummaryHome", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<Map<String,String>> getDefectsSummaryHome(@RequestParam("pName") String projName,@RequestParam("almType") String almType){
		 
	
		
		return this.service.getDefectsSummaryHome(projName,almType);
	}
	@RequestMapping(value = "/taskRisk", method = GET, produces = APPLICATION_JSON_VALUE)
	public  List<ComponentTaskRisk> getTaskRiskStoryPoint(@RequestParam("pName") String projName,@RequestParam("almType") String almType,@RequestParam("storyPointBasedFlag") boolean storyPointBased){
		 
	
		
		return this.service.getTaskRisk(projName,almType,storyPointBased);
	}
	@RequestMapping(value = "/burndown", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<ComponentBurnDown>  burndownCalculation(@RequestParam("pName") String projName,@RequestParam("almType") String almType,@RequestParam("storyPointBasedFlag") boolean storyPointBased){
		
		return this.service.burndownCalculation(projName,almType,storyPointBased);
	}
	
	@RequestMapping(value = "/getProductionSlippage", method = GET, produces = APPLICATION_JSON_VALUE)
	public Map<String,List<DefectProductionSlippage>> getProductionSlippage(@RequestParam("pName") String projName,@RequestParam("almType") String almType, @RequestParam("componentBasedFlag") boolean componentBased){	
		return this.service.getProductionSlippage(projName,almType,componentBased);
	}
	@RequestMapping(value = "/getDefectDensity", method = GET, produces = APPLICATION_JSON_VALUE)
	public Map<String,List<DefectDensity>> getDefectDensity(@RequestParam("pName") String projName,@RequestParam("almType") String almType, @RequestParam("componentBasedFlag") boolean componentBased){	
		return this.service.getDefectDensity(projName,almType,componentBased);
	}
	@RequestMapping(value = "/getDefectBacklog", method = GET, produces = APPLICATION_JSON_VALUE)
	public Map<String,DefectBacklog> getDefectBacklog(@RequestParam("pName") String projName,@RequestParam("almType") String almType, @RequestParam("componentBasedFlag") boolean componentBased){	
		return this.service.getDefectBacklog(projName,almType,componentBased);
	}
	
	@RequestMapping(value = "/getDefectPareto", method = GET, produces = APPLICATION_JSON_VALUE)
	public Map<String,List<DefectParetoModel>> getDefectPareto(@RequestParam("pName") String projName,@RequestParam("almType") String almType, @RequestParam("componentBasedFlag") boolean componentBased){	
		return this.service.defectParetoCalculation(projName,almType,componentBased);
	}
	
	
	
	@RequestMapping(value = "/activeSprints", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<String> getActiveSprints(@RequestParam("pName") String projName,@RequestParam("almType") String almType){
		return this.service.getActiveSprints(projName,almType);
	}

	
	
	@RequestMapping(value = "/delAllIssues", method =RequestMethod.GET)
	public String delAllIsues(@RequestParam("pName") String projName) {
		return service.delAllIssues(projName);
	}

	@RequestMapping(value = "/getMetric", method = GET, produces = APPLICATION_JSON_VALUE)
	public MonogOutMetrics getMetricsDatas(@RequestParam("wId") String wId) {
		return service.getMetricDetails(wId);
	}
	@RequestMapping(value = "/getAllTransitions", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<TransitionModel> getAllTransitions(@RequestParam("pName") String pName) {
		return service.getAllTransitions(pName);
	}

	@RequestMapping(value = "/getProjectMetrics", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<MonogOutMetrics> getProjectMetrics(@RequestParam("pName") String pName) {
		return service.getAllMetrics(pName);
	}

	@RequestMapping(value = "/getChangeItems", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<ChangeHistoryModel> getChangesItems(@RequestParam("wId") String wId) {
		return service.getChangesItems(wId);
	}

	@RequestMapping(value = "/getTransitions", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<TransitionModel> getTransitionsData(@RequestParam("wId") String wId) {
		return service.getTransitionsData(wId);
	}

	@RequestMapping(value = "/getIteration", method = GET, produces = APPLICATION_JSON_VALUE)
	public IterationOutModel getIterationData(@RequestParam("pName") String pName,
			@RequestParam("itrName") String itrName) {
		return service.getIterationData(pName, itrName, getAlmType(pName));
	}

	@RequestMapping(value = "/getEfort", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<EffortHistoryModel> getEffortData(@RequestParam("wId") String wId) {
		return service.getEffortData(wId);
	}

	@RequestMapping(value = "/getProjectDetails", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<IterationOutModel> getProjectDetials(@RequestParam("pName") String pName,
			@RequestParam("almType") String almType) {
		return service.getProjectDetails(pName, almType);
	}
	@RequestMapping(value = "/getCurrentProjectDetails", method = GET, produces = APPLICATION_JSON_VALUE)
	public Map <Integer,List<IterationOutModel>> getCurrentProjectDetials(@RequestParam("pName") String pName,
			@RequestParam("almType") String almType) {
		return service.getCurrentProjectDetails(pName, almType);
	}
	
	
	@RequestMapping(value = "/getCurrentIter", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<IterationOutModel> getCurrentIter(@RequestParam("pName") String pName,
			@RequestParam("almType") String almType) {
		return service.getCrtItr(pName, almType);
	}
	@RequestMapping(value = "/getIterations", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<IterationModel> getIterations(@RequestParam("pName") String pName) {
		return service.getAllIterations(pName);
	}

	@RequestMapping(value = "/getDefectCount", method = GET, produces = APPLICATION_JSON_VALUE)
	public ProjectModel getDefectCount(@RequestParam("pName") String pName, @RequestParam("almType") String almType) {
		return service.getDefectCounts(pName, almType);
	}

	@RequestMapping(value = "/getRelease", method = GET, produces = APPLICATION_JSON_VALUE)
	public ReleaseDetails getRelease(@RequestParam("pName") String projectName) {
		return service.getRelease(projectName);
	}

	@RequestMapping(value = "/getUnReleaseData", produces = APPLICATION_JSON_VALUE, method = RequestMethod.GET)
	public List<MetricsModel> getUnReleaseData(@RequestParam("project") String project,
			@RequestParam("almType") String almType) {
		List<MetricsModel> response = service.getUnReleaseData(project, almType);
		return response;
	}

	@RequestMapping(value = "/getDefects", produces = APPLICATION_JSON_VALUE, method = RequestMethod.GET)
	public List<MetricsModel> getDefects(@RequestParam("project") String projectName,
			@RequestParam("almType") String almType, @RequestParam("defect") String defect) {
		List<MetricsModel> response = service.getDefects(projectName, almType, defect);
		return response;
	}

	@RequestMapping(value = "/slaData", produces = APPLICATION_JSON_VALUE, method = RequestMethod.GET)
	public List<IterationOutModel> getSlaData(@RequestParam("project") String projectName,
			@RequestParam("almType") String almType, @RequestParam("createDate") long createDate) {
		return service.getSlaData(projectName, almType, createDate);
	}
	@RequestMapping(value = "/assigneeAlm", produces = APPLICATION_JSON_VALUE, method = RequestMethod.GET)
	public List<IterationOutModel> getAssigneeIssues(@RequestParam("project") String projectName,
			@RequestParam("almType") String almType, @RequestParam("members") String[] members) {
		return service.getAssigneeIssues(projectName, almType, members);
	}

	@RequestMapping(value = "/dateIteration", produces = APPLICATION_JSON_VALUE, method = RequestMethod.GET)
	public List<IterationOutModel> getDateIterations(@RequestParam("project") String projectName,
			@RequestParam("almType") String almType, @RequestParam("startDate") long startDate,
			@RequestParam("endDate") long endDate) {
		return service.getDateIterations(projectName, almType, startDate, endDate);
	}

	@RequestMapping(value = "/getProdDefects", produces = APPLICATION_JSON_VALUE, method = RequestMethod.GET)
	public List<MetricsModel> getProdDefects(@RequestParam("project") String projectName,
			@RequestParam("almType") String almType, @RequestParam("defect") String defect,
			@RequestParam("whereFound") String whereFound, @RequestParam("whenFound") String whenFound) {
		List<MetricsModel> response = service.getProdDefects(projectName, almType, defect, whereFound, whenFound);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/getAlmType", method = GET, produces = APPLICATION_JSON_VALUE)
	public String getAlmType(@RequestParam("pName") String pName) {
		String almType = "";
		ConfigurationSetting config = DataConfig.getContext().getBean(ConfigurationSettingRep.class)
				.findByProjectName(pName).get(0);
		Set<ConfigurationToolInfoMetric> metric = config.getMetrics();
		Iterator iter = metric.iterator();
		while (iter.hasNext()) {
			Object configuration1 = iter.next();
			ConfigurationToolInfoMetric metric1 = (ConfigurationToolInfoMetric) configuration1;
			if ("Jira".equals(metric1.getToolName())
					|| ("Jira Defects".equals(metric1.getToolName()) && !((metric1.getUrl()).equals("")))) {
				almType = "JIRA";
				break;
			} else if ("Jira Defects".equals(metric1.getToolName()) && !((metric1.getUrl()).equals(""))) {
				almType = "JIRA DEFECTS";
				break;
			}
			 else if ("JIRA-KANBAN".equals(metric1.getToolName()) && !((metric1.getUrl()).equals(""))) {
					almType = "JIRA-KANBAN";
					break;
				}else {
				almType = "TFS";
			}
		}
		return almType;
	}

	
	@RequestMapping(value = "/getVelocityChart", produces = APPLICATION_JSON_VALUE, method = RequestMethod.GET)
	public List<ComponentVelocityList> getVelocityChart(@RequestParam("project") String projectName) {
		return service.getComponentVelocityChart(projectName,false);
	}
	
	
	@RequestMapping(value = "/getIssueHierarchy", produces = APPLICATION_JSON_VALUE, method = RequestMethod.GET)
	public Map<String, List> getIssueHierarchy(@RequestParam("project") String projectName) {
		Map<String, List> response=  service.getIssueHierarchyChart(projectName);
		return response;
	}
	
	
	@RequestMapping(value = "/getComponentWiseIssueHierarchy", produces = APPLICATION_JSON_VALUE, method = RequestMethod.GET)
	public Map<String, Map> getComponentWiseIssueHierarchy(@RequestParam("project") String projectName) {
		Map<String, Map> response=  service.getComponentWiseIssueHierarchyChart(projectName);
		return response;
	}
	
	
	@RequestMapping(value = "/getComponentWiseVelocityChart", produces = APPLICATION_JSON_VALUE, method = RequestMethod.GET)
	public List<ComponentVelocityList> getComponentWiseVelocityChart(@RequestParam("project") String projectName) {
		List<ComponentVelocityList> resp=service.getComponentVelocityChart(projectName,true);
		return resp;
	}
	
	
	@RequestMapping(value = "/getComponontWiseSprintWiseStories", produces = APPLICATION_JSON_VALUE, method = RequestMethod.GET)
	public List<ComponentSprintWiseStories> getComponontWiseSprintWiseStories(@RequestParam("project") String projectName) {
		List<ComponentSprintWiseStories> response = service.getComponentsSprintStories(projectName);
		return response;
	}
	
	@RequestMapping(value = "/getComponents", produces = APPLICATION_JSON_VALUE, method = RequestMethod.GET)
	public List<String> getComponents(@RequestParam("project") String projectName) {
		List<String> response = service.getComponentsChart(projectName);
		return response;
	}
	
	@RequestMapping(value = "/updateComponent", produces = APPLICATION_JSON_VALUE, method = RequestMethod.POST)
	public void updateComponent(@RequestParam("project") String projectName) {
		service.updateComponentsOfTaskandSubtask(projectName);
		
	}
	
	@RequestMapping(value = "/saveEngScore", method = GET, produces = APPLICATION_JSON_VALUE)
	public String saveEngScore(@RequestParam("pName")String projectName,@RequestParam("month")String month,@RequestParam("engScore")double engScore){
	
		return service.saveEngScore(projectName,month,engScore);
		
	}
	
	@RequestMapping(value = "/getFeatureMetrics", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<MonogOutMetrics>  getFeatureMetrics(@RequestParam("pName") String pName,
			@RequestParam("almType") String almType) {
		return service.getFeatureMetrics(pName, almType);
	}
	
	/*
	 * public String getAlmType(String projectName) { 
	 * ConfigurationSetting config =
	 * DataConfig.getContext().getBean(ConfigurationSettingRep.class)
	 * .findByProjectName(projectName); Set<ConfigurationToolInfoMetric> metric
	 * = config.getMetrics(); Iterator iter = metric.iterator(); while
	 * (iter.hasNext()) { Object configuration1 = iter.next();
	 * ConfigurationToolInfoMetric metric1 = (ConfigurationToolInfoMetric)
	 * configuration1; if ("Jira".equals(metric1.getToolName()) || (
	 * "Jira Defects".equals(metric1.getToolName()) && metric1.getUrl() == ""))
	 * { almType = "JIRA"; break; } else if ("Jira Defects"
	 * .equals(metric1.getToolName()) && metric1.getUrl() != "") { almType =
	 * "JIRA DEFECTS"; break; } else { almType = "TFS"; } }
	 */
}
