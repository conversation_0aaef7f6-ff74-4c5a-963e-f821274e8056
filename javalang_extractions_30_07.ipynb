{"cells": [{"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["# v7\n", "\n", "import os\n", "import javalang\n", "from collections import defaultdict\n", "import json\n", "\n", "def extract_relations_to_json_javalang(project_path):\n", "    nodes = {}  # node_id -> metadata\n", "    relations = []\n", "    collection_entities = {}  # full class name → collection\n", "    file_uses_collections = defaultdict(set)\n", "    class_uses_classes = defaultdict(set)\n", "\n", "    def register_node(raw_node, file_path):\n", "        if raw_node and raw_node not in nodes:\n", "            node_type, full_name = raw_node.split(\":\", 1)\n", "            if node_type == \"file\" or \".\" not in full_name:\n", "                short_name = os.path.basename(full_name)\n", "            else:\n", "                short_name = full_name.split(\".\")[-1] if \".\" in full_name else full_name\n", "\n", "            nodes[raw_node] = {\n", "                \"id\": raw_node,\n", "                \"type\": node_type,\n", "                \"name\": short_name,\n", "                \"full_name\": full_name,\n", "                \"file_path\": file_path\n", "            }\n", "\n", "    def add_relation(src, rel, dst, file_path):\n", "        if src is None or dst is None:\n", "            return\n", "        register_node(src, file_path)\n", "        register_node(dst, file_path)\n", "        relations.append([src, rel, dst])\n", "\n", "    java_files = []\n", "    for root, dirs, files in os.walk(project_path):\n", "        rel_root = os.path.relpath(root, project_path)\n", "        rel_root = os.path.join(project_path, rel_root)\n", "        folder_node = f\"folder:{rel_root}\" if rel_root != \".\" else f\"folder:{os.path.basename(project_path)}\"\n", "\n", "        for d in dirs:\n", "            subfolder_rel = os.path.relpath(os.path.join(root, d), project_path)\n", "            subfolder_rel = os.path.join(project_path, subfolder_rel)\n", "            folder_path = os.path.dirname(subfolder_rel)\n", "            subfolder_node = f\"folder:{subfolder_rel}\"\n", "\n", "            add_relation(folder_node, \"contains\", subfolder_node, rel_root)\n", "\n", "        for file in files:\n", "            if file.endswith(\".java\"):\n", "                java_files.append(os.path.join(root, file))\n", "\n", "    parsed_files = {}\n", "    for file_path in java_files:\n", "        with open(file_path, \"r\", encoding=\"utf-8\") as f:\n", "            try:\n", "                parsed_files[file_path] = javalang.parse.parse(f.read())\n", "            except javalang.parser.JavaSyntaxError:\n", "                continue\n", "\n", "    for file_path, tree in parsed_files.items():\n", "        import_map = {}\n", "        package_name = tree.package.name if tree.package else None\n", "\n", "        for imp in tree.imports:\n", "            if imp.path and not imp.wildcard and not imp.path.startswith((\"java.\", \"javax.\")):\n", "                class_name = imp.path.split('.')[-1]\n", "                import_map[class_name] = imp.path\n", "\n", "        for type_decl in tree.types:\n", "            if not isinstance(type_decl, javalang.tree.ClassDeclaration):\n", "                continue\n", "\n", "            class_name = type_decl.name\n", "            full_class_name = f\"{package_name}.{class_name}\" if package_name else class_name\n", "            class_node = f\"class:{full_class_name}\"\n", "            for annotation in type_decl.annotations:\n", "                if annotation.name == \"Document\":\n", "                    for pair in annotation.element:\n", "                        if pair.name == \"collection\":\n", "                            collection = pair.value.value\n", "                            collection_entities[full_class_name] = collection\n", "                            rel_path = os.path.relpath(file_path, project_path)\n", "                            add_relation(class_node, \"mapped_to_collection\", f\"collection:{collection}\", rel_path)\n", "\n", "    for file_path, tree in parsed_files.items():\n", "        rel_path = os.path.relpath(file_path, project_path)\n", "        rel_path = os.path.join(project_path, rel_path)\n", "        folder_path = os.path.dirname(rel_path)\n", "        folder_node = f\"folder:{folder_path}\" if folder_path != \".\" else f\"folder:{project_path}\"\n", "        file_node = f\"file:{rel_path}\"\n", "\n", "        add_relation(folder_node, \"contains\", file_node, rel_path)\n", "\n", "        import_map = {}\n", "        package_name = tree.package.name if tree.package else None\n", "\n", "        for imp in tree.imports:\n", "            if imp.path and not imp.wildcard and not imp.path.startswith((\"java.\", \"javax.\")):\n", "                class_name = imp.path.split('.')[-1]\n", "                import_map[class_name] = imp.path\n", "\n", "        for type_decl in tree.types:\n", "            if not isinstance(type_decl, (javalang.tree.ClassDeclaration, javalang.tree.InterfaceDeclaration)):\n", "                continue\n", "\n", "            decl_type = \"class\" if isinstance(type_decl, javalang.tree.ClassDeclaration) else \"interface\"\n", "            full_decl_name = f\"{package_name}.{type_decl.name}\" if package_name else type_decl.name\n", "            decl_node = f\"{decl_type}:{full_decl_name}\"\n", "            add_relation(file_node, \"declares\", decl_node, rel_path)\n", "\n", "            # Handle implements (class → interface)\n", "            if isinstance(type_decl, javalang.tree.ClassDeclaration) and type_decl.implements:\n", "                for impl in type_decl.implements:\n", "                    interface_name = impl.name\n", "                    if interface_name in import_map:\n", "                        impl_full = import_map[interface_name]\n", "                        add_relation(decl_node, \"implements\", f\"interface:{impl_full}\", rel_path)\n", "\n", "            # Handle extends (class or interface)\n", "            if type_decl.extends:\n", "                if isinstance(type_decl.extends, list):\n", "                    for ext in type_decl.extends:\n", "                        if ext.name in import_map:\n", "                            ext_full = import_map[ext.name]\n", "                            add_relation(decl_node, \"extends\", f\"{decl_type}:{ext_full}\", rel_path)\n", "                else:\n", "                    ext = type_decl.extends\n", "                    if ext.name in import_map:\n", "                        ext_full = import_map[ext.name]\n", "                        add_relation(decl_node, \"extends\", f\"{decl_type}:{ext_full}\", rel_path)\n", "\n", "            for field in getattr(type_decl, \"fields\", []):\n", "                for decl in field.declarators:\n", "                    var_name = decl.name\n", "                    var_node = f\"variable:{full_decl_name}.{var_name}\"\n", "                    add_relation(decl_node, \"has_variable\", var_node, rel_path)\n", "\n", "                    if hasattr(field.type, 'name') and field.type.name in import_map:\n", "                        imp_class = import_map[field.type.name]\n", "                        # add_relation(decl_node, \"uses_class\", f\"class:{imp_class}\", rel_path)\n", "                        class_uses_classes[full_decl_name].add(imp_class)\n", "\n", "                        if imp_class in collection_entities:\n", "                            collection = collection_entities[imp_class]\n", "                            add_relation(var_node, \"uses_collection\", f\"collection:{collection}\", rel_path)\n", "\n", "            for method in getattr(type_decl, \"methods\", []):\n", "                method_node = f\"method:{full_decl_name}.{method.name}\"\n", "                add_relation(decl_node, \"has_method\", method_node, rel_path)\n", "\n", "                if not method.body:\n", "                    continue\n", "\n", "                declared_var_types = {}\n", "\n", "\n", "                # local variabels\n", "                for path, node in method:\n", "                    if isinstance(node, javalang.tree.LocalVariableDeclaration):\n", "                        for decl in node.declarators:\n", "                            var_name = decl.name\n", "                            var_node = f\"variable:{full_decl_name}.{method.name}.{var_name}\"\n", "                            add_relation(method_node, \"uses\", var_node, rel_path)\n", "\n", "                            if hasattr(node.type, 'name'):\n", "                                type_name = node.type.name\n", "                                declared_var_types[var_name] = type_name\n", "                                if type_name in import_map:\n", "                                    imp_class = import_map[type_name]\n", "                                    add_relation(var_node, \"instance_of\", f\"class:{imp_class}\", rel_path)\n", "                                    class_uses_classes[full_decl_name].add(imp_class)\n", "\n", "                                if hasattr(node.type, 'arguments') and node.type.arguments:\n", "                                    for arg in node.type.arguments:\n", "                                        if hasattr(arg, 'type') and hasattr(arg.type, 'name'):\n", "                                            generic_type = arg.type.name\n", "                                            if generic_type in import_map:\n", "                                                imp_class = import_map[generic_type]\n", "                                                add_relation(var_node, \"instance_of\", f\"class:{imp_class}\", rel_path)\n", "\n", "                    elif isinstance(node, javalang.tree.MethodInvocation):\n", "                        skip_standard = False\n", "                        called_method_node = None\n", "                        if node.qualifier and node.arguments:\n", "                            qualifier = node.qualifier\n", "                            for arg in node.arguments:\n", "                                if isinstance(arg, javalang.tree.MemberReference):\n", "                                    src = f\"variable:{full_decl_name}.{method.name}.{arg.member}\"\n", "                                    dst = f\"variable:{full_decl_name}.{method.name}.{qualifier}\"\n", "                                    add_relation(src, \"flows_to\", dst, rel_path)\n", "\n", "                        if node.qualifier and node.qualifier in declared_var_types:\n", "                            type_name = declared_var_types[node.qualifier]\n", "                            if type_name in import_map:\n", "                                imp_class = import_map[type_name]\n", "                                called_method_node = f\"method:{imp_class}.{node.member}\"\n", "                            else:\n", "                                skip_standard = True\n", "                        elif node.qualifier:\n", "                            if node.qualifier[0].islower():\n", "                                skip_standard = True\n", "                            else:\n", "                                called_method_node = f\"method:{node.qualifier}.{node.member}\"\n", "                        else:\n", "                            called_method_node = f\"method:{full_decl_name}.{node.member}\"\n", "\n", "                        if not skip_standard and called_method_node:\n", "                            add_relation(method_node, \"calls\", called_method_node, rel_path)\n", "\n", "                        for arg in node.arguments:\n", "                            if isinstance(arg, javalang.tree.MemberReference):\n", "                                var_node = f\"variable:{full_decl_name}.{method.name}.{arg.member}\"\n", "                            elif isinstance(arg, str):\n", "                                var_node = f\"literal:{arg}\"\n", "                            else:\n", "                                continue\n", "                            add_relation(called_method_node, \"uses\", var_node, rel_path)\n", "\n", "                        if node.qualifier:\n", "                            qualifier_var = f\"variable:{full_decl_name}.{method.name}.{node.qualifier}\"\n", "                            if node.qualifier in declared_var_types:\n", "                                type_name = declared_var_types[node.qualifier]\n", "                                if type_name in import_map:\n", "                                    imp_class = import_map[type_name]\n", "                                    add_relation(qualifier_var, \"instance_of\", f\"class:{imp_class}\", rel_path)\n", "                                    class_uses_classes[full_decl_name].add(imp_class)\n", "\n", "                    elif isinstance(node, javalang.tree.Assignment):\n", "                        left = getattr(node, 'expressionl', None) or getattr(node, 'left', None)\n", "                        right = node.value\n", "\n", "                        if isinstance(left, javalang.tree.MemberReference):\n", "                            left_name = left.member\n", "                        elif isinstance(left, str):\n", "                            left_name = left\n", "                        else:\n", "                            continue\n", "\n", "                        left_var = f\"variable:{full_decl_name}.{method.name}.{left_name}\"\n", "                        add_relation(method_node, \"declares\", left_var, rel_path)\n", "\n", "                        if isinstance(right, javalang.tree.MethodInvocation):\n", "                            if right.qualifier:\n", "                                called_method_node = f\"method:{right.qualifier}.{right.member}\"\n", "                            else:\n", "                                called_method_node = f\"method:{full_decl_name}.{right.member}\"\n", "                            add_relation(called_method_node, \"assigns\", left_var, rel_path)\n", "                            add_relation(method_node, \"calls\", called_method_node, rel_path)\n", "\n", "    return {\n", "        \"nodes\": list(nodes.values()),\n", "        \"relations\": relations\n", "    }\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["project_path = \"OneInsights\"\n", "graph_data = extract_relations_to_json_javalang(project_path)\n", "\n", "\n", "\n", "with open(json_path, \"w\") as f:\n", "    json.dump(graph_data, f, indent=2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Push to neo4j"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import json\n", "from neo4j import GraphDatabase\n", "\n", "def load_json_to_neo4j(json_path, uri, user, password, db):\n", "    # Load JSON content\n", "    with open(json_path, 'r', encoding='utf-8') as f:\n", "        data = json.load(f)\n", "    \n", "    nodes = data[\"nodes\"]\n", "    relations = data[\"relations\"]\n", "\n", "    driver = GraphDatabase.driver(uri, auth=(user, password))\n", "\n", "    nodes = data[\"nodes\"]\n", "    relations = data[\"relations\"]\n", "\n", "    driver = GraphDatabase.driver(uri, auth=(user, password))\n", "\n", "    def import_data(tx):\n", "        # Create typed nodes\n", "        for node in nodes:\n", "            label = node[\"type\"].capitalize()  # e.g., 'class' → 'Class'\n", "            tx.run(f\"\"\"\n", "                MERGE (n:{label} {{id: $id}})\n", "                SET n.name = $name,\n", "                    n.full_name = $full_name\n", "            \"\"\", id=node[\"id\"], name=node[\"name\"], full_name=node[\"full_name\"])\n", "\n", "        # Create typed relationships\n", "        for src_id, rel_type, dst_id in relations:\n", "            # Use dynamic Cypher for relationship type\n", "            tx.run(f\"\"\"\n", "                MATCH (src {{id: $src_id}})\n", "                MATCH (dst {{id: $dst_id}})\n", "                MERGE (src)-[:`{rel_type}`]->(dst)\n", "            \"\"\", src_id=src_id, dst_id=dst_id)\n", "\n", "\n", "    def clean_db(tx):\n", "        tx.run(\"MATCH (n) DETACH DELETE n\")\n", "\n", "\n", "    with driver.session(database=db) as session:\n", "        session.execute_write(clean_db)\n", "        session.execute_write(import_data)\n", "\n", "    driver.close()\n", "    print(\"Graph imported to Neo4j.\")\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Neo4j Configuration\n", "NEO4J_URI = \"bolt://localhost:7687\"\n", "NEO4J_USER = \"neo4j\"\n", "NEO4J_PASSWORD = \"Test@7889\"\n", "NEO4J_DB = \"final\"\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Graph imported to Neo4j.\n"]}], "source": ["json_path = \"graph_30_07_final.json\"\n", "\n", "load_json_to_neo4j(\n", "    json_path=json_path,               \n", "    uri=NEO4J_URI,          \n", "    user=\"neo4j\",                    \n", "    password=NEO4J_PASSWORD,\n", "    db= \"final\"\n", ")\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 2}