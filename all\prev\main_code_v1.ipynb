{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"id": "Pd0Yx704vBJN"}, "outputs": [], "source": ["!pip install --upgrade --quiet  langchain langchain-community langchain-groq neo4j  Neo4jGraph"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "1OlKFMd3vHxq"}, "outputs": [], "source": ["NEO4J_URI=\"neo4j+s://8510420b.databases.neo4j.io\"\n", "NEO4J_USERNAME=\"neo4j\"\n", "NEO4J_PASSWORD=\"G9kUgNUG0DO6aVtjDZ6yxT0Wlj7i4HJ1qHG5KbkoG4U\"\n", "NEO4J_DATABASE=\"neo4j\""]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "Ol0ZTdfHvNei"}, "outputs": [], "source": ["import os\n", "os.environ[\"NEO4J_URI\"]=NEO4J_URI\n", "os.environ[\"NEO4J_USERNAME\"]=NEO4J_USERNAME\n", "os.environ[\"NEO4J_PASSWORD\"]=NEO4J_PASSWORD"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "amvrpcKHvPdj"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_11296\\**********.py:2: LangChainDeprecationWarning: The class `Neo4jGraph` was deprecated in LangChain 0.3.8 and will be removed in 1.0. An updated version of the class exists in the :class:`~langchain-neo4j package and should be used instead. To use it run `pip install -U :class:`~langchain-neo4j` and import as `from :class:`~langchain_neo4j import Neo4jGraph``.\n", "  graph=Neo4jGraph(\n", "Unable to retrieve routing information\n"]}, {"ename": "ValueError", "evalue": "Could not connect to Neo4j database. Please ensure that the url is correct", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mServiceUnavailable\u001b[39m                        <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\langchain_community\\graphs\\neo4j_graph.py:415\u001b[39m, in \u001b[36mNeo4jGraph.__init__\u001b[39m\u001b[34m(self, url, username, password, database, timeout, sanitize, refresh_schema, driver_config, enhanced_schema)\u001b[39m\n\u001b[32m    414\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m415\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_driver\u001b[49m\u001b[43m.\u001b[49m\u001b[43mverify_connectivity\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    416\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m neo4j.exceptions.ServiceUnavailable:\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\neo4j\\_sync\\driver.py:1082\u001b[39m, in \u001b[36mDriver.verify_connectivity\u001b[39m\u001b[34m(self, **config)\u001b[39m\n\u001b[32m   1081\u001b[39m session_config = \u001b[38;5;28mself\u001b[39m._read_session_config(config)\n\u001b[32m-> \u001b[39m\u001b[32m1082\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_get_server_info\u001b[49m\u001b[43m(\u001b[49m\u001b[43msession_config\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\neo4j\\_sync\\driver.py:1297\u001b[39m, in \u001b[36mDriver._get_server_info\u001b[39m\u001b[34m(self, session_config)\u001b[39m\n\u001b[32m   1296\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m._session(session_config) \u001b[38;5;28;01mas\u001b[39;00m session:\n\u001b[32m-> \u001b[39m\u001b[32m1297\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43msession\u001b[49m\u001b[43m.\u001b[49m\u001b[43m_get_server_info\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\neo4j\\_sync\\work\\session.py:183\u001b[39m, in \u001b[36mSession._get_server_info\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    182\u001b[39m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m._connection\n\u001b[32m--> \u001b[39m\u001b[32m183\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_connect\u001b[49m\u001b[43m(\u001b[49m\u001b[43mREAD_ACCESS\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mliveness_check_timeout\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m0\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m    184\u001b[39m server_info = \u001b[38;5;28mself\u001b[39m._connection.server_info\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\neo4j\\_sync\\work\\session.py:136\u001b[39m, in \u001b[36mSession._connect\u001b[39m\u001b[34m(self, access_mode, **acquire_kwargs)\u001b[39m\n\u001b[32m    135\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m136\u001b[39m     \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43m_connect\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    137\u001b[39m \u001b[43m        \u001b[49m\u001b[43maccess_mode\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mauth\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_config\u001b[49m\u001b[43m.\u001b[49m\u001b[43mauth\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43macquire_kwargs\u001b[49m\n\u001b[32m    138\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    139\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m asyncio.CancelledError:\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\neo4j\\_sync\\work\\workspace.py:186\u001b[39m, in \u001b[36mWorkspace._connect\u001b[39m\u001b[34m(self, access_mode, auth, **acquire_kwargs)\u001b[39m\n\u001b[32m    185\u001b[39m ssr_enabled = \u001b[38;5;28mself\u001b[39m._pool.ssr_enabled\n\u001b[32m--> \u001b[39m\u001b[32m186\u001b[39m target_db = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_get_routing_target_database\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    187\u001b[39m \u001b[43m    \u001b[49m\u001b[43macquire_auth\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mssr_enabled\u001b[49m\u001b[43m=\u001b[49m\u001b[43mssr_enabled\u001b[49m\n\u001b[32m    188\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    189\u001b[39m acquire_kwargs_ = {\n\u001b[32m    190\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33maccess_mode\u001b[39m\u001b[33m\"\u001b[39m: access_mode,\n\u001b[32m    191\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mtimeout\u001b[39m\u001b[33m\"\u001b[39m: acquisition_timeout,\n\u001b[32m   (...)\u001b[39m\u001b[32m    196\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mdatabase_callback\u001b[39m\u001b[33m\"\u001b[39m: \u001b[38;5;28mself\u001b[39m._make_db_resolution_callback(),\n\u001b[32m    197\u001b[39m }\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\neo4j\\_sync\\work\\workspace.py:260\u001b[39m, in \u001b[36mWorkspace._get_routing_target_database\u001b[39m\u001b[34m(self, acquire_auth, ssr_enabled)\u001b[39m\n\u001b[32m    259\u001b[39m log.debug(\u001b[33m\"\u001b[39m\u001b[33m[#0000]  _: <WORKSPACE> resolve home database\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m--> \u001b[39m\u001b[32m260\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_pool\u001b[49m\u001b[43m.\u001b[49m\u001b[43mupdate_routing_table\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    261\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdatabase\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_config\u001b[49m\u001b[43m.\u001b[49m\u001b[43mdatabase\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    262\u001b[39m \u001b[43m    \u001b[49m\u001b[43mimp_user\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_config\u001b[49m\u001b[43m.\u001b[49m\u001b[43mimpersonated_user\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    263\u001b[39m \u001b[43m    \u001b[49m\u001b[43mbookmarks\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_get_bookmarks\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    264\u001b[39m \u001b[43m    \u001b[49m\u001b[43mauth\u001b[49m\u001b[43m=\u001b[49m\u001b[43macquire_auth\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    265\u001b[39m \u001b[43m    \u001b[49m\u001b[43macquisition_timeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43macquisition_timeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    266\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdatabase_callback\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_make_db_resolution_callback\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    267\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    268\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m AcquisitionDatabase(\u001b[38;5;28mself\u001b[39m._config.database)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\neo4j\\_sync\\io\\_pool.py:1011\u001b[39m, in \u001b[36mNeo4jPool.update_routing_table\u001b[39m\u001b[34m(self, database, imp_user, bookmarks, auth, acquisition_timeout, database_callback)\u001b[39m\n\u001b[32m   1010\u001b[39m log.error(\u001b[33m\"\u001b[39m\u001b[33mUnable to retrieve routing information\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m-> \u001b[39m\u001b[32m1011\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m ServiceUnavailable(\u001b[33m\"\u001b[39m\u001b[33mUnable to retrieve routing information\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[31mServiceUnavailable\u001b[39m: Unable to retrieve routing information", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[3]\u001b[39m\u001b[32m, line 2\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mlangchain_community\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mgraphs\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mi<PERSON><PERSON>\u001b[39;00m Neo4jGraph\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m graph=\u001b[43mNeo4jGraph\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m      3\u001b[39m \u001b[43m    \u001b[49m\u001b[43murl\u001b[49m\u001b[43m=\u001b[49m\u001b[43mNEO4J_URI\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m      4\u001b[39m \u001b[43m    \u001b[49m\u001b[43musername\u001b[49m\u001b[43m=\u001b[49m\u001b[43mNEO4J_USERNAME\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m      5\u001b[39m \u001b[43m    \u001b[49m\u001b[43mpassword\u001b[49m\u001b[43m=\u001b[49m\u001b[43mNEO4J_PASSWORD\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m      6\u001b[39m \u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\langchain_core\\_api\\deprecation.py:222\u001b[39m, in \u001b[36mdeprecated.<locals>.deprecate.<locals>.finalize.<locals>.warn_if_direct_instance\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m    220\u001b[39m     warned = \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[32m    221\u001b[39m     emit_warning()\n\u001b[32m--> \u001b[39m\u001b[32m222\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mwrapped\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\langchain_community\\graphs\\neo4j_graph.py:417\u001b[39m, in \u001b[36mNeo4jGraph.__init__\u001b[39m\u001b[34m(self, url, username, password, database, timeout, sanitize, refresh_schema, driver_config, enhanced_schema)\u001b[39m\n\u001b[32m    415\u001b[39m     \u001b[38;5;28mself\u001b[39m._driver.verify_connectivity()\n\u001b[32m    416\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m neo4j.exceptions.ServiceUnavailable:\n\u001b[32m--> \u001b[39m\u001b[32m417\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[32m    418\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mCould not connect to Neo4j database. \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    419\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mPlease ensure that the url is correct\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    420\u001b[39m     )\n\u001b[32m    421\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m neo4j.exceptions.AuthError:\n\u001b[32m    422\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[32m    423\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mCould not connect to Neo4j database. \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    424\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mPlease ensure that the username and password are correct\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    425\u001b[39m     )\n", "\u001b[31mValueError\u001b[39m: Could not connect to Neo4j database. Please ensure that the url is correct"]}], "source": ["from langchain_community.graphs import Neo4jGraph\n", "graph=Neo4jGraph(\n", "    url=NEO4J_URI,\n", "    username=NEO4J_USERNAME,\n", "    password=NEO4J_PASSWORD,\n", ")"]}, {"cell_type": "code", "execution_count": 59, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "aX6iwOxavSU5", "outputId": "8eb1c181-04de-4091-8d82-e0d7b9e5cfd6"}, "outputs": [{"data": {"text/plain": ["<langchain_community.graphs.neo4j_graph.Neo4jGraph at 0x7d11002e0dd0>"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["graph"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "DDLV-UMevUqJ"}, "outputs": [], "source": ["groq_api_key=\"********************************************************\""]}, {"cell_type": "code", "execution_count": 61, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "5Dk7NMQZvZER", "outputId": "959346d0-248d-4e56-b63d-3eafba29ca59"}, "outputs": [{"data": {"text/plain": ["ChatGroq(client=<groq.resources.chat.completions.Completions object at 0x7d11004a0450>, async_client=<groq.resources.chat.completions.AsyncCompletions object at 0x7d11004a2b90>, model_name='Gemma2-9b-It', model_kwargs={}, groq_api_key=SecretStr('**********'))"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_groq import ChatGroq\n", "\n", "llm=ChatGroq(groq_api_key=groq_api_key,model_name=\"Gemma2-9b-It\")\n", "llm"]}, {"cell_type": "code", "execution_count": 62, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "JFAwYKCkvcGu", "outputId": "8791086f-3890-4b41-f269-885ad359b59b"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- Document 1 ---\n", "Metadata: {'source': 'app.py', 'language': <Language.PYTHON: 'python'>}\n", "from flask import Flask, render_template, jsonify, request\n", "from flask_cors import CORS\n", "from flask_limiter import Limiter\n", "from flask_limiter.util import get_remote_address\n", "import redis\n", "from langchain_chroma import Chroma\n", "from langchain_google_genai import ChatGoogleGenerativeAI\n", "from langchain.memory  ...\n", "\n", "--- Document 2 ---\n", "Metadata: {'source': 'app.py', 'language': <Language.PYTHON: 'python'>}\n", "# Register the cleanup function\n", "import atexit\n", "atexit.register(cleanup_on_shutdown)\n", "\n", "def remove_readonly(func, path, _):\n", "    \"\"\"Clear the readonly bit and reattempt the removal\"\"\"\n", "    try:\n", "        os.chmod(path, stat.S_IWRITE)\n", "        func(path)\n", "    except Exception as e:\n", "        logger.error(f\"Error ...\n", "\n", "--- Document 3 ---\n", "Metadata: {'source': 'app.py', 'language': <Language.PYTHON: 'python'>}\n", "# Force garbage collection to release file handles\n", "        import gc\n", "        gc.collect()\n", "\n", "        if os.path.exists(\"repo\"):\n", "            logger.info(\"Removing existing repo directory\")\n", "            shutil.rmtree(\"repo\", onerror=force_remove_readonly)\n", "\n", "        if os.path.exists(\"db\"):\n", "            log ...\n", "\n"]}], "source": ["!pip install --upgrade --quiet langchain langchain_community\n", "\n", "\n", "from langchain_community.document_loaders.generic import GenericLoader\n", "from langchain_community.document_loaders.parsers.language.language_parser import LanguageParser\n", "from langchain.text_splitter import Language\n", "from langchain_core.documents import Document\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "\n", "def load_and_chunk_code_files(chunk_size=1000, chunk_overlap=200):\n", "    # Loader for Python files\n", "    python_loader = GenericLoader.from_filesystem(\n", "        \".\",\n", "        glob=\"**/*.py\",\n", "        parser=LanguageParser(language=Language.PYTHON, parser_threshold=500)\n", "    )\n", "    python_documents = python_loader.load()\n", "\n", "    # Loader for Java files\n", "    java_loader = GenericLoader.from_filesystem(\n", "        \".\",\n", "        glob=\"**/*.java\",\n", "        parser=LanguageParser(language=Language.JAVA, parser_threshold=500)\n", "    )\n", "    java_documents = java_loader.load()\n", "\n", "    all_documents = python_documents + java_documents\n", "\n", "    # Apply chunking\n", "    text_splitter = RecursiveCharacterTextSplitter(\n", "        chunk_size=chunk_size,\n", "        chunk_overlap=chunk_overlap\n", "    )\n", "\n", "    chunked_documents = text_splitter.split_documents(all_documents)\n", "\n", "    return chunked_documents\n", "\n", "# Load and chunk\n", "documents = load_and_chunk_code_files()\n", "\n", "# Check results\n", "for i, doc in enumerate(documents[:3], 1):\n", "    print(f\"--- Document {i} ---\")\n", "    print(\"Metadata:\", doc.metadata)\n", "    print(doc.page_content[:300], \"...\\n\")\n"]}, {"cell_type": "code", "execution_count": 63, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "rspGC8TxwSdm", "outputId": "8e86b21b-05e2-4f63-d101-59dca144c970"}, "outputs": [{"data": {"text/plain": ["[Document(metadata={'source': 'app.py', 'language': <Language.PYTHON: 'python'>}, page_content='from flask import Flask, render_template, jsonify, request\\nfrom flask_cors import CORS\\nfrom flask_limiter import Limiter\\nfrom flask_limiter.util import get_remote_address\\nimport redis\\nfrom langchain_chroma import Chroma\\nfrom langchain_google_genai import ChatGoogleGenerativeAI\\nfrom langchain.memory import ConversationBufferMemory\\nfrom langchain.chains import ConversationalRetrievalChain\\nfrom src.helper import load_embedding, repo_ingestion\\nfrom dotenv import load_dotenv\\nimport os\\nimport logging\\nimport shutil\\nimport stat\\n\\n# Configure logging\\nlogging.basicConfig(level=logging.INFO)\\nlogger = logging.getLogger(__name__)\\n\\n# Ensure the application properly closes resources on shutdown\\ndef cleanup_on_shutdown():\\n    try:\\n        if os.path.exists(\"db\"):\\n            import chromadb\\n            # Close any open ChromaDB instances\\n            client = chromadb.Client()\\n            client.reset()\\n    except Exception as e:\\n        logger.error(f\"Error during shutdown cleanup: {str(e)}\")'),\n", " Document(metadata={'source': 'app.py', 'language': <Language.PYTHON: 'python'>}, page_content='# Register the cleanup function\\nimport atexit\\natexit.register(cleanup_on_shutdown)\\n\\ndef remove_readonly(func, path, _):\\n    \"\"\"Clear the readonly bit and reattempt the removal\"\"\"\\n    try:\\n        os.chmod(path, stat.S_IWRITE)\\n        func(path)\\n    except Exception as e:\\n        logger.error(f\"Error removing readonly file {path}: {str(e)}\")\\n\\ndef cleanup_directories():\\n    \"\"\"Clean up directories with better error handling for Git repositories\"\"\"\\n    def force_remove_readonly(func, path, _):\\n        try:\\n            os.chmod(path, stat.S_IWRITE)\\n            func(path)\\n        except Exception as e:\\n            logger.warning(f\"Could not remove {path}: {str(e)}\")\\n\\n    try:\\n        # Force close any open database connections\\n        import sqlite3\\n        try:\\n            sqlite3.connect(\\'db/chroma.sqlite3\\').close()\\n        except:\\n            pass\\n\\n        # Force garbage collection to release file handles\\n        import gc\\n        gc.collect()'),\n", " Document(metadata={'source': 'app.py', 'language': <Language.PYTHON: 'python'>}, page_content='# Force garbage collection to release file handles\\n        import gc\\n        gc.collect()\\n\\n        if os.path.exists(\"repo\"):\\n            logger.info(\"Removing existing repo directory\")\\n            shutil.rmtree(\"repo\", onerror=force_remove_readonly)\\n\\n        if os.path.exists(\"db\"):\\n            logger.info(\"Removing existing db directory\")\\n            # Wait a moment for any file handles to be released\\n            import time\\n            time.sleep(1)\\n            shutil.rmtree(\"db\", onerror=force_remove_readonly)\\n\\n    except Exception as e:\\n        logger.error(f\"Error during cleanup: {str(e)}\")\\n        # Continue execution even if cleanup fails\\n        pass\\n\\n# Run cleanup at startup\\ncleanup_directories()\\n\\n# Load environment variables\\nload_dotenv()\\nGOOGLE_API_KEY = os.getenv(\\'GOOGLE_API_KEY\\')\\n\\nif not GOOGLE_API_KEY:\\n    raise ValueError(\"GOOGLE_API_KEY environment variable is not set\")\\n\\n# Initialize Flask app and limiter\\napp = Flask(__name__)\\nCORS(app)'),\n", " Document(metadata={'source': 'app.py', 'language': <Language.PYTHON: 'python'>}, page_content='if not GOOGLE_API_KEY:\\n    raise ValueError(\"GOOGLE_API_KEY environment variable is not set\")\\n\\n# Initialize Flask app and limiter\\napp = Flask(__name__)\\nCORS(app)\\n\\n# Simple in-memory limiter configuration\\nlimiter = Limiter(\\n    app=app,\\n    key_func=get_remote_address,\\n    default_limits=[\"200 per day\", \"50 per hour\"],\\n    storage_options={\"STORAGE_BACKEND\": \"memory\"}\\n)\\n\\ndef validate_github_url(url):\\n    \"\"\"Validate if the provided URL is a GitHub repository URL.\"\"\"\\n    if not isinstance(url, str):\\n        return False\\n    url = url.lower()\\n    return url.startswith((\\'http://github.com/\\', \\'https://github.com/\\')) and len(url.split(\\'/\\')) >= 5'),\n", " Document(metadata={'source': 'app.py', 'language': <Language.PYTHON: 'python'>}, page_content='def initialize_chat_model():\\n    \"\"\"Initialize the chat model and vector store.\"\"\"\\n    try:\\n        embeddings = load_embedding()\\n        vectordb = Chroma(\\n            persist_directory=\"db\",\\n            embedding_function=embeddings\\n        )\\n        \\n        llm = ChatGoogleGenerativeAI(\\n            model=\"gemini-2.0-flash-lite\",\\n            temperature=0.6,\\n            google_api_key=GOOGLE_API_KEY,\\n            max_output_tokens=2048  # Add token limit\\n        )\\n        \\n        memory = ConversationBufferMemory(\\n            memory_key=\"chat_history\",\\n            return_messages=True,\\n            output_key=\"answer\"  # Specify output key\\n        )\\n        \\n        qa_chain = ConversationalRetrievalChain.from_llm(\\n            llm=llm,\\n            retriever=vectordb.as_retriever(\\n                search_type=\"mmr\",\\n                search_kwargs={\"k\": 4, \"fetch_k\": 20}  # Adjust retrieval parameters\\n            ),\\n            memory=memory,'),\n", " Document(metadata={'source': 'app.py', 'language': <Language.PYTHON: 'python'>}, page_content='search_type=\"mmr\",\\n                search_kwargs={\"k\": 4, \"fetch_k\": 20}  # Adjust retrieval parameters\\n            ),\\n            memory=memory,\\n            return_source_documents=True,  # Include source documents\\n            verbose=True\\n        )\\n        return qa_chain\\n    except Exception as e:\\n        logger.error(f\"Error initializing chat model: {str(e)}\")\\n        raise'),\n", " Document(metadata={'source': 'app.py', 'language': <Language.PYTHON: 'python'>}, page_content='@app.route(\\'/\\')\\ndef index():\\n    return render_template(\\'index.html\\')\\n\\<EMAIL>(\\'/chatbot\\', methods=[\"POST\"])\\<EMAIL>(\"10 per minute\")\\ndef process_repository():\\n    try:\\n        repo_url = request.form.get(\\'question\\')\\n        \\n        if not repo_url:\\n            return jsonify({\"error\": \"Repository URL is required\"}), 400\\n        \\n        if not validate_github_url(repo_url):\\n            return jsonify({\"error\": \"Invalid GitHub repository URL\"}), 400\\n\\n        # Extract repo name from URL\\n        repo_name = repo_url.split(\\'/\\')[-1]\\n        if repo_name.endswith(\\'.git\\'):\\n            repo_name = repo_name[:-4]\\n\\n        # Clear existing repo and db if they exist\\n        cleanup_directories()'),\n", " Document(metadata={'source': 'app.py', 'language': <Language.PYTHON: 'python'>}, page_content='# Clear existing repo and db if they exist\\n        cleanup_directories()\\n\\n        # Clone and process the repository\\n        repo_ingestion(repo_url)\\n        \\n        # Initialize the vector store\\n        try:\\n            os.system(\"python store_index.py\")\\n        except Exception as e:\\n            logger.error(f\"Error creating index: {str(e)}\")\\n            return jsonify({\"error\": \"Failed to create search index\"}), 500\\n\\n        return jsonify({\\n            \"success\": True, \\n            \"message\": \"Repository processed successfully\",\\n            \"repo_name\": repo_name\\n        })\\n\\n    except Exception as e:\\n        logger.error(f\"Error processing repository: {str(e)}\")\\n        cleanup_directories()  # Cleanup on error\\n        return jsonify({\"error\": \"Failed to process repository. Please try again.\"}), 500'),\n", " Document(metadata={'source': 'app.py', 'language': <Language.PYTHON: 'python'>}, page_content='@app.route(\"/get\", methods=[\"POST\"])\\<EMAIL>(\"30 per minute\")\\ndef chat():\\n    try:\\n        message = request.form.get(\\'msg\\')\\n        \\n        if not message:\\n            return jsonify({\"error\": \"Message is required\"}), 400\\n\\n        if not os.path.exists(\"db\"):\\n            return jsonify({\"error\": \"Please analyze a repository first\"}), 400\\n\\n        qa_chain = initialize_chat_model()\\n        result = qa_chain.invoke({\\n            \"question\": message\\n        })\\n        \\n        # Extract the answer from the result\\n        answer = result.get(\\'answer\\', \\'\\')\\n        if not answer:\\n            return jsonify({\"error\": \"No response generated\"}), 500\\n\\n        # Clean and format the response\\n        answer = answer.strip()\\n        return jsonify({\"answer\": answer})\\n\\n    except Exception as e:\\n        logger.error(f\"Error in chat endpoint: {str(e)}\")\\n        return jsonify({\"error\": \"An error occurred while processing your request\"}), 500'),\n", " Document(metadata={'source': 'app.py', 'language': <Language.PYTHON: 'python'>}, page_content='@app.route(\"/get_current_repo\", methods=[\"GET\"])\\ndef get_current_repo():\\n    try:\\n        if os.path.exists(\"repo\"):\\n            # Get the repository name from the cloned URL\\n            with open(\"repo/.git/config\", \"r\") as f:\\n                config = f.read()\\n                # Extract repo name from the URL\\n                for line in config.split(\\'\\\\n\\'):\\n                    if \\'url = \\' in line:\\n                        url = line.strip().split(\\'url = \\')[1]\\n                        repo_name = url.split(\\'/\\')[-1]\\n                        if repo_name.endswith(\\'.git\\'):\\n                            repo_name = repo_name[:-4]\\n                        return jsonify({\"repo_name\": repo_name})\\n        return jsonify({\"repo_name\": None})\\n    except Exception as e:\\n        logger.error(f\"Error getting repo name: {str(e)}\")\\n        return jsonify({\"error\": \"Failed to get repo name\"}), 500'),\n", " Document(metadata={'source': 'app.py', 'language': <Language.PYTHON: 'python'>}, page_content='@app.route(\"/clear_cache\", methods=[\"POST\"])\\ndef clear_cache():\\n    try:\\n        def force_remove_readonly(func, path, _):\\n            os.chmod(path, stat.S_IWRITE)\\n            if os.path.exists(path):\\n                func(path)\\n\\n        # Close any open file handles in the db directory\\n        if os.path.exists(\"db\"):\\n            try:\\n                import gc\\n                gc.collect()  # Force garbage collection\\n                shutil.rmtree(\"db\", onerror=force_remove_readonly)\\n            except Exception as e:\\n                logger.warning(f\"Error removing db directory: {str(e)}\")\\n\\n        # Handle Git repository cleanup\\n        if os.path.exists(\"repo\"):\\n            try:\\n                # Force close Git repository\\n                import git\\n                try:\\n                    repo = git.Repo(\"repo\")\\n                    repo.git.gc()\\n                    repo.close()\\n                except:\\n                    pass'),\n", " Document(metadata={'source': 'app.py', 'language': <Language.PYTHON: 'python'>}, page_content='# Wait for handles to be released\\n                import time\\n                time.sleep(1)\\n\\n                # Remove read-only attributes recursively\\n                for root, dirs, files in os.walk(\"repo\", topdown=False):\\n                    for name in files:\\n                        try:\\n                            file_path = os.path.join(root, name)\\n                            os.chmod(file_path, stat.S_IWRITE)\\n                        except:\\n                            pass\\n                    for name in dirs:\\n                        try:\\n                            dir_path = os.path.join(root, name)\\n                            os.chmod(dir_path, stat.S_IWRITE)\\n                        except:\\n                            pass\\n\\n                shutil.rmtree(\"repo\", onerror=force_remove_readonly)\\n            except Exception as e:\\n                logger.warning(f\"Error removing repo directory: {str(e)}\")'),\n", " Document(metadata={'source': 'app.py', 'language': <Language.PYTHON: 'python'>}, page_content='shutil.rmtree(\"repo\", onerror=force_remove_readonly)\\n            except Exception as e:\\n                logger.warning(f\"Error removing repo directory: {str(e)}\")\\n\\n        return jsonify({\"message\": \"<PERSON><PERSON> cleared successfully\"}), 200\\n    except Exception as e:\\n        logger.error(f\"Error clearing cache: {str(e)}\")\\n        return jsonify({\"error\": \"Failed to clear cache\"}), 500\\n\\nif __name__ == \\'__main__\\':\\n    app.run(host=\"0.0.0.0\", port=8080, debug=True)'),\n", " Document(metadata={'source': 'test_python.py', 'language': <Language.PYTHON: 'python'>}, page_content=\"import os\\nimport sys\\nimport json\\nfrom datetime import datetime\\n\\nclass DataLoader:\\n    def __init__(self, filepath):\\n        self.filepath = filepath\\n\\n    def load_json(self):\\n        with open(self.filepath, 'r') as f:\\n            data = json.load(f)\\n        return data\\n\\nclass DataProcessor:\\n    def __init__(self, data):\\n        self.data = data\\n\\n    def filter_data(self, key, value):\\n        return [item for item in self.data if item.get(key) == value]\\n\\n    def transform_data(self):\\n        for item in self.data:\\n            item['processed_at'] = datetime.now().isoformat()\\n        return self.data\\n\\ndef save_data(data, output_path):\\n    with open(output_path, 'w') as f:\\n        json.dump(data, f, indent=4)\\n\\ndef main():\\n    loader = DataLoader('data/input.json')\\n    data = loader.load_json()\\n    processor = DataProcessor(data)\\n    filtered = processor.filter_data('status', 'active')\\n    transformed = processor.transform_data()\\n    save_data(transformed, 'data/output.json')\"),\n", " Document(metadata={'source': 'test_python.py', 'language': <Language.PYTHON: 'python'>}, page_content=\"if __name__ == '__main__':\\n    main()\"),\n", " Document(metadata={'source': 'helper.py', 'language': <Language.PYTHON: 'python'>}, page_content='import os\\nimport logging\\nfrom git import Repo\\nfrom langchain_community.document_loaders.generic import GenericLoader\\nfrom langchain_community.document_loaders.parsers.language.language_parser import LanguageParser\\nfrom langchain.text_splitter import Language\\nfrom langchain.text_splitter import RecursiveCharacterTextSplitter\\nfrom langchain_google_genai import GoogleGenerativeAIEmbeddings\\n\\nfrom dotenv import load_dotenv\\nload_dotenv()\\n\\nGOOGLE_API_KEY = os.environ.get(\\'GOOGLE_API_KEY\\')\\nos.environ[\"GOOGLE_API_KEY\"] = GOOGLE_API_KEY\\n\\n\\n\\n#clone any github repositories \\ndef repo_ingestion(repo_url):\\n    try:\\n        os.makedirs(\"repo\", exist_ok=True)\\n        repo_path = \"repo/\"\\n        Repo.clone_from(repo_url, to_path=repo_path)\\n    except Exception as e:\\n        logging.error(f\"Failed to clone repository: {str(e)}\")\\n        raise'),\n", " Document(metadata={'source': 'helper.py', 'language': <Language.PYTHON: 'python'>}, page_content='#Loading repositories as documents\\ndef load_repo(repo_path):\\n    try:\\n        loader = GenericLoader.from_filesystem(repo_path,\\n                                        glob = \"**/*\",\\n                                       suffixes=[\".py\"],\\n                                       parser = LanguageParser(language=Language.PYTHON, parser_threshold=500)\\n                                        )\\n    except Exception as e:\\n        logging.error(f\"Failed to clone repository: {str(e)}\")\\n        raise\\n    \\n    documents = loader.load()\\n\\n    return documents'),\n", " Document(metadata={'source': 'helper.py', 'language': <Language.PYTHON: 'python'>}, page_content='return documents\\n\\n#Creating text chunks \\ndef text_splitter(documents):\\n    try:\\n        documents_splitter = RecursiveCharacterTextSplitter.from_language(language = Language.PYTHON,\\n                                                             chunk_size = 2000,\\n                                                             chunk_overlap = 200)\\n    except Exception as e:\\n        logging.error(f\"Failed to clone repository: {str(e)}\")\\n        raise\\n    \\n    text_chunks = documents_splitter.split_documents(documents)\\n\\n    return text_chunks\\n\\n\\n\\n#loading embeddings model\\ndef load_embedding():\\n    try:\\n        embeddings = GoogleGenerativeAIEmbeddings(\\n            model=\"models/embedding-001\",\\n            google_api_key=GOOGLE_API_KEY,\\n            task_type=\"retrieval_query\"\\n        )\\n    except Exception as e:\\n        logging.error(f\"Failed to load embeddings: {str(e)}\")\\n        raise\\n    \\n    return embeddings')]"]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["documents"]}, {"cell_type": "code", "execution_count": 64, "metadata": {"id": "jbC7LFIewjX8"}, "outputs": [], "source": ["!pip install --upgrade --quiet langchain_experimental"]}, {"cell_type": "code", "execution_count": 65, "metadata": {"id": "ERjpR7QVw3Kx"}, "outputs": [], "source": ["from langchain_experimental.graph_transformers import LLMGraphTransformer\n", "llm_transformer=LLMGraphTransformer(llm=llm)"]}, {"cell_type": "code", "execution_count": 66, "metadata": {"id": "EHBQ04hYw4oS"}, "outputs": [], "source": ["graph_documents=llm_transformer.convert_to_graph_documents(documents)"]}, {"cell_type": "code", "execution_count": 67, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "5GQ44_X5w7OM", "outputId": "df3ac156-af6b-40b2-a03c-2175b5cc6c49"}, "outputs": [{"data": {"text/plain": ["[GraphDocument(nodes=[Node(id='Flask', type='Library', properties={}), Node(id='Render_Template', type='Function', properties={}), Node(id='Jsonify', type='Function', properties={}), Node(id='Request', type='Object', properties={}), Node(id='Flask_Cors', type='Library', properties={}), Node(id='Limiter', type='Library', properties={}), Node(id='Get_Remote_Address', type='Function', properties={}), Node(id='Redis', type='Library', properties={}), Node(id='Chroma', type='Library', properties={}), Node(id='Chatgooglegenerativeai', type='Library', properties={}), Node(id='Conversationbuffermemory', type='Class', properties={}), Node(id='Conversationalretrievalchain', type='Class', properties={}), Node(id='Load_Embedding', type='Function', properties={}), Node(id='Repo_Ingestion', type='Function', properties={}), Node(id='Load_Dotenv', type='Function', properties={}), Node(id='Os', type='Library', properties={}), Node(id='Shutil', type='Library', properties={}), Node(id='Stat', type='Library', properties={})], relationships=[Relationship(source=Node(id='Flask', type='Library', properties={}), target=Node(id='Render_Template', type='Function', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='Flask', type='Library', properties={}), target=Node(id='Jsonify', type='Function', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='Flask', type='Library', properties={}), target=Node(id='Request', type='Object', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='Flask', type='Library', properties={}), target=Node(id='Flask_Cors', type='Library', properties={}), type='USES', properties={}), Relationship(source=Node(id='Flask', type='Library', properties={}), target=Node(id='Limiter', type='Library', properties={}), type='USES', properties={}), Relationship(source=Node(id='Limiter', type='Library', properties={}), target=Node(id='Get_Remote_Address', type='Function', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='Flask', type='Library', properties={}), target=Node(id='Redis', type='Library', properties={}), type='USES', properties={}), Relationship(source=Node(id='Flask', type='Library', properties={}), target=Node(id='Chroma', type='Library', properties={}), type='USES', properties={}), Relationship(source=Node(id='Flask', type='Library', properties={}), target=Node(id='Chatgooglegenerativeai', type='Library', properties={}), type='USES', properties={}), Relationship(source=Node(id='Flask', type='Library', properties={}), target=Node(id='Conversationbuffermemory', type='Class', properties={}), type='USES', properties={}), Relationship(source=Node(id='Flask', type='Library', properties={}), target=Node(id='Conversationalretrievalchain', type='Class', properties={}), type='USES', properties={}), Relationship(source=Node(id='Flask', type='Library', properties={}), target=Node(id='Load_Embedding', type='Function', properties={}), type='USES', properties={}), Relationship(source=Node(id='Flask', type='Library', properties={}), target=Node(id='Repo_Ingestion', type='Function', properties={}), type='USES', properties={}), Relationship(source=Node(id='Flask', type='Library', properties={}), target=Node(id='Load_Dotenv', type='Function', properties={}), type='USES', properties={}), Relationship(source=Node(id='Flask', type='Library', properties={}), target=Node(id='Os', type='Library', properties={}), type='USES', properties={}), Relationship(source=Node(id='Flask', type='Library', properties={}), target=Node(id='Shutil', type='Library', properties={}), type='USES', properties={}), Relationship(source=Node(id='Flask', type='Library', properties={}), target=Node(id='Stat', type='Library', properties={}), type='USES', properties={})], source=Document(metadata={'source': 'app.py', 'language': <Language.PYTHON: 'python'>}, page_content='from flask import Flask, render_template, jsonify, request\\nfrom flask_cors import CORS\\nfrom flask_limiter import Limiter\\nfrom flask_limiter.util import get_remote_address\\nimport redis\\nfrom langchain_chroma import Chroma\\nfrom langchain_google_genai import ChatGoogleGenerativeAI\\nfrom langchain.memory import ConversationBufferMemory\\nfrom langchain.chains import ConversationalRetrievalChain\\nfrom src.helper import load_embedding, repo_ingestion\\nfrom dotenv import load_dotenv\\nimport os\\nimport logging\\nimport shutil\\nimport stat\\n\\n# Configure logging\\nlogging.basicConfig(level=logging.INFO)\\nlogger = logging.getLogger(__name__)\\n\\n# Ensure the application properly closes resources on shutdown\\ndef cleanup_on_shutdown():\\n    try:\\n        if os.path.exists(\"db\"):\\n            import chromadb\\n            # Close any open ChromaDB instances\\n            client = chromadb.Client()\\n            client.reset()\\n    except Exception as e:\\n        logger.error(f\"Error during shutdown cleanup: {str(e)}\")')),\n", " GraphDocument(nodes=[Node(id='Atexit', type='Module', properties={}), Node(id='Cleanup_On_Shutdown', type='Function', properties={}), Node(id='Os', type='Module', properties={}), Node(id='Stat', type='Module', properties={}), Node(id='Logger', type='Class', properties={}), Node(id='Sqlite3', type='Module', properties={}), Node(id='Gc', type='Module', properties={})], relationships=[Relationship(source=Node(id='Atexit', type='Module', properties={}), target=Node(id='Cleanup_On_Shutdown', type='Function', properties={}), type='REGISTERED_FUNCTION', properties={}), Relationship(source=Node(id='Cleanup_On_Shutdown', type='Function', properties={}), target=Node(id='Remove_Readonly', type='Function', properties={}), type='CALLS_FUNCTION', properties={}), Relationship(source=Node(id='Remove_Readonly', type='Function', properties={}), target=Node(id='Os', type='Module', properties={}), type='USES_MODULE', properties={}), Relationship(source=Node(id='Remove_Readonly', type='Function', properties={}), target=Node(id='Stat', type='Module', properties={}), type='USES_MODULE', properties={}), Relationship(source=Node(id='Remove_Readonly', type='Function', properties={}), target=Node(id='Logger', type='Class', properties={}), type='USES_CLASS', properties={}), Relationship(source=Node(id='Cleanup_Directories', type='Function', properties={}), target=Node(id='Sqlite3', type='Module', properties={}), type='USES_MODULE', properties={}), Relationship(source=Node(id='Cleanup_Directories', type='Function', properties={}), target=Node(id='Gc', type='Module', properties={}), type='USES_MODULE', properties={})], source=Document(metadata={'source': 'app.py', 'language': <Language.PYTHON: 'python'>}, page_content='# Register the cleanup function\\nimport atexit\\natexit.register(cleanup_on_shutdown)\\n\\ndef remove_readonly(func, path, _):\\n    \"\"\"Clear the readonly bit and reattempt the removal\"\"\"\\n    try:\\n        os.chmod(path, stat.S_IWRITE)\\n        func(path)\\n    except Exception as e:\\n        logger.error(f\"Error removing readonly file {path}: {str(e)}\")\\n\\ndef cleanup_directories():\\n    \"\"\"Clean up directories with better error handling for Git repositories\"\"\"\\n    def force_remove_readonly(func, path, _):\\n        try:\\n            os.chmod(path, stat.S_IWRITE)\\n            func(path)\\n        except Exception as e:\\n            logger.warning(f\"Could not remove {path}: {str(e)}\")\\n\\n    try:\\n        # Force close any open database connections\\n        import sqlite3\\n        try:\\n            sqlite3.connect(\\'db/chroma.sqlite3\\').close()\\n        except:\\n            pass\\n\\n        # Force garbage collection to release file handles\\n        import gc\\n        gc.collect()')),\n", " GraphDocument(nodes=[Node(id='Gc', type='Function', properties={}), Node(id='Os', type='Module', properties={}), Node(id='Shutil', type='Module', properties={}), Node(id='Logger', type='Object', properties={}), Node(id='Force_Remove_Readonly', type='Function', properties={}), Node(id='Time', type='Module', properties={}), Node(id='Flask', type='Class', properties={}), Node(id='Cors', type='Function', properties={}), Node(id='Load_Dotenv', type='Function', properties={}), Node(id='Os', type='Module', properties={}), Node(id='Valueerror', type='Exception', properties={}), Node(id='Cleanup_Directories', type='Function', properties={})], relationships=[Relationship(source=Node(id='Gc', type='Function', properties={}), target=Node(id='Gc.Collect', type='Function', properties={}), type='CALL', properties={}), Relationship(source=Node(id='Os', type='Module', properties={}), target=Node(id='Os.Path.Exists', type='Function', properties={}), type='MEMBER', properties={}), Relationship(source=Node(id='Os', type='Module', properties={}), target=Node(id='Os.Path.Exists', type='Function', properties={}), type='MEMBER', properties={}), Relationship(source=Node(id='Shutil', type='Module', properties={}), target=Node(id='Shutil.Rmtree', type='Function', properties={}), type='MEMBER', properties={}), Relationship(source=Node(id='Logger', type='Object', properties={}), target=Node(id='Logger.Info', type='Function', properties={}), type='MEMBER', properties={}), Relationship(source=Node(id='Time', type='Module', properties={}), target=Node(id='Time.Sleep', type='Function', properties={}), type='MEMBER', properties={}), Relationship(source=Node(id='Flask', type='Class', properties={}), target=Node(id='App', type='Object', properties={}), type='INSTANCE', properties={}), Relationship(source=Node(id='Cors', type='Function', properties={}), target=Node(id='App', type='Object', properties={}), type='METHOD', properties={}), Relationship(source=Node(id='Load_Dotenv', type='Function', properties={}), target=Node(id='None', type='None', properties={}), type='CALL', properties={}), Relationship(source=Node(id='Os', type='Module', properties={}), target=Node(id='Os.Getenv', type='Function', properties={}), type='MEMBER', properties={})], source=Document(metadata={'source': 'app.py', 'language': <Language.PYTHON: 'python'>}, page_content='# Force garbage collection to release file handles\\n        import gc\\n        gc.collect()\\n\\n        if os.path.exists(\"repo\"):\\n            logger.info(\"Removing existing repo directory\")\\n            shutil.rmtree(\"repo\", onerror=force_remove_readonly)\\n\\n        if os.path.exists(\"db\"):\\n            logger.info(\"Removing existing db directory\")\\n            # Wait a moment for any file handles to be released\\n            import time\\n            time.sleep(1)\\n            shutil.rmtree(\"db\", onerror=force_remove_readonly)\\n\\n    except Exception as e:\\n        logger.error(f\"Error during cleanup: {str(e)}\")\\n        # Continue execution even if cleanup fails\\n        pass\\n\\n# Run cleanup at startup\\ncleanup_directories()\\n\\n# Load environment variables\\nload_dotenv()\\nGOOGLE_API_KEY = os.getenv(\\'GOOGLE_API_KEY\\')\\n\\nif not GOOGLE_API_KEY:\\n    raise ValueError(\"GOOGLE_API_KEY environment variable is not set\")\\n\\n# Initialize Flask app and limiter\\napp = Flask(__name__)\\nCORS(app)')),\n", " GraphDocument(nodes=[Node(id='Google_Api_Key', type='String', properties={})], relationships=[Relationship(source=Node(id='Google_Api_Key', type='String', properties={}), target=Node(id='Pending', type='Pending', properties={}), type='USED_IN', properties={})], source=Document(metadata={'source': 'app.py', 'language': <Language.PYTHON: 'python'>}, page_content='if not GOOGLE_API_KEY:\\n    raise ValueError(\"GOOGLE_API_KEY environment variable is not set\")\\n\\n# Initialize Flask app and limiter\\napp = Flask(__name__)\\nCORS(app)\\n\\n# Simple in-memory limiter configuration\\nlimiter = Limiter(\\n    app=app,\\n    key_func=get_remote_address,\\n    default_limits=[\"200 per day\", \"50 per hour\"],\\n    storage_options={\"STORAGE_BACKEND\": \"memory\"}\\n)\\n\\ndef validate_github_url(url):\\n    \"\"\"Validate if the provided URL is a GitHub repository URL.\"\"\"\\n    if not isinstance(url, str):\\n        return False\\n    url = url.lower()\\n    return url.startswith((\\'http://github.com/\\', \\'https://github.com/\\')) and len(url.split(\\'/\\')) >= 5')),\n", " GraphDocument(nodes=[Node(id='Embeddings', type='Function', properties={}), Node(id='Vectordb', type='Object', properties={}), Node(id='Llm', type='Object', properties={}), Node(id='Memory', type='Object', properties={}), Node(id='Qa_Chain', type='Object', properties={})], relationships=[Relationship(source=Node(id='Initialize_Chat_Model', type='Function', properties={}), target=Node(id='Embeddings', type='Function', properties={}), type='CALLS', properties={}), Relationship(source=Node(id='Initialize_Chat_Model', type='Function', properties={}), target=Node(id='Vectordb', type='Object', properties={}), type='CALLS', properties={}), Relationship(source=Node(id='Initialize_Chat_Model', type='Function', properties={}), target=Node(id='Llm', type='Object', properties={}), type='CALLS', properties={}), Relationship(source=Node(id='Initialize_Chat_Model', type='Function', properties={}), target=Node(id='Memory', type='Object', properties={}), type='CALLS', properties={}), Relationship(source=Node(id='Initialize_Chat_Model', type='Function', properties={}), target=Node(id='Qa_Chain', type='Object', properties={}), type='CALLS', properties={}), Relationship(source=Node(id='Llm', type='Object', properties={}), target=Node(id='Qa_Chain', type='Object', properties={}), type='USES', properties={}), Relationship(source=Node(id='Vectordb', type='Object', properties={}), target=Node(id='Qa_Chain', type='Object', properties={}), type='USES', properties={}), Relationship(source=Node(id='Memory', type='Object', properties={}), target=Node(id='Qa_Chain', type='Object', properties={}), type='USES', properties={})], source=Document(metadata={'source': 'app.py', 'language': <Language.PYTHON: 'python'>}, page_content='def initialize_chat_model():\\n    \"\"\"Initialize the chat model and vector store.\"\"\"\\n    try:\\n        embeddings = load_embedding()\\n        vectordb = Chroma(\\n            persist_directory=\"db\",\\n            embedding_function=embeddings\\n        )\\n        \\n        llm = ChatGoogleGenerativeAI(\\n            model=\"gemini-2.0-flash-lite\",\\n            temperature=0.6,\\n            google_api_key=GOOGLE_API_KEY,\\n            max_output_tokens=2048  # Add token limit\\n        )\\n        \\n        memory = ConversationBufferMemory(\\n            memory_key=\"chat_history\",\\n            return_messages=True,\\n            output_key=\"answer\"  # Specify output key\\n        )\\n        \\n        qa_chain = ConversationalRetrievalChain.from_llm(\\n            llm=llm,\\n            retriever=vectordb.as_retriever(\\n                search_type=\"mmr\",\\n                search_kwargs={\"k\": 4, \"fetch_k\": 20}  # Adjust retrieval parameters\\n            ),\\n            memory=memory,')),\n", " GraphDocument(nodes=[Node(id='Search_Type', type='String', properties={}), Node(id='Search_Kwargs', type='Object', properties={}), Node(id='Memory', type='Object', properties={}), Node(id='Return_Source_Documents', type='Boolean', properties={}), Node(id='Verbose', type='Boolean', properties={})], relationships=[], source=Document(metadata={'source': 'app.py', 'language': <Language.PYTHON: 'python'>}, page_content='search_type=\"mmr\",\\n                search_kwargs={\"k\": 4, \"fetch_k\": 20}  # Adjust retrieval parameters\\n            ),\\n            memory=memory,\\n            return_source_documents=True,  # Include source documents\\n            verbose=True\\n        )\\n        return qa_chain\\n    except Exception as e:\\n        logger.error(f\"Error initializing chat model: {str(e)}\")\\n        raise')),\n", " GraphDocument(nodes=[Node(id='Index', type='Function', properties={}), Node(id='Process_Repository', type='Function', properties={}), Node(id='Render_Template', type='Function', properties={}), Node(id='Cleanup_Directories', type='Function', properties={})], relationships=[Relationship(source=Node(id='Index', type='Function', properties={}), target=Node(id='Render_Template', type='Function', properties={}), type='CALLS', properties={}), Relationship(source=Node(id='Process_Repository', type='Function', properties={}), target=Node(id='Cleanup_Directories', type='Function', properties={}), type='CALLS', properties={})], source=Document(metadata={'source': 'app.py', 'language': <Language.PYTHON: 'python'>}, page_content='@app.route(\\'/\\')\\ndef index():\\n    return render_template(\\'index.html\\')\\n\\<EMAIL>(\\'/chatbot\\', methods=[\"POST\"])\\<EMAIL>(\"10 per minute\")\\ndef process_repository():\\n    try:\\n        repo_url = request.form.get(\\'question\\')\\n        \\n        if not repo_url:\\n            return jsonify({\"error\": \"Repository URL is required\"}), 400\\n        \\n        if not validate_github_url(repo_url):\\n            return jsonify({\"error\": \"Invalid GitHub repository URL\"}), 400\\n\\n        # Extract repo name from URL\\n        repo_name = repo_url.split(\\'/\\')[-1]\\n        if repo_name.endswith(\\'.git\\'):\\n            repo_name = repo_name[:-4]\\n\\n        # Clear existing repo and db if they exist\\n        cleanup_directories()')),\n", " GraphDocument(nodes=[Node(id='Repo_Ingestion', type='Function', properties={}), Node(id='Cleanup_Directories', type='Function', properties={}), Node(id='Store_Index.Py', type='File', properties={})], relationships=[Relationship(source=Node(id='Repo_Ingestion', type='Function', properties={}), target=Node(id='Cleanup_Directories', type='Function', properties={}), type='BEFORE', properties={}), Relationship(source=Node(id='Cleanup_Directories', type='Function', properties={}), target=Node(id='Store_Index.Py', type='File', properties={}), type='BEFORE', properties={})], source=Document(metadata={'source': 'app.py', 'language': <Language.PYTHON: 'python'>}, page_content='# Clear existing repo and db if they exist\\n        cleanup_directories()\\n\\n        # Clone and process the repository\\n        repo_ingestion(repo_url)\\n        \\n        # Initialize the vector store\\n        try:\\n            os.system(\"python store_index.py\")\\n        except Exception as e:\\n            logger.error(f\"Error creating index: {str(e)}\")\\n            return jsonify({\"error\": \"Failed to create search index\"}), 500\\n\\n        return jsonify({\\n            \"success\": True, \\n            \"message\": \"Repository processed successfully\",\\n            \"repo_name\": repo_name\\n        })\\n\\n    except Exception as e:\\n        logger.error(f\"Error processing repository: {str(e)}\")\\n        cleanup_directories()  # Cleanup on error\\n        return jsonify({\"error\": \"Failed to process repository. Please try again.\"}), 500')),\n", " GraphDocument(nodes=[Node(id='Chat', type='Function', properties={})], relationships=[Relationship(source=Node(id='Chat', type='Function', properties={}), target=Node(id='Message', type='String', properties={}), type='INPUT', properties={}), Relationship(source=Node(id='Chat', type='Function', properties={}), target=Node(id='Answer', type='String', properties={}), type='OUTPUT', properties={})], source=Document(metadata={'source': 'app.py', 'language': <Language.PYTHON: 'python'>}, page_content='@app.route(\"/get\", methods=[\"POST\"])\\<EMAIL>(\"30 per minute\")\\ndef chat():\\n    try:\\n        message = request.form.get(\\'msg\\')\\n        \\n        if not message:\\n            return jsonify({\"error\": \"Message is required\"}), 400\\n\\n        if not os.path.exists(\"db\"):\\n            return jsonify({\"error\": \"Please analyze a repository first\"}), 400\\n\\n        qa_chain = initialize_chat_model()\\n        result = qa_chain.invoke({\\n            \"question\": message\\n        })\\n        \\n        # Extract the answer from the result\\n        answer = result.get(\\'answer\\', \\'\\')\\n        if not answer:\\n            return jsonify({\"error\": \"No response generated\"}), 500\\n\\n        # Clean and format the response\\n        answer = answer.strip()\\n        return jsonify({\"answer\": answer})\\n\\n    except Exception as e:\\n        logger.error(f\"Error in chat endpoint: {str(e)}\")\\n        return jsonify({\"error\": \"An error occurred while processing your request\"}), 500')),\n", " GraphDocument(nodes=[Node(id='Repo', type='File', properties={})], relationships=[Relationship(source=Node(id='Repo', type='File', properties={}), target=Node(id='Repo_Name', type='String', properties={}), type='CONTAINS', properties={})], source=Document(metadata={'source': 'app.py', 'language': <Language.PYTHON: 'python'>}, page_content='@app.route(\"/get_current_repo\", methods=[\"GET\"])\\ndef get_current_repo():\\n    try:\\n        if os.path.exists(\"repo\"):\\n            # Get the repository name from the cloned URL\\n            with open(\"repo/.git/config\", \"r\") as f:\\n                config = f.read()\\n                # Extract repo name from the URL\\n                for line in config.split(\\'\\\\n\\'):\\n                    if \\'url = \\' in line:\\n                        url = line.strip().split(\\'url = \\')[1]\\n                        repo_name = url.split(\\'/\\')[-1]\\n                        if repo_name.endswith(\\'.git\\'):\\n                            repo_name = repo_name[:-4]\\n                        return jsonify({\"repo_name\": repo_name})\\n        return jsonify({\"repo_name\": None})\\n    except Exception as e:\\n        logger.error(f\"Error getting repo name: {str(e)}\")\\n        return jsonify({\"error\": \"Failed to get repo name\"}), 500')),\n", " GraphDocument(nodes=[Node(id='Clear_Cache', type='Function', properties={})], relationships=[Relationship(source=Node(id='Clear_Cache', type='Function', properties={}), target=Node(id='Db_Directory', type='Directory', properties={}), type='REMOVES', properties={}), Relationship(source=Node(id='Clear_Cache', type='Function', properties={}), target=Node(id='Repo', type='Git_repository', properties={}), type='CLEANS', properties={})], source=Document(metadata={'source': 'app.py', 'language': <Language.PYTHON: 'python'>}, page_content='@app.route(\"/clear_cache\", methods=[\"POST\"])\\ndef clear_cache():\\n    try:\\n        def force_remove_readonly(func, path, _):\\n            os.chmod(path, stat.S_IWRITE)\\n            if os.path.exists(path):\\n                func(path)\\n\\n        # Close any open file handles in the db directory\\n        if os.path.exists(\"db\"):\\n            try:\\n                import gc\\n                gc.collect()  # Force garbage collection\\n                shutil.rmtree(\"db\", onerror=force_remove_readonly)\\n            except Exception as e:\\n                logger.warning(f\"Error removing db directory: {str(e)}\")\\n\\n        # Handle Git repository cleanup\\n        if os.path.exists(\"repo\"):\\n            try:\\n                # Force close Git repository\\n                import git\\n                try:\\n                    repo = git.Repo(\"repo\")\\n                    repo.git.gc()\\n                    repo.close()\\n                except:\\n                    pass')),\n", " GraphDocument(nodes=[Node(id='Repo', type='Directory', properties={})], relationships=[Relationship(source=Node(id='Repo', type='Directory', properties={}), target=Node(id='Os', type='Module', properties={}), type='USES', properties={}), Relationship(source=Node(id='Repo', type='Directory', properties={}), target=Node(id='Shutil', type='Module', properties={}), type='USES', properties={}), Relationship(source=Node(id='Os', type='Module', properties={}), target=Node(id='Os.Chmod', type='Function', properties={}), type='HAS_FUNCTION', properties={}), Relationship(source=Node(id='Shutil', type='Module', properties={}), target=Node(id='Shutil.Rmtree', type='Function', properties={}), type='HAS_FUNCTION', properties={})], source=Document(metadata={'source': 'app.py', 'language': <Language.PYTHON: 'python'>}, page_content='# Wait for handles to be released\\n                import time\\n                time.sleep(1)\\n\\n                # Remove read-only attributes recursively\\n                for root, dirs, files in os.walk(\"repo\", topdown=False):\\n                    for name in files:\\n                        try:\\n                            file_path = os.path.join(root, name)\\n                            os.chmod(file_path, stat.S_IWRITE)\\n                        except:\\n                            pass\\n                    for name in dirs:\\n                        try:\\n                            dir_path = os.path.join(root, name)\\n                            os.chmod(dir_path, stat.S_IWRITE)\\n                        except:\\n                            pass\\n\\n                shutil.rmtree(\"repo\", onerror=force_remove_readonly)\\n            except Exception as e:\\n                logger.warning(f\"Error removing repo directory: {str(e)}\")')),\n", " GraphDocument(nodes=[Node(id='Shutil.Rmtree', type='Function', properties={}), Node(id='Repo', type='Directory', properties={}), Node(id='Onerror', type='Function', properties={}), Node(id='Force_Remove_Readonly', type='Function', properties={}), Node(id='Logger', type='Logger', properties={}), Node(id='Jsonify', type='Function', properties={}), Node(id='Exception', type='Exception', properties={})], relationships=[Relationship(source=Node(id='Shutil.Rmtree', type='Function', properties={}), target=Node(id='Repo', type='Directory', properties={}), type='REMOVES', properties={}), Relationship(source=Node(id='Shutil.Rmtree', type='Function', properties={}), target=Node(id='Onerror', type='Function', properties={}), type='CALLS', properties={}), Relationship(source=Node(id='Onerror', type='Function', properties={}), target=Node(id='Force_Remove_Readonly', type='Function', properties={}), type='CALLS', properties={}), Relationship(source=Node(id='Logger', type='Logger', properties={}), target=Node(id='Exception', type='Exception', properties={}), type='LOGS', properties={}), Relationship(source=Node(id='Jsonify', type='Function', properties={}), target=Node(id='Message', type='String', properties={}), type='CREATES', properties={})], source=Document(metadata={'source': 'app.py', 'language': <Language.PYTHON: 'python'>}, page_content='shutil.rmtree(\"repo\", onerror=force_remove_readonly)\\n            except Exception as e:\\n                logger.warning(f\"Error removing repo directory: {str(e)}\")\\n\\n        return jsonify({\"message\": \"Cache cleared successfully\"}), 200\\n    except Exception as e:\\n        logger.error(f\"Error clearing cache: {str(e)}\")\\n        return jsonify({\"error\": \"Failed to clear cache\"}), 500\\n\\nif __name__ == \\'__main__\\':\\n    app.run(host=\"0.0.0.0\", port=8080, debug=True)')),\n", " GraphDocument(nodes=[Node(id='Dataloader', type='Class', properties={}), Node(id='Dataprocessor', type='Class', properties={}), Node(id='Main', type='Function', properties={}), Node(id='Input.Json', type='File', properties={}), Node(id='Output.Json', type='File', properties={})], relationships=[Relationship(source=Node(id='Dataloader', type='Class', properties={}), target=Node(id='Load_Json', type='Method', properties={}), type='METHOD', properties={}), Relationship(source=Node(id='Dataloader', type='Class', properties={}), target=Node(id='Input.Json', type='File', properties={}), type='LOAD_FROM', properties={}), Relationship(source=Node(id='Dataprocessor', type='Class', properties={}), target=Node(id='Filter_Data', type='Method', properties={}), type='METHOD', properties={}), Relationship(source=Node(id='Dataprocessor', type='Class', properties={}), target=Node(id='Transform_Data', type='Method', properties={}), type='METHOD', properties={}), Relationship(source=Node(id='Main', type='Function', properties={}), target=Node(id='Dataloader', type='Class', properties={}), type='INSTANCE_OF', properties={}), Relationship(source=Node(id='Main', type='Function', properties={}), target=Node(id='Dataprocessor', type='Class', properties={}), type='INSTANCE_OF', properties={}), Relationship(source=Node(id='Main', type='Function', properties={}), target=Node(id='Save_Data', type='Function', properties={}), type='CALL', properties={}), Relationship(source=Node(id='Main', type='Function', properties={}), target=Node(id='Output.Json', type='File', properties={}), type='SAVE_TO', properties={})], source=Document(metadata={'source': 'test_python.py', 'language': <Language.PYTHON: 'python'>}, page_content=\"import os\\nimport sys\\nimport json\\nfrom datetime import datetime\\n\\nclass DataLoader:\\n    def __init__(self, filepath):\\n        self.filepath = filepath\\n\\n    def load_json(self):\\n        with open(self.filepath, 'r') as f:\\n            data = json.load(f)\\n        return data\\n\\nclass DataProcessor:\\n    def __init__(self, data):\\n        self.data = data\\n\\n    def filter_data(self, key, value):\\n        return [item for item in self.data if item.get(key) == value]\\n\\n    def transform_data(self):\\n        for item in self.data:\\n            item['processed_at'] = datetime.now().isoformat()\\n        return self.data\\n\\ndef save_data(data, output_path):\\n    with open(output_path, 'w') as f:\\n        json.dump(data, f, indent=4)\\n\\ndef main():\\n    loader = DataLoader('data/input.json')\\n    data = loader.load_json()\\n    processor = DataProcessor(data)\\n    filtered = processor.filter_data('status', 'active')\\n    transformed = processor.transform_data()\\n    save_data(transformed, 'data/output.json')\")),\n", " GraphDocument(nodes=[], relationships=[], source=Document(metadata={'source': 'test_python.py', 'language': <Language.PYTHON: 'python'>}, page_content=\"if __name__ == '__main__':\\n    main()\")),\n", " GraphDocument(nodes=[Node(id='Repo_Ingestion', type='Function', properties={})], relationships=[Relationship(source=Node(id='Repo_Ingestion', type='Function', properties={}), target=Node(id='Clone_Repository', type='Action', properties={}), type='PERFORMS', properties={})], source=Document(metadata={'source': 'helper.py', 'language': <Language.PYTHON: 'python'>}, page_content='import os\\nimport logging\\nfrom git import Repo\\nfrom langchain_community.document_loaders.generic import GenericLoader\\nfrom langchain_community.document_loaders.parsers.language.language_parser import LanguageParser\\nfrom langchain.text_splitter import Language\\nfrom langchain.text_splitter import RecursiveCharacterTextSplitter\\nfrom langchain_google_genai import GoogleGenerativeAIEmbeddings\\n\\nfrom dotenv import load_dotenv\\nload_dotenv()\\n\\nGOOGLE_API_KEY = os.environ.get(\\'GOOGLE_API_KEY\\')\\nos.environ[\"GOOGLE_API_KEY\"] = GOOGLE_API_KEY\\n\\n\\n\\n#clone any github repositories \\ndef repo_ingestion(repo_url):\\n    try:\\n        os.makedirs(\"repo\", exist_ok=True)\\n        repo_path = \"repo/\"\\n        Repo.clone_from(repo_url, to_path=repo_path)\\n    except Exception as e:\\n        logging.error(f\"Failed to clone repository: {str(e)}\")\\n        raise')),\n", " GraphDocument(nodes=[Node(id='Genericloader', type='Class', properties={}), Node(id='Languageparser', type='Class', properties={}), Node(id='Language', type='Enum', properties={}), Node(id='Genericloader.From_Filesystem', type='Function', properties={}), Node(id='Repo_Path', type='String', properties={}), Node(id='Documents', type='List', properties={})], relationships=[Relationship(source=Node(id='Genericloader', type='Class', properties={}), target=Node(id='Genericloader.From_Filesystem', type='Function', properties={}), type='METHOD', properties={}), Relationship(source=Node(id='Languageparser', type='Class', properties={}), target=Node(id='Language', type='Enum', properties={}), type='USES', properties={}), Relationship(source=Node(id='Genericloader.From_Filesystem', type='Function', properties={}), target=Node(id='Documents', type='List', properties={}), type='RETURNS', properties={})], source=Document(metadata={'source': 'helper.py', 'language': <Language.PYTHON: 'python'>}, page_content='#Loading repositories as documents\\ndef load_repo(repo_path):\\n    try:\\n        loader = GenericLoader.from_filesystem(repo_path,\\n                                        glob = \"**/*\",\\n                                       suffixes=[\".py\"],\\n                                       parser = LanguageParser(language=Language.PYTHON, parser_threshold=500)\\n                                        )\\n    except Exception as e:\\n        logging.error(f\"Failed to clone repository: {str(e)}\")\\n        raise\\n    \\n    documents = loader.load()\\n\\n    return documents')),\n", " GraphDocument(nodes=[Node(id='Documents', type='Text', properties={})], relationships=[Relationship(source=Node(id='Documents', type='Text', properties={}), target=Node(id='Text_Chunks', type='List', properties={}), type='CONTAINS', properties={})], source=Document(metadata={'source': 'helper.py', 'language': <Language.PYTHON: 'python'>}, page_content='return documents\\n\\n#Creating text chunks \\ndef text_splitter(documents):\\n    try:\\n        documents_splitter = RecursiveCharacterTextSplitter.from_language(language = Language.PYTHON,\\n                                                             chunk_size = 2000,\\n                                                             chunk_overlap = 200)\\n    except Exception as e:\\n        logging.error(f\"Failed to clone repository: {str(e)}\")\\n        raise\\n    \\n    text_chunks = documents_splitter.split_documents(documents)\\n\\n    return text_chunks\\n\\n\\n\\n#loading embeddings model\\ndef load_embedding():\\n    try:\\n        embeddings = GoogleGenerativeAIEmbeddings(\\n            model=\"models/embedding-001\",\\n            google_api_key=GOOGLE_API_KEY,\\n            task_type=\"retrieval_query\"\\n        )\\n    except Exception as e:\\n        logging.error(f\"Failed to load embeddings: {str(e)}\")\\n        raise\\n    \\n    return embeddings'))]"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["graph_documents"]}, {"cell_type": "code", "execution_count": 69, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "H-_mXV7vw_0G", "outputId": "122d2323-5c79-4991-f7ff-25d1383d10ac"}, "outputs": [{"data": {"text/plain": ["[Node(id='Flask', type='Library', properties={}),\n", " Node(id='Render_Template', type='Function', properties={}),\n", " Node(id='Jsonify', type='Function', properties={}),\n", " Node(id='Request', type='Object', properties={}),\n", " Node(id='Flask_Cors', type='Library', properties={}),\n", " Node(id='Limiter', type='Library', properties={}),\n", " Node(id='Get_Remote_Address', type='Function', properties={}),\n", " Node(id='Redis', type='Library', properties={}),\n", " Node(id='Chroma', type='Library', properties={}),\n", " Node(id='Chatgooglegenerativeai', type='Library', properties={}),\n", " Node(id='Conversationbuffermemory', type='Class', properties={}),\n", " Node(id='Conversationalretrievalchain', type='Class', properties={}),\n", " Node(id='Load_Embedding', type='Function', properties={}),\n", " Node(id='Repo_Ingestion', type='Function', properties={}),\n", " Node(id='Load_Dotenv', type='Function', properties={}),\n", " Node(id='Os', type='Library', properties={}),\n", " Node(id='Shutil', type='Library', properties={}),\n", " Node(id='Stat', type='Library', properties={})]"]}, "execution_count": 69, "metadata": {}, "output_type": "execute_result"}], "source": ["graph_documents[0].nodes"]}, {"cell_type": "code", "execution_count": 68, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Y1kVFDQDxGoY", "outputId": "4366b88f-c57d-4dc9-e468-5bab846f9521"}, "outputs": [{"data": {"text/plain": ["[Relationship(source=Node(id='Flask', type='Library', properties={}), target=Node(id='Render_Template', type='Function', properties={}), type='CONTAINS', properties={}),\n", " Relationship(source=Node(id='Flask', type='Library', properties={}), target=Node(id='Jsonify', type='Function', properties={}), type='CONTAINS', properties={}),\n", " Relationship(source=Node(id='Flask', type='Library', properties={}), target=Node(id='Request', type='Object', properties={}), type='CONTAINS', properties={}),\n", " Relationship(source=Node(id='Flask', type='Library', properties={}), target=Node(id='Flask_Cors', type='Library', properties={}), type='USES', properties={}),\n", " Relationship(source=Node(id='Flask', type='Library', properties={}), target=Node(id='Limiter', type='Library', properties={}), type='USES', properties={}),\n", " Relationship(source=Node(id='Limiter', type='Library', properties={}), target=Node(id='Get_Remote_Address', type='Function', properties={}), type='CONTAINS', properties={}),\n", " Relationship(source=Node(id='Flask', type='Library', properties={}), target=Node(id='Redis', type='Library', properties={}), type='USES', properties={}),\n", " Relationship(source=Node(id='Flask', type='Library', properties={}), target=Node(id='Chroma', type='Library', properties={}), type='USES', properties={}),\n", " Relationship(source=Node(id='Flask', type='Library', properties={}), target=Node(id='Chatgooglegenerativeai', type='Library', properties={}), type='USES', properties={}),\n", " Relationship(source=Node(id='Flask', type='Library', properties={}), target=Node(id='Conversationbuffermemory', type='Class', properties={}), type='USES', properties={}),\n", " Relationship(source=Node(id='Flask', type='Library', properties={}), target=Node(id='Conversationalretrievalchain', type='Class', properties={}), type='USES', properties={}),\n", " Relationship(source=Node(id='Flask', type='Library', properties={}), target=Node(id='Load_Embedding', type='Function', properties={}), type='USES', properties={}),\n", " Relationship(source=Node(id='Flask', type='Library', properties={}), target=Node(id='Repo_Ingestion', type='Function', properties={}), type='USES', properties={}),\n", " Relationship(source=Node(id='Flask', type='Library', properties={}), target=Node(id='Load_Dotenv', type='Function', properties={}), type='USES', properties={}),\n", " Relationship(source=Node(id='Flask', type='Library', properties={}), target=Node(id='Os', type='Library', properties={}), type='USES', properties={}),\n", " Relationship(source=Node(id='Flask', type='Library', properties={}), target=Node(id='Shutil', type='Library', properties={}), type='USES', properties={}),\n", " Relationship(source=Node(id='Flask', type='Library', properties={}), target=Node(id='Stat', type='Library', properties={}), type='USES', properties={})]"]}, "execution_count": 68, "metadata": {}, "output_type": "execute_result"}], "source": ["graph_documents[0].relationships"]}, {"cell_type": "markdown", "metadata": {"id": "IS3AaO-CxQW1"}, "source": []}, {"cell_type": "code", "execution_count": 70, "metadata": {"id": "D6A5qwlJxIXV"}, "outputs": [], "source": ["graph.add_graph_documents(graph_documents)"]}, {"cell_type": "code", "execution_count": 71, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "r8Olz3YRxf8F", "outputId": "75b48111-b788-4cdc-f096-f668dc65bcce"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Node properties:\n", "Class {id: STRING}\n", "Function {id: STRING}\n", "File {id: STRING}\n", "Data {id: STRING}\n", "Library {id: STRING}\n", "Object {id: STRING}\n", "Module {id: STRING}\n", "Exception {id: STRING}\n", "None {id: STRING}\n", "String {id: STRING}\n", "Pending {id: STRING}\n", "Boolean {id: STRING}\n", "Directory {id: STRING}\n", "Git_repository {id: STRING}\n", "Logger {id: STRING}\n", "Method {id: STRING}\n", "Action {id: STRING}\n", "Enum {id: STRING}\n", "List {id: STRING}\n", "Text {id: STRING}\n", "Relationship properties:\n", "\n", "The relationships:\n", "(:Class)-[:HAS_FUNCTION]->(:Function)\n", "(:Class)-[:READS_FROM]->(:File)\n", "(:Class)-[:LOAD_FROM]->(:File)\n", "(:Class)-[:METHOD]->(:Method)\n", "(:Class)-[:METHOD]->(:Function)\n", "(:Class)-[:PROCESSES]->(:Data)\n", "(:Class)-[:INSTANCE]->(:Object)\n", "(:Class)-[:USES]->(:Enum)\n", "(:Function)-[:CREATES]->(:Class)\n", "(:Function)-[:CREATES]->(:String)\n", "(:Function)-[:INSTANCE_OF]->(:Class)\n", "(:Function)-[:SAVE_TO]->(:File)\n", "(:Function)-[:CALLS]->(:Function)\n", "(:Function)-[:CALLS]->(:Object)\n", "(:Function)-[:CALL]->(:Function)\n", "(:Function)-[:CALL]->(:None)\n", "(:Function)-[:WRITES_TO]->(:File)\n", "(:Function)-[:BEFORE]->(:Function)\n", "(:Function)-[:BEFORE]->(:File)\n", "(:Function)-[:PERFORMS]->(:Action)\n", "(:Function)-[:CALLS_FUNCTION]->(:Function)\n", "(:Function)-[:USES_MODULE]->(:Mo<PERSON><PERSON>)\n", "(:Function)-[:USES_CLASS]->(:Class)\n", "(:Function)-[:METHOD]->(:Object)\n", "(:Function)-[:REMOVES]->(:Directory)\n", "(:Function)-[:INPUT]->(:String)\n", "(:Function)-[:OUTPUT]->(:String)\n", "(:Function)-[:CLEANS]->(:Git_repository)\n", "(:Function)-[:RETURNS]->(:List)\n", "(:File)-[:CONTAINS]->(:String)\n", "(:Library)-[:CONTAINS]->(:Function)\n", "(:Library)-[:CONTAINS]->(:Object)\n", "(:Library)-[:USES]->(:Library)\n", "(:Library)-[:USES]->(:Class)\n", "(:Library)-[:USES]->(:Function)\n", "(:Object)-[:MEMBER]->(:Function)\n", "(:Object)-[:USES]->(:Object)\n", "(:Module)-[:REGISTERED_FUNCTION]->(:Function)\n", "(:Module)-[:HAS_FUNCTION]->(:Function)\n", "(:Module)-[:MEMBER]->(:Function)\n", "(:String)-[:USED_IN]->(:Pending)\n", "(:Directory)-[:USES]->(:Mo<PERSON><PERSON>)\n", "(:Logger)-[:LOGS]->(:Exception)\n", "(:Text)-[:CONTAINS]->(:List)\n"]}], "source": ["graph.refresh_schema()\n", "print(graph.schema)"]}, {"cell_type": "code", "execution_count": 72, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "KQn9UNMnxmNo", "outputId": "6e82fb33-b22f-44c2-f428-442be9806d71"}, "outputs": [{"data": {"text/plain": ["GraphCypherQ<PERSON>hain(verbose=True, graph=<langchain_community.graphs.neo4j_graph.Neo4jGraph object at 0x7d11002e0dd0>, cypher_generation_chain=LLMChain(verbose=False, prompt=PromptTemplate(input_variables=['question', 'schema'], input_types={}, partial_variables={}, template='Task:Generate Cypher statement to query a graph database.\\nInstructions:\\nUse only the provided relationship types and properties in the schema.\\nDo not use any other relationship types or properties that are not provided.\\nSchema:\\n{schema}\\nNote: Do not include any explanations or apologies in your responses.\\nDo not respond to any questions that might ask anything else than for you to construct a Cypher statement.\\nDo not include any text except the generated Cypher statement.\\n\\nThe question is:\\n{question}'), llm=ChatGroq(client=<groq.resources.chat.completions.Completions object at 0x7d11004a0450>, async_client=<groq.resources.chat.completions.AsyncCompletions object at 0x7d11004a2b90>, model_name='Gemma2-9b-It', model_kwargs={}, groq_api_key=SecretStr('**********')), output_parser=StrOutputParser(), llm_kwargs={}), qa_chain=LLMChain(verbose=False, prompt=PromptTemplate(input_variables=['context', 'question'], input_types={}, partial_variables={}, template=\"You are an assistant that helps to form nice and human understandable answers.\\nThe information part contains the provided information that you must use to construct an answer.\\nThe provided information is authoritative, you must never doubt it or try to use your internal knowledge to correct it.\\nMake the answer sound as a response to the question. Do not mention that you based the result on the given information.\\nHere is an example:\\n\\nQuestion: Which managers own Neo4j stocks?\\nContext:[manager:CTL LLC, manager:JANE STREET GROUP LLC]\\nHelpful Answer: CTL LLC, JANE STREET GROUP LLC owns Neo4j stocks.\\n\\nFollow this example when generating answers.\\nIf the provided information is empty, say that you don't know the answer.\\nInformation:\\n{context}\\n\\nQuestion: {question}\\nHelpful Answer:\"), llm=ChatGroq(client=<groq.resources.chat.completions.Completions object at 0x7d11004a0450>, async_client=<groq.resources.chat.completions.AsyncCompletions object at 0x7d11004a2b90>, model_name='Gemma2-9b-It', model_kwargs={}, groq_api_key=SecretStr('**********')), output_parser=StrOutputParser(), llm_kwargs={}), graph_schema='Node properties are the following:\\nClass {id: STRING},Function {id: STRING},File {id: STRING},Data {id: STRING},Library {id: STRING},Object {id: STRING},Module {id: STRING},Exception {id: STRING},None {id: STRING},String {id: STRING},Pending {id: STRING},Boolean {id: STRING},Directory {id: STRING},Git_repository {id: STRING},Logger {id: STRING},Method {id: STRING},Action {id: STRING},Enum {id: STRING},List {id: STRING},Text {id: STRING}\\nRelationship properties are the following:\\n\\nThe relationships are the following:\\n(:Class)-[:HAS_FUNCTION]->(:Function),(:Class)-[:READS_FROM]->(:File),(:Class)-[:LOAD_FROM]->(:File),(:Class)-[:METHOD]->(:Method),(:Class)-[:METHOD]->(:Function),(:Class)-[:PROCESSES]->(:Data),(:Class)-[:INSTANCE]->(:Object),(:Class)-[:USES]->(:Enum),(:Function)-[:CREATES]->(:Class),(:Function)-[:CREATES]->(:String),(:Function)-[:INSTANCE_OF]->(:Class),(:Function)-[:SAVE_TO]->(:File),(:Function)-[:CALLS]->(:Function),(:Function)-[:CALLS]->(:Object),(:Function)-[:CALL]->(:Function),(:Function)-[:CALL]->(:None),(:Function)-[:WRITES_TO]->(:File),(:Function)-[:BEFORE]->(:Function),(:Function)-[:BEFORE]->(:File),(:Function)-[:PERFORMS]->(:Action),(:Function)-[:CALLS_FUNCTION]->(:Function),(:Function)-[:USES_MODULE]->(:Module),(:Function)-[:USES_CLASS]->(:Class),(:Function)-[:METHOD]->(:Object),(:Function)-[:REMOVES]->(:Directory),(:Function)-[:INPUT]->(:String),(:Function)-[:OUTPUT]->(:String),(:Function)-[:CLEANS]->(:Git_repository),(:Function)-[:RETURNS]->(:List),(:File)-[:CONTAINS]->(:String),(:Library)-[:CONTAINS]->(:Function),(:Library)-[:CONTAINS]->(:Object),(:Library)-[:USES]->(:Library),(:Library)-[:USES]->(:Class),(:Library)-[:USES]->(:Function),(:Object)-[:MEMBER]->(:Function),(:Object)-[:USES]->(:Object),(:Module)-[:REGISTERED_FUNCTION]->(:Function),(:Module)-[:HAS_FUNCTION]->(:Function),(:Module)-[:MEMBER]->(:Function),(:String)-[:USED_IN]->(:Pending),(:Directory)-[:USES]->(:Module),(:Logger)-[:LOGS]->(:Exception),(:Text)-[:CONTAINS]->(:List)', allow_dangerous_requests=True)"]}, "execution_count": 72, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain.chains import GraphCypherQAChain\n", "chain=GraphCypherQAChain.from_llm(llm=llm,graph=graph,verbose=True,allow_dangerous_requests=True)\n", "chain"]}, {"cell_type": "code", "execution_count": 73, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ozFqhtIgxzSc", "outputId": "39d09eac-0790-4f1e-aee6-462ce1fc4486"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new GraphCypherQAChain chain...\u001b[0m\n", "Generated Cypher:\n", "\u001b[32;1m\u001b[1;3mMATCH (c:Class)\n", "RETURN COUNT(c) AS numberOfClasses\u001b[0m\n", "Full Context:\n", "\u001b[32;1m\u001b[1;3m[{'numberOfClasses': 9}]\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["{'query': 'how many classes we have', 'result': 'We have 9 classes. \\n'}"]}, "execution_count": 73, "metadata": {}, "output_type": "execute_result"}], "source": ["response=chain.invoke({\"query\":\"how many classes we have\"})\n", "\n", "response"]}, {"cell_type": "code", "execution_count": 74, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "JFGf50Xtx3NK", "outputId": "13af90d2-261d-4698-a5c6-dff8ed18d1cf"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new GraphCypherQAChain chain...\u001b[0m\n", "Generated Cypher:\n", "\u001b[32;1m\u001b[1;3mMATCH (c:Class) RETURN c.id  \n", "\u001b[0m\n", "Full Context:\n", "\u001b[32;1m\u001b[1;3m[{'c.id': 'Dataloader'}, {'c.id': 'Dataprocessor'}, {'c.id': 'Datetime'}, {'c.id': 'Conversationbuffermemory'}, {'c.id': 'Conversationalretrievalchain'}, {'c.id': 'Logger'}, {'c.id': 'Flask'}, {'c.id': 'Genericloader'}, {'c.id': 'Languageparser'}]\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["{'query': 'name of those classes',\n", " 'result': 'Dataloader, Dataprocessor, Datetime, Conversationbuffermemory, Conversationalretrievalchain, Logger, Flask, Genericloader, Languageparser \\n'}"]}, "execution_count": 74, "metadata": {}, "output_type": "execute_result"}], "source": ["response=chain.invoke({\"query\":\"name of those classes\"})\n", "\n", "response"]}, {"cell_type": "code", "execution_count": 75, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "3cxY761gx7RL", "outputId": "64efa23f-129a-458b-f489-faef4cf98153"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new GraphCypherQAChain chain...\u001b[0m\n", "Generated Cypher:\n", "\u001b[32;1m\u001b[1;3mMATCH (f:Function) RETURN f.id, count(f)  \n", "\u001b[0m\n", "Full Context:\n", "\u001b[32;1m\u001b[1;3m[{'f.id': 'Main', 'count(f)': 1}, {'f.id': 'Save_Data', 'count(f)': 1}, {'f.id': 'Filter_Data', 'count(f)': 1}, {'f.id': 'Transform_Data', 'count(f)': 1}, {'f.id': 'Load_Json', 'count(f)': 1}, {'f.id': 'Render_Template', 'count(f)': 1}, {'f.id': 'Jsonify', 'count(f)': 1}, {'f.id': 'Get_Remote_Address', 'count(f)': 1}, {'f.id': 'Load_Embedding', 'count(f)': 1}, {'f.id': 'Repo_Ingestion', 'count(f)': 1}]\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["{'query': 'How many functions do we have and their names',\n", " 'result': 'We have 10 functions: Main, Save_Data, Filter_Data, Transform_Data, Load_Json, Render_Template, Jsonify, Get_Remote_Address, Load_Embedding, and Repo_Ingestion. \\n'}"]}, "execution_count": 75, "metadata": {}, "output_type": "execute_result"}], "source": ["response=chain.invoke({\"query\":\"How many functions do we have and their names\"})\n", "\n", "response"]}, {"cell_type": "code", "execution_count": 76, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "hKnjdcKwyX1G", "outputId": "562c5542-dc9d-4a72-ea57-cd64ce8d4609"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new GraphCypherQAChain chain...\u001b[0m\n", "Generated Cypher:\n", "\u001b[32;1m\u001b[1;3mMATCH (f:Function {id: 'Filter_Data'})-[:PERFORMS]->(a:Action) RETURN a.id\n", "\u001b[0m\n", "Full Context:\n", "\u001b[32;1m\u001b[1;3m[]\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["{'query': 'what is this function Filter_Data doing ',\n", " 'result': \"I don't know the answer. \\n\"}"]}, "execution_count": 76, "metadata": {}, "output_type": "execute_result"}], "source": ["response=chain.invoke({\"query\":\"what is this function Filter_Data doing \"})\n", "\n", "response"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "p2oOBGpMyfoT"}, "outputs": [], "source": []}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 0}