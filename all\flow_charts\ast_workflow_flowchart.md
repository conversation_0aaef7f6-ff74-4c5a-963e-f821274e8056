# 🚀 AST workflow for Java Code Structure Extraction

```mermaid
flowchart TD
    A["🚀 Start: Import Libraries<br/>tree-sitter, tree-sitter-java<br/>neo4j, pandas, tqdm"] --> B["⚙️ Initialize Tree-sitter"]

    B --> C["📂 Walk Java Directory<br/>os.walk through 'java' folder<br/>Find all .java files"]

    C --> D{"📄 For Each Java File?"}

    D --> E["📖 Read Source Code<br/>Open file, read content<br/>Encode to bytes"]

    E --> F["🌳 Parse with Tree-sitter<br/>tree = parser.parse<br/>Get AST root node"]

    F --> G["🔍 Extract Structure<br/>Traverse AST nodes:<br/>• class_declaration → Class<br/>• method_declaration → Method<br/>• method_invocation → Method Call<br/>• variable_declarator → Variable"]

    G --> H["📝 Store Results<br/>Add extracted elements<br/>to all_results list"]

    H --> I{"🔄 More Files?"}
    I -->|Yes| D
    I -->|No| J["📊 Create DataFrame<br/>Convert all_results<br/>to pandas DataFrame"]

    J --> K["🔌 Connect to Neo4j<br/>bolt://127.0.0.1:7687<br/>Test connection"]

    K --> L["🏗️ Build Graph Structure<br/>Create folder hierarchy<br/>Insert files into folders"]

    L --> M["📤 Push Data to Neo4j<br/>• Create Class nodes<br/>• Create Method nodes<br/>• Create DECLARES relationships<br/>• Create CALLS relationships<br/>• Create Variable nodes<br/>• Create USES/HAS_FIELD relationships"]

    M --> N["✅ Complete<br/>Print success message"]

    N --> O["🗑️ Clear Database<br/>MATCH (n) DETACH DELETE n<br/>Clean slate for next run"]

    %% Styling
    style A fill:#e3f2fd,stroke:#1976d2,stroke-width:3px,color:#000
    style B fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    style C fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#000
    style D fill:#fff8e1,stroke:#f57c00,stroke-width:2px,color:#000
    style E fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#000
    style F fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#000
    style G fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#000
    style H fill:#fff8e1,stroke:#f57c00,stroke-width:2px,color:#000
    style I fill:#fff8e1,stroke:#f57c00,stroke-width:2px,color:#000
    style J fill:#fce4ec,stroke:#c2185b,stroke-width:2px,color:#000
    style K fill:#e0f2f1,stroke:#00695c,stroke-width:2px,color:#000
    style L fill:#e0f2f1,stroke:#00695c,stroke-width:2px,color:#000
    style M fill:#e0f2f1,stroke:#00695c,stroke-width:2px,color:#000
    style N fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px,color:#000
    style O fill:#ffebee,stroke:#d32f2f,stroke-width:2px,color:#000
```

## How It Works

1️⃣ **Repository Parsing with Tree-sitter (AST)**  
Uses Tree-sitter Java grammar for deterministic parsing.  
Recursively traverses AST to extract:
- `class_declaration`
- `method_declaration`
- `method_invocation`
- `variable_declarator`  
Builds structured records with type, parent relationships, and file path.

2️⃣ **Directory + File Nodes**  
Captures folder structure via `os.walk`.  
Builds Folder nodes and relationships.  
Creates File nodes for each `.java` file.

3️⃣ **Detailed Code Structure Extraction**  
Extracts entity-level relationships:
- Class declared in File
- Method declared in Class (`Class-[:DECLARES]->Method`)
- Method calls another Method (`Method-[:CALLS]->Method`)
- Variable declared in Class (`Class-[:HAS_FIELD]->Variable`)
- Variable declared in Method (`Method-[:USES]->Variable`)  
No hallucinations—Tree-sitter parses source accurately.

4️⃣ **Neo4j Graph Construction**  
Pushes data using Cypher:
- Creates Class, Method, Variable nodes with `MERGE`
- Creates appropriate relationships
- Uses consistent file and folder context anchoring

**Key Strengths**  
✅ Deterministic extraction with AST  
✅ Captures fine-grained structure reliably  
✅ Handles large codebases scalably  
✅ No reliance on LLM for extraction  
✅ Outputs structured CSV for inspection/debugging  
