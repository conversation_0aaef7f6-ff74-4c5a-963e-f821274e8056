
package com.morganstanley.loanApp.configuration;
import javax.sql.DataSource;

import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;

@Configuration
public class StoredProcDBConfig {
    @Bean
    @Qualifier("dataSource")
    @Primary
    public DataSource primaryDataSource(PrimaryDataSourceProperties props) {
        HikariDataSource ds = new HikariDataSource();
        ds.setJdbcUrl(props.getUrl());
        ds.setUsername(props.getUsername());
        ds.setPassword(props.getPassword());
        ds.setDriverClassName(props.getDriverClassName());

        System.out.println(">>> Primary DataSource initialized with URL: " + props.getUrl());

        return ds;
    }

    @Bean(name = "storeProcDataSource")
    public DataSource storeProcDataSource(SecondDataSourceProperties props) {
        HikariDataSource ds = new HikariDataSource();
        ds.setJdbcUrl(props.getUrl());
        ds.setUsername(props.getUsername());
        ds.setPassword(props.getPassword());
        ds.setDriverClassName(props.getDriverClassName());

        System.out.println(">>> Second DataSource initialized with URL: " + props.getUrl());

        return ds;
    }

    @Bean
    public JdbcTemplate storeProcJdbcTemplate(@Qualifier("storeProcDataSource") DataSource ds) {
        return new JdbcTemplate(ds);
    }
}

