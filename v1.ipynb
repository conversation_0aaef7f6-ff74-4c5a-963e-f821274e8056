# ================== IMPORTS ==================
import os
import re
import javalang
import pandas as pd
import json
import hashlib
from datetime import datetime
from collections import defaultdict, deque
from pathlib import Path
from neo4j import GraphDatabase

# ================== HELPER FUNCTIONS ==================

def normalize_node(node_type, full_name, file_path, class_name=None, method_name=None, extra=None):
    """Standardized node metadata for Neo4j."""
    short_name = os.path.basename(full_name) if node_type in ("file", "folder") else full_name.split(".")[-1]
    if node_type == "file" and short_name.endswith(".java"):
        short_name = short_name[:-5]
    data = {
        "id": f"{node_type}:{full_name}",
        "type": node_type,
        "name": short_name,
        "full_name": full_name,
        "file_path": file_path,
        "class_name": class_name,
        "method_name": method_name
    }
    if extra:
        data.update(extra)
    return data

def add_node(nodes, node_type, full_name, file_path, class_name=None, method_name=None, extra=None):
    node_id = f"{node_type}:{full_name}"
    if node_id not in nodes:
        nodes[node_id] = normalize_node(node_type, full_name, file_path, class_name, method_name, extra)
    return node_id

def add_relation(relations, src, rel, dst):
    if src and dst:
        relations.add((src, rel.upper(), dst))

# ================== ADVANCED LINEAGE TRACKING ==================

class VariableLineageTracker:
    """Track variable transformations and data flow."""
    def __init__(self):
        self.variable_registry = {}  # var_id -> metadata
        self.transformations = []    # transformation chains
        self.data_flows = defaultdict(list)  # source -> [targets]
        self.operation_chains = defaultdict(list)  # method -> [operations]
    
    def register_variable(self, var_name, var_type, scope, file_path, line_num=None):
        var_id = f"{scope}.{var_name}"
        self.variable_registry[var_id] = {
            'name': var_name,
            'type': var_type,
            'scope': scope,
            'file_path': file_path,
            'line_number': line_num,
            'created_at': datetime.now().isoformat()
        }
        return var_id
    
    def add_transformation(self, source_var, target_var, operation, context):
        transformation = {
            'source': source_var,
            'target': target_var,
            'operation': operation,
            'context': context,
            'timestamp': datetime.now().isoformat()
        }
        self.transformations.append(transformation)
        self.data_flows[source_var].append(target_var)
    
    def get_lineage_path(self, var_id):
        """Get complete lineage path for a variable."""
        path = []
        visited = set()
        queue = deque([var_id])
        
        while queue:
            current = queue.popleft()
            if current in visited:
                continue
            visited.add(current)
            path.append(current)
            
            # Add all variables that flow into this one
            for transform in self.transformations:
                if transform['target'] == current and transform['source'] not in visited:
                    queue.append(transform['source'])
        
        return path

class ApplicationNodeExtractor:
    """Extract application-specific nodes and relationships."""
    
    @staticmethod
    def extract_spring_annotations(code):
        """Extract Spring framework annotations and their configurations."""
        annotations = []
        patterns = {
            'controller': r'@(?:RestController|Controller)(?:\([^)]*\))?',
            'service': r'@Service(?:\([^)]*\))?',
            'repository': r'@Repository(?:\([^)]*\))?',
            'component': r'@Component(?:\([^)]*\))?',
            'configuration': r'@Configuration(?:\([^)]*\))?',
            'autowired': r'@Autowired',
            'value': r'@Value\(["\']([^"\']*)["\'\])',
            'qualifier': r'@Qualifier\(["\']([^"\']*)["\'\])',
            'transactional': r'@Transactional(?:\([^)]*\))?',
            'cacheable': r'@Cacheable(?:\([^)]*\))?'
        }
        
        for annotation_type, pattern in patterns.items():
            for match in re.finditer(pattern, code):
                annotations.append({
                    'type': annotation_type,
                    'full_match': match.group(0),
                    'value': match.group(1) if match.groups() else None,
                    'start': match.start(),
                    'end': match.end()
                })
        
        return annotations
    
    @staticmethod
    def extract_jpa_entities(code):
        """Extract JPA entity information."""
        entities = []
        
        # Entity annotations
        entity_pattern = r'@Entity(?:\([^)]*\))?'
        table_pattern = r'@Table\(name\s*=\s*["\']([^"\']*)["\'\]'
        column_pattern = r'@Column\(name\s*=\s*["\']([^"\']*)["\'\]'
        id_pattern = r'@Id'
        
        for match in re.finditer(entity_pattern, code):
            entities.append({
                'type': 'entity',
                'annotation': match.group(0)
            })
        
        # Extract table names
        tables = []
        for match in re.finditer(table_pattern, code):
            tables.append(match.group(1))
        
        # Extract column mappings
        columns = []
        for match in re.finditer(column_pattern, code):
            columns.append(match.group(1))
        
        return {'entities': entities, 'tables': tables, 'columns': columns}
    
    @staticmethod
    def extract_configuration_properties(code):
        """Extract configuration properties and their bindings."""
        properties = []
        
        # Configuration properties
        config_pattern = r'@ConfigurationProperties\(prefix\s*=\s*["\']([^"\']*)["\'\]'
        value_pattern = r'@Value\(["\']\$\{([^}]*)\}["\'\]'
        
        for match in re.finditer(config_pattern, code):
            properties.append({
                'type': 'configuration_prefix',
                'value': match.group(1)
            })
        
        for match in re.finditer(value_pattern, code):
            properties.append({
                'type': 'property_binding',
                'value': match.group(1)
            })
        
        return properties

SQL_STOPWORDS = {
    "select","from","where","group","order","by","join","on","as","and","or",
    "if","then","else","when","end","case","distinct","limit","offset",
    "like","not","null","is","inner","left","right","outer","full","cross"
}

# ================== ENHANCED NODE TYPES ==================

JAVA_NODE_TYPES = {
    'structural': ['package', 'folder', 'file', 'class', 'interface', 'enum', 'annotation'],
    'behavioral': ['method', 'constructor', 'lambda', 'operation', 'condition'],
    'data': ['variable', 'field', 'parameter', 'return_value', 'constant'],
    'persistence': ['table', 'column', 'index', 'constraint', 'view', 'procedure'],
    'integration': ['api_endpoint', 'message_queue', 'cache', 'external_service'],
    'configuration': ['property', 'profile', 'bean', 'component_scan'],
    'security': ['role', 'permission', 'authentication', 'authorization'],
    'monitoring': ['metric', 'log', 'trace', 'health_check']
}

RELATIONSHIP_TYPES = {
    'structural': ['CONTAINS', 'DECLARES', 'EXTENDS', 'IMPLEMENTS', 'IMPORTS'],
    'behavioral': ['CALLS', 'INVOKES', 'RETURNS', 'THROWS', 'HANDLES'],
    'data_flow': ['READS', 'WRITES', 'TRANSFORMS', 'PRODUCES', 'CONSUMES'],
    'dependency': ['DEPENDS_ON', 'INJECTS', 'AUTOWIRES', 'CONFIGURES'],
    'persistence': ['MAPS_TO', 'JOINS', 'REFERENCES', 'CASCADES'],
    'integration': ['CALLS_API', 'PUBLISHES', 'SUBSCRIBES', 'CACHES'],
    'security': ['SECURES', 'AUTHORIZES', 'AUTHENTICATES', 'VALIDATES']
}

# ================== TABLE EXTRACTION ==================

def extract_db_table_usage(code):
    """Enhanced database table usage extraction with detailed operations."""
    operations = {
        'reads': set(),
        'writes': set(),
        'deletes': set(),
        'creates': set(),
        'alters': set()
    }
    
    # Enhanced patterns for different SQL operations
    patterns = [
        # Read operations
        (r'\bFROM\s+([A-Za-z_][\w]*)', operations['reads']),
        (r'\bJOIN\s+([A-Za-z_][\w]*)', operations['reads']),
        (r'@Query\s*\([^)]*["\'][^"\']*(?:SELECT|select).*?(?:FROM|from)\s+([A-Za-z_][\w]*)', operations['reads']),
        
        # Write operations
        (r'\bUPDATE\s+([A-Za-z_][\w]*)', operations['writes']),
        (r'\bINSERT\s+INTO\s+([A-Za-z_][\w]*)', operations['writes']),
        (r'@Modifying.*?UPDATE\s+([A-Za-z_][\w]*)', operations['writes']),
        
        # Delete operations
        (r'\bDELETE\s+FROM\s+([A-Za-z_][\w]*)', operations['deletes']),
        (r'@Modifying.*?DELETE\s+FROM\s+([A-Za-z_][\w]*)', operations['deletes']),
        
        # DDL operations
        (r'\bCREATE\s+TABLE\s+([A-Za-z_][\w]*)', operations['creates']),
        (r'\bALTER\s+TABLE\s+([A-Za-z_][\w]*)', operations['alters'])
    ]
    
    for pattern, target_set in patterns:
        for match in re.findall(pattern, code, re.IGNORECASE | re.DOTALL):
            table = match.strip().lower()
            if len(table) >= 2 and table not in SQL_STOPWORDS and not table.isdigit():
                target_set.add(table)
    
    # Extract JPA repository methods
    jpa_patterns = [
        (r'findBy([A-Za-z]+)', operations['reads']),
        (r'save\(([A-Za-z]+)', operations['writes']),
        (r'delete\(([A-Za-z]+)', operations['deletes'])
    ]
    
    for pattern, target_set in jpa_patterns:
        for match in re.findall(pattern, code):
            entity = match.strip().lower()
            if len(entity) >= 2:
                target_set.add(f"entity_{entity}")
    
    return operations

def extract_api_endpoints(code):
    """Enhanced API endpoint extraction with HTTP methods and parameters."""
    endpoints = []
    
    # Spring REST mappings with HTTP methods
    mapping_patterns = {
        'GET': r'@GetMapping\(["\']([^"\']*)["\'\]\)',
        'POST': r'@PostMapping\(["\']([^"\']*)["\'\]\)',
        'PUT': r'@PutMapping\(["\']([^"\']*)["\'\]\)',
        'DELETE': r'@DeleteMapping\(["\']([^"\']*)["\'\]\)',
        'PATCH': r'@PatchMapping\(["\']([^"\']*)["\'\]\)',
        'REQUEST': r'@RequestMapping\([^)]*value\s*=\s*["\']([^"\']*)["\'\]'
    }
    
    for method, pattern in mapping_patterns.items():
        for match in re.finditer(pattern, code):
            path = match.group(1)
            if path:
                endpoints.append({
                    'path': path,
                    'method': method,
                    'type': 'rest_endpoint'
                })
    
    # Extract path variables
    path_var_pattern = r'@PathVariable\(["\']?([^"\')]*)["\']?\)'
    path_variables = re.findall(path_var_pattern, code)
    
    # Extract request parameters
    request_param_pattern = r'@RequestParam\(["\']?([^"\')]*)["\']?\)'
    request_params = re.findall(request_param_pattern, code)
    
    # Extract external API calls
    external_api_patterns = [
        r'RestTemplate.*?(?:getForObject|postForObject|exchange)\(["\']([^"\']*)["\'\]',
        r'WebClient.*?\.uri\(["\']([^"\']*)["\'\]',
        r'@FeignClient\([^)]*url\s*=\s*["\']([^"\']*)["\'\]'
    ]
    
    for pattern in external_api_patterns:
        for match in re.finditer(pattern, code):
            url = match.group(1)
            endpoints.append({
                'path': url,
                'method': 'EXTERNAL',
                'type': 'external_api_call'
            })
    
    return {
        'endpoints': endpoints,
        'path_variables': path_variables,
        'request_params': request_params
    }

def extract_messaging_patterns(code):
    """Extract message queue and event patterns."""
    messaging = {
        'publishers': [],
        'subscribers': [],
        'topics': [],
        'queues': []
    }
    
    # Spring messaging annotations
    publisher_patterns = [
        r'@EventListener\([^)]*\)',
        r'@RabbitListener\([^)]*queue\s*=\s*["\']([^"\']*)["\'\]',
        r'@KafkaListener\([^)]*topics\s*=\s*["\']([^"\']*)["\'\]'
    ]
    
    for pattern in publisher_patterns:
        matches = re.findall(pattern, code)
        messaging['subscribers'].extend(matches)
    
    # Message publishing
    publish_patterns = [
        r'rabbitTemplate\.send\(["\']([^"\']*)["\'\]',
        r'kafkaTemplate\.send\(["\']([^"\']*)["\'\]',
        r'applicationEventPublisher\.publishEvent'
    ]
    
    for pattern in publish_patterns:
        matches = re.findall(pattern, code)
        messaging['publishers'].extend(matches)
    
    return messaging

# ================== MULTI-STEP EXPRESSION HANDLER ==================

def handle_expression(expr, full_decl_name, method_name, file_path, nodes, relations, lineage_tracker=None):
    """Enhanced expression handler with detailed transformation tracking."""
    
    # Variable reference
    if isinstance(expr, javalang.tree.MemberReference):
        var_id = f"variable:{full_decl_name}.{method_name}.{expr.member}"
        if lineage_tracker:
            lineage_tracker.register_variable(
                expr.member, 'reference', f"{full_decl_name}.{method_name}", file_path
            )
        return var_id

    # Method call -> Operation node with enhanced tracking
    elif isinstance(expr, javalang.tree.MethodInvocation):
        op_name = expr.member
        qualifier_str = str(expr.qualifier) if expr.qualifier else ''
        op_display = f"{qualifier_str+'.' if qualifier_str else ''}{op_name}()"
        op_full = f"{full_decl_name}.{method_name}.{op_display}_op_{id(expr)}"
        
        # Determine operation type
        op_type = 'method_call'
        if op_name in ['get', 'find', 'select', 'query']:
            op_type = 'data_read'
        elif op_name in ['set', 'save', 'update', 'insert', 'delete']:
            op_type = 'data_write'
        elif op_name in ['map', 'filter', 'reduce', 'transform']:
            op_type = 'data_transform'
        
        op_node = add_node(nodes, "operation", op_full, file_path, full_decl_name, method_name, 
                          extra={
                              "expression": op_display,
                              "operation_type": op_type,
                              "method_name": op_name,
                              "qualifier": qualifier_str
                          })

        # Link qualifier variable -> operation
        if expr.qualifier:
            if isinstance(expr.qualifier, javalang.tree.MemberReference):
                src_node = f"variable:{full_decl_name}.{method_name}.{expr.qualifier.member}"
            else:
                src_node = handle_expression(expr.qualifier, full_decl_name, method_name, file_path, nodes, relations, lineage_tracker)
            
            if src_node:
                add_relation(relations, src_node, "TRANSFORMS_TO", op_node)
                if lineage_tracker:
                    lineage_tracker.add_transformation(
                        src_node, op_node, op_name, 
                        {'type': 'method_invocation', 'qualifier': qualifier_str}
                    )

        # Link arguments with detailed tracking
        for i, arg in enumerate(expr.arguments):
            src_node = handle_expression(arg, full_decl_name, method_name, file_path, nodes, relations, lineage_tracker)
            if src_node:
                add_relation(relations, src_node, "INPUTS_TO", op_node)
                if lineage_tracker:
                    lineage_tracker.add_transformation(
                        src_node, op_node, f"argument_{i}",
                        {'type': 'method_argument', 'position': i}
                    )

        return op_node

    # Binary operation with enhanced operator tracking
    elif isinstance(expr, javalang.tree.BinaryOperation):
        op_name = expr.operator
        op_full = f"{full_decl_name}.{method_name}.{op_name}_binop_{id(expr)}"
        
        # Categorize binary operations
        arithmetic_ops = ['+', '-', '*', '/', '%']
        comparison_ops = ['==', '!=', '<', '>', '<=', '>=']
        logical_ops = ['&&', '||', '&', '|', '^']
        
        op_category = 'unknown'
        if op_name in arithmetic_ops:
            op_category = 'arithmetic'
        elif op_name in comparison_ops:
            op_category = 'comparison'
        elif op_name in logical_ops:
            op_category = 'logical'
        
        op_node = add_node(nodes, "operation", op_full, file_path, full_decl_name, method_name, 
                          extra={
                              "expression": op_name,
                              "operation_type": "binary_operation",
                              "operator": op_name,
                              "category": op_category
                          })

        # Process left and right operands
        for side, operand in [('left', expr.operandl), ('right', expr.operandr)]:
            src_node = handle_expression(operand, full_decl_name, method_name, file_path, nodes, relations, lineage_tracker)
            if src_node:
                add_relation(relations, src_node, "TRANSFORMS_TO", op_node)
                if lineage_tracker:
                    lineage_tracker.add_transformation(
                        src_node, op_node, f"{side}_operand",
                        {'type': 'binary_operation', 'operator': op_name, 'side': side}
                    )

        return op_node

    # Literal values
    elif isinstance(expr, javalang.tree.Literal):
        literal_id = f"literal:{full_decl_name}.{method_name}.{expr.value}_{id(expr)}"
        add_node(nodes, "literal", literal_id, file_path, full_decl_name, method_name,
                extra={
                    "value": expr.value,
                    "type": "literal"
                })
        return literal_id

    # Array access
    elif isinstance(expr, javalang.tree.ArraySelector):
        array_access_id = f"array_access:{full_decl_name}.{method_name}_{id(expr)}"
        add_node(nodes, "array_access", array_access_id, file_path, full_decl_name, method_name,
                extra={"type": "array_access"})
        
        # Link array and index
        if hasattr(expr, 'array'):
            array_node = handle_expression(expr.array, full_decl_name, method_name, file_path, nodes, relations, lineage_tracker)
            if array_node:
                add_relation(relations, array_node, "ACCESSED_BY", array_access_id)
        
        if hasattr(expr, 'index'):
            index_node = handle_expression(expr.index, full_decl_name, method_name, file_path, nodes, relations, lineage_tracker)
            if index_node:
                add_relation(relations, index_node, "INDEXES", array_access_id)
        
        return array_access_id

    return None

# ================== MAIN EXTRACTION FUNCTION ==================

def extract_relations_enhanced(project_path):
    """Enhanced extraction with comprehensive lineage tracking."""
    nodes = {}
    relations = set()
    java_files = []
    lineage_tracker = VariableLineageTracker()
    app_extractor = ApplicationNodeExtractor()

    # Pass 0: Folder & File hierarchy
    for root, dirs, files in os.walk(project_path):
        rel_root = os.path.relpath(root, project_path)
        folder_name = rel_root if rel_root != "." else os.path.basename(project_path)
        folder_node = add_node(nodes, "folder", folder_name, root)

        for d in dirs:
            subfolder_rel = os.path.relpath(os.path.join(root, d), project_path)
            subfolder_node = add_node(nodes, "folder", subfolder_rel, os.path.join(root, d))
            add_relation(relations, folder_node, "CONTAINS", subfolder_node)

        for file in files:
            if file.endswith(".java"):
                file_rel = os.path.relpath(os.path.join(root, file), project_path)
                file_node = add_node(nodes, "file", file_rel, os.path.join(root, file))
                add_relation(relations, folder_node, "CONTAINS", file_node)
                java_files.append(os.path.join(root, file))

    # Pass 1: Parse Java
    parsed_files = {}
    for file_path in java_files:
        with open(file_path, "r", encoding="utf-8") as f:
            code = f.read()
        try:
            parsed_files[file_path] = (code, javalang.parse.parse(code))
        except javalang.parser.JavaSyntaxError:
            continue

    # Pass 2: Extract Relations
    for file_path, (code, tree) in parsed_files.items():
        rel_path = os.path.relpath(file_path, project_path)
        file_node = f"file:{rel_path}"

        # Enhanced Database Operations
        db_operations = extract_db_table_usage(code)
        for operation_type, tables in db_operations.items():
            for table in tables:
                table_node = add_node(nodes, "table", table, file_path)
                relation_type = {
                    'reads': 'READS_FROM',
                    'writes': 'WRITES_TO',
                    'deletes': 'DELETES_FROM',
                    'creates': 'CREATES',
                    'alters': 'ALTERS'
                }.get(operation_type, 'INTERACTS_WITH')
                add_relation(relations, file_node, relation_type, table_node)

        # Enhanced API Endpoints
        api_data = extract_api_endpoints(code)
        for endpoint in api_data['endpoints']:
            api_node = add_node(nodes, "api_endpoint", endpoint['path'], file_path,
                              extra={
                                  'http_method': endpoint['method'],
                                  'endpoint_type': endpoint['type']
                              })
            relation_type = 'EXPOSES' if endpoint['type'] == 'rest_endpoint' else 'CALLS_API'
            add_relation(relations, file_node, relation_type, api_node)
        
        # Path Variables and Request Parameters
        for path_var in api_data['path_variables']:
            if path_var:
                param_node = add_node(nodes, "path_variable", path_var, file_path)
                add_relation(relations, file_node, "USES_PATH_VARIABLE", param_node)
        
        for req_param in api_data['request_params']:
            if req_param:
                param_node = add_node(nodes, "request_parameter", req_param, file_path)
                add_relation(relations, file_node, "USES_REQUEST_PARAM", param_node)

        # Messaging Patterns
        messaging = extract_messaging_patterns(code)
        for publisher in messaging['publishers']:
            if publisher:
                msg_node = add_node(nodes, "message_publisher", publisher, file_path)
                add_relation(relations, file_node, "PUBLISHES_TO", msg_node)
        
        for subscriber in messaging['subscribers']:
            if subscriber:
                msg_node = add_node(nodes, "message_subscriber", subscriber, file_path)
                add_relation(relations, file_node, "SUBSCRIBES_TO", msg_node)

        # Spring Annotations
        annotations = app_extractor.extract_spring_annotations(code)
        for annotation in annotations:
            ann_node = add_node(nodes, "annotation", f"{annotation['type']}_{id(annotation)}", file_path,
                              extra={
                                  'annotation_type': annotation['type'],
                                  'value': annotation.get('value'),
                                  'full_match': annotation['full_match']
                              })
            add_relation(relations, file_node, "HAS_ANNOTATION", ann_node)

        # JPA Entities
        jpa_data = app_extractor.extract_jpa_entities(code)
        for table in jpa_data['tables']:
            table_node = add_node(nodes, "entity_table", table, file_path)
            add_relation(relations, file_node, "MAPS_TO_TABLE", table_node)
        
        for column in jpa_data['columns']:
            column_node = add_node(nodes, "entity_column", column, file_path)
            add_relation(relations, file_node, "MAPS_TO_COLUMN", column_node)

        # Configuration Properties
        config_props = app_extractor.extract_configuration_properties(code)
        for prop in config_props:
            prop_node = add_node(nodes, "configuration_property", prop['value'], file_path,
                               extra={'property_type': prop['type']})
            add_relation(relations, file_node, "USES_PROPERTY", prop_node)

        package_name = tree.package.name if tree.package else None

        for type_decl in tree.types:
            if not isinstance(type_decl, (javalang.tree.ClassDeclaration, javalang.tree.InterfaceDeclaration)):
                continue

            decl_type = "class" if isinstance(type_decl, javalang.tree.ClassDeclaration) else "interface"
            full_decl_name = f"{package_name}.{type_decl.name}" if package_name else type_decl.name
            class_node = add_node(nodes, decl_type, full_decl_name, file_path)
            add_relation(relations, file_node, "DECLARES", class_node)

            # Fields
            for field in getattr(type_decl, "fields", []):
                for decl in field.declarators:
                    var_full = f"{full_decl_name}.{decl.name}"
                    var_node = add_node(nodes, "variable", var_full, file_path, class_name=full_decl_name)
                    add_relation(relations, class_node, "HAS_VARIABLE", var_node)

            # Methods
            for method in getattr(type_decl, "methods", []):
                method_full = f"{full_decl_name}.{method.name}"
                method_node = add_node(nodes, "method", method_full, file_path, class_name=full_decl_name)
                add_relation(relations, class_node, "HAS_METHOD", method_node)

                if not method.body:
                    continue

                for node_path, node in method:
                    # Enhanced Local Variables with type tracking
                    if isinstance(node, javalang.tree.LocalVariableDeclaration):
                        var_type = str(node.type) if node.type else 'unknown'
                        for decl in node.declarators:
                            var_full = f"{full_decl_name}.{method.name}.{decl.name}"
                            var_node = add_node(nodes, "variable", var_full, file_path, 
                                              class_name=full_decl_name, method_name=method.name,
                                              extra={
                                                  'variable_type': var_type,
                                                  'scope': 'local',
                                                  'declaration_type': 'local_variable'
                                              })
                            add_relation(relations, method_node, "DECLARES", var_node)
                            
                            # Register with lineage tracker
                            lineage_tracker.register_variable(
                                decl.name, var_type, f"{full_decl_name}.{method.name}", file_path
                            )
                            
                            # Handle initial assignment
                            if decl.initializer:
                                init_node = handle_expression(decl.initializer, full_decl_name, method.name, 
                                                             file_path, nodes, relations, lineage_tracker)
                                if init_node:
                                    add_relation(relations, init_node, "INITIALIZES", var_node)

                    # Enhanced Assignments with detailed transformation tracking
                    elif isinstance(node, javalang.tree.Assignment):
                        left = node.expressionl
                        right = node.value

                        if isinstance(left, javalang.tree.MemberReference):
                            left_var = f"variable:{full_decl_name}.{method.name}.{left.member}"
                            
                            # Create assignment operation node
                            assignment_id = f"assignment:{full_decl_name}.{method.name}_{id(node)}"
                            assignment_node = add_node(nodes, "assignment", assignment_id, file_path,
                                                     class_name=full_decl_name, method_name=method.name,
                                                     extra={
                                                         'operation_type': 'assignment',
                                                         'target_variable': left.member
                                                     })
                            
                            add_relation(relations, method_node, "PERFORMS", assignment_node)
                            add_relation(relations, assignment_node, "ASSIGNS_TO", left_var)

                            # Process right-hand side with enhanced tracking
                            src_node = handle_expression(right, full_decl_name, method.name, 
                                                        file_path, nodes, relations, lineage_tracker)
                            if src_node:
                                add_relation(relations, src_node, "TRANSFORMS_TO", assignment_node)
                                add_relation(relations, assignment_node, "PRODUCES", left_var)
                                
                                # Track in lineage
                                lineage_tracker.add_transformation(
                                    src_node, left_var, 'assignment',
                                    {'type': 'variable_assignment', 'method': method.name}
                                )

                    # Method Invocations (standalone)
                    elif isinstance(node, javalang.tree.MethodInvocation):
                        invocation_node = handle_expression(node, full_decl_name, method.name, 
                                                           file_path, nodes, relations, lineage_tracker)
                        if invocation_node:
                            add_relation(relations, method_node, "CONTAINS", invocation_node)

                    # Return Statements with value tracking
                    elif isinstance(node, javalang.tree.ReturnStatement):
                        if node.expression:
                            return_expr_node = handle_expression(node.expression, full_decl_name, method.name,
                                                                file_path, nodes, relations, lineage_tracker)
                            if return_expr_node:
                                return_var = f"{full_decl_name}.{method.name}.return"
                                return_node = add_node(nodes, "return_value", return_var, file_path, 
                                                     class_name=full_decl_name, method_name=method.name)
                                add_relation(relations, return_expr_node, "FLOWS_TO", return_node)
                                add_relation(relations, method_node, "RETURNS", return_node)

                # Enhanced return type tracking
                if method.return_type and str(method.return_type) != 'void':
                    return_var = f"{full_decl_name}.{method.name}.return"
                    return_node = add_node(nodes, "return_type", return_var, file_path, 
                                         class_name=full_decl_name, method_name=method.name,
                                         extra={
                                             'return_type': str(method.return_type),
                                             'method_signature': f"{method.name}({', '.join(str(p.type) for p in method.parameters) if method.parameters else ''})"
                                         })
                    add_relation(relations, method_node, "HAS_RETURN_TYPE", return_node)

    # Add lineage summary to nodes
    lineage_summary = {
        'total_variables': len(lineage_tracker.variable_registry),
        'total_transformations': len(lineage_tracker.transformations),
        'data_flow_chains': len(lineage_tracker.data_flows)
    }
    
    return {
        'nodes': nodes,
        'relations': relations,
        'lineage_tracker': lineage_tracker,
        'lineage_summary': lineage_summary
    }

# ================== SAVE TO CSV ==================

def save_graph_to_csv(nodes, relations, output_dir="graph_csv"):
    os.makedirs(output_dir, exist_ok=True)
    nodes_df = pd.DataFrame(nodes.values())
    rel_df = pd.DataFrame(list(relations), columns=["src", "rel", "dst"])
    nodes_df.to_csv(os.path.join(output_dir, "nodes.csv"), index=False)
    rel_df.to_csv(os.path.join(output_dir, "relations.csv"), index=False)
    return nodes_df, rel_df

# ================== PUSH TO NEO4J ==================

def push_to_neo4j(nodes, relations, uri, user, password, database="neo4j"):
    driver = GraphDatabase.driver(uri, auth=(user, password))
    with driver.session(database=database) as session:
        # Clear DB
        session.run("MATCH (n) DETACH DELETE n")

        # Push nodes
        for n in nodes.values():
            session.run(
                "MERGE (a:{type} {{id:$id}}) SET a += $props".format(type=n["type"].capitalize()),
                id=n["id"], props=n
            )

        # Push relationships
        for src, rel, dst in relations:
            query = f"""
                MATCH (a {{id:$src}}), (b {{id:$dst}})
                MERGE (a)-[r:{rel}]->(b)
                """
            session.run(query, src=src, dst=dst)
    driver.close()

# ================== RUN ==================

project_path = r"C:\Shaik\sample\OneInsights"

nodes, relations = extract_relations_v17(project_path)
save_graph_to_csv(nodes, relations, output_dir="graph_csv")

NEO4J_URI = "bolt://localhost:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "Test@7889"
NEO4J_DB = "final"

push_to_neo4j(nodes, relations, NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD, NEO4J_DB)

print("✅ Extraction complete: {} nodes, {} relations".format(len(nodes), len(relations)))
