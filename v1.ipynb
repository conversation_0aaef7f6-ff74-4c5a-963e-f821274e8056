# ================== IMPORTS ==================
import os
import re
import javalang
import pandas as pd
from neo4j import GraphDatabase

# ================== HELPER FUNCTIONS ==================

def normalize_node(node_type, full_name, file_path, class_name=None, method_name=None, extra=None):
    """Standardized node metadata for Neo4j."""
    short_name = os.path.basename(full_name) if node_type in ("file", "folder") else full_name.split(".")[-1]
    if node_type == "file" and short_name.endswith(".java"):
        short_name = short_name[:-5]
    data = {
        "id": f"{node_type}:{full_name}",
        "type": node_type,
        "name": short_name,
        "full_name": full_name,
        "file_path": file_path,
        "class_name": class_name,
        "method_name": method_name
    }
    if extra:
        data.update(extra)
    return data

def add_node(nodes, node_type, full_name, file_path, class_name=None, method_name=None, extra=None):
    node_id = f"{node_type}:{full_name}"
    if node_id not in nodes:
        nodes[node_id] = normalize_node(node_type, full_name, file_path, class_name, method_name, extra)
    return node_id

def add_relation(relations, src, rel, dst):
    if src and dst:
        relations.add((src, rel.upper(), dst))

SQL_STOPWORDS = {
    "select","from","where","group","order","by","join","on","as","and","or",
    "if","then","else","when","end","case","distinct","limit","offset",
    "like","not","null","is","inner","left","right","outer","full","cross"
}

# ================== TABLE EXTRACTION ==================

def extract_db_table_usage(code):
    """Extract database table usage as READ/WRITE from queries."""
    reads, writes = set(), set()

    patterns = [
        (r'\bFROM\s+([A-Za-z_][\w]*)', reads),
        (r'\bJOIN\s+([A-Za-z_][\w]*)', reads),
        (r'\bUPDATE\s+([A-Za-z_][\w]*)', writes),
        (r'\bINTO\s+([A-Za-z_][\w]*)', writes),
        (r'@Query\s*\([^)]*["\'][^"\']*(?:from|FROM)\s+([A-Za-z_][\w]*)', reads)
    ]

    for pattern, target_set in patterns:
        for match in re.findall(pattern, code):
            table = match.strip().lower()
            if len(table) >= 2 and table not in SQL_STOPWORDS and not table.isdigit():
                target_set.add(table)

    return reads, writes

def extract_api_calls(code):
    """Detect Spring REST endpoints"""
    apis = set()
    for m in re.finditer(r'@(?:GetMapping|PostMapping|PutMapping|DeleteMapping|RequestMapping)\("([^"]+)"\)', code):
        path = m.group(1)
        if path and path.startswith("/"):
            apis.add(path)
    return apis

# ================== MULTI-STEP EXPRESSION HANDLER ==================

def handle_expression(expr, full_decl_name, method_name, file_path, nodes, relations):
    """Recursively handle expressions to create operation chains."""
    # Variable reference
    if isinstance(expr, javalang.tree.MemberReference):
        return f"variable:{full_decl_name}.{method_name}.{expr.member}"

    # Method call -> Operation node
    elif isinstance(expr, javalang.tree.MethodInvocation):
        op_name = expr.member
        op_display = f"{expr.qualifier+'.' if expr.qualifier else ''}{op_name}()"
        op_full = f"{full_decl_name}.{method_name}.{op_display}_op_{id(expr)}"
        op_node = add_node(nodes, "operation", op_full, file_path, full_decl_name, method_name, extra={"expression": op_display})

        # Link qualifier variable -> operation
        if expr.qualifier:
            src_node = f"variable:{full_decl_name}.{method_name}.{expr.qualifier}"
            add_relation(relations, src_node, "TRANSFORMS_TO", op_node)

        # Link arguments
        for arg in expr.arguments:
            src_node = handle_expression(arg, full_decl_name, method_name, file_path, nodes, relations)
            if src_node:
                add_relation(relations, src_node, "TRANSFORMS_TO", op_node)

        return op_node

    # Binary operation (a + b)
    elif isinstance(expr, javalang.tree.BinaryOperation):
        op_name = expr.operator
        op_full = f"{full_decl_name}.{method_name}.{op_name}_binop_{id(expr)}"
        op_node = add_node(nodes, "operation", op_full, file_path, full_decl_name, method_name, extra={"expression": op_name})

        for operand in (expr.operandl, expr.operandr):
            src_node = handle_expression(operand, full_decl_name, method_name, file_path, nodes, relations)
            if src_node:
                add_relation(relations, src_node, "TRANSFORMS_TO", op_node)

        return op_node

    return None

# ================== MAIN EXTRACTION FUNCTION ==================

def extract_relations_v17(project_path):
    nodes = {}
    relations = set()
    java_files = []

    # Pass 0: Folder & File hierarchy
    for root, dirs, files in os.walk(project_path):
        rel_root = os.path.relpath(root, project_path)
        folder_name = rel_root if rel_root != "." else os.path.basename(project_path)
        folder_node = add_node(nodes, "folder", folder_name, root)

        for d in dirs:
            subfolder_rel = os.path.relpath(os.path.join(root, d), project_path)
            subfolder_node = add_node(nodes, "folder", subfolder_rel, os.path.join(root, d))
            add_relation(relations, folder_node, "CONTAINS", subfolder_node)

        for file in files:
            if file.endswith(".java"):
                file_rel = os.path.relpath(os.path.join(root, file), project_path)
                file_node = add_node(nodes, "file", file_rel, os.path.join(root, file))
                add_relation(relations, folder_node, "CONTAINS", file_node)
                java_files.append(os.path.join(root, file))

    # Pass 1: Parse Java
    parsed_files = {}
    for file_path in java_files:
        with open(file_path, "r", encoding="utf-8") as f:
            code = f.read()
        try:
            parsed_files[file_path] = (code, javalang.parse.parse(code))
        except javalang.parser.JavaSyntaxError:
            continue

    # Pass 2: Extract Relations
    for file_path, (code, tree) in parsed_files.items():
        rel_path = os.path.relpath(file_path, project_path)
        file_node = f"file:{rel_path}"

        # DB Tables
        reads, writes = extract_db_table_usage(code)
        for t in reads:
            t_node = add_node(nodes, "table", t, file_path)
            add_relation(relations, file_node, "READS_FROM", t_node)
        for t in writes:
            t_node = add_node(nodes, "table", t, file_path)
            add_relation(relations, file_node, "WRITES_TO", t_node)

        # API calls
        for api in extract_api_calls(code):
            api_node = add_node(nodes, "api", api, file_path)
            add_relation(relations, file_node, "CALLS_API", api_node)

        package_name = tree.package.name if tree.package else None

        for type_decl in tree.types:
            if not isinstance(type_decl, (javalang.tree.ClassDeclaration, javalang.tree.InterfaceDeclaration)):
                continue

            decl_type = "class" if isinstance(type_decl, javalang.tree.ClassDeclaration) else "interface"
            full_decl_name = f"{package_name}.{type_decl.name}" if package_name else type_decl.name
            class_node = add_node(nodes, decl_type, full_decl_name, file_path)
            add_relation(relations, file_node, "DECLARES", class_node)

            # Fields
            for field in getattr(type_decl, "fields", []):
                for decl in field.declarators:
                    var_full = f"{full_decl_name}.{decl.name}"
                    var_node = add_node(nodes, "variable", var_full, file_path, class_name=full_decl_name)
                    add_relation(relations, class_node, "HAS_VARIABLE", var_node)

            # Methods
            for method in getattr(type_decl, "methods", []):
                method_full = f"{full_decl_name}.{method.name}"
                method_node = add_node(nodes, "method", method_full, file_path, class_name=full_decl_name)
                add_relation(relations, class_node, "HAS_METHOD", method_node)

                if not method.body:
                    continue

                for node_path, node in method:
                    # Local Variables
                    if isinstance(node, javalang.tree.LocalVariableDeclaration):
                        for decl in node.declarators:
                            var_full = f"{full_decl_name}.{method.name}.{decl.name}"
                            var_node = add_node(nodes, "variable", var_full, file_path, class_name=full_decl_name, method_name=method.name)
                            add_relation(relations, method_node, "USES", var_node)

                    # Assignments -> variable transformation chain
                    elif isinstance(node, javalang.tree.Assignment):
                        left = node.expressionl
                        right = node.value

                        if isinstance(left, javalang.tree.MemberReference):
                            left_var = f"variable:{full_decl_name}.{method.name}.{left.member}"
                            add_relation(relations, method_node, "PRODUCES", left_var)

                            src_node = handle_expression(right, full_decl_name, method.name, file_path, nodes, relations)
                            if src_node:
                                add_relation(relations, src_node, "TRANSFORMS_TO", left_var)

                # Track return variables
                if method.return_type:
                    return_var = f"{full_decl_name}.{method.name}.return"
                    return_node = add_node(nodes, "variable", return_var, file_path, class_name=full_decl_name, method_name=method.name)
                    add_relation(relations, method_node, "RETURNS", return_node)

    return nodes, relations

# ================== SAVE TO CSV ==================

def save_graph_to_csv(nodes, relations, output_dir="graph_csv"):
    os.makedirs(output_dir, exist_ok=True)
    nodes_df = pd.DataFrame(nodes.values())
    rel_df = pd.DataFrame(list(relations), columns=["src", "rel", "dst"])
    nodes_df.to_csv(os.path.join(output_dir, "nodes.csv"), index=False)
    rel_df.to_csv(os.path.join(output_dir, "relations.csv"), index=False)
    return nodes_df, rel_df

# ================== PUSH TO NEO4J ==================

def push_to_neo4j(nodes, relations, uri, user, password, database="neo4j"):
    driver = GraphDatabase.driver(uri, auth=(user, password))
    with driver.session(database=database) as session:
        # Clear DB
        session.run("MATCH (n) DETACH DELETE n")

        # Push nodes
        for n in nodes.values():
            session.run(
                "MERGE (a:{type} {{id:$id}}) SET a += $props".format(type=n["type"].capitalize()),
                id=n["id"], props=n
            )

        # Push relationships
        for src, rel, dst in relations:
            query = f"""
                MATCH (a {{id:$src}}), (b {{id:$dst}})
                MERGE (a)-[r:{rel}]->(b)
                """
            session.run(query, src=src, dst=dst)
    driver.close()

# ================== RUN ==================

project_path = r"C:\Shaik\sample\OneInsights"

nodes, relations = extract_relations_v17(project_path)
save_graph_to_csv(nodes, relations, output_dir="graph_csv")

NEO4J_URI = "bolt://localhost:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "Test@7889"
NEO4J_DB = "final"

push_to_neo4j(nodes, relations, NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD, NEO4J_DB)

print("✅ Extraction complete: {} nodes, {} relations".format(len(nodes), len(relations)))
