{"cells": [{"cell_type": "code", "execution_count": null, "id": "stage1_configuration", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 1: CONFIGURATION & INITIALIZATION ==========\n", "import os\n", "import json\n", "import re\n", "import uuid\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "import pandas as pd\n", "from collections import defaultdict\n", "from concurrent.futures import ThreadPoolExecutor, as_completed\n", "import threading\n", "\n", "# Tree-sitter for AST parsing\n", "from tree_sitter import Language, Parser\n", "import tree_sitter_java as tsjava\n", "\n", "# LangChain components\n", "from langchain_community.document_loaders import TextLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter, Language as LC_Language\n", "from langchain_experimental.graph_transformers import LLMGraphTransformer\n", "from langchain_community.graphs import Neo4jGraph\n", "from langchain_openai import AzureChatOpenAI\n", "from langchain_core.prompts import PromptTemplate\n", "from langchain.schema import Document\n", "\n", "# Configuration\n", "BASE_PATH = Path(r\"C:/Shaik/sample/ServicesBolt\")\n", "\n", "# Neo4j Configuration\n", "NEO4J_URI = \"bolt://localhost:7687\"\n", "NEO4J_USER = \"neo4j\"\n", "NEO4J_PASSWORD = \"Test@7889\"\n", "NEO4J_DB = \"servicesbolt\"\n", "\n", "# Initialize connections\n", "graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)\n", "JAVA_LANGUAGE = Language(tsjava.language())\n", "parser = Parser(JAVA_LANGUAGE)\n", "\n", "# Azure OpenAI Configuration\n", "llm = AzureChatOpenAI(\n", "    api_key=\"********************************\",\n", "    azure_endpoint=\"https://azureopenaibrsc.openai.azure.com/\",\n", "    azure_deployment=\"gpt-4o\",\n", "    api_version=\"2024-12-01-preview\"\n", ")\n", "\n", "# Temp variables to filter out (User Preference)\n", "TEMP_VARIABLES = {\n", "    'i', 'j', 'k', 'l', 'm', 'n', 'x', 'y', 'z',\n", "    'temp', 'tmp', 'temporary', 'temp1', 'temp2',\n", "    'count', 'counter', 'index', 'idx', 'iter',\n", "    'result', 'res', 'ret', 'val', 'value',\n", "    'item', 'elem', 'element', 'obj', 'object',\n", "    'str', 'string', 'num', 'number', 'flag',\n", "    'bool', 'boolean', 'arr', 'array', 'list',\n", "    'map', 'set', 'data', 'info', 'param', 'arg','Status'\n", "}\n", "\n", "# Long-term memory storage\n", "MEMORY_FILE = \"servicesbolt_memory_v10.json\"\n", "memory_lock = threading.Lock()\n", "\n", "def load_memory():\n", "    \"\"\"Load long-term memory from disk\"\"\"\n", "    # Try to load JSON file first\n", "    try:\n", "        with open(MEMORY_FILE, 'r', encoding='utf-8') as f:\n", "            data = json.load(f)\n", "            \n", "            # Convert lists back to sets where needed\n", "            def convert_from_json(obj):\n", "                if isinstance(obj, dict):\n", "                    result = {}\n", "                    for k, v in obj.items():\n", "                        if k == 'validated_edges' and isinstance(v, list):\n", "                            result[k] = set(v)\n", "                        else:\n", "                            result[k] = convert_from_json(v)\n", "                    return result\n", "                elif isinstance(obj, list):\n", "                    return [convert_from_json(item) for item in obj]\n", "                else:\n", "                    return obj\n", "            \n", "            return convert_from_json(data)\n", "            \n", "    except FileNotFoundError:\n", "        # Try to load old pickle file if JSON doesn't exist\n", "        old_pickle_file = MEMORY_FILE.replace('.json', '.pkl')\n", "        try:\n", "            import pickle\n", "            with open(old_pickle_file, 'rb') as f:\n", "                data = pickle.load(f)\n", "                print(f\"📦 Loaded memory from old pickle file: {old_pickle_file}\")\n", "                print(f\"🔄 Converting to JSON format...\")\n", "                # Save as JSON for future use\n", "                save_memory(data)\n", "                return data\n", "        except FileNotFoundError:\n", "            pass\n", "        \n", "        # Return default memory structure\n", "        return {\n", "            'class_registry': {},\n", "            'dto_mappings': {},\n", "            'validated_edges': set(),\n", "            'code_index': {},\n", "            'variable_flows': {},\n", "            'method_signatures': {},\n", "            'transformation_cache': {},\n", "            'variable_contexts': {}  # New: track variable contexts\n", "        }\n", "    except json.JSONDecodeError as e:\n", "        print(f\"⚠️ Error loading JSON memory file: {e}\")\n", "        print(f\"🔄 Returning default memory structure\")\n", "        return {\n", "            'class_registry': {},\n", "            'dto_mappings': {},\n", "            'validated_edges': set(),\n", "            'code_index': {},\n", "            'variable_flows': {},\n", "            'method_signatures': {},\n", "            'transformation_cache': {},\n", "            'variable_contexts': {}  # New: track variable contexts\n", "        }\n", "\n", "def save_memory(memory):\n", "    \"\"\"Save long-term memory to disk\"\"\"\n", "    with memory_lock:\n", "        try:\n", "            # Create a copy to avoid modifying the original\n", "            memory_copy = memory.copy()\n", "            \n", "            # Convert sets to lists for JSON serialization\n", "            def convert_for_json(obj):\n", "                if isinstance(obj, set):\n", "                    return list(obj)\n", "                elif isinstance(obj, dict):\n", "                    return {k: convert_for_json(v) for k, v in obj.items()}\n", "                elif isinstance(obj, list):\n", "                    return [convert_for_json(item) for item in obj]\n", "                else:\n", "                    return obj\n", "            \n", "            memory_copy = convert_for_json(memory_copy)\n", "            \n", "            with open(MEMORY_FILE, 'w', encoding='utf-8') as f:\n", "                json.dump(memory_copy, f, indent=2, ensure_ascii=False)\n", "        except Exception as e:\n", "            print(f\"⚠️ Error saving memory to JSON: {e}\")\n", "            # Fallback: save as pickle if JSON fails\n", "            backup_file = MEMORY_FILE.replace('.json', '_backup.pkl')\n", "            import pickle\n", "            with open(backup_file, 'wb') as f:\n", "                pickle.dump(memory, f)\n", "            print(f\"💾 Memory saved as backup pickle file: {backup_file}\")\n", "\n", "# Initialize memory\n", "memory = load_memory()\n", "\n", "# Clear Neo4j database\n", "graph.query(\"MATCH (n) DETACH DELETE n\")\n", "print(\"✅ Stage 1 Complete: Configuration loaded and Neo4j cleared\")"]}, {"cell_type": "code", "execution_count": null, "id": "utility_functions", "metadata": {}, "outputs": [], "source": ["# ========== IMPROVED UTILITY FUNCTIONS ==========\n", "\n", "def to_pascal_case(text):\n", "    \"\"\"Convert text to PascalCase with improved handling\"\"\"\n", "    if not text:\n", "        return text\n", "    \n", "    # Remove file extensions first\n", "    text = re.sub(r'\\.(java|class)$', '', text, flags=re.IGNORECASE)\n", "    \n", "    # Handle file paths - extract just the filename\n", "    if '/' in text or '\\\\' in text:\n", "        text = os.path.basename(text)\n", "        text = re.sub(r'\\.(java|class)$', '', text, flags=re.IGNORECASE)\n", "    \n", "    # If already in PascalCase, return as is\n", "    if re.match(r'^[A-Z][a-zA-Z0-9]*$', text):\n", "        return text\n", "    \n", "    # Handle camelCase to PascalCase conversion\n", "    if re.match(r'^[a-z][a-zA-Z0-9]*$', text):\n", "        return text[0].upper() + text[1:]\n", "    \n", "    # Split on common delimiters and capitalize each part\n", "    parts = re.split(r'[_\\-\\s]+', text)\n", "    result = ''\n", "    for part in parts:\n", "        if part:\n", "            result += part[0].upper() + part[1:].lower() if len(part) > 1 else part.upper()\n", "    \n", "    return result if result else text\n", "\n", "def extract_clean_name(full_name, name_type):\n", "    \"\"\"Extract clean name from potentially concatenated strings\"\"\"\n", "    if not full_name:\n", "        return full_name\n", "    \n", "    # Remove common prefixes\n", "    prefixes = ['method:', 'class:', 'variable:', 'field:', 'table:', 'endpoint:']\n", "    for prefix in prefixes:\n", "        if full_name.lower().startswith(prefix):\n", "            full_name = full_name[len(prefix):]\n", "    \n", "    # Remove file extensions EXCEPT for file type (preserve .java for files to distinguish from classes)\n", "    if name_type.lower() != 'file':\n", "        full_name = re.sub(r'\\.(java|class)$', '', full_name, flags=re.IGNORECASE)\n", "    \n", "    # Handle file.class patterns - extract only class name\n", "    if '.' in full_name and name_type.lower() in ['class', 'interface']:\n", "        parts = full_name.split('.')\n", "        # Take the last part as the class name\n", "        full_name = parts[-1] if parts[-1] else parts[-2] if len(parts) > 1 else full_name\n", "    \n", "    # Handle classname:method or classname.method patterns - extract only method name\n", "    if name_type.lower() == 'method':\n", "        # Handle both colon and dot separators\n", "        if ':' in full_name:\n", "            parts = full_name.split(':')\n", "            full_name = parts[-1] if parts[-1] else parts[-2] if len(parts) > 1 else full_name\n", "        elif '.' in full_name:\n", "            parts = full_name.split('.')\n", "            full_name = parts[-1] if parts[-1] else parts[-2] if len(parts) > 1 else full_name\n", "    \n", "    # Apply PascalCase for classes, methods, files, folders\n", "    if name_type.lower() in ['class', 'interface', 'method', 'file', 'folder']:\n", "        return to_pascal_case(full_name)\n", "    \n", "    # For variables, keep original name (context handled separately)\n", "    if name_type.lower() == 'variable':\n", "        if '.' in full_name:\n", "            return full_name.split('.')[-1]  # Return only variable name\n", "        return full_name\n", "    \n", "    # For tables, apply PascalCase\n", "    if name_type.lower() == 'table':\n", "        return to_pascal_case(full_name)\n", "    \n", "    return full_name\n", "\n", "def is_temp_variable(var_name):\n", "    \"\"\"Check if variable is a common temp variable\"\"\"\n", "    if not var_name:\n", "        return True\n", "    var_lower = var_name.lower().strip()\n", "    return var_lower in TEMP_VARIABLES or len(var_lower) <= 1\n", "\n", "def get_variable_context(var_name, method_name=None, class_name=None):\n", "    \"\"\"Get context for a variable (method or class where it's defined)\"\"\"\n", "    if method_name:\n", "        return extract_clean_name(method_name, 'method')\n", "    elif class_name:\n", "        return extract_clean_name(class_name, 'class')\n", "    return None\n", "\n", "print(\"✅ Improved utility functions loaded\")"]}, {"cell_type": "code", "execution_count": null, "id": "variable_registry", "metadata": {}, "outputs": [], "source": ["# ========== IMPROVED VARIABLE METADATA REGISTRY ==========\n", "\n", "class ImprovedVariableRegistry:\n", "    \"\"\"Enhanced registry to track variables with proper context separation\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.variables = {}  # var_id -> metadata\n", "        self.name_to_id = {}  # (var_name, context) -> var_id\n", "        self.chunk_memory = {}  # chunk_id -> variables seen\n", "        \n", "    def register_variable(self, var_name, context, chunk_id, context_info):\n", "        \"\"\"Register a variable with unique ID and context metadata\"\"\"\n", "        # Clean variable name\n", "        clean_var_name = extract_clean_name(var_name, 'variable')\n", "        clean_context = extract_clean_name(context, context_info.get('context_type', 'method'))\n", "        \n", "        # Create unique key\n", "        var_key = (clean_var_name, clean_context)\n", "        \n", "        if var_key in self.name_to_id:\n", "            var_id = self.name_to_id[var_key]\n", "            self.variables[var_id]['chunks'].add(chunk_id)\n", "            self.variables[var_id]['contexts'].append(context_info)\n", "        else:\n", "            var_id = f\"var_{uuid.uuid4().hex[:8]}\"\n", "            self.name_to_id[var_key] = var_id\n", "            self.variables[var_id] = {\n", "                'variable_name': clean_var_name,  # Only variable name for display\n", "                'context_name': clean_context,    # Method/class context\n", "                'context_type': context_info.get('context_type', 'method'),\n", "                'chunks': {chunk_id},\n", "                'contexts': [context_info],\n", "                'declared_in': chunk_id if context_info.get('action') == 'declared' else None,\n", "                'modifications': [],\n", "                'usages': [],\n", "                'data_type': context_info.get('data_type'),\n", "                'lineage_path': []\n", "            }\n", "        \n", "        if chunk_id not in self.chunk_memory:\n", "            self.chunk_memory[chunk_id] = set()\n", "        self.chunk_memory[chunk_id].add(var_id)\n", "        \n", "        return var_id\n", "    \n", "    def get_variable_for_neo4j(self, var_id):\n", "        \"\"\"Get variable data formatted for Neo4j\"\"\"\n", "        if var_id in self.variables:\n", "            var_data = self.variables[var_id]\n", "            return {\n", "                'name': var_data['variable_name'],  # Display name only\n", "                'context': var_data['context_name'], # Context for uniqueness\n", "                'context_type': var_data['context_type'],\n", "                'full_context': f\"{var_data['context_name']}.{var_data['variable_name']}\"\n", "            }\n", "        return None\n", "\n", "# Initialize improved variable registry\n", "variable_registry = ImprovedVariableRegistry()\n", "\n", "print(\"✅ Improved Variable Registry initialized\")"]}, {"cell_type": "code", "execution_count": null, "id": "stage2_folder_file_hierarchy", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 2: IMPROVED FOLDER-FILE HIERARCHY ==========\n", "\n", "def extract_folder_file_hierarchy():\n", "    \"\"\"Extract and normalize folder-file relationships with improved naming\"\"\"\n", "    relationships = []\n", "    base_folder = to_pascal_case(BASE_PATH.name)\n", "\n", "    for root, dirs, files in os.walk(BASE_PATH):\n", "        current_path = Path(root)\n", "        rel_path = current_path.relative_to(BASE_PATH)\n", "\n", "        # Determine current folder name and its parent\n", "        if rel_path != Path('.'):\n", "            folder_name = to_pascal_case(current_path.name)\n", "            parent_rel_path = current_path.parent.relative_to(BASE_PATH)\n", "            parent_name = base_folder if parent_rel_path == Path('.') else to_pascal_case(current_path.parent.name)\n", "\n", "            relationships.append({\n", "                'source_node': parent_name,\n", "                'source_type': 'Folder',\n", "                'destination_node': folder_name,\n", "                'destination_type': 'Folder',\n", "                'relationship': 'CONTAINS'\n", "            })\n", "            current_folder_name = folder_name\n", "        else:\n", "            current_folder_name = base_folder\n", "\n", "        # Process files inside the folder\n", "        for file in files:\n", "            if file.lower().endswith(\".java\"):\n", "                file_name = extract_clean_name(file, 'file')\n", "                relationships.append({\n", "                    'source_node': current_folder_name,\n", "                    'source_type': 'Folder',\n", "                    'destination_node': file_name,\n", "                    'destination_type': 'File',\n", "                    'relationship': 'CONTAINS',\n", "                    'file_path': str(current_path / file)\n", "                })\n", "\n", "    return relationships\n", "\n", "# Execute Stage 2\n", "folder_file_relationships = extract_folder_file_hierarchy()\n", "df_hierarchy = pd.DataFrame(folder_file_relationships)\n", "\n", "# Store Stage 2 results in memory\n", "memory['stage_2_results'] = {\n", "    'relationships': len(df_hierarchy),\n", "    'folders': len([r for r in folder_file_relationships if r['destination_type'] == 'Folder']),\n", "    'files': len([r for r in folder_file_relationships if r['destination_type'] == 'File'])\n", "}\n", "\n", "# Add validated edges to prevent duplicates\n", "for _, row in df_hierarchy.iterrows():\n", "    edge_key = f\"{row['source_node']}-{row['relationship']}-{row['destination_node']}\"\n", "    memory['validated_edges'].add(edge_key)\n", "save_memory(memory)\n", "\n", "print(f\"✅ Stage 2 Complete: {len(df_hierarchy)} folder/file relationships extracted\")\n", "print(f\"📁 Folders: {memory['stage_2_results']['folders']}, Files: {memory['stage_2_results']['files']}\")"]}, {"cell_type": "code", "execution_count": null, "id": "stage2b_file_class_ast", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 2B: AST-BASED FILE-CLASS RELATIONSHIPS ==========\n", "\n", "def extract_file_class_relationships_ast():\n", "    \"\"\"Extract file-class relationships using AST parsing\"\"\"\n", "    file_class_relationships = []\n", "    \n", "    print(\"🔍 Extracting file-class relationships using AST...\")\n", "    \n", "    # Get all Java files from the hierarchy data\n", "    java_files = df_hierarchy[df_hierarchy['destination_type'] == 'File']\n", "    \n", "    for _, file_row in java_files.iterrows():\n", "        if 'file_path' in file_row and file_row['file_path']:\n", "            file_path = file_row['file_path']\n", "            file_name = file_row['destination_node']\n", "            \n", "            try:\n", "                # Read and parse the Java file\n", "                with open(file_path, 'r', encoding='utf-8') as f:\n", "                    source_code = f.read().encode('utf-8')\n", "                \n", "                tree = parser.parse(source_code)\n", "                root_node = tree.root_node\n", "                \n", "                # Find class declarations\n", "                def find_classes(node):\n", "                    classes = []\n", "                    if node.type == 'class_declaration':\n", "                        # Find class name\n", "                        for child in node.children:\n", "                            if child.type == 'identifier':\n", "                                class_name = source_code[child.start_byte:child.end_byte].decode('utf-8')\n", "                                classes.append(to_pascal_case(class_name))\n", "                                break\n", "                    \n", "                    # Recursively search child nodes\n", "                    for child in node.children:\n", "                        classes.extend(find_classes(child))\n", "                    \n", "                    return classes\n", "                \n", "                classes_in_file = find_classes(root_node)\n", "                \n", "                # Create file-class relationships\n", "                for class_name in classes_in_file:\n", "                    file_class_relationships.append({\n", "                        'source_node': file_name,\n", "                        'source_type': 'File',\n", "                        'destination_node': class_name,\n", "                        'destination_type': 'Class',\n", "                        'relationship': 'DECLARES',\n", "                        'file_path': file_path\n", "                    })\n", "                    print(f\"📎 Found: {file_name} -[DECLARES]-> {class_name}\")\n", "                    \n", "                    # DEBUG: Verify file name includes .java extension\n", "                    if not file_name.endswith('.java'):\n", "                        print(f\"⚠️ WARNING: File name '{file_name}' missing .java extension!\")\n", "\n", "                    \n", "            except Exception as e:\n", "                print(f\"⚠️ Error processing {file_path}: {e}\")\n", "                continue\n", "    \n", "    return file_class_relationships\n", "\n", "# Execute Stage 2B: Extract file-class relationships\n", "file_class_relationships = extract_file_class_relationships_ast()\n", "df_file_class = pd.DataFrame(file_class_relationships)\n", "\n", "# Append file-class relationships to the hierarchy DataFrame\n", "if len(df_file_class) > 0:\n", "    df_hierarchy = pd.concat([df_hierarchy, df_file_class], ignore_index=True)\n", "    print(f\"📎 Added {len(df_file_class)} file-class relationships to Stage 2 data\")\n", "    \n", "    # Update memory with file-class relationships\n", "    memory['stage_2_results']['file_class_relationships'] = len(df_file_class)\n", "    \n", "    # Add validated edges for file-class relationships\n", "    for _, row in df_file_class.iterrows():\n", "        edge_key = f\"{row['source_node']}-{row['relationship']}-{row['destination_node']}\"\n", "        memory['validated_edges'].add(edge_key)\n", "    \n", "    save_memory(memory)\n", "else:\n", "    print(\"⚠️ No file-class relationships found\")\n", "\n", "print(f\"✅ Stage 2B Complete: Total relationships now {len(df_hierarchy)} (including {len(df_file_class)} file-class)\")"]}, {"cell_type": "code", "execution_count": null, "id": "stage3_enhanced_class_registry", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 3: ENHANCED CLASS REGISTRY & ANALYSIS ==========\n", "\n", "# Patterns for analysis\n", "PACKAGE_PATTERN = r'package\\s+([\\w\\.]+);'\n", "IMPORT_PATTERN = r'import\\s+([\\w\\.]+);'\n", "MAPPING_PATTERNS = {\n", "    'GetMapping': r'@GetMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "    'PostMapping': r'@PostMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "    'PutMapping': r'@PutMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "    'DeleteMapping': r'@DeleteMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "    'RequestMapping': r'@RequestMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']'\n", "}\n", "\n", "def extract_package_and_imports(source_code_str):\n", "    \"\"\"Extract package name and import list from Java source\"\"\"\n", "    package_match = re.search(PACKAGE_PATTERN, source_code_str)\n", "    package_name = package_match.group(1) if package_match else None\n", "    import_matches = re.findall(IMPORT_PATTERN, source_code_str)\n", "    return package_name, import_matches\n", "\n", "def extract_api_endpoints(source_code_str):\n", "    \"\"\"Extract API endpoints using Spring annotations\"\"\"\n", "    endpoints = []\n", "    for mapping_type, pattern in MAPPING_PATTERNS.items():\n", "        matches = re.findall(pattern, source_code_str, re.MULTILINE)\n", "        for match in matches:\n", "            path = match.strip()\n", "            if path:\n", "                method = mapping_type.replace('Mapping', '').upper() if mapping_type != 'RequestMapping' else 'GET'\n", "                endpoints.append({\n", "                    'type': mapping_type,\n", "                    'path': path,\n", "                    'method': method\n", "                })\n", "    return endpoints\n", "\n", "def extract_database_entities(source_code_str):\n", "    \"\"\"Extract @Entity, @Table, and @Query usage from Java file\"\"\"\n", "    entities = []\n", "\n", "    # @Entity/@Table extraction\n", "    if \"@Entity\" in source_code_str:\n", "        table_matches = re.findall(r'@Table\\s*\\(\\s*name\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']', source_code_str)\n", "        for table_name in table_matches:\n", "            entities.append({'type': 'table', 'name': table_name.strip()})\n", "\n", "        if not table_matches:\n", "            class_match = re.search(r'(public\\s+)?(class|abstract class|interface)\\s+(\\w+)', source_code_str)\n", "            if class_match:\n", "                class_name = class_match.group(3)\n", "                snake_case = re.sub('([a-z0-9])([A-Z])', r'\\1_\\2', class_name).lower()\n", "                entities.append({'type': 'table', 'name': snake_case})\n", "\n", "    # @Query: detect raw SQL or JPQL references to tables\n", "    query_pattern = r'@Query\\s*\\([^)]*[\"\\']([^\"\\']*(?:FROM|from)\\s+([\\w]+)[^\"\\']*)[\"\\']'\n", "    query_matches = re.findall(query_pattern, source_code_str, re.MULTILINE | re.IGNORECASE)\n", "    for _, table in query_matches:\n", "        table = table.strip()\n", "        if table and table.lower() not in {'select', 'where', 'group', 'order'}:\n", "            entities.append({'type': 'table', 'name': table})\n", "\n", "    return entities\n", "\n", "def build_enhanced_class_registry():\n", "    \"\"\"Build enhanced class registry with improved memory integration\"\"\"\n", "    enhanced_registry = {}\n", "    code_index = {}\n", "    \n", "    for root, _, files in os.walk(BASE_PATH):\n", "        for file in files:\n", "            if file.endswith('.java'):\n", "                file_path = os.path.join(root, file)\n", "                try:\n", "                    with open(file_path, 'r', encoding='utf-8') as f:\n", "                        source_code_str = f.read()\n", "                    \n", "                    package_name, imports = extract_package_and_imports(source_code_str)\n", "                    endpoints = extract_api_endpoints(source_code_str)\n", "                    db_entities = extract_database_entities(source_code_str)\n", "                    \n", "                    # Apply improved name cleaning\n", "                    class_name = extract_clean_name(file.replace('.java', ''), 'class')\n", "                    fqcn = f'{package_name}.{class_name}' if package_name else class_name\n", "                    \n", "                    enhanced_registry[class_name] = {\n", "                        'fqcn': fqcn,\n", "                        'package': package_name,\n", "                        'file_path': file_path,\n", "                        'imports': imports,\n", "                        'endpoints': endpoints,\n", "                        'db_entities': db_entities,\n", "                        'source_code': source_code_str\n", "                    }\n", "                    \n", "                    # Build code index for fast method retrieval\n", "                    methods = re.findall(r'(?:public|private|protected)\\s+\\w+\\s+(\\w+)\\s*\\(', source_code_str)\n", "                    clean_methods = [extract_clean_name(m, 'method') for m in methods]\n", "                    \n", "                    code_index[class_name] = {\n", "                        'methods': clean_methods,\n", "                        'variables': re.findall(r'\\b(\\w+)\\s+(\\w+)\\s*[=;]', source_code_str),\n", "                        'annotations': re.findall(r'@(\\w+)', source_code_str)\n", "                    }\n", "                    \n", "                    # Store method signatures in memory for cross-stage reference\n", "                    for method in clean_methods:\n", "                        memory['method_signatures'][method] = {\n", "                            'class': class_name,\n", "                            'file_path': file_path,\n", "                            'stage': 'stage_3_registry'\n", "                        }\n", "                    \n", "                except Exception as e:\n", "                    print(f\"⚠️ Error processing {file_path}: {e}\")\n", "                    continue\n", "    \n", "    return enhanced_registry, code_index\n", "\n", "# Execute Stage 3\n", "class_registry, code_index = build_enhanced_class_registry()\n", "\n", "# Store in memory\n", "memory['class_registry'] = class_registry\n", "memory['code_index'] = code_index\n", "save_memory(memory)\n", "\n", "print(f\"✅ Stage 3 Complete: Enhanced class registry built with {len(class_registry)} classes\")\n", "print(f\"🧠 Memory updated with {len(memory['method_signatures'])} method signatures\")"]}, {"cell_type": "code", "execution_count": null, "id": "stage3b_ast_extraction", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 3B: AST EXTRACTION ==========\n", "\n", "def read_source_code(file_path):\n", "    \"\"\"Read source code from file\"\"\"\n", "    with open(file_path, 'r', encoding='utf-8') as f:\n", "        return f.read().encode('utf-8')\n", "\n", "def extract_ast_structure(file_path):\n", "    \"\"\"Extract AST structure from Java file using tree-sitter\"\"\"\n", "    records = []\n", "    source_code = read_source_code(file_path)\n", "    tree = parser.parse(source_code)\n", "    root_node = tree.root_node\n", "    file_name = os.path.basename(file_path)\n", "\n", "    def clean_node_name(name):\n", "        \"\"\"Clean node names to remove prefixes and suffixes\"\"\"\n", "        if not name:\n", "            return name\n", "        \n", "        # Remove common prefixes\n", "        prefixes_to_remove = ['method:', 'class:', 'variable:', 'field:']\n", "        for prefix in prefixes_to_remove:\n", "            if name.lower().startswith(prefix):\n", "                name = name[len(prefix):]\n", "        \n", "        # Remove file extensions\n", "        name = re.sub(r'\\.(java|class)$', '', name, flags=re.IGNORECASE)\n", "        \n", "        return name.strip()\n", "\n", "    def traverse(node, parent_type=None, parent_name=None):\n", "        # Handle class declarations\n", "        if node.type == 'class_declaration':\n", "            class_name = None\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    class_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "                    \n", "                    # File -> Class relationship\n", "                    records.append({\n", "                        'source_node': file_name,\n", "                        'source_type': 'file',\n", "                        'destination_node': class_name,\n", "                        'destination_type': 'class',\n", "                        'relationship': 'declares',\n", "                        'file_path': file_path\n", "                    })\n", "                    \n", "                    # Add API endpoints from registry\n", "                    class_info = class_registry.get(class_name, {})\n", "                    endpoints = class_info.get('endpoints', [])\n", "                    for ep in endpoints:\n", "                        endpoint_name = f\"{ep['method']} {ep['path']}\"\n", "                        records.append({\n", "                            'source_node': class_name,\n", "                            'source_type': 'class',\n", "                            'destination_node': endpoint_name,\n", "                            'destination_type': 'endpoint',\n", "                            'relationship': 'declares',\n", "                            'file_path': file_path\n", "                        })\n", "                    \n", "                    # Add database entities from registry\n", "                    db_entities = class_info.get('db_entities', [])\n", "                    for entity in db_entities:\n", "                        records.append({\n", "                            'source_node': class_name,\n", "                            'source_type': 'class',\n", "                            'destination_node': entity['name'],\n", "                            'destination_type': 'table',\n", "                            'relationship': 'maps_to',\n", "                            'file_path': file_path\n", "                        })\n", "                    break\n", "            \n", "            # Traverse children with class context\n", "            for child in node.children:\n", "                traverse(child, 'class', class_name)\n", "                \n", "        # Handle interface declarations\n", "        elif node.type == 'interface_declaration':\n", "            interface_name = None\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    interface_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "                    records.append({\n", "                        'source_node': file_name,\n", "                        'source_type': 'file',\n", "                        'destination_node': interface_name,\n", "                        'destination_type': 'interface',\n", "                        'relationship': 'declares',\n", "                        'file_path': file_path\n", "                    })\n", "                    break\n", "            \n", "            # Traverse children with interface context\n", "            for child in node.children:\n", "                traverse(child, 'interface', interface_name)\n", "                \n", "        # Handle method declarations - FIXED HIERARCHY\n", "        elif node.type == 'method_declaration':\n", "            method_name = None\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    method_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "                    \n", "                    # CORRECT: Class -> Method (not Method -> Class)\n", "                    if parent_name and parent_type in ['class', 'interface']:\n", "                        records.append({\n", "                            'source_node': parent_name,\n", "                            'source_type': parent_type,\n", "                            'destination_node': method_name,\n", "                            'destination_type': 'method',\n", "                            'relationship': 'declares',\n", "                            'file_path': file_path\n", "                        })\n", "                    break\n", "            \n", "            # Traverse children with method context\n", "            for child in node.children:\n", "                traverse(child, 'method', method_name)\n", "                \n", "        # Handle field declarations - FIXED HIERARCHY\n", "        elif node.type == 'field_declaration':\n", "            for child in node.children:\n", "                if child.type == 'variable_declarator':\n", "                    for grandchild in child.children:\n", "                        if grandchild.type == 'identifier':\n", "                            field_name = clean_node_name(source_code[grandchild.start_byte:grandchild.end_byte].decode('utf-8'))\n", "                            \n", "                            # CORRECT: Class -> Variable (not Variable -> Class)\n", "                            if parent_name and parent_type == 'class':\n", "                                records.append({\n", "                                    'source_node': parent_name,\n", "                                    'source_type': parent_type,\n", "                                    'destination_node': field_name,\n", "                                    'destination_type': 'variable',\n", "                                    'relationship': 'has_field',\n", "                                    'file_path': file_path\n", "                                })\n", "                                \n", "        # Handle variable usage in methods - FIXED HIERARCHY\n", "        elif node.type in ['assignment_expression', 'variable_declarator'] and parent_type == 'method':\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    var_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "                    if var_name and var_name != 'this' and parent_name:\n", "                        # CORRECT: Method -> Variable (not Variable -> Method)\n", "                        records.append({\n", "                            'source_node': parent_name,\n", "                            'source_type': parent_type,\n", "                            'destination_node': var_name,\n", "                            'destination_type': 'variable',\n", "                            'relationship': 'uses',\n", "                            'file_path': file_path\n", "                        })\n", "        else:\n", "            # Continue traversing for other node types\n", "            for child in node.children:\n", "                traverse(child, parent_type, parent_name)\n", "\n", "    traverse(root_node)\n", "    return records\n", "\n", "# Execute AST extraction\n", "ast_records = []\n", "for root, _, files in os.walk(BASE_PATH):\n", "    for file in files:\n", "        if file.endswith('.java'):\n", "            file_path = os.path.join(root, file)\n", "            try:\n", "                ast_records.extend(extract_ast_structure(file_path))\n", "            except Exception as e:\n", "                print(f'⚠️ Error processing {file}: {e}')\n", "                continue\n", "\n", "df_ast = pd.DataFrame(ast_records)\n", "print(f'✅ Stage 3B Complete: {len(df_ast)} AST relationships extracted')\n", "\n", "# Create AST name mapping for correcting LLM output\n", "def create_ast_name_mapping(df_ast):\n", "    \"\"\"Create mapping from lowercase names to correct AST names\"\"\"\n", "    name_mapping = {}\n", "    \n", "    # Extract all unique class and method names from AST\n", "    for _, row in df_ast.iterrows():\n", "        source_name = row['source_node']\n", "        dest_name = row['destination_node']\n", "        source_type = row['source_type']\n", "        dest_type = row['destination_type']\n", "        \n", "        # Map class names\n", "        if source_type == 'class' and source_name:\n", "            name_mapping[source_name.lower()] = source_name\n", "        if dest_type == 'class' and dest_name:\n", "            name_mapping[dest_name.lower()] = dest_name\n", "            \n", "        # Map method names\n", "        if source_type == 'method' and source_name:\n", "            name_mapping[source_name.lower()] = source_name\n", "        if dest_type == 'method' and dest_name:\n", "            name_mapping[dest_name.lower()] = dest_name\n", "    \n", "    return name_mapping\n", "\n", "# Create the mapping\n", "ast_name_mapping = create_ast_name_mapping(df_ast)\n", "print(f\"📝 Created AST name mapping with {len(ast_name_mapping)} entries\")\n", "\n", "# Store AST results in memory\n", "memory['ast_relationships'] = len(df_ast)\n", "memory['ast_name_mapping'] = ast_name_mapping\n", "save_memory(memory)"]}, {"cell_type": "code", "execution_count": null, "id": "stage4_improved_llm_extraction", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 4: IMPROVED LLM EXTRACTION WITH AST CONTEXT ==========\n", "\n", "def escape_braces_for_langchain(text):\n", "    \"\"\"Escape curly braces in Java code to prevent LangChain template variable conflicts\"\"\"\n", "    if not text:\n", "        return text\n", "    return text.replace('{', '{{').replace('}', '}}')\n", "\n", "def build_optimized_stage4b_prompt(file_path, ast_df):\n", "    \"\"\"Build optimized Stage 4B prompt with minimal context (AST + file info only)\"\"\"\n", "    \n", "    # Build AST context for the specific file\n", "    ast_subset = ast_df[ast_df['file_path'] == file_path] if len(ast_df) > 0 else pd.DataFrame()\n", "    ast_context = ''\n", "    for _, row in ast_subset.iterrows():\n", "        ast_context += f\"{row['source_type']}:{row['source_node']} -[{row['relationship']}]-> {row['destination_type']}:{row['destination_node']}\\n\"\n", "    \n", "    # Extract file name from path\n", "    file_name = Path(file_path).stem if file_path else 'UnknownFile'\n", "    \n", "    # Build simplified prompt for structural extraction only\n", "    prompt = f\"\"\"\n", "You are a Java code lineage extraction engine focused on STRUCTURAL relationships only.\n", "\n", "CURRENT FILE: {file_name}\n", "\n", "AST RELATIONSHIPS (FOLLOW THESE PATTERNS EXACTLY):\n", "{ast_context}\n", "\n", "EXTRACTION RULES - STRUCTURAL ONLY:\n", "\n", "1. Extract ONLY basic structural relationships:\n", "   - file -[declares]-> class\n", "   - class -[declares]-> method  \n", "   - class -[has_field]-> variable\n", "   - method -[uses]-> variable\n", "   - class -[declares]-> endpoint\n", "   - class -[maps_to]-> table\n", "\n", "2. Use SIMPLE names only (remove prefixes like method:, class:, etc.)\n", "3. NEVER create reverse relationships (method->class, variable->method, etc.)\n", "4. Follow the AST RELATIONSHIPS above for correct structure\n", "5. Clean node names (remove method:, class: prefixes)\n", "\n", "Extract relationships in format:\n", "[SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName\n", "\n", "Return ONLY the structural triples, no explanations.\n", "\"\"\"\n", "    return prompt.replace(\"{\", \"{{\").replace(\"}\", \"}}\")\n", "\n", "def smart_chunk_strategy(file_path, content):\n", "    \"\"\"Smart chunking: whole file if <1000 lines, language chunks if larger\"\"\"\n", "    lines = content.count('\\n') + 1\n", "    \n", "    # Escape curly braces to prevent <PERSON><PERSON><PERSON><PERSON> template conflicts\n", "    escaped_content = escape_braces_for_langchain(content)\n", "    \n", "    if lines <= 1000:\n", "        return [{'content': escaped_content, 'metadata': {'source': file_path, 'chunk_type': 'whole_file'}}]\n", "    else:\n", "        splitter = RecursiveCharacterTextSplitter.from_language(\n", "            language=LC_Language.JAVA,\n", "            chunk_size=8000,\n", "            chunk_overlap=400\n", "        )\n", "        doc = Document(page_content=escaped_content, metadata={'source': file_path})\n", "        chunks = splitter.split_documents([doc])\n", "        return [{'content': chunk.page_content, 'metadata': {**chunk.metadata, 'chunk_type': 'language_chunk'}} for chunk in chunks]\n", "\n", "# Collect documents with smart chunking\n", "smart_docs = []\n", "for root, _, files in os.walk(BASE_PATH):\n", "    for file in files:\n", "        if file.endswith('.java'):\n", "            file_path = os.path.join(root, file)\n", "            try:\n", "                with open(file_path, 'r', encoding='utf-8') as f:\n", "                    content = f.read()\n", "                smart_docs.extend(smart_chunk_strategy(file_path, content))\n", "                print(f\"📄 {file}: {content.count(chr(10)) + 1} lines → {len(smart_chunk_strategy(file_path, content))} chunk(s)\")\n", "            except Exception as e:\n", "                print(f\"⚠️ Error loading {file}: {e}\")\n", "                continue\n", "\n", "print(f\"📚 Total documents prepared: {len(smart_docs)}\")"]}, {"cell_type": "code", "execution_count": null, "id": "stage4b_improved_llm_processing", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 4B: IMPROVED LLM PROCESSING ==========\n", "\n", "all_llm_lineage = []\n", "for doc_info in tqdm(smart_docs, desc='🤖 Stage 4B: Enhanced LLM Processing'):\n", "    file_path = doc_info['metadata'].get('source')\n", "    \n", "    # Use optimized Stage 4B prompt with minimal context (AST + file info only)\n", "    enhanced_prompt = build_optimized_stage4b_prompt(file_path, df_ast)\n", "    \n", "    transformer = LLMGraphTransformer(\n", "        llm=llm,\n", "        additional_instructions=enhanced_prompt,\n", "        allowed_nodes=['class', 'interface', 'method', 'variable', 'table', 'endpoint'],  \n", "        allowed_relationships=[\n", "          \n", "            ('class', 'declares', 'method'),\n", "            ('interface', 'declares', 'method'),\n", "            ('class', 'declares', 'endpoint'),\n", "            ('method', 'calls', 'method'),\n", "            ('class', 'has_field', 'variable'),\n", "            ('method', 'uses', 'variable'),\n", "            ('class', 'uses', 'class'),\n", "            ('interface', 'extends', 'interface'),\n", "            ('class', 'extends', 'class'),\n", "            ('class', 'implements', 'interface'),\n", "            ('class', 'maps_to', 'table'),\n", "            ('method', 'reads_from', 'table'),\n", "            ('method', 'writes_to', 'table')\n", "        ],\n", "        strict_mode=True,\n", "        node_properties=False,\n", "        relationship_properties=False\n", "    )\n", "    \n", "    try:\n", "        # Ensure content is properly escaped for LangChain\n", "        escaped_content = escape_braces_for_langchain(doc_info['content'])\n", "        doc = Document(page_content=escaped_content, metadata=doc_info['metadata'])\n", "        graph_docs = transformer.convert_to_graph_documents([doc])\n", "        \n", "        for gd in graph_docs:\n", "            for rel in gd.relationships:\n", "                s_node = rel.source.id.strip()\n", "                s_type = rel.source.type.strip().lower()\n", "                t_node = rel.target.id.strip()\n", "                t_type = rel.target.type.strip().lower()\n", "                rel_type = rel.type.strip().lower()\n", "                \n", "                # Validate and fix relationship directions\n", "                def validate_relationship_direction(source_type, rel_type, target_type):\n", "                    \"\"\"Validate relationship direction and fix if needed\"\"\"\n", "                    valid_directions = {\n", "                        ('class', 'declares', 'method'),\n", "                        ('interface', 'declares', 'method'),\n", "                        ('class', 'declares', 'endpoint'),\n", "                        ('method', 'calls', 'method'),\n", "                        ('class', 'has_field', 'variable'),\n", "                        ('method', 'uses', 'variable'),\n", "                        ('class', 'uses', 'class'),\n", "                        ('interface', 'extends', 'interface'),\n", "                        ('class', 'extends', 'class'),\n", "                        ('class', 'implements', 'interface'),\n", "                        ('class', 'maps_to', 'table'),\n", "                        ('method', 'reads_from', 'table'),\n", "                        ('method', 'writes_to', 'table')\n", "                    }\n", "                    \n", "                    # Check if current direction is valid\n", "                    if (source_type, rel_type, target_type) in valid_directions:\n", "                        return source_type, rel_type, target_type, False\n", "                    \n", "                    # Check if reverse direction is valid\n", "                    if (target_type, rel_type, source_type) in valid_directions:\n", "                        return target_type, rel_type, source_type, True\n", "                    \n", "                    # Invalid relationship - skip\n", "                    return None, None, None, False\n", "                \n", "                # Validate direction\n", "                validated_s_type, validated_rel_type, validated_t_type, was_reversed = validate_relationship_direction(s_type, rel_type, t_type)\n", "                \n", "                if validated_s_type is None:\n", "                    print(f\"⚠️ Skipping invalid relationship: {s_type} -[{rel_type}]-> {t_type}\")\n", "                    continue\n", "                \n", "                if was_reversed:\n", "                    print(f\"🔄 Fixed direction: {s_type}:{s_node} -[{rel_type}]-> {t_type}:{t_node} → {validated_s_type}:{t_node} -[{validated_rel_type}]-> {validated_t_type}:{s_node}\")\n", "                    s_node, t_node = t_node, s_node\n", "                    s_type, t_type = validated_s_type, validated_t_type\n", "                    rel_type = validated_rel_type\n", "\n", "                def normalize_entity_improved(entity_name, entity_type):\n", "                    if not entity_name:\n", "                        return entity_name\n", "                    \n", "                    # Apply improved name cleaning\n", "                    clean_name = extract_clean_name(entity_name, entity_type)\n", "                    \n", "                    # CORRECTION: Use AST mapping to fix case inconsistencies\n", "                    if entity_type in ['class', 'method'] and clean_name:\n", "                        # Look up correct name from AST mapping\n", "                        correct_name = ast_name_mapping.get(clean_name.lower())\n", "                        if correct_name:\n", "                            clean_name = correct_name\n", "                            print(f\"🔧 Corrected {entity_type} name: {entity_name} → {clean_name}\")\n", "                    \n", "                    # Special handling for variables\n", "                    if entity_type == 'variable':\n", "                        # Filter out temp variables\n", "                        if is_temp_variable(clean_name):\n", "                            return None\n", "                        \n", "                        # Store variable context in memory\n", "                        if '.' in entity_name:\n", "                            context_part = entity_name.split('.')[0]\n", "                            context = extract_clean_name(context_part, 'method')\n", "                            memory['variable_contexts'][clean_name] = {\n", "                                'context': context,\n", "                                'context_type': 'method',\n", "                                'full_name': entity_name\n", "                            }\n", "                    \n", "                    return clean_name\n", "\n", "                s_node = normalize_entity_improved(s_node, s_type)\n", "                t_node = normalize_entity_improved(t_node, t_type)\n", "\n", "                if not s_node or not t_node or s_node == t_node:\n", "                    continue\n", "                \n", "                # Enforce correct relationship directions\n", "                valid_directions = {\n", "                    ('file', 'declares', 'class'),\n", "                    ('file', 'declares', 'interface'),\n", "                    ('class', 'declares', 'method'),\n", "                    ('interface', 'declares', 'method'),\n", "                    ('class', 'declares', 'endpoint'),\n", "                    ('class', 'has_field', 'variable'),\n", "                    ('method', 'uses', 'variable'),\n", "                    ('class', 'maps_to', 'table'),\n", "                    ('class', 'extends', 'class'),\n", "                    ('class', 'implements', 'interface'),\n", "                    ('interface', 'extends', 'interface'),\n", "                    ('method', 'calls', 'method'),\n", "                    ('method', 'reads_from', 'table'),\n", "                    ('method', 'writes_to', 'table')\n", "                }\n", "                \n", "                if (s_type, rel_type, t_type) not in valid_directions:\n", "                    continue\n", "\n", "                all_llm_lineage.append({\n", "                    'source_node': s_node,\n", "                    'source_type': s_type.title(),\n", "                    'destination_node': t_node,\n", "                    'destination_type': t_type.title(),\n", "                    'relationship': rel_type.upper(),\n", "                    'file_path': file_path\n", "                })\n", "                \n", "    except Exception as e:\n", "        print(f\"⚠️ LLM processing error for {file_path}: {e}\")\n", "        continue\n", "\n", "df_llm_lineage = pd.DataFrame(all_llm_lineage)\n", "\n", "# Update memory with LLM results\n", "for _, row in df_llm_lineage.iterrows():\n", "    edge_key = f\"{row['source_node']}-{row['relationship']}-{row['destination_node']}\"\n", "    memory['validated_edges'].add(edge_key)\n", "\n", "save_memory(memory)\n", "\n", "print(f'✅ Stage 4B Complete: {len(df_llm_lineage)} LLM relationships extracted')\n", "print(f'🧠 Memory updated with {len(memory[\"variable_contexts\"])} variable contexts')"]}, {"cell_type": "code", "execution_count": null, "id": "stage5_setup", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 5: BATCH PROCESSING WITH MEMORY OPTIMIZATION ==========\n", "\n", "import gc\n", "\n", "# Batch configuration for scalability\n", "BATCH_SIZE = 50  # Process 50 classes at a time\n", "\n", "# Initialize long-term memory for persistent storage\n", "long_term_memory = {\n", "    'all_validated_edges': set(),\n", "    'global_variable_contexts': {},\n", "    'global_method_signatures': {},\n", "    'processed_classes': set()\n", "}\n", "\n", "def get_class_specific_memory(class_name, long_term_memory):\n", "    \"\"\"Extract only memory relevant to current class for lightweight processing\"\"\"\n", "    relevant_memory = {\n", "        'variable_contexts': {},\n", "        'method_signatures': {}\n", "    }\n", "    \n", "    # Only include memory related to current class\n", "    for var, context in long_term_memory.get('global_variable_contexts', {}).items():\n", "        if context.get('context') == class_name or context.get('class_context') == class_name:\n", "            relevant_memory['variable_contexts'][var] = context\n", "    \n", "    for method, info in long_term_memory.get('global_method_signatures', {}).items():\n", "        if info.get('class') == class_name:\n", "            relevant_memory['method_signatures'][method] = info\n", "    \n", "    return relevant_memory\n", "\n", "def build_lightweight_memory_context(relevant_memory):\n", "    \"\"\"Build memory context string from filtered memory\"\"\"\n", "    memory_context = ''\n", "    \n", "    if relevant_memory.get('variable_contexts'):\n", "        memory_context += f\"Relevant Variable Contexts: {len(relevant_memory['variable_contexts'])} variables\\n\"\n", "        for var_name, context in list(relevant_memory['variable_contexts'].items())[:3]:  # Show first 3\n", "            memory_context += f\"  - {var_name} (context: {context.get('context', 'Unknown')})\\n\"\n", "    \n", "    if relevant_memory.get('method_signatures'):\n", "        memory_context += f\"Relevant Methods: {len(relevant_memory['method_signatures'])} methods\\n\"\n", "    \n", "    return memory_context\n", "\n", "# Prepare class batches\n", "class_names = list(class_registry.keys())\n", "class_batches = [class_names[i:i+BATCH_SIZE] for i in range(0, len(class_names), BATCH_SIZE)]\n", "\n", "print(f\"Stage 5 Setup: {len(class_names)} classes split into {len(class_batches)} batches of {BATCH_SIZE}\")\n", "print(f\"Batch processing will prevent memory overflow for large codebases\")"]}, {"cell_type": "code", "execution_count": null, "id": "stage5_batch_execution", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 5: BATCH EXECUTION WITH SHORT/LONG TERM MEMORY ==========\n", "\n", "all_transformation_relationships = []\n", "\n", "# Process each batch with memory optimization\n", "for batch_num, batch_classes in enumerate(class_batches):\n", "    print(f\"\\nProcessing batch {batch_num + 1}/{len(class_batches)} ({len(batch_classes)} classes)\")\n", "    \n", "    # Initialize short-term memory for this batch only\n", "    short_term_memory = {\n", "        'variable_contexts': {},\n", "        'method_signatures': {},\n", "        'current_batch_edges': set()\n", "    }\n", "    \n", "    batch_relationships = []\n", "    \n", "    # Process each class in the batch\n", "    for class_name in tqdm(batch_classes, desc=f\"Batch {batch_num + 1} Processing\"):\n", "        if class_name not in class_registry:\n", "            continue\n", "            \n", "        class_info = class_registry[class_name]\n", "        source_code = class_info['source_code']\n", "        escaped_source_code = escape_braces_for_langchain(source_code)\n", "        \n", "        # Get class-specific memory context (lightweight)\n", "        relevant_memory = get_class_specific_memory(class_name, long_term_memory)\n", "        memory_context = build_lightweight_memory_context(relevant_memory)\n", "        \n", "        try:\n", "            # Enhanced transformation prompt with lightweight memory context\n", "            transformation_prompt = f\"\"\"\n", "You are a Java data flow analysis engine for class: {class_name}\n", "\n", "LIGHTWEIGHT MEMORY CONTEXT:\n", "{memory_context}\n", "\n", "EXTRACT DATA FLOW RELATIONSHIPS:\n", "\n", "1. VARIABLE TRANSFORMATIONS & FLOWS:\n", "   - Variable -[FLOWS_TO]-> Variable (data passing)\n", "   - Variable -[TRANSFORMS_TO]-> Variable (data conversion)\n", "   - Method -[PRODUCES]-> Variable (method creates variable)\n", "\n", "2. DATABASE OPERATIONS:\n", "   - Method -[READS_FROM]-> Table (SELECT operations)\n", "   - Method -[WRITES_TO]-> Table (INSERT/UPDATE/DELETE)\n", "   - Variable -[PERSISTS_TO]-> Table (entity persistence)\n", "\n", "3. API RELATIONSHIPS:\n", "   - Class -[EXPOSES]-> Endpoint (REST endpoints)\n", "   - Method -[MAPS_TO]-> Endpoint (method-endpoint mapping)\n", "   - Endpoint -[ACCEPTS]-> Variable (request parameters)\n", "   - Endpoint -[RETURNS]-> Variable (response data)\n", "\n", "4. METHOD OPERATIONS:\n", "   - Method -[CALLS]-> Method (method invocations)\n", "   - Method -[INVOKES]-> ExternalService (external API calls)\n", "\n", "NAMING RULES:\n", "- PascalCase for Methods, Classes, Endpoints\n", "- Variables: only variable name (userDto, not CreateUser.userDto)\n", "- Endpoints: include HTTP method (GET:/api/users)\n", "- Tables: singular form (User, not Users)\n", "- Filter temp variables (i, j, temp, tmp, counter)\n", "\n", "CODE:\n", "{escaped_source_code}\n", "\n", "Return format: [SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName\n", "\"\"\"\n", "            \n", "            response = llm.invoke(transformation_prompt)\n", "            content = response.content if hasattr(response, 'content') else str(response)\n", "            \n", "            doc = Document(page_content=content, metadata={'class_name': class_name})\n", "            \n", "            transformer = LLMGraphTransformer(\n", "                llm=llm,\n", "                allowed_nodes=['variable', 'method', 'table', 'class', 'endpoint', 'database', 'externalservice'],\n", "                allowed_relationships=[\n", "                    ('variable', 'flows_to', 'variable'),\n", "                    ('variable', 'transforms_to', 'variable'),\n", "                    ('method', 'produces', 'variable'),\n", "                    ('method', 'reads_from', 'table'),\n", "                    ('method', 'writes_to', 'table'),\n", "                    ('variable', 'persists_to', 'table'),\n", "                    ('class', 'exposes', 'endpoint'),\n", "                    ('method', 'maps_to', 'endpoint'),\n", "                    ('endpoint', 'accepts', 'variable'),\n", "                    ('endpoint', 'returns', 'variable'),\n", "                    ('method', 'calls', 'method'),\n", "                    ('method', 'invokes', 'externalservice')\n", "                ],\n", "                strict_mode=False,\n", "                node_properties=False,\n", "                relationship_properties=False,\n", "            )\n", "            \n", "            graph_docs = transformer.convert_to_graph_documents([doc])\n", "            \n", "            for gd in graph_docs:\n", "                for rel in gd.relationships:\n", "                    s_type = rel.source.type.title()\n", "                    t_type = rel.target.type.title()\n", "                    s_node = extract_clean_name(rel.source.id, s_type.lower())\n", "                    t_node = extract_clean_name(rel.target.id, t_type.lower())\n", "                    rel_type = rel.type.upper()\n", "                    \n", "                    # CORRECTION: Use AST mapping to fix case inconsistencies in Stage 5\n", "                    if s_type.lower() in ['class', 'method'] and s_node:\n", "                        correct_name = ast_name_mapping.get(s_node.lower())\n", "                        if correct_name:\n", "                            s_node = correct_name\n", "                    \n", "                    if t_type.lower() in ['class', 'method'] and t_node:\n", "                        correct_name = ast_name_mapping.get(t_node.lower())\n", "                        if correct_name:\n", "                            t_node = correct_name\n", "                    \n", "                    # Filter out temp variables\n", "                    if s_type == 'Variable' and is_temp_variable(s_node):\n", "                        continue\n", "                    if t_type == 'Variable' and is_temp_variable(t_node):\n", "                        continue\n", "                    \n", "                    if not s_node or not t_node or s_node == t_node:\n", "                        continue\n", "                    \n", "                    relationship = {\n", "                        'source_node': s_node,\n", "                        'source_type': s_type,\n", "                        'destination_node': t_node,\n", "                        'destination_type': t_type,\n", "                        'relationship': rel_type,\n", "                        'class_context': class_name,\n", "                        'stage': 'batch_transformations'\n", "                    }\n", "                    \n", "                    batch_relationships.append(relationship)\n", "                    \n", "                    # Store in short-term memory for this batch\n", "                    edge_key = f\"{s_node}-{rel_type}-{t_node}\"\n", "                    short_term_memory['current_batch_edges'].add(edge_key)\n", "                    \n", "                    # Store variable context in short-term memory\n", "                    for node_name, node_type in [(s_node, s_type), (t_node, t_type)]:\n", "                        if node_type == 'Variable':\n", "                            short_term_memory['variable_contexts'][node_name] = {\n", "                                'context': class_name,\n", "                                'context_type': 'class',\n", "                                'stage': '5_batch_transformations'\n", "                            }\n", "            \n", "        except Exception as llm_error:\n", "            print(f\"LLM transformation error for {class_name}: {llm_error}\")\n", "            continue\n", "    \n", "    # Move batch results to long-term memory\n", "    long_term_memory['all_validated_edges'].update(short_term_memory['current_batch_edges'])\n", "    long_term_memory['global_variable_contexts'].update(short_term_memory['variable_contexts'])\n", "    long_term_memory['processed_classes'].update(batch_classes)\n", "    \n", "    # Add batch relationships to overall results\n", "    all_transformation_relationships.extend(batch_relationships)\n", "    \n", "    print(f\"Batch {batch_num + 1} complete: {len(batch_relationships)} relationships extracted\")\n", "    \n", "    # MEMORY CLEANUP - Critical for scalability\n", "    del short_term_memory\n", "    del batch_relationships\n", "    del relevant_memory\n", "    gc.collect()  # Force garbage collection\n", "    \n", "    print(f\"Memory cleaned for batch {batch_num + 1}\")\n", "\n", "# Create final DataFrame\n", "df_transformations = pd.DataFrame(all_transformation_relationships)\n", "\n", "print(f'\\nStage 5 Complete: {len(df_transformations)} transformation relationships extracted')\n", "print(f'Total classes processed: {len(long_term_memory[\"processed_classes\"])}')\n", "print(f'Total edges in long-term memory: {len(long_term_memory[\"all_validated_edges\"])}')\n", "\n", "# Summary statistics\n", "transformations_count = len([r for r in all_transformation_relationships if 'TRANSFORMS_TO' in r.get('relationship', '')])\n", "flows_count = len([r for r in all_transformation_relationships if 'FLOWS_TO' in r.get('relationship', '')])\n", "produces_count = len([r for r in all_transformation_relationships if 'PRODUCES' in r.get('relationship', '')])\n", "db_ops_count = len([r for r in all_transformation_relationships if r.get('relationship') in ['READS_FROM', 'WRITES_TO']])\n", "method_calls_count = len([r for r in all_transformation_relationships if 'CALLS' in r.get('relationship', '')])\n", "\n", "print(f'\\nRelationship breakdown:')\n", "print(f'Transformations: {transformations_count}, Flows: {flows_count}')\n", "print(f'Produces: {produces_count}, DB Ops: {db_ops_count}')\n", "print(f'Method Calls: {method_calls_count}')\n", "print(f'Scalability: Processed in {len(class_batches)} batches with memory cleanup')"]}, {"cell_type": "code", "execution_count": null, "id": "stage6_final_consolidation", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 6: FINAL CONSOLIDATION ==========\n", "\n", "# Combine all DataFrames\n", "all_dataframes = []\n", "\n", "if len(df_hierarchy) > 0:\n", "    all_dataframes.append(df_hierarchy)\n", "    print(f\"Hierarchy relationships: {len(df_hierarchy)}\")\n", "    \n", "\n", "if len(df_llm_lineage) > 0:\n", "    all_dataframes.append(df_llm_lineage)\n", "    print(f\"LLM lineage relationships: {len(df_llm_lineage)}\")\n", "\n", "if len(df_transformations) > 0:\n", "    all_dataframes.append(df_transformations)\n", "    print(f\"Transformation relationships: {len(df_transformations)}\")\n", "\n", "# Consolidate all relationships\n", "if all_dataframes:\n", "    df_final = pd.concat(all_dataframes, ignore_index=True)\n", "else:\n", "    df_final = pd.DataFrame()\n", "\n", "# Remove duplicates based on core relationship columns\n", "df_final = df_final.drop_duplicates(subset=['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship'])\n", "\n", "# Filter to only allowed nodes and relationships\n", "allowed_nodes = {'Folder', 'File', 'Class', 'Interface', 'Method', 'Variable', 'Table', 'Endpoint', 'Database', 'Externalservice'}\n", "allowed_relationships = {\n", "    'CONTAINS', 'DECLARES', 'HAS_FIELD', 'USES', 'CALLS', 'EXTENDS', 'IMPLEMENTS',\n", "    'MAPS_TO', 'READS_FROM', 'WRITES_TO', 'FLOWS_TO', 'TRANSFORMS_TO', 'PRODUCES',\n", "    'EXPOSES', 'ACCEPTS', 'RETURNS', 'INVOKES', 'PERSISTS_TO'\n", "}\n", "\n", "# Apply filters\n", "df_final = df_final[\n", "    (df_final['source_type'].isin(allowed_nodes)) &\n", "    (df_final['destination_type'].isin(allowed_nodes)) &\n", "    (df_final['relationship'].isin(allowed_relationships))\n", "]\n", "\n", "\n", "# Clean up column names and ensure consistency\n", "required_columns = ['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship']\n", "for col in required_columns:\n", "    if col not in df_final.columns:\n", "        df_final[col] = ''\n", "\n", "# Final cleaning and validation\n", "df_final = df_final[df_final['source_node'].notna() & (df_final['source_node'] != '')]\n", "df_final = df_final[df_final['destination_node'].notna() & (df_final['destination_node'] != '')]\n", "\n", "# Filter out self-referential relationships EXCEPT for valid File-Class DECLARES\n", "file_class_declares = (\n", "    (df_final['source_type'] == 'File') & \n", "    (df_final['destination_type'] == 'Class') & \n", "    (df_final['relationship'] == 'DECLARES')\n", ")\n", "df_final = df_final[\n", "    (df_final['source_node'] != df_final['destination_node']) | file_class_declares\n", "]\n", "\n", "# Save to CSV\n", "csv_filename = 'servicesbolt_lineage_v10.csv'\n", "df_final[required_columns].to_csv(csv_filename, index=False)\n", "\n", "print(f\"\\n✅ Stage 6 Complete: {len(df_final)} relationships consolidated\")\n", "print(f\"📄 CSV saved: {csv_filename}\")\n", "\n", "# Summary by relationship type\n", "relationship_summary = df_final['relationship'].value_counts()\n", "print(\"\\nRelationship breakdown:\")\n", "for rel_type, count in relationship_summary.items():\n", "    print(f\"  {rel_type}: {count}\")"]}, {"cell_type": "code", "execution_count": null, "id": "stage7_neo4j_upload", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 7: NEO4J UPLOAD ==========\n", "\n", "def upload_to_neo4j(df_final):\n", "    \"\"\"Upload relationships to Neo4j database\"\"\"\n", "    # Collect unique nodes\n", "    unique_nodes = set()\n", "    for _, row in df_final.iterrows():\n", "        unique_nodes.add((row['source_node'], row['source_type']))\n", "        unique_nodes.add((row['destination_node'], row['destination_type']))\n", "    \n", "    print(f\"Creating {len(unique_nodes)} unique nodes...\")\n", "    \n", "    # Create nodes\n", "    for node_name, node_type in tqdm(unique_nodes, desc=\"Creating nodes\"):\n", "        create_query = f\"MERGE (n:{node_type} {{name: '{node_name}'}})\"\n", "        try:\n", "            graph.query(create_query)\n", "        except Exception as e:\n", "            print(f\"Error creating node {node_name} ({node_type}): {e}\")\n", "    \n", "    # Create relationships\n", "    for _, row in tqdm(df_final.iterrows(), desc=\"Creating relationships\", total=len(df_final)):\n", "        create_rel_query = f\"\"\"\n", "        MATCH (s:{row['source_type']} {{name: '{row['source_node']}'}})\n", "        MATCH (t:{row['destination_type']} {{name: '{row['destination_node']}'}})\n", "        MERGE (s)-[:{row['relationship']}]->(t)\n", "        \"\"\"\n", "        try:\n", "            graph.query(create_rel_query)\n", "        except Exception as e:\n", "            print(f\"Error creating relationship: {e}\")\n", "    \n", "    print(f\"✅ Neo4j upload complete: {len(unique_nodes)} nodes, {len(df_final)} relationships\")\n", "\n", "\n", "# Execute Neo4j upload\n", "upload_to_neo4j(df_final)\n", "\n", "# Final memory save\n", "save_memory(memory)\n", "\n", "print(\"\\n========== PIPELINE COMPLETE ==========\")\n", "print(f\"✅ Stage 7 Complete: Data uploaded to Neo4j\")\n", "print(f\"📊 Final relationships: {len(df_final)}\")\n", "print(f\"💾 Memory file: {MEMORY_FILE}\")\n", "print(f\"📄 CSV output: java_lineage_v10.csv\")\n", "print(f\"🗄️ Neo4j database: {NEO4J_DB}\")\n", "print(\"========================================\")"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 4}