package AppOne;

import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/user")
public class UserService {

    private UserRepository userRepository = new UserRepository();

    @PostMapping("/create")
    public User createUser(@RequestBody UserDto userDto) {
        User user = UserUtils.convertDtoToEntity(userDto);
        user.setStatus("ACTIVE");
        userRepository.save(user); // writes to User table
        return user;
    }

    @GetMapping("/get/{id}")
    public User getUser(@PathVariable int id) {
        return userRepository.findById(id); // reads from User table
    }
}