package com.bolt.dashboard.jira;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.joda.time.DateTime;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.json.simple.parser.ParseException;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.config.MongoAggregate;
import com.bolt.dashboard.core.model.ALMConfiguration;
import com.bolt.dashboard.core.model.ChangeHistoryModel;
import com.bolt.dashboard.core.model.EffortHistoryModel;
import com.bolt.dashboard.core.model.IterationModel;
import com.bolt.dashboard.core.model.MetricsModel;
import com.bolt.dashboard.core.model.PortfolioViewConfig;
import com.bolt.dashboard.core.model.ProjectModel;
import com.bolt.dashboard.core.model.TransitionModel;
import com.bolt.dashboard.core.repository.ALMConfigRepo;
import com.bolt.dashboard.core.repository.ChangeHisortyRepo;
import com.bolt.dashboard.core.repository.EffortHistoryRepo;
import com.bolt.dashboard.core.repository.IterationRepo;
import com.bolt.dashboard.core.repository.MetricRepo;
import com.bolt.dashboard.core.repository.PortfolioViewConfigRepo;
import com.bolt.dashboard.core.repository.ProjectRepo;
import com.bolt.dashboard.core.repository.TransitionRepo;

/**
 * <AUTHOR>
 *
 */
public class ALMClientImplementation implements JIRAClient {

	private static final Logger LOGGER = LogManager.getLogger(ALMClientImplementation.class);
	String almType = "";
	// Modulewise class separation
	JiraAuthentication auth = null;
	ChangeHisortyRepo changeHisortyRepo = null;
	List<ChangeHistoryModel> changeLogs = null;
	ALMConfigRepo almConfigRepo = null;
	ALMConfiguration almConfig = null;
	PortfolioViewConfig portfolioViewConfig = null;
	long createdDate;
	long creationTime;
	private AnnotationConfigApplicationContext ctx = null;
	EffortHistoryRepo effortHistoryRepo = null;
	EffortAndChangeItemInfo effortInfo = null;
	List<EffortHistoryModel> efforts = null;
	JSONObject fieldsOfJSONData = null;
	JSONObject historyjsonOutput = null;
	JSONArray historyArray = null;
	IterationModel iteration = null;
	IterationInfo iterationInfo = null;
	IterationRepo iterationRepo = null;
	Set<IterationModel> iterationSet;
	MetricRepo metricRepo = null;
	MetricsModel metrics = null;
	MetricsInfo metricsInfo = null;
	Set<MetricsModel> metricsSet = new LinkedHashSet<>();
	double origEst = 0;
	String projectName = "";
	ProjectRepo projectRepo = null;
	PortfolioViewConfigRepo portfolioViewConfigRepo = null;
	int sprintId;
	String sprintName = "";
	List<Integer> multipleSprints = new ArrayList<>();
	SprintWiseCalculation sprintWiseCalculation = null;
	Set<String> subTaskStateList = new LinkedHashSet<>();
	List<TransitionModel> taskDetailsList = null;
	long taskEffort;
	String taskFirstState = null;

	String taskLastState = null;
	List<String> taskList = null;
	Set<String> taskSet = null;
	TransitionInfo transitionInfo = null;
	TransitionRepo transitionRepo = null;
	String wid = "";
	RallyAuthentication rallyAuth = null;
	
	// New field for project key
	String projKey = "";

	String rallyFeatureURL = "https://rally1.rallydev.com/slm/webservice/v2.0/portfolioitem/feature?query=(FormattedID = \"";

	String rallyFeatureEnd = "\")";

	String FeatureId = "";
    public MongoTemplate template = null;
    String filter="";
//	public static void main(String[] args) {
//		AnnotationConfigApplicationContext	ctx = DataConfig.getContext();
//		PortfolioViewConfigRepo portfolioViewConfigRepo = ctx.getBean(PortfolioViewConfigRepo.class);
//		PortfolioViewConfig	portfolioViewConfig = portfolioViewConfigRepo.findByProjectName("Network Personalization ART");
//		rallyAuth = new RallyAuthentication();
//		new ALMClientImplementation().rallyAuth.callRallyUrl(
//				"https://rally1.rallydev.com/slm/webservice/v2.0/portfolioitem/feature?query=(FormattedID = \"F1\")",
//				portfolioViewConfig.getRallyPassword());
//	}

	/**
	 * @throws ParseException
	 *
	 **/
	@SuppressWarnings("unchecked")
	@Override
	public ProjectModel getALMToolData(String jiraUrl, String userName, String password, String name, String key,
			String type,String board) throws JiraExceptions, ParseException {
		projectName = name;
		this.projKey = key;
		
		almType = type;
		// initialize all required repository and classes
		init();
		LOGGER.info("init done added");
		String releaseUrl = jiraUrl.replace("search", "project/" + key + "/versions");
		String statusUrl = jiraUrl.replace("search", "project/" + key + "/statuses");
		String customFieldUrl = jiraUrl.replace("search", "field");
		JSONArray customFieldsJsonArray = (JSONArray) new JSONParser()
				.parse(auth.jiraConnectionForStatus(customFieldUrl, userName, password/*
																						 * , key
																						 */).toString());
		JSONArray releaseJsonArray = (JSONArray) new JSONParser()
				.parse(auth.jiraConnectionForStatus(releaseUrl, userName, password/* , key */).toString());
		
		// populate release
		if (!almType.equals("JIRA DEFECTS")) {
			new ReleaseInfo().getReleaseDetails(releaseJsonArray, projectName, almType);
		}else {
			LOGGER.info("Release in else :" +almType);
		}

		almConfig = almConfigRepo.findByProjectName(name).get(0);
		if (!almConfig.getEnvironment().equals("")) {
			customFieldNames.environmetKey = almConfig.getEnvironment();
		}
		Map<String, String> customFieldsMap = customFieldNames.getCustomFieldsInfo(customFieldsJsonArray);
		Map<String, String> customFieldsMapOfLpm = customFieldNames.getCustomFieldsOfLpm(customFieldsJsonArray);
//	for(String custom:customFieldsMap.keySet()) {
//		LOGGER.info("Custom Fields "+custom + " Value " + customFieldsMap.get(custom));
//          }
		JSONArray statusArray = (JSONArray) new JSONParser()
				.parse(auth.jiraConnectionForStatus(statusUrl, userName, password/* , key */).toString());
		setVariables(statusArray, subTaskStateList);
		// filter by project name and almType
		ProjectModel pDetails = ctx.getBean(ProjectRepo.class).findByProjectNameAndAlmType(projectName, almType);
		Long cRunDate = null;
		if (pDetails != null)
			cRunDate = pDetails.getcRuns();
		else {
			pDetails = new ProjectModel();
			pDetails.setAlmType(almType);
			pDetails.setProjectName(projectName);
			pDetails.setProjKey(projKey);
		}
		Long currentTimeMiliseconds = ConstantVariable.timestamp(new DateTime(), projectName);
		Date lastCollRunTime = cRunDate != null ? new Date(cRunDate) : null;
		JSONObject jsonOutput = null;
		
		//check if board is configure and fect board filter id if configured
		if(board!=null && !board.isEmpty()) {
			filter=auth.getBoardFilterId(jiraUrl, userName, password, board);
			filter = "%20AND%20filter="+filter+"%20";
		}
		
		try {
			if (key.equals("IN")) {
				// tempResponse=auth.alternateJiraConnection(jiraUrl, userName, password, key,
				// 0, 0, cRunDate,currentTimeMiliseconds,false);
				// jsonOutput = parseAsNewObject(tempResponse);
			} else {
				// tempResponse=auth.alternateJiraConnection(jiraUrl, userName, password, key,
				// 0, 0, cRunDate,currentTimeMiliseconds,false);
				// jsonOutput=parseAsNewObject(tempResponse);
				jsonOutput = (JSONObject) new JSONParser().parse(auth.alternateJiraConnection(jiraUrl, userName,
						password, key, 0, 0, cRunDate, currentTimeMiliseconds, false,filter).toString());
			}
			Long totalIssues = 0L;
			if (jsonOutput != null && jsonOutput.containsKey("total")) {
				totalIssues = (long) jsonOutput.get("total");
			}
			int startAt = 0, maxResults = 100;
			while (startAt < totalIssues) {
				LOGGER.info("total Issues : " + totalIssues + " , startAt :" + startAt);

				jsonOutput = (JSONObject) new JSONParser().parse(auth.alternateJiraConnection(jiraUrl, userName,
						password, key, startAt, maxResults, cRunDate, currentTimeMiliseconds, false,filter).toString());

				// jsonOutput = (JSONObject) new JSONParser().parse(new
				// FileReader("C:\\Mahesh\\backup\\Bolt\\REsponse ac component\\Response
				// 1.json"));
				JSONArray jsonArrayOutput = (JSONArray) jsonOutput.get("issues");
				
				startAt += maxResults;
				for (Object obj : jsonArrayOutput) {
					// Object obj = jsonOutput;
					metrics = new MetricsModel();
					JSONObject jsonNew = (JSONObject) new JSONParser()
							.parse(new StringBuilder(obj.toString()).toString());
					wid = jsonNew.get("key").toString();
				//	LOGGER.info(" Issues id: " + wid);
					taskList = new ArrayList<>();

					historyjsonOutput = (JSONObject) jsonNew.get("changelog");
					historyArray = (JSONArray) historyjsonOutput.get("histories");
					historyArray = getSortedJson(historyArray);

					fieldsOfJSONData = (JSONObject) jsonNew.get("fields");
					JSONArray sprintJson = (JSONArray) fieldsOfJSONData
							.get(customFieldNames.JIRA_SPRINT_FIELD_BRILLIO.toString());
				
					List<Object> dataList;
					// get sprint details

					if (!(sprintJson == null)) {
					
						dataList = iterationInfo.populateIteration(fieldsOfJSONData, projectName, iterationSet, almType,
								this.projKey, customFieldsMap);

						iterationSet.addAll((Set<IterationModel>) dataList.get(0));
						// changelog historyjsonoutput
						// getSprintName(historyjsonOutput) //get histories
						sprintName = dataList.get(1).toString();
						sprintId = Integer.parseInt(dataList.get(2).toString());
						multipleSprints = (List<Integer>) dataList.get(4);
					} else {
						boolean response = true;
						sprintId = 0;
						sprintName = ConstantVariable.ALM_BACKLOG;
						iteration = new IterationModel();
						iteration.setpName(projectName);
						iteration.setsName(ConstantVariable.ALM_BACKLOG);

						iteration.setProjKey(this.projKey);
						iteration.setpAlmType(almType);
						Iterator<IterationModel> modelIterator = iterationSet.iterator();
						while (modelIterator.hasNext())
							if (modelIterator.next().getsId() == (iteration.getsId()))
								response = false;
						if (response)
							iterationSet.add(iteration);
					}
					// populate metrics details
					if (fieldsOfJSONData.get(ConstantVariable.ALM_TIMEORIGINALESTIMATE) != null)
						origEst = Double.parseDouble(
								fieldsOfJSONData.get(ConstantVariable.ALM_TIMEORIGINALESTIMATE).toString());
					
					populateMetrics(fieldsOfJSONData, customFieldsMap, customFieldsMapOfLpm);
					taskEffort = fieldsOfJSONData.get("timespent") == null ? 0
							: Long.parseLong(fieldsOfJSONData.get("timespent").toString());

					creationTime = ConstantVariable
							.timestamp(fieldsOfJSONData.get(ConstantVariable.ALM_CREATED).toString(), projectName);
					List<Object> objectList = effortInfo.populateSubArrays(historyArray, wid, cRunDate, projectName,
							sprintName, origEst, this.projKey);
					changeLogs.addAll((List<ChangeHistoryModel>) objectList.get(2));
					efforts.addAll((List<EffortHistoryModel>) objectList.get(3));
					metrics.setEpicLink(objectList.get(4) == null ? "" : objectList.get(4).toString());

					/*
					 * TransitionMetrices initialization to avoid multiple arguments
					 */
					
					TransitionMetrices transitionMetrices = new TransitionMetrices();
					transitionMetrices.setpName(projectName);
					transitionMetrices.setCrTime(creationTime);
					transitionMetrices.setEffort(taskEffort);
					transitionMetrices.setFilteredStatusArray((JSONArray) objectList.get(0));
					transitionMetrices.setFirstState(taskFirstState);
					transitionMetrices.setLastState(taskLastState);
					transitionMetrices.setModifiedDateList((List<Long>) objectList.get(1));

					transitionMetrices.setsName(sprintName);
					transitionMetrices.setTaskDetailsList(taskDetailsList);
					transitionMetrices.setwId(metrics.getwId());

					transitionInfo.populateTransition(transitionMetrices, wid, metrics, this.projectName, this.projKey);
					// populateIteration(jsonNew);
					metricsSet.add(metrics);
					//LOGGER.info("metrics Added :" + metrics.getwId());
				}

			}
			pDetails.setcRuns(currentTimeMiliseconds);
			// sort based on sprint start date
			List<IterationModel> sortedIteration = sortIterationData(iterationSet);
			if (lastCollRunTime == null) {

				iterationRepo.save(sortedIteration);
				metricRepo.save(metricsSet);

			} else {

				saveIteration(sortedIteration);
				saveMetrics(metricsSet);
			}
			if (changeHisortyRepo == null) {
				ctx.getBean(ChangeHisortyRepo.class).save(changeLogs);
			} else {
				changeHisortyRepo.save(changeLogs);
			}

			transitionRepo.save(taskDetailsList);
			effortHistoryRepo.save(efforts);
			sprintWiseCalculation.getDefectsSev(projectName, pDetails);
			projectRepo.save(pDetails);
			// sprint wise data calculation
			sprintWiseCalculation.getSprintData(projectName);
			

			MongoAggregate mongoAggregate = new MongoAggregate();
			mongoAggregate.updateComponentForTaskAndSubtasks(projectName);
			if(almType.toLowerCase().contains("kanban")) {
		    	deleteIterations();
		    }
			mongoAggregate.aggregate(projectName, almType);

		} catch (Exception e) {
			LOGGER.error(e);
		}
		return null;
	}
	
    private void deleteIterations() {
    	Query query = new Query();
		query.addCriteria(Criteria.where("pName").is(projectName));
		if (template.count(query, "Iterations") > 0) {
			template.remove(query, "Iterations");
		}
		List<MetricsModel> totalMetrics = metricRepo.findByPNameAndPAlmType(projectName, almType);
		int count = totalMetrics.size() /500;
		List<MetricsModel> newMetrics = new ArrayList<MetricsModel>(); 
		int i=0;
		while(i<=count) {
			List<MetricsModel> metricspage;
			if((i*500)+500<=totalMetrics.size())
				metricspage = totalMetrics.subList(i*500, (i*500)+500);
			else
				metricspage = totalMetrics.subList(i*500,totalMetrics.size());
			final int count1= i;
			metricspage.stream().forEach((m) ->{
				m.setsName("Kanban"+count1);
				m.setsId(count1);
				
			});
			IterationModel itr = new IterationModel();
			itr.setpAlmType(almType);
			itr.setpName(projectName);
			itr.setStDate(0L);
			itr.setCompletedDate(1999999999999L);
			itr.setEndDate(1999999999999L);
			itr.setState("closed");
			itr.setsName("Kanban"+count1);
			itr.setsId(count1);
			itr.setProjKey(projKey);
			iterationRepo.save(itr);	
			newMetrics.addAll(metricspage);
			i++;
		}
		 query = new Query();
			query.addCriteria(Criteria.where("pName").is(projectName).and("pAlmType").is(almType));
			template.remove(query, "Metrics");
		
		metricRepo.save(newMetrics);
		
		
//		 template.updateMulti(
//				 new Query(Criteria.where("pName").is(projectName).and("pAlmType").is(almType)),new Update().set("sName","Kanban").set("sId", 0),MetricsModel.class);
		
    }

	public List<IterationModel> sortIterationData(Set<IterationModel> iterationSet) {
		List<IterationModel> list = new ArrayList<IterationModel>(iterationSet);
		List<IterationModel> soretedjsonValues = new ArrayList<>();

		Collections.sort(list, new Comparator<IterationModel>() {
			@Override
			public int compare(IterationModel a, IterationModel b) {
				String valA = null;
				String valB = null;

				try {
					valA = String.valueOf(a.getStDate());
					valB = String.valueOf(b.getStDate());
					if (valA == null)
						valA = String.valueOf(0);
					if (valB == null)
						valB = String.valueOf(0);
				} catch (Exception e) {
					LOGGER.error(e.getMessage());
				}
				if (valA != null)
					return valA.compareTo(valB);
				else
					return -1;
			}

		});

		for (int i = 0; i < list.size(); i++) {
			soretedjsonValues.add(list.get(i));
		}
		return soretedjsonValues;
	}

	public void init() {
		ctx = DataConfig.getContext();
		transitionRepo = ctx.getBean(TransitionRepo.class);
		// ALMProject
		projectRepo = ctx.getBean(ProjectRepo.class);
		metricRepo = ctx.getBean(MetricRepo.class);
		iterationRepo = ctx.getBean(IterationRepo.class);
		effortHistoryRepo = ctx.getBean(EffortHistoryRepo.class);
		changeHisortyRepo = ctx.getBean(ChangeHisortyRepo.class);
		changeLogs = new ArrayList<>();
		efforts = new ArrayList<>();
		taskDetailsList = new ArrayList<>();
		iterationSet = new LinkedHashSet<>();
		metricsSet = new LinkedHashSet<>();

		auth = new JiraAuthentication();
		metricsInfo = new MetricsInfo();
		iterationInfo = new IterationInfo();
		effortInfo = new EffortAndChangeItemInfo();
		transitionInfo = new TransitionInfo();
		sprintWiseCalculation = new SprintWiseCalculation();
		almConfigRepo = ctx.getBean(ALMConfigRepo.class);
		portfolioViewConfigRepo = ctx.getBean(PortfolioViewConfigRepo.class);
		rallyAuth = new RallyAuthentication();
		try {
			template = DataConfig.getInstance().mongoTemplate();
		} catch (Exception e) {
			
			LOGGER.error("Error In ALM Client:While intitializing mongotemplate", e);
		}
	}

	public JSONArray getSortedJson(JSONArray jsonArr) {
		JSONArray sortedJsonArray = new JSONArray();

		List<JSONObject> jsonValues = new ArrayList<JSONObject>();
		for (int i = 0; i < jsonArr.size(); i++) {
			jsonValues.add((JSONObject) jsonArr.get(i));
		}
		Collections.sort(jsonValues, new Comparator<JSONObject>() {
			@Override
			public int compare(JSONObject a, JSONObject b) {
				String valA = null;
				String valB = null;

				try {
					valA = (String) a.get("created");
					valB = (String) b.get("created");
				} catch (Exception e) {
					// do something
					LOGGER.error(e.getMessage());
				}

				if (valA != null)
					return valA.compareTo(valB);
				else
					return -1;
			}
		});

		for (int i = 0; i < jsonArr.size(); i++) {
			sortedJsonArray.add(jsonValues.get(i));
		}
		return sortedJsonArray;
	}

	public void populateMetrics(JSONObject json, Map<String, String> customFieldsMap,Map<String,String> customFieldsMapOfLpm) {

		metrics.setpName(projectName);
		// Adding Features and Rally Link in Verizon
		portfolioViewConfig = portfolioViewConfigRepo.findByProjectName(projectName);
		if (fieldsOfJSONData.get(customFieldNames.acFeature_capability.toString()) != null) {
			try {
				JSONArray acFeature = (JSONArray) fieldsOfJSONData
						.get(customFieldNames.acFeature_capability.toString());
				FeatureId = (acFeature.size() > 0 ? acFeature.get(0) : "").toString();
//		FeatureId="hgghghg";
				metrics.setAcFeatureId(FeatureId);
			} catch (Exception e) {

				LOGGER.error("acFeature" + e.getMessage());
			}
			try {
				if (portfolioViewConfig.getRallyUserName() != null &&portfolioViewConfig.getRallyPassword() != null&& !FeatureId.equals("")) {
					// Calling the Rally Rest API for Feature Name
					
					JSONObject rallyFeatureObj = rallyAuth.callRallyUrl(rallyFeatureURL + FeatureId + rallyFeatureEnd,
							portfolioViewConfig.getRallyPassword());
					if (rallyFeatureObj != null) {
						String featureName = rallyFeatureObj.get("_refObjectName").toString();
						String refURL = rallyFeatureObj.get("_ref").toString();
//				String featureName = rallyFeatureObj.get("Name").toString();
					//	LOGGER.info("Feature Name:"+featureName);
						metrics.setAcFeatureOrCapability(featureName + " - " + FeatureId);
						metrics.setURL(refURL);
					}
				}
			} catch (Exception e) {
				LOGGER.error("setAcFeatureOrCapability: " + e.getMessage());
			}

		} else {

			//LOGGER.error("No Ac Feature ");
		}

		metricsInfo.getFixVersion(json, metrics);
		metricsInfo.getIssueLinks(json, metrics, taskList);
		metricsInfo.getPriority(json, metrics, customFieldsMap);
		metricsInfo.getLpmCustomFileds(json, metrics, customFieldsMapOfLpm);
		metrics.setwId(wid);
		// metrics.setMultiSprints(multipleSprints);
		metricsInfo.getAssignee(json, metrics);
		metricsInfo.getDescription(json, metrics);

		metrics.setProjKey(this.projKey);
		metrics.setsName(sprintName);
		if (!(sprintId == 0))
			metrics.setsId(sprintId);
		metricsInfo.setSeverity(json, metrics, customFieldsMap);
		if (!almConfig.getEnvironment().equals(""))
			metricsInfo.setEnvironment(json, metrics, customFieldsMap);

		metricsInfo.setResolutionDate(json, metrics);
		/*
		 * Metrics containing only Brillio components should add for FordDirect is
		 * pending
		 */
		metricsInfo.setComponentData(json, metrics, customFieldsMap);
		metricsInfo.setCreationDate(json, metrics);
		metricsInfo.setAffectedVersionsData(json, metrics, customFieldsMap);
		metricsInfo.setDefectInjector(json, metrics, customFieldsMap);
		metricsInfo.setIssueType(json, metrics);
		metricsInfo.getStatus(json, metrics);
		metricsInfo.setBaseLine(metrics, json, customFieldsMap);
		metrics.setStateSet(taskSet);
		metricsInfo.setRemainingEffort(metrics, json);
		// Added by Avinash
		metricsInfo.getSubtask(metrics, json);
		metricsInfo.populateOrgEstimate(json, metrics);
		metricsInfo.setEffort(json, metrics);
		metricsInfo.setActEst(json, metrics);
		metricsInfo.setUpdatedDate(json, metrics);
		metrics.setpAlmType(almType);
		// Below code is for MOVE
		if (metrics.getType().equals("Bug")) {
			metricsInfo.setDefectCategory(metrics, fieldsOfJSONData, customFieldNames.defectCategory);
			metricsInfo.setDefectSquads(metrics, fieldsOfJSONData, customFieldNames.defectSquads);
			metricsInfo.setWhenFound(metrics, fieldsOfJSONData, customFieldNames.whenFound);
			metricsInfo.setWhereFound(metrics, fieldsOfJSONData, customFieldNames.whereFound);
			metricsInfo.setHowFound(metrics, fieldsOfJSONData, customFieldNames.howFound);
		}

		Map<Long, String> sprintAlocatedDate = metricsInfo.getSprintAllocation(metrics, historyArray, fieldsOfJSONData);
		Map<Long, Double> storyAllocation = metricsInfo.getStoryPointAllocation(metrics, historyArray);
		List<String> epicList = metricsInfo.getEpicIssue(metrics, historyArray);
		if (!epicList.isEmpty()) {
			metrics.setEpicIssues(epicList);
		}
		if (storyAllocation.isEmpty()) {
			double initalSPoints = metricsInfo.setStoryPoints(metrics, fieldsOfJSONData, customFieldsMap);
			storyAllocation.put(ConstantVariable.timestamp(
					fieldsOfJSONData.get(ConstantVariable.ALM_CREATED).toString(), projectName), initalSPoints);
		}
		if (!sprintAlocatedDate.isEmpty())
			metrics.setAllocatedDate(sprintAlocatedDate);
		metrics.setStoryPoints(storyAllocation);
		metricsInfo.setTargetReleaseReports(fieldsOfJSONData, metrics, customFieldNames.targetRelease);
	}

	/**
	 * **/
	public void saveIteration(List<IterationModel> iterationSet) {
		List<IterationModel> itrList = new ArrayList<>();
		for (IterationModel itr : iterationSet) {
			IterationModel temp = iterationRepo.findBySNameAndPNameAndPAlmType(itr.getsName(), projectName, almType);
			if (temp != null)
				itr.setId(temp.getId());
			itrList.add(itr);
		}
		iterationRepo.save(itrList);
	}

	public void saveMetrics(Set<MetricsModel> metricsSet) {
		List<MetricsModel> mtrList = new ArrayList<>();
		for (MetricsModel mtr : metricsSet) {
			MetricsModel temp = metricRepo.findByPNameAndWId(projectName, mtr.getwId());
			if (temp != null)
				mtr.setId(temp.getId());
			mtrList.add(mtr);
		}
		metricRepo.save(mtrList);
	}

	public void setVariables(JSONArray statusArray, Set<String> statusStateList) {
		new customFieldNames().getProjectStatus(statusArray);
		taskLastState = customFieldNames.taskStateList.toArray()[customFieldNames.taskStateList.size() - 1].toString();
		taskFirstState = customFieldNames.taskStateList.toArray()[0].toString();
		taskSet = new LinkedHashSet<>();
		taskSet.addAll(customFieldNames.taskStateList);

	}


}
