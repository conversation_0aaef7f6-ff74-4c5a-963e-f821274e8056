package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.ConfigurationSetting;

public interface ConfigurationSettingRep extends CrudRepository<ConfigurationSetting, ObjectId> {
    List<ConfigurationSetting> findByProjectName(String projectName);
    List<ConfigurationSetting> findByProjectNameIn(List<String> projectNames);
    int deleteByProjectName(String projectName);
}
