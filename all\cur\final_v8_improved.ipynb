{"cells": [{"cell_type": "code", "execution_count": 121, "id": "stage1_configuration", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Stage 1 Complete: Configuration loaded and Neo4j cleared\n"]}], "source": ["# ========== STAGE 1: CONFIGURATION & INITIALIZATION ==========\n", "import os\n", "import json\n", "import re\n", "import uuid\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "import pandas as pd\n", "from collections import defaultdict\n", "from concurrent.futures import ThreadPoolExecutor, as_completed\n", "import threading\n", "\n", "# Tree-sitter for AST parsing\n", "from tree_sitter import Language, Parser\n", "import tree_sitter_java as tsjava\n", "\n", "# LangChain components\n", "from langchain_community.document_loaders import TextLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter, Language as LC_Language\n", "from langchain_experimental.graph_transformers import LLMGraphTransformer\n", "from langchain_community.graphs import Neo4jGraph\n", "from langchain_openai import AzureChatOpenAI\n", "from langchain_core.prompts import PromptTemplate\n", "from langchain.schema import Document\n", "\n", "# Configuration\n", "BASE_PATH = Path(r\"C:/Shaik/sample/OneInsights\")\n", "\n", "# Neo4j Configuration\n", "NEO4J_URI = \"bolt://localhost:7687\"\n", "NEO4J_USER = \"neo4j\"\n", "NEO4J_PASSWORD = \"Test@7889\"\n", "NEO4J_DB = \"OneInsights\"\n", "\n", "# Initialize connections\n", "graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)\n", "JAVA_LANGUAGE = Language(tsjava.language())\n", "parser = Parser(JAVA_LANGUAGE)\n", "\n", "# Azure OpenAI Configuration\n", "llm = AzureChatOpenAI(\n", "    api_key=\"********************************\",\n", "    azure_endpoint=\"https://azureopenaibrsc.openai.azure.com/\",\n", "    azure_deployment=\"gpt-4o\",\n", "    api_version=\"2024-12-01-preview\"\n", ")\n", "\n", "# Temp variables to filter out (User Preference)\n", "TEMP_VARIABLES = {\n", "    'i', 'j', 'k', 'l', 'm', 'n', 'x', 'y', 'z',\n", "    'temp', 'tmp', 'temporary', 'temp1', 'temp2',\n", "    'count', 'counter', 'index', 'idx', 'iter',\n", "    'result', 'res', 'ret', 'val', 'value',\n", "    'item', 'elem', 'element', 'obj', 'object',\n", "    'str', 'string', 'num', 'number', 'flag',\n", "    'bool', 'boolean', 'arr', 'array', 'list',\n", "    'map', 'set', 'data', 'info', 'param', 'arg','Status'\n", "}\n", "\n", "# Long-term memory storage\n", "MEMORY_FILE = \"lineage_memory_v8_improved.json\"\n", "memory_lock = threading.Lock()\n", "\n", "def load_memory():\n", "    \"\"\"Load long-term memory from disk\"\"\"\n", "    # Try to load JSON file first\n", "    try:\n", "        with open(MEMORY_FILE, 'r', encoding='utf-8') as f:\n", "            data = json.load(f)\n", "            \n", "            # Convert lists back to sets where needed\n", "            def convert_from_json(obj):\n", "                if isinstance(obj, dict):\n", "                    result = {}\n", "                    for k, v in obj.items():\n", "                        if k == 'validated_edges' and isinstance(v, list):\n", "                            result[k] = set(v)\n", "                        else:\n", "                            result[k] = convert_from_json(v)\n", "                    return result\n", "                elif isinstance(obj, list):\n", "                    return [convert_from_json(item) for item in obj]\n", "                else:\n", "                    return obj\n", "            \n", "            return convert_from_json(data)\n", "            \n", "    except FileNotFoundError:\n", "        # Try to load old pickle file if JSON doesn't exist\n", "        old_pickle_file = MEMORY_FILE.replace('.json', '.pkl')\n", "        try:\n", "            import pickle\n", "            with open(old_pickle_file, 'rb') as f:\n", "                data = pickle.load(f)\n", "                print(f\"📦 Loaded memory from old pickle file: {old_pickle_file}\")\n", "                print(f\"🔄 Converting to JSON format...\")\n", "                # Save as JSON for future use\n", "                save_memory(data)\n", "                return data\n", "        except FileNotFoundError:\n", "            pass\n", "        \n", "        # Return default memory structure\n", "        return {\n", "            'class_registry': {},\n", "            'dto_mappings': {},\n", "            'validated_edges': set(),\n", "            'code_index': {},\n", "            'variable_flows': {},\n", "            'method_signatures': {},\n", "            'transformation_cache': {},\n", "            'variable_contexts': {}  # New: track variable contexts\n", "        }\n", "    except json.JSONDecodeError as e:\n", "        print(f\"⚠️ Error loading JSON memory file: {e}\")\n", "        print(f\"🔄 Returning default memory structure\")\n", "        return {\n", "            'class_registry': {},\n", "            'dto_mappings': {},\n", "            'validated_edges': set(),\n", "            'code_index': {},\n", "            'variable_flows': {},\n", "            'method_signatures': {},\n", "            'transformation_cache': {},\n", "            'variable_contexts': {}  # New: track variable contexts\n", "        }\n", "\n", "def save_memory(memory):\n", "    \"\"\"Save long-term memory to disk\"\"\"\n", "    with memory_lock:\n", "        try:\n", "            # Create a copy to avoid modifying the original\n", "            memory_copy = memory.copy()\n", "            \n", "            # Convert sets to lists for JSON serialization\n", "            def convert_for_json(obj):\n", "                if isinstance(obj, set):\n", "                    return list(obj)\n", "                elif isinstance(obj, dict):\n", "                    return {k: convert_for_json(v) for k, v in obj.items()}\n", "                elif isinstance(obj, list):\n", "                    return [convert_for_json(item) for item in obj]\n", "                else:\n", "                    return obj\n", "            \n", "            memory_copy = convert_for_json(memory_copy)\n", "            \n", "            with open(MEMORY_FILE, 'w', encoding='utf-8') as f:\n", "                json.dump(memory_copy, f, indent=2, ensure_ascii=False)\n", "        except Exception as e:\n", "            print(f\"⚠️ Error saving memory to JSON: {e}\")\n", "            # Fallback: save as pickle if JSON fails\n", "            backup_file = MEMORY_FILE.replace('.json', '_backup.pkl')\n", "            import pickle\n", "            with open(backup_file, 'wb') as f:\n", "                pickle.dump(memory, f)\n", "            print(f\"💾 Memory saved as backup pickle file: {backup_file}\")\n", "\n", "# Initialize memory\n", "memory = load_memory()\n", "\n", "# Clear Neo4j database\n", "graph.query(\"MATCH (n) DETACH DELETE n\")\n", "print(\"✅ Stage 1 Complete: Configuration loaded and Neo4j cleared\")"]}, {"cell_type": "code", "execution_count": 122, "id": "utility_functions", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Improved utility functions loaded\n"]}], "source": ["# ========== IMPROVED UTILITY FUNCTIONS ==========\n", "\n", "def to_pascal_case(text):\n", "    \"\"\"Convert text to PascalCase with improved handling\"\"\"\n", "    if not text:\n", "        return text\n", "    \n", "    # Remove file extensions first\n", "    text = re.sub(r'\\.(java|class)$', '', text, flags=re.IGNORECASE)\n", "    \n", "    # Handle file paths - extract just the filename\n", "    if '/' in text or '\\\\' in text:\n", "        text = os.path.basename(text)\n", "        text = re.sub(r'\\.(java|class)$', '', text, flags=re.IGNORECASE)\n", "    \n", "    # If already in PascalCase, return as is\n", "    if re.match(r'^[A-Z][a-zA-Z0-9]*$', text):\n", "        return text\n", "    \n", "    # Split on common delimiters and capitalize each part\n", "    parts = re.split(r'[_\\-\\s]+', text)\n", "    result = ''\n", "    for part in parts:\n", "        if part:\n", "            result += part[0].upper() + part[1:].lower() if len(part) > 1 else part.upper()\n", "    \n", "    return result if result else text\n", "\n", "def extract_clean_name(full_name, name_type):\n", "    \"\"\"Extract clean name from potentially concatenated strings\"\"\"\n", "    if not full_name:\n", "        return full_name\n", "    \n", "    # Remove common prefixes\n", "    prefixes = ['method:', 'class:', 'variable:', 'field:', 'table:', 'endpoint:']\n", "    for prefix in prefixes:\n", "        if full_name.lower().startswith(prefix):\n", "            full_name = full_name[len(prefix):]\n", "    \n", "    # Remove file extensions\n", "    full_name = re.sub(r'\\.(java|class)$', '', full_name, flags=re.IGNORECASE)\n", "    \n", "    # Handle file.class patterns - extract only class name\n", "    if '.' in full_name and name_type.lower() in ['class', 'interface']:\n", "        parts = full_name.split('.')\n", "        # Take the last part as the class name\n", "        full_name = parts[-1] if parts[-1] else parts[-2] if len(parts) > 1 else full_name\n", "    \n", "    # Handle classname.method patterns - extract only method name\n", "    if '.' in full_name and name_type.lower() == 'method':\n", "        parts = full_name.split('.')\n", "        # Take the last part as the method name\n", "        full_name = parts[-1] if parts[-1] else parts[-2] if len(parts) > 1 else full_name\n", "    \n", "    # Apply PascalCase for classes, methods, files, folders\n", "    if name_type.lower() in ['class', 'interface', 'method', 'file', 'folder']:\n", "        return to_pascal_case(full_name)\n", "    \n", "    # For variables, keep original name (context handled separately)\n", "    if name_type.lower() == 'variable':\n", "        if '.' in full_name:\n", "            return full_name.split('.')[-1]  # Return only variable name\n", "        return full_name\n", "    \n", "    # For tables, apply PascalCase\n", "    if name_type.lower() == 'table':\n", "        return to_pascal_case(full_name)\n", "    \n", "    return full_name\n", "\n", "def is_temp_variable(var_name):\n", "    \"\"\"Check if variable is a common temp variable\"\"\"\n", "    if not var_name:\n", "        return True\n", "    var_lower = var_name.lower().strip()\n", "    return var_lower in TEMP_VARIABLES or len(var_lower) <= 1\n", "\n", "def get_variable_context(var_name, method_name=None, class_name=None):\n", "    \"\"\"Get context for a variable (method or class where it's defined)\"\"\"\n", "    if method_name:\n", "        return extract_clean_name(method_name, 'method')\n", "    elif class_name:\n", "        return extract_clean_name(class_name, 'class')\n", "    return None\n", "\n", "print(\"✅ Improved utility functions loaded\")"]}, {"cell_type": "code", "execution_count": 123, "id": "variable_registry", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Improved Variable Registry initialized\n"]}], "source": ["# ========== IMPROVED VARIABLE METADATA REGISTRY ==========\n", "\n", "class ImprovedVariableRegistry:\n", "    \"\"\"Enhanced registry to track variables with proper context separation\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.variables = {}  # var_id -> metadata\n", "        self.name_to_id = {}  # (var_name, context) -> var_id\n", "        self.chunk_memory = {}  # chunk_id -> variables seen\n", "        \n", "    def register_variable(self, var_name, context, chunk_id, context_info):\n", "        \"\"\"Register a variable with unique ID and context metadata\"\"\"\n", "        # Clean variable name\n", "        clean_var_name = extract_clean_name(var_name, 'variable')\n", "        clean_context = extract_clean_name(context, context_info.get('context_type', 'method'))\n", "        \n", "        # Create unique key\n", "        var_key = (clean_var_name, clean_context)\n", "        \n", "        if var_key in self.name_to_id:\n", "            var_id = self.name_to_id[var_key]\n", "            self.variables[var_id]['chunks'].add(chunk_id)\n", "            self.variables[var_id]['contexts'].append(context_info)\n", "        else:\n", "            var_id = f\"var_{uuid.uuid4().hex[:8]}\"\n", "            self.name_to_id[var_key] = var_id\n", "            self.variables[var_id] = {\n", "                'variable_name': clean_var_name,  # Only variable name for display\n", "                'context_name': clean_context,    # Method/class context\n", "                'context_type': context_info.get('context_type', 'method'),\n", "                'chunks': {chunk_id},\n", "                'contexts': [context_info],\n", "                'declared_in': chunk_id if context_info.get('action') == 'declared' else None,\n", "                'modifications': [],\n", "                'usages': [],\n", "                'data_type': context_info.get('data_type'),\n", "                'lineage_path': []\n", "            }\n", "        \n", "        if chunk_id not in self.chunk_memory:\n", "            self.chunk_memory[chunk_id] = set()\n", "        self.chunk_memory[chunk_id].add(var_id)\n", "        \n", "        return var_id\n", "    \n", "    def get_variable_for_neo4j(self, var_id):\n", "        \"\"\"Get variable data formatted for Neo4j\"\"\"\n", "        if var_id in self.variables:\n", "            var_data = self.variables[var_id]\n", "            return {\n", "                'name': var_data['variable_name'],  # Display name only\n", "                'context': var_data['context_name'], # Context for uniqueness\n", "                'context_type': var_data['context_type'],\n", "                'full_context': f\"{var_data['context_name']}.{var_data['variable_name']}\"\n", "            }\n", "        return None\n", "\n", "# Initialize improved variable registry\n", "variable_registry = ImprovedVariableRegistry()\n", "\n", "print(\"✅ Improved Variable Registry initialized\")"]}, {"cell_type": "code", "execution_count": 124, "id": "stage2_folder_file_hierarchy", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Stage 2 Complete: 88 folder/file relationships extracted\n", "📁 Folders: 13, Files: 75\n", "🔍 Extracting file-class relationships using AST...\n", "📎 Found: Application -[DECLARES]-> Application\n", "📎 Found: TriggerCollector -[DECLARES]-> TriggerCollector\n", "📎 Found: ALMConfigController -[DECLARES]-> ALMConfigController\n", "📎 Found: AlmController -[DECLARES]-> AlmController\n", "📎 Found: ALMConfigReq -[DECLARES]-> ALMConfigReq\n", "📎 Found: ALMToolReq -[DECLARES]-> ALMToolReq\n", "📎 Found: DataResponse -[DECLARES]-> DataResponse\n", "📎 Found: ALMConfigServiceImplementation -[DECLARES]-> ALMConfigServiceImplementation\n", "📎 Found: ALMServiceImplementation -[DECLARES]-> ALMServiceImplementation\n", "📎 Found: ALMServiceImplementation -[DECLARES]-> TransitionComparator\n", "📎 Found: ALMServiceImplementation -[DECLARES]-> SprintComparatort\n", "📎 Found: ConfigurationSettingServiceImplementation -[DECLARES]-> ConfigurationSettingServiceImplementation\n", "📎 Found: DateUtil -[DECLARES]-> DateUtil\n", "📎 Found: Application -[DECLARES]-> Application\n", "📎 Found: ConstantVariable -[DECLARES]-> ConstantVariable\n", "📎 Found: ProjectCollector -[DECLARES]-> ProjectCollector\n", "📎 Found: Configuration -[DECLARES]-> Configuration\n", "📎 Found: DataConfig -[DECLARES]-> DataConfig\n", "📎 Found: MongoAggregate -[DECLARES]-> MongoAggregate\n", "📎 Found: ALMConfiguration -[DECLARES]-> ALMConfiguration\n", "📎 Found: BaseModel -[DECLARES]-> BaseModel\n", "📎 Found: ChangeHistoryModel -[DECLARES]-> ChangeHistoryModel\n", "📎 Found: ComponentVelocityList -[DECLARES]-> ComponentVelocityList\n", "📎 Found: ConfigurationSetting -[DECLARES]-> ConfigurationSetting\n", "📎 Found: ConfigurationToolInfoMetric -[DECLARES]-> ConfigurationToolInfoMetric\n", "📎 Found: CustomFields -[DECLARES]-> CustomFields\n", "📎 Found: IterationModel -[DECLARES]-> IterationModel\n", "📎 Found: IterationOutModel -[DECLARES]-> IterationOutModel\n", "📎 Found: MetricsModel -[DECLARES]-> MetricsModel\n", "📎 Found: MonogOutMetrics -[DECLARES]-> MonogOutMetrics\n", "📎 Found: ScoreCardSprintData -[DECLARES]-> ScoreCardSprintData\n", "📎 Found: TransitionModel -[DECLARES]-> TransitionModel\n", "📎 Found: VelocityList -[DECLARES]-> VelocityList\n", "📎 Found: ALMClientImplementation -[DECLARES]-> ALMClientImplementation\n", "📎 Found: ChartCalculations -[DECLARES]-> ChartCalculations\n", "📎 Found: ChartCalculations -[DECLARES]-> TransitionComparator\n", "📎 Found: ChartCalculations -[DECLARES]-> SprintComparatort\n", "📎 Found: Customfieldnames -[DECLARES]-> Customfieldnames\n", "📎 Found: DeleteJiraIssues -[DECLARES]-> DeleteJiraIssues\n", "📎 Found: EffortAndChangeItemInfo -[DECLARES]-> EffortAndChangeItemInfo\n", "📎 Found: IssueHierarchy -[DECLARES]-> IssueHierarchy\n", "📎 Found: IterationInfo -[DECLARES]-> IterationInfo\n", "📎 Found: JIRAApplication -[DECLARES]-> JIRAApplication\n", "📎 Found: JiraAuthentication -[DECLARES]-> JiraAuthentication\n", "📎 Found: JiraExceptions -[DECLARES]-> JiraExceptions\n", "📎 Found: MetricsInfo -[DECLARES]-> MetricsInfo\n", "📎 Found: RallyAuthentication -[DECLARES]-> RallyAuthentication\n", "📎 Found: ReleaseInfo -[DECLARES]-> ReleaseInfo\n", "📎 Found: SprintWiseCalculation -[DECLARES]-> SprintWiseCalculation\n", "📎 Found: TransitionInfo -[DECLARES]-> TransitionInfo\n", "📎 Found: TransitionMetrices -[DECLARES]-> TransitionMetrices\n", "📎 Found: BacklogCalculation -[DECLARES]-> BacklogCalculation\n", "📎 Found: BuildCalculations -[DECLARES]-> BuildCalculations\n", "📎 Found: CommonFunctions -[DECLARES]-> CommonFunctions\n", "📎 Found: Constant -[DECLARES]-> Constant\n", "📎 Found: CryptoUtils -[DECLARES]-> CryptoUtils\n", "📎 Found: DateUtil -[DECLARES]-> DateUtil\n", "📎 Found: DefectCalculations -[DECLARES]-> DefectCalculations\n", "📎 Found: DefectCalculations -[DECLARES]-> ValueComparator\n", "📎 Found: DefectCalculations -[DECLARES]-> SprintComparatort\n", "📎 Found: EncryptionDecryptionAES -[DECLARES]-> EncryptionDecryptionAES\n", "📎 Found: EncryptorAesGcmPassword -[DECLARES]-> EncryptorAesGcmPassword\n", "📎 Found: RestClient -[DECLARES]-> RestClient\n", "📎 Found: SprintProgress -[DECLARES]-> SprintProgress\n", "📎 Found: SprintProgress -[DECLARES]-> LogDateComparator\n", "📎 Found: SprintProgress -[DECLARES]-> SprintComparator\n", "📎 Found: SprintProgressCalculations -[DECLARES]-> SprintProgressCalculations\n", "📎 Found: StoryProgressModel -[DECLARES]-> StoryProgressModel\n", "📎 Found: StoryProgressSprintwise -[DECLARES]-> StoryProgressSprintwise\n", "📎 Found: TaskRiskSprint -[DECLARES]-> TaskRiskSprint\n", "📎 Found: TeamQualityUtils -[DECLARES]-> TeamQualityUtils\n", "📎 Found: VelocityCalculations -[DECLARES]-> VelocityCalculations\n", "📎 Added 72 file-class relationships to Stage 2 data\n", "✅ Stage 2B Complete: Total relationships now 160 (including 72 file-class)\n"]}], "source": ["# ========== STAGE 2: IMPROVED FOLDER-FILE HIERARCHY ==========\n", "\n", "def extract_folder_file_hierarchy():\n", "    \"\"\"Extract and normalize folder-file relationships with improved naming\"\"\"\n", "    relationships = []\n", "    base_folder = to_pascal_case(BASE_PATH.name)\n", "\n", "    for root, dirs, files in os.walk(BASE_PATH):\n", "        current_path = Path(root)\n", "        rel_path = current_path.relative_to(BASE_PATH)\n", "\n", "        # Determine current folder name and its parent\n", "        if rel_path != Path('.'):\n", "            folder_name = to_pascal_case(current_path.name)\n", "            parent_rel_path = current_path.parent.relative_to(BASE_PATH)\n", "            parent_name = base_folder if parent_rel_path == Path('.') else to_pascal_case(current_path.parent.name)\n", "\n", "            relationships.append({\n", "                'source_node': parent_name,\n", "                'source_type': 'Folder',\n", "                'destination_node': folder_name,\n", "                'destination_type': 'Folder',\n", "                'relationship': 'CONTAINS'\n", "            })\n", "            current_folder_name = folder_name\n", "        else:\n", "            current_folder_name = base_folder\n", "\n", "        # Process files inside the folder\n", "        for file in files:\n", "            if file.lower().endswith(\".java\"):\n", "                file_name = extract_clean_name(file, 'file')\n", "                relationships.append({\n", "                    'source_node': current_folder_name,\n", "                    'source_type': 'Folder',\n", "                    'destination_node': file_name,\n", "                    'destination_type': 'File',\n", "                    'relationship': 'CONTAINS',\n", "                    'file_path': str(current_path / file)\n", "                })\n", "\n", "    return relationships\n", "\n", "# Execute Stage 2\n", "folder_file_relationships = extract_folder_file_hierarchy()\n", "df_hierarchy = pd.DataFrame(folder_file_relationships)\n", "\n", "# Store Stage 2 results in memory\n", "memory['stage_2_results'] = {\n", "    'relationships': len(df_hierarchy),\n", "    'folders': len([r for r in folder_file_relationships if r['destination_type'] == 'Folder']),\n", "    'files': len([r for r in folder_file_relationships if r['destination_type'] == 'File'])\n", "}\n", "\n", "# Add validated edges to prevent duplicates\n", "for _, row in df_hierarchy.iterrows():\n", "    edge_key = f\"{row['source_node']}-{row['relationship']}-{row['destination_node']}\"\n", "    memory['validated_edges'].add(edge_key)\n", "save_memory(memory)\n", "\n", "print(f\"✅ Stage 2 Complete: {len(df_hierarchy)} folder/file relationships extracted\")\n", "print(f\"📁 Folders: {memory['stage_2_results']['folders']}, Files: {memory['stage_2_results']['files']}\")\n", "\n", "# ========== STAGE 2B: AST-BASED FILE-CLASS RELATIONSHIPS ==========\n", "\n", "def extract_file_class_relationships_ast():\n", "    \"\"\"Extract file-class relationships using AST parsing\"\"\"\n", "    file_class_relationships = []\n", "    \n", "    print(\"🔍 Extracting file-class relationships using AST...\")\n", "    \n", "    # Get all Java files from the hierarchy data\n", "    java_files = df_hierarchy[df_hierarchy['destination_type'] == 'File']\n", "    \n", "    for _, file_row in java_files.iterrows():\n", "        if 'file_path' in file_row and file_row['file_path']:\n", "            file_path = file_row['file_path']\n", "            file_name = file_row['destination_node']\n", "            \n", "            try:\n", "                # Read and parse the Java file\n", "                with open(file_path, 'r', encoding='utf-8') as f:\n", "                    source_code = f.read().encode('utf-8')\n", "                \n", "                tree = parser.parse(source_code)\n", "                root_node = tree.root_node\n", "                \n", "                # Find class declarations\n", "                def find_classes(node):\n", "                    classes = []\n", "                    if node.type == 'class_declaration':\n", "                        # Find class name\n", "                        for child in node.children:\n", "                            if child.type == 'identifier':\n", "                                class_name = source_code[child.start_byte:child.end_byte].decode('utf-8')\n", "                                classes.append(to_pascal_case(class_name))\n", "                                break\n", "                    \n", "                    # Recursively search child nodes\n", "                    for child in node.children:\n", "                        classes.extend(find_classes(child))\n", "                    \n", "                    return classes\n", "                \n", "                classes_in_file = find_classes(root_node)\n", "                \n", "                # Create file-class relationships\n", "                for class_name in classes_in_file:\n", "                    file_class_relationships.append({\n", "                        'source_node': file_name,\n", "                        'source_type': 'File',\n", "                        'destination_node': class_name,\n", "                        'destination_type': 'Class',\n", "                        'relationship': 'DECLARES',\n", "                        'file_path': file_path\n", "                    })\n", "                    print(f\"📎 Found: {file_name} -[DECLARES]-> {class_name}\")\n", "                    \n", "            except Exception as e:\n", "                print(f\"⚠️ Error processing {file_path}: {e}\")\n", "                continue\n", "    \n", "    return file_class_relationships\n", "\n", "# Execute Stage 2B: Extract file-class relationships\n", "file_class_relationships = extract_file_class_relationships_ast()\n", "df_file_class = pd.DataFrame(file_class_relationships)\n", "\n", "# Append file-class relationships to the hierarchy DataFrame\n", "if len(df_file_class) > 0:\n", "    df_hierarchy = pd.concat([df_hierarchy, df_file_class], ignore_index=True)\n", "    print(f\"📎 Added {len(df_file_class)} file-class relationships to Stage 2 data\")\n", "    \n", "    # Update memory with file-class relationships\n", "    memory['stage_2_results']['file_class_relationships'] = len(df_file_class)\n", "    \n", "    # Add validated edges for file-class relationships\n", "    for _, row in df_file_class.iterrows():\n", "        edge_key = f\"{row['source_node']}-{row['relationship']}-{row['destination_node']}\"\n", "        memory['validated_edges'].add(edge_key)\n", "    \n", "    save_memory(memory)\n", "else:\n", "    print(\"⚠️ No file-class relationships found\")\n", "\n", "print(f\"✅ Stage 2B Complete: Total relationships now {len(df_hierarchy)} (including {len(df_file_class)} file-class)\")"]}, {"cell_type": "code", "execution_count": 125, "id": "c302a0fd", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>source_node</th>\n", "      <th>source_type</th>\n", "      <th>destination_node</th>\n", "      <th>destination_type</th>\n", "      <th>relationship</th>\n", "      <th>file_path</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Application</td>\n", "      <td>File</td>\n", "      <td>Application</td>\n", "      <td>Class</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\ServicesBolt\\Appli...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>TriggerCollector</td>\n", "      <td>File</td>\n", "      <td>TriggerCollector</td>\n", "      <td>Class</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\ServicesBolt\\Trigg...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ALMConfigController</td>\n", "      <td>File</td>\n", "      <td>ALMConfigController</td>\n", "      <td>Class</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\ServicesBolt\\api\\A...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>AlmController</td>\n", "      <td>File</td>\n", "      <td>AlmController</td>\n", "      <td>Class</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\ServicesBolt\\api\\A...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>ALMConfigReq</td>\n", "      <td>File</td>\n", "      <td>ALMConfigReq</td>\n", "      <td>Class</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\ServicesBolt\\reque...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67</th>\n", "      <td>StoryProgressModel</td>\n", "      <td>File</td>\n", "      <td>StoryProgressModel</td>\n", "      <td>Class</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\util\\S...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>68</th>\n", "      <td>StoryProgressSprintwise</td>\n", "      <td>File</td>\n", "      <td>StoryProgressSprintwise</td>\n", "      <td>Class</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\util\\S...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>69</th>\n", "      <td>TaskRiskSprint</td>\n", "      <td>File</td>\n", "      <td>TaskRiskSprint</td>\n", "      <td>Class</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\util\\T...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>70</th>\n", "      <td>TeamQualityUtils</td>\n", "      <td>File</td>\n", "      <td>TeamQualityUtils</td>\n", "      <td>Class</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\util\\T...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71</th>\n", "      <td>VelocityCalculations</td>\n", "      <td>File</td>\n", "      <td>VelocityCalculations</td>\n", "      <td>Class</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\util\\V...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>72 rows × 6 columns</p>\n", "</div>"], "text/plain": ["                source_node source_type         destination_node  \\\n", "0               Application        File              Application   \n", "1          TriggerCollector        File         TriggerCollector   \n", "2       ALMConfigController        File      ALMConfigController   \n", "3             AlmController        File            AlmController   \n", "4              ALMConfigReq        File             ALMConfigReq   \n", "..                      ...         ...                      ...   \n", "67       StoryProgressModel        File       StoryProgressModel   \n", "68  StoryProgressSprintwise        File  StoryProgressSprintwise   \n", "69           TaskRiskSprint        File           TaskRiskSprint   \n", "70         TeamQualityUtils        File         TeamQualityUtils   \n", "71     VelocityCalculations        File     VelocityCalculations   \n", "\n", "   destination_type relationship  \\\n", "0             Class     DECLARES   \n", "1             Class     DECLARES   \n", "2             Class     DECLARES   \n", "3             Class     DECLARES   \n", "4             Class     DECLARES   \n", "..              ...          ...   \n", "67            Class     DECLARES   \n", "68            Class     DECLARES   \n", "69            Class     DECLARES   \n", "70            Class     DECLARES   \n", "71            Class     DECLARES   \n", "\n", "                                            file_path  \n", "0   C:\\Shaik\\sample\\OneInsights\\ServicesBolt\\Appli...  \n", "1   C:\\Shaik\\sample\\OneInsights\\ServicesBolt\\Trigg...  \n", "2   C:\\Shaik\\sample\\OneInsights\\ServicesBolt\\api\\A...  \n", "3   C:\\Shaik\\sample\\OneInsights\\ServicesBolt\\api\\A...  \n", "4   C:\\Shaik\\sample\\OneInsights\\ServicesBolt\\reque...  \n", "..                                                ...  \n", "67  C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\util\\S...  \n", "68  C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\util\\S...  \n", "69  C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\util\\T...  \n", "70  C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\util\\T...  \n", "71  C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\util\\V...  \n", "\n", "[72 rows x 6 columns]"]}, "execution_count": 125, "metadata": {}, "output_type": "execute_result"}], "source": ["df_file_class"]}, {"cell_type": "code", "execution_count": 126, "id": "79434ab1", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>source_node</th>\n", "      <th>source_type</th>\n", "      <th>destination_node</th>\n", "      <th>destination_type</th>\n", "      <th>relationship</th>\n", "      <th>file_path</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>OneInsights</td>\n", "      <td>Folder</td>\n", "      <td>ServicesBolt</td>\n", "      <td>Folder</td>\n", "      <td>CONTAINS</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ServicesBolt</td>\n", "      <td>Folder</td>\n", "      <td>Application</td>\n", "      <td>File</td>\n", "      <td>CONTAINS</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\ServicesBolt\\Appli...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ServicesBolt</td>\n", "      <td>Folder</td>\n", "      <td>TriggerCollector</td>\n", "      <td>File</td>\n", "      <td>CONTAINS</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\ServicesBolt\\Trigg...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ServicesBolt</td>\n", "      <td>Folder</td>\n", "      <td>Api</td>\n", "      <td>Folder</td>\n", "      <td>CONTAINS</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Api</td>\n", "      <td>Folder</td>\n", "      <td>ALMConfigController</td>\n", "      <td>File</td>\n", "      <td>CONTAINS</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\ServicesBolt\\api\\A...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>155</th>\n", "      <td>StoryProgressModel</td>\n", "      <td>File</td>\n", "      <td>StoryProgressModel</td>\n", "      <td>Class</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\util\\S...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>156</th>\n", "      <td>StoryProgressSprintwise</td>\n", "      <td>File</td>\n", "      <td>StoryProgressSprintwise</td>\n", "      <td>Class</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\util\\S...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>157</th>\n", "      <td>TaskRiskSprint</td>\n", "      <td>File</td>\n", "      <td>TaskRiskSprint</td>\n", "      <td>Class</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\util\\T...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>158</th>\n", "      <td>TeamQualityUtils</td>\n", "      <td>File</td>\n", "      <td>TeamQualityUtils</td>\n", "      <td>Class</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\util\\T...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>159</th>\n", "      <td>VelocityCalculations</td>\n", "      <td>File</td>\n", "      <td>VelocityCalculations</td>\n", "      <td>Class</td>\n", "      <td>DECLARES</td>\n", "      <td>C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\util\\V...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>160 rows × 6 columns</p>\n", "</div>"], "text/plain": ["                 source_node source_type         destination_node  \\\n", "0                OneInsights      Folder             ServicesBolt   \n", "1               ServicesBolt      Folder              Application   \n", "2               ServicesBolt      Folder         TriggerCollector   \n", "3               ServicesBolt      Folder                      Api   \n", "4                        Api      Folder      ALMConfigController   \n", "..                       ...         ...                      ...   \n", "155       StoryProgressModel        File       StoryProgressModel   \n", "156  StoryProgressSprintwise        File  StoryProgressSprintwise   \n", "157           TaskRiskSprint        File           TaskRiskSprint   \n", "158         TeamQualityUtils        File         TeamQualityUtils   \n", "159     VelocityCalculations        File     VelocityCalculations   \n", "\n", "    destination_type relationship  \\\n", "0             Folder     CONTAINS   \n", "1               File     CONTAINS   \n", "2               File     CONTAINS   \n", "3             Folder     CONTAINS   \n", "4               File     CONTAINS   \n", "..               ...          ...   \n", "155            Class     DECLARES   \n", "156            Class     DECLARES   \n", "157            Class     DECLARES   \n", "158            Class     DECLARES   \n", "159            Class     DECLARES   \n", "\n", "                                             file_path  \n", "0                                                  NaN  \n", "1    C:\\Shaik\\sample\\OneInsights\\ServicesBolt\\Appli...  \n", "2    C:\\Shaik\\sample\\OneInsights\\ServicesBolt\\Trigg...  \n", "3                                                  NaN  \n", "4    C:\\Shaik\\sample\\OneInsights\\ServicesBolt\\api\\A...  \n", "..                                                 ...  \n", "155  C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\util\\S...  \n", "156  C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\util\\S...  \n", "157  C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\util\\T...  \n", "158  C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\util\\T...  \n", "159  C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\util\\V...  \n", "\n", "[160 rows x 6 columns]"]}, "execution_count": 126, "metadata": {}, "output_type": "execute_result"}], "source": ["df_hierarchy"]}, {"cell_type": "code", "execution_count": 127, "id": "stage3_enhanced_class_registry", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Stage 3 Complete: Enhanced class registry built with 73 classes\n", "🧠 Memory updated with 486 method signatures\n"]}], "source": ["# ========== STAGE 3: ENHANCED CLASS REGISTRY & ANALYSIS ==========\n", "\n", "# Patterns for analysis\n", "PACKAGE_PATTERN = r'package\\s+([\\w\\.]+);'\n", "IMPORT_PATTERN = r'import\\s+([\\w\\.]+);'\n", "MAPPING_PATTERNS = {\n", "    'GetMapping': r'@GetMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "    'PostMapping': r'@PostMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "    'PutMapping': r'@PutMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "    'DeleteMapping': r'@DeleteMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']',\n", "    'RequestMapping': r'@RequestMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']'\n", "}\n", "\n", "def extract_package_and_imports(source_code_str):\n", "    \"\"\"Extract package name and import list from Java source\"\"\"\n", "    package_match = re.search(PACKAGE_PATTERN, source_code_str)\n", "    package_name = package_match.group(1) if package_match else None\n", "    import_matches = re.findall(IMPORT_PATTERN, source_code_str)\n", "    return package_name, import_matches\n", "\n", "def extract_api_endpoints(source_code_str):\n", "    \"\"\"Extract API endpoints using Spring annotations\"\"\"\n", "    endpoints = []\n", "    for mapping_type, pattern in MAPPING_PATTERNS.items():\n", "        matches = re.findall(pattern, source_code_str, re.MULTILINE)\n", "        for match in matches:\n", "            path = match.strip()\n", "            if path:\n", "                method = mapping_type.replace('Mapping', '').upper() if mapping_type != 'RequestMapping' else 'GET'\n", "                endpoints.append({\n", "                    'type': mapping_type,\n", "                    'path': path,\n", "                    'method': method\n", "                })\n", "    return endpoints\n", "\n", "def extract_database_entities(source_code_str):\n", "    \"\"\"Extract @Entity, @Table, and @Query usage from Java file\"\"\"\n", "    entities = []\n", "\n", "    # @Entity/@Table extraction\n", "    if \"@Entity\" in source_code_str:\n", "        table_matches = re.findall(r'@Table\\s*\\(\\s*name\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']', source_code_str)\n", "        for table_name in table_matches:\n", "            entities.append({'type': 'table', 'name': table_name.strip()})\n", "\n", "        if not table_matches:\n", "            class_match = re.search(r'(public\\s+)?(class|abstract class|interface)\\s+(\\w+)', source_code_str)\n", "            if class_match:\n", "                class_name = class_match.group(3)\n", "                snake_case = re.sub('([a-z0-9])([A-Z])', r'\\1_\\2', class_name).lower()\n", "                entities.append({'type': 'table', 'name': snake_case})\n", "\n", "    # @Query: detect raw SQL or JPQL references to tables\n", "    query_pattern = r'@Query\\s*\\([^)]*[\"\\']([^\"\\']*(?:FROM|from)\\s+([\\w]+)[^\"\\']*)[\"\\']'\n", "    query_matches = re.findall(query_pattern, source_code_str, re.MULTILINE | re.IGNORECASE)\n", "    for _, table in query_matches:\n", "        table = table.strip()\n", "        if table and table.lower() not in {'select', 'where', 'group', 'order'}:\n", "            entities.append({'type': 'table', 'name': table})\n", "\n", "    return entities\n", "\n", "def build_enhanced_class_registry():\n", "    \"\"\"Build enhanced class registry with improved memory integration\"\"\"\n", "    enhanced_registry = {}\n", "    code_index = {}\n", "    \n", "    for root, _, files in os.walk(BASE_PATH):\n", "        for file in files:\n", "            if file.endswith('.java'):\n", "                file_path = os.path.join(root, file)\n", "                try:\n", "                    with open(file_path, 'r', encoding='utf-8') as f:\n", "                        source_code_str = f.read()\n", "                    \n", "                    package_name, imports = extract_package_and_imports(source_code_str)\n", "                    endpoints = extract_api_endpoints(source_code_str)\n", "                    db_entities = extract_database_entities(source_code_str)\n", "                    \n", "                    # Apply improved name cleaning\n", "                    class_name = extract_clean_name(file.replace('.java', ''), 'class')\n", "                    fqcn = f'{package_name}.{class_name}' if package_name else class_name\n", "                    \n", "                    enhanced_registry[class_name] = {\n", "                        'fqcn': fqcn,\n", "                        'package': package_name,\n", "                        'file_path': file_path,\n", "                        'imports': imports,\n", "                        'endpoints': endpoints,\n", "                        'db_entities': db_entities,\n", "                        'source_code': source_code_str\n", "                    }\n", "                    \n", "                    # Build code index for fast method retrieval\n", "                    methods = re.findall(r'(?:public|private|protected)\\s+\\w+\\s+(\\w+)\\s*\\(', source_code_str)\n", "                    clean_methods = [extract_clean_name(m, 'method') for m in methods]\n", "                    \n", "                    code_index[class_name] = {\n", "                        'methods': clean_methods,\n", "                        'variables': re.findall(r'\\b(\\w+)\\s+(\\w+)\\s*[=;]', source_code_str),\n", "                        'annotations': re.findall(r'@(\\w+)', source_code_str)\n", "                    }\n", "                    \n", "                    # Store method signatures in memory for cross-stage reference\n", "                    for method in clean_methods:\n", "                        memory['method_signatures'][method] = {\n", "                            'class': class_name,\n", "                            'file_path': file_path,\n", "                            'stage': '3_enhanced_registry'\n", "                        }\n", "                    \n", "                    if endpoints:\n", "                        print(f\"🌐 Found {len(endpoints)} endpoints in {class_name}\")\n", "                        \n", "                except Exception as e:\n", "                    print(f\"⚠️ Error processing {file}: {e}\")\n", "                    continue\n", "    \n", "    # Store in long-term memory\n", "    memory['class_registry'] = enhanced_registry\n", "    memory['code_index'] = code_index\n", "    save_memory(memory)\n", "    \n", "    return enhanced_registry, code_index\n", "\n", "# Execute Stage 3\n", "enhanced_class_registry, code_index = build_enhanced_class_registry()\n", "print(f'✅ Stage 3 Complete: Enhanced class registry built with {len(enhanced_class_registry)} classes')\n", "print(f'🧠 Memory updated with {len(memory[\"method_signatures\"])} method signatures')"]}, {"cell_type": "code", "execution_count": 128, "id": "stage4_improved_llm_extraction", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Stage 3B Complete: 4794 AST relationships extracted\n"]}], "source": ["# ========== STAGE 3B: AST EXTRACTION ==========\n", "\n", "def read_source_code(file_path):\n", "    \"\"\"Read source code from file\"\"\"\n", "    with open(file_path, 'r', encoding='utf-8') as f:\n", "        return f.read().encode('utf-8')\n", "\n", "def extract_ast_structure(file_path):\n", "    \"\"\"Extract AST structure from Java file using tree-sitter\"\"\"\n", "    records = []\n", "    source_code = read_source_code(file_path)\n", "    tree = parser.parse(source_code)\n", "    root_node = tree.root_node\n", "    file_name = os.path.basename(file_path)\n", "\n", "    def clean_node_name(name):\n", "        \"\"\"Clean node names to remove prefixes and suffixes\"\"\"\n", "        if not name:\n", "            return name\n", "        \n", "        # Remove common prefixes\n", "        prefixes_to_remove = ['method:', 'class:', 'variable:', 'field:']\n", "        for prefix in prefixes_to_remove:\n", "            if name.lower().startswith(prefix):\n", "                name = name[len(prefix):]\n", "        \n", "        # Remove file extensions\n", "        name = re.sub(r'\\.(java|class)$', '', name, flags=re.IGNORECASE)\n", "        \n", "        return name.strip()\n", "\n", "    def traverse(node, parent_type=None, parent_name=None):\n", "        # Handle class declarations\n", "        if node.type == 'class_declaration':\n", "            class_name = None\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    class_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "                    \n", "                    # File -> Class relationship\n", "                    records.append({\n", "                        'source_node': file_name,\n", "                        'source_type': 'file',\n", "                        'destination_node': class_name,\n", "                        'destination_type': 'class',\n", "                        'relationship': 'declares',\n", "                        'file_path': file_path\n", "                    })\n", "                    \n", "                    # Add API endpoints from registry\n", "                    class_info = enhanced_class_registry.get(class_name, {})\n", "                    endpoints = class_info.get('endpoints', [])\n", "                    for ep in endpoints:\n", "                        endpoint_name = f\"{ep['method']} {ep['path']}\"\n", "                        records.append({\n", "                            'source_node': class_name,\n", "                            'source_type': 'class',\n", "                            'destination_node': endpoint_name,\n", "                            'destination_type': 'endpoint',\n", "                            'relationship': 'declares',\n", "                            'file_path': file_path\n", "                        })\n", "                    \n", "                    # Add database entities from registry\n", "                    db_entities = class_info.get('db_entities', [])\n", "                    for entity in db_entities:\n", "                        records.append({\n", "                            'source_node': class_name,\n", "                            'source_type': 'class',\n", "                            'destination_node': entity['name'],\n", "                            'destination_type': 'table',\n", "                            'relationship': 'maps_to',\n", "                            'file_path': file_path\n", "                        })\n", "                    break\n", "            \n", "            # Traverse children with class context\n", "            for child in node.children:\n", "                traverse(child, 'class', class_name)\n", "                \n", "        # Handle interface declarations\n", "        elif node.type == 'interface_declaration':\n", "            interface_name = None\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    interface_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "                    records.append({\n", "                        'source_node': file_name,\n", "                        'source_type': 'file',\n", "                        'destination_node': interface_name,\n", "                        'destination_type': 'interface',\n", "                        'relationship': 'declares',\n", "                        'file_path': file_path\n", "                    })\n", "                    break\n", "            \n", "            # Traverse children with interface context\n", "            for child in node.children:\n", "                traverse(child, 'interface', interface_name)\n", "                \n", "        # Handle method declarations - FIXED HIERARCHY\n", "        elif node.type == 'method_declaration':\n", "            method_name = None\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    method_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "                    \n", "                    # CORRECT: Class -> Method (not Method -> Class)\n", "                    if parent_name and parent_type in ['class', 'interface']:\n", "                        records.append({\n", "                            'source_node': parent_name,\n", "                            'source_type': parent_type,\n", "                            'destination_node': method_name,\n", "                            'destination_type': 'method',\n", "                            'relationship': 'declares',\n", "                            'file_path': file_path\n", "                        })\n", "                    break\n", "            \n", "            # Traverse children with method context\n", "            for child in node.children:\n", "                traverse(child, 'method', method_name)\n", "                \n", "        # Handle field declarations - FIXED HIERARCHY\n", "        elif node.type == 'field_declaration':\n", "            for child in node.children:\n", "                if child.type == 'variable_declarator':\n", "                    for grandchild in child.children:\n", "                        if grandchild.type == 'identifier':\n", "                            field_name = clean_node_name(source_code[grandchild.start_byte:grandchild.end_byte].decode('utf-8'))\n", "                            \n", "                            # CORRECT: Class -> Variable (not Variable -> Class)\n", "                            if parent_name and parent_type == 'class':\n", "                                records.append({\n", "                                    'source_node': parent_name,\n", "                                    'source_type': parent_type,\n", "                                    'destination_node': field_name,\n", "                                    'destination_type': 'variable',\n", "                                    'relationship': 'has_field',\n", "                                    'file_path': file_path\n", "                                })\n", "                                \n", "        # Handle variable usage in methods - FIXED HIERARCHY\n", "        elif node.type in ['assignment_expression', 'variable_declarator'] and parent_type == 'method':\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    var_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "                    if var_name and var_name != 'this' and parent_name:\n", "                        # CORRECT: Method -> Variable (not Variable -> Method)\n", "                        records.append({\n", "                            'source_node': parent_name,\n", "                            'source_type': parent_type,\n", "                            'destination_node': var_name,\n", "                            'destination_type': 'variable',\n", "                            'relationship': 'uses',\n", "                            'file_path': file_path\n", "                        })\n", "        else:\n", "            # Continue traversing for other node types\n", "            for child in node.children:\n", "                traverse(child, parent_type, parent_name)\n", "\n", "    traverse(root_node)\n", "    return records\n", "\n", "# Execute AST extraction\n", "ast_records = []\n", "for root, _, files in os.walk(BASE_PATH):\n", "    for file in files:\n", "        if file.endswith('.java'):\n", "            file_path = os.path.join(root, file)\n", "            try:\n", "                ast_records.extend(extract_ast_structure(file_path))\n", "            except Exception as e:\n", "                print(f'⚠️ Error processing {file}: {e}')\n", "                continue\n", "\n", "df_ast = pd.DataFrame(ast_records)\n", "print(f'✅ Stage 3B Complete: {len(df_ast)} AST relationships extracted')\n", "\n", "# Store AST results in memory\n", "memory['ast_relationships'] = len(df_ast)\n", "save_memory(memory)"]}, {"cell_type": "code", "execution_count": 129, "id": "stage4_improved_llm_extraction", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📄 Application.java: 35 lines → 1 chunk(s)\n", "📄 TriggerCollector.java: 62 lines → 1 chunk(s)\n", "📄 ALMConfigController.java: 58 lines → 1 chunk(s)\n", "📄 AlmController.java: 385 lines → 1 chunk(s)\n", "📄 ALMConfigReq.java: 276 lines → 1 chunk(s)\n", "📄 ALMToolReq.java: 26 lines → 1 chunk(s)\n", "📄 Authentication.java: 42 lines → 1 chunk(s)\n", "📄 DataResponse.java: 20 lines → 1 chunk(s)\n", "📄 ALMConfigService.java: 23 lines → 1 chunk(s)\n", "📄 ALMConfigServiceImplementation.java: 53 lines → 1 chunk(s)\n", "📄 AlmService.java: 145 lines → 1 chunk(s)\n", "📄 ALMServiceImplementation.java: 1401 lines → 8 chunk(s)\n", "📄 ConfigurationSettingService.java: 20 lines → 1 chunk(s)\n", "📄 ConfigurationSettingServiceImplementation.java: 162 lines → 1 chunk(s)\n", "📄 DateUtil.java: 40 lines → 1 chunk(s)\n", "📄 Application.java: 19 lines → 1 chunk(s)\n", "📄 ConstantVariable.java: 252 lines → 1 chunk(s)\n", "📄 ProjectCollector.java: 699 lines → 1 chunk(s)\n", "📄 Configuration.java: 21 lines → 1 chunk(s)\n", "📄 DataConfig.java: 168 lines → 1 chunk(s)\n", "📄 MongoAggregate.java: 340 lines → 1 chunk(s)\n", "📄 ALMConfiguration.java: 291 lines → 1 chunk(s)\n", "📄 BaseModel.java: 22 lines → 1 chunk(s)\n", "📄 ChangeHistoryModel.java: 72 lines → 1 chunk(s)\n", "📄 ComponentVelocityList.java: 22 lines → 1 chunk(s)\n", "📄 ConfigurationSetting.java: 81 lines → 1 chunk(s)\n", "📄 ConfigurationToolInfoMetric.java: 196 lines → 1 chunk(s)\n", "📄 CustomFields.java: 21 lines → 1 chunk(s)\n", "📄 IterationModel.java: 259 lines → 1 chunk(s)\n", "📄 IterationOutModel.java: 215 lines → 1 chunk(s)\n", "📄 MetricsModel.java: 511 lines → 1 chunk(s)\n", "📄 MonogOutMetrics.java: 523 lines → 1 chunk(s)\n", "📄 ScoreCardSprintData.java: 201 lines → 1 chunk(s)\n", "📄 TransitionModel.java: 121 lines → 1 chunk(s)\n", "📄 VelocityList.java: 57 lines → 1 chunk(s)\n", "📄 ALMConfigRepo.java: 15 lines → 1 chunk(s)\n", "📄 ChangeHisortyRepo.java: 17 lines → 1 chunk(s)\n", "📄 ConfigurationSettingRep.java: 15 lines → 1 chunk(s)\n", "📄 IterationRepo.java: 19 lines → 1 chunk(s)\n", "📄 MetricRepo.java: 39 lines → 1 chunk(s)\n", "📄 TransitionRepo.java: 18 lines → 1 chunk(s)\n", "📄 ALMClientImplementation.java: 628 lines → 1 chunk(s)\n", "📄 ChartCalculations.java: 878 lines → 1 chunk(s)\n", "📄 customFieldNames.java: 290 lines → 1 chunk(s)\n", "📄 DeleteJiraIssues.java: 119 lines → 1 chunk(s)\n", "📄 EffortAndChangeItemInfo.java: 142 lines → 1 chunk(s)\n", "📄 IssueHierarchy.java: 352 lines → 1 chunk(s)\n", "📄 IterationInfo.java: 186 lines → 1 chunk(s)\n", "📄 JIRAApplication.java: 167 lines → 1 chunk(s)\n", "📄 JiraAuthentication.java: 341 lines → 1 chunk(s)\n", "📄 JIRAClient.java: 23 lines → 1 chunk(s)\n", "📄 JiraExceptions.java: 25 lines → 1 chunk(s)\n", "📄 MetricsInfo.java: 491 lines → 1 chunk(s)\n", "📄 RallyAuthentication.java: 96 lines → 1 chunk(s)\n", "📄 ReleaseInfo.java: 210 lines → 1 chunk(s)\n", "📄 SprintWiseCalculation.java: 310 lines → 1 chunk(s)\n", "📄 TransitionInfo.java: 85 lines → 1 chunk(s)\n", "📄 TransitionMetrices.java: 116 lines → 1 chunk(s)\n", "📄 BacklogCalculation.java: 519 lines → 1 chunk(s)\n", "📄 BuildCalculations.java: 426 lines → 1 chunk(s)\n", "📄 CommonFunctions.java: 134 lines → 1 chunk(s)\n", "📄 Constant.java: 11 lines → 1 chunk(s)\n", "📄 CryptoUtils.java: 73 lines → 1 chunk(s)\n", "📄 DateUtil.java: 40 lines → 1 chunk(s)\n", "📄 DefectCalculations.java: 1813 lines → 10 chunk(s)\n", "📄 EncryptionDecryptionAES.java: 47 lines → 1 chunk(s)\n", "📄 EncryptorAesGcmPassword.java: 79 lines → 1 chunk(s)\n", "📄 RestClient.java: 193 lines → 1 chunk(s)\n", "📄 SprintProgress.java: 884 lines → 1 chunk(s)\n", "📄 SprintProgressCalculations.java: 448 lines → 1 chunk(s)\n", "📄 StoryProgressModel.java: 46 lines → 1 chunk(s)\n", "📄 StoryProgressSprintwise.java: 33 lines → 1 chunk(s)\n", "📄 TaskRiskSprint.java: 74 lines → 1 chunk(s)\n", "📄 TeamQualityUtils.java: 94 lines → 1 chunk(s)\n", "📄 VelocityCalculations.java: 577 lines → 1 chunk(s)\n", "📚 Total documents prepared: 91\n"]}], "source": ["# ========== STAGE 4: IMPROVED LLM EXTRACTION WITH AST CONTEXT ==========\n", "\n", "def escape_braces_for_langchain(text):\n", "    \"\"\"Escape curly braces in Java code to prevent LangChain template variable conflicts\"\"\"\n", "    if not text:\n", "        return text\n", "    return text.replace('{', '{{').replace('}', '}}')\n", "\n", "def build_optimized_stage4b_prompt(file_path, ast_df):\n", "    \"\"\"Build optimized Stage 4B prompt with minimal context (AST + file info only)\"\"\"\n", "    \n", "    # Build AST context for the specific file\n", "    ast_subset = ast_df[ast_df['file_path'] == file_path] if len(ast_df) > 0 else pd.DataFrame()\n", "    ast_context = ''\n", "    for _, row in ast_subset.iterrows():\n", "        ast_context += f\"{row['source_type']}:{row['source_node']} -[{row['relationship']}]-> {row['destination_type']}:{row['destination_node']}\\n\"\n", "    \n", "    # Extract file name from path\n", "    file_name = Path(file_path).stem if file_path else 'UnknownFile'\n", "    \n", "    # Build simplified prompt for structural extraction only\n", "    prompt = f\"\"\"\n", "You are a Java code lineage extraction engine focused on STRUCTURAL relationships only.\n", "\n", "CURRENT FILE: {file_name}\n", "\n", "AST RELATIONSHIPS (FOLLOW THESE PATTERNS EXACTLY):\n", "{ast_context}\n", "\n", "EXTRACTION RULES - STRUCTURAL ONLY:\n", "\n", "1. Extract ONLY basic structural relationships:\n", "   - file -[declares]-> class\n", "   - class -[declares]-> method  \n", "   - class -[has_field]-> variable\n", "   - method -[uses]-> variable\n", "   - class -[declares]-> endpoint\n", "   - class -[maps_to]-> table\n", "\n", "2. Use SIMPLE names only (remove prefixes like method:, class:, etc.)\n", "3. NEVER create reverse relationships (method->class, variable->method, etc.)\n", "4. Follow the AST RELATIONSHIPS above for correct structure\n", "5. Clean node names (remove method:, class: prefixes)\n", "\n", "Extract relationships in format:\n", "[SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName\n", "\n", "Return ONLY the structural triples, no explanations.\n", "\"\"\"\n", "    return prompt.replace(\"{\", \"{{\").replace(\"}\", \"}}\")\n", "\n", "def smart_chunk_strategy(file_path, content):\n", "    \"\"\"Smart chunking: whole file if <1000 lines, language chunks if larger\"\"\"\n", "    lines = content.count('\\n') + 1\n", "    \n", "    # Escape curly braces to prevent <PERSON><PERSON><PERSON><PERSON> template conflicts\n", "    escaped_content = escape_braces_for_langchain(content)\n", "    \n", "    if lines <= 1000:\n", "        return [{'content': escaped_content, 'metadata': {'source': file_path, 'chunk_type': 'whole_file'}}]\n", "    else:\n", "        splitter = RecursiveCharacterTextSplitter.from_language(\n", "            language=LC_Language.JAVA,\n", "            chunk_size=8000,\n", "            chunk_overlap=400\n", "        )\n", "        doc = Document(page_content=escaped_content, metadata={'source': file_path})\n", "        chunks = splitter.split_documents([doc])\n", "        return [{'content': chunk.page_content, 'metadata': {**chunk.metadata, 'chunk_type': 'language_chunk'}} for chunk in chunks]\n", "\n", "# Collect documents with smart chunking\n", "smart_docs = []\n", "for root, _, files in os.walk(BASE_PATH):\n", "    for file in files:\n", "        if file.endswith('.java'):\n", "            file_path = os.path.join(root, file)\n", "            try:\n", "                with open(file_path, 'r', encoding='utf-8') as f:\n", "                    content = f.read()\n", "                smart_docs.extend(smart_chunk_strategy(file_path, content))\n", "                print(f\"📄 {file}: {content.count(chr(10)) + 1} lines → {len(smart_chunk_strategy(file_path, content))} chunk(s)\")\n", "            except Exception as e:\n", "                print(f\"⚠️ Error loading {file}: {e}\")\n", "                continue\n", "\n", "print(f\"📚 Total documents prepared: {len(smart_docs)}\")"]}, {"cell_type": "code", "execution_count": 130, "id": "stage4b_llm_processing", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["🤖 Stage 4B: Enhanced LLM Processing: 100%|██████████| 91/91 [26:05<00:00, 17.20s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Stage 4B Complete: 2775 LLM relationships extracted\n", "🧠 Memory updated with 14 variable contexts\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# ========== STAGE 4B: IMPROVED LLM PROCESSING ==========\n", "\n", "all_llm_lineage = []\n", "for doc_info in tqdm(smart_docs, desc='🤖 Stage 4B: Enhanced LLM Processing'):\n", "    file_path = doc_info['metadata'].get('source')\n", "    \n", "    # Use optimized Stage 4B prompt with minimal context (AST + file info only)\n", "    enhanced_prompt = build_optimized_stage4b_prompt(file_path, df_ast)\n", "    \n", "    transformer = LLMGraphTransformer(\n", "        llm=llm,\n", "        additional_instructions=enhanced_prompt,\n", "        allowed_nodes=['class', 'interface', 'method', 'variable', 'table', 'endpoint'],  \n", "        allowed_relationships=[\n", "          \n", "            ('class', 'declares', 'method'),\n", "            ('interface', 'declares', 'method'),\n", "            ('class', 'declares', 'endpoint'),\n", "            ('method', 'calls', 'method'),\n", "            ('class', 'has_field', 'variable'),\n", "            ('method', 'uses', 'variable'),\n", "            ('class', 'uses', 'class'),\n", "            ('interface', 'extends', 'interface'),\n", "            ('class', 'extends', 'class'),\n", "            ('class', 'implements', 'interface'),\n", "            ('class', 'maps_to', 'table'),\n", "            ('method', 'reads_from', 'table'),\n", "            ('method', 'writes_to', 'table')\n", "        ],\n", "        strict_mode=True,\n", "        node_properties=False,\n", "        relationship_properties=False\n", "    )\n", "    \n", "    try:\n", "        # Ensure content is properly escaped for LangChain\n", "        escaped_content = escape_braces_for_langchain(doc_info['content'])\n", "        doc = Document(page_content=escaped_content, metadata=doc_info['metadata'])\n", "        graph_docs = transformer.convert_to_graph_documents([doc])\n", "        \n", "        for gd in graph_docs:\n", "            for rel in gd.relationships:\n", "                s_node = rel.source.id.strip()\n", "                s_type = rel.source.type.strip().lower()\n", "                t_node = rel.target.id.strip()\n", "                t_type = rel.target.type.strip().lower()\n", "                rel_type = rel.type.strip().lower()\n", "                \n", "                # Validate and fix relationship directions\n", "                def validate_relationship_direction(source_type, rel_type, target_type):\n", "                    \"\"\"Validate relationship direction and fix if needed\"\"\"\n", "                    valid_directions = {\n", "                        ('class', 'declares', 'method'),\n", "                        ('interface', 'declares', 'method'),\n", "                        ('class', 'declares', 'endpoint'),\n", "                        ('method', 'calls', 'method'),\n", "                        ('class', 'has_field', 'variable'),\n", "                        ('method', 'uses', 'variable'),\n", "                        ('class', 'uses', 'class'),\n", "                        ('interface', 'extends', 'interface'),\n", "                        ('class', 'extends', 'class'),\n", "                        ('class', 'implements', 'interface'),\n", "                        ('class', 'maps_to', 'table'),\n", "                        ('method', 'reads_from', 'table'),\n", "                        ('method', 'writes_to', 'table')\n", "                    }\n", "                    \n", "                    # Check if current direction is valid\n", "                    if (source_type, rel_type, target_type) in valid_directions:\n", "                        return source_type, rel_type, target_type, False\n", "                    \n", "                    # Check if reverse direction is valid\n", "                    if (target_type, rel_type, source_type) in valid_directions:\n", "                        return target_type, rel_type, source_type, True\n", "                    \n", "                    # Invalid relationship - skip\n", "                    return None, None, None, False\n", "                \n", "                # Validate direction\n", "                validated_s_type, validated_rel_type, validated_t_type, was_reversed = validate_relationship_direction(s_type, rel_type, t_type)\n", "                \n", "                if validated_s_type is None:\n", "                    print(f\"⚠️ Skipping invalid relationship: {s_type} -[{rel_type}]-> {t_type}\")\n", "                    continue\n", "                \n", "                if was_reversed:\n", "                    print(f\"🔄 Fixed direction: {s_type}:{s_node} -[{rel_type}]-> {t_type}:{t_node} → {validated_s_type}:{t_node} -[{validated_rel_type}]-> {validated_t_type}:{s_node}\")\n", "                    s_node, t_node = t_node, s_node\n", "                    s_type, t_type = validated_s_type, validated_t_type\n", "                    rel_type = validated_rel_type\n", "\n", "                def normalize_entity_improved(entity_name, entity_type):\n", "                    if not entity_name:\n", "                        return entity_name\n", "                    \n", "                    # Apply improved name cleaning\n", "                    clean_name = extract_clean_name(entity_name, entity_type)\n", "                    \n", "                    # Special handling for variables\n", "                    if entity_type == 'variable':\n", "                        # Filter out temp variables\n", "                        if is_temp_variable(clean_name):\n", "                            return None\n", "                        \n", "                        # Store variable context in memory\n", "                        if '.' in entity_name:\n", "                            context_part = entity_name.split('.')[0]\n", "                            context = extract_clean_name(context_part, 'method')\n", "                            memory['variable_contexts'][clean_name] = {\n", "                                'context': context,\n", "                                'context_type': 'method',\n", "                                'full_name': entity_name\n", "                            }\n", "                    \n", "                    return clean_name\n", "\n", "                s_node = normalize_entity_improved(s_node, s_type)\n", "                t_node = normalize_entity_improved(t_node, t_type)\n", "\n", "                if not s_node or not t_node or s_node == t_node:\n", "                    continue\n", "                \n", "                # Enforce correct relationship directions\n", "                valid_directions = {\n", "                    ('file', 'declares', 'class'),\n", "                    ('file', 'declares', 'interface'),\n", "                    ('class', 'declares', 'method'),\n", "                    ('interface', 'declares', 'method'),\n", "                    ('class', 'declares', 'endpoint'),\n", "                    ('class', 'has_field', 'variable'),\n", "                    ('method', 'uses', 'variable'),\n", "                    ('class', 'maps_to', 'table'),\n", "                    ('class', 'extends', 'class'),\n", "                    ('class', 'implements', 'interface'),\n", "                    ('interface', 'extends', 'interface'),\n", "                    ('method', 'calls', 'method'),\n", "                    ('method', 'reads_from', 'table'),\n", "                    ('method', 'writes_to', 'table')\n", "                }\n", "                \n", "                if (s_type, rel_type, t_type) not in valid_directions:\n", "                    continue\n", "\n", "                all_llm_lineage.append({\n", "                    'source_node': s_node,\n", "                    'source_type': s_type.title(),\n", "                    'destination_node': t_node,\n", "                    'destination_type': t_type.title(),\n", "                    'relationship': rel_type.upper(),\n", "                    'file_path': file_path\n", "                })\n", "                \n", "    except Exception as e:\n", "        print(f\"⚠️ LLM processing error for {file_path}: {e}\")\n", "        continue\n", "\n", "df_llm_lineage = pd.DataFrame(all_llm_lineage)\n", "\n", "# Update memory with LLM results\n", "for _, row in df_llm_lineage.iterrows():\n", "    edge_key = f\"{row['source_node']}-{row['relationship']}-{row['destination_node']}\"\n", "    memory['validated_edges'].add(edge_key)\n", "\n", "save_memory(memory)\n", "\n", "print(f'✅ Stage 4B Complete: {len(df_llm_lineage)} LLM relationships extracted')\n", "print(f'🧠 Memory updated with {len(memory[\"variable_contexts\"])} variable contexts')"]}, {"cell_type": "code", "execution_count": 131, "id": "stage5_improved_variable_transformations", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["🔄 Stage 5: Variable Transformations: 100%|██████████| 73/73 [25:45<00:00, 21.17s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Stage 5 Complete: 1755 variable transformation relationships extracted\n", "🔄 Transformations: 94, Flows: 353\n", "📊 Produces: 139, DB Ops: 136\n", "📞 Method Calls: 323\n"]}], "source": ["# ========== STAGE 5: IMPROVED LLM-ENHANCED VARIABLE TRANSFORMATIONS ==========\n", "\n", "def extract_improved_variable_transformations(class_registry, memory):\n", "    \"\"\"Extract variable transformations using improved LLM analysis with memory\"\"\"\n", "    relationships = []\n", "    \n", "    # Build memory context for better LLM understanding\n", "    memory_context = ''\n", "    if memory.get('variable_contexts'):\n", "        memory_context += f\"Known Variable Contexts: {len(memory['variable_contexts'])} variables\\n\"\n", "        for var_name, context in list(memory['variable_contexts'].items())[:5]:  # Show first 5 as examples\n", "            memory_context += f\"  - {var_name} (context: {context.get('context', 'Unknown')})\\n\"\n", "    \n", "    if memory.get('method_signatures'):\n", "        memory_context += f\"Known Methods: {len(memory['method_signatures'])} methods\\n\"\n", "    \n", "    for class_name, class_info in tqdm(class_registry.items(), desc=\"🔄 Stage 5: Variable Transformations\"):\n", "        source_code = class_info['source_code']\n", "        # Escape curly braces in source code to prevent LangChain template conflicts\n", "        escaped_source_code = escape_braces_for_langchain(source_code)\n", "        \n", "        try:\n", "            # Enhanced comprehensive data flow analysis prompt\n", "            transformation_prompt = f\"\"\"\n", "You are a comprehensive Java data flow analysis engine.\n", "\n", "CLASS CONTEXT: {class_name}\n", "\n", "MEMORY CONTEXT:\n", "{memory_context}\n", "\n", "COMPREHENSIVE DATA FLOW EXTRACTION:\n", "\n", "1. VARIABLE TRANSFORMATIONS & FLOWS:\n", "   - Variable -[FLOWS_TO]-> Variable (data passing between variables)\n", "   - Variable -[TRANSFORMS_TO]-> Variable (data conversion/mapping)\n", "   - Method -[PRODUCES]-> Variable (method creates/returns variable)\n", "   - Variable -[VALIDATES_AGAINST]-> Rule (validation logic)\n", "\n", "2. DATABASE & PERSISTENCE OPERATIONS:\n", "   - Method -[READS_FROM]-> Table (SELECT, find operations)\n", "   - Method -[WRITES_TO]-> Table (INSERT, UPDATE, DELETE)\n", "   - Method -[QUERIES]-> Database (complex queries)\n", "   - Variable -[PERSISTS_TO]-> Table (entity persistence)\n", "\n", "3. API & ENDPOINT RELATIONSHIPS:\n", "   - Class -[EXPOSES]-> Endpoint (REST API endpoints)\n", "   - Method -[MAPS_TO]-> Endpoint (method-endpoint mapping)\n", "   - Endpoint -[ACCEPTS]-> Variable (request parameters)\n", "   - Endpoint -[RETURNS]-> Variable (response data)\n", "\n", "4. BUSINESS OPERATIONS & METHOD CALLS:\n", "   - Method -[CALLS]-> Method (method invocations)\n", "   - Method -[INVOKES]-> ExternalService (external API calls)\n", "   - Variable -[TRIGGERS]-> Operation (business logic triggers)\n", "   - Method -[PROCESSES]-> BusinessRule (business logic processing)\n", "\n", "5. DATA VALIDATION & SECURITY:\n", "   - Variable -[VALIDATES_WITH]-> Validator (validation rules)\n", "   - Method -[AUTHENTICATES]-> User (authentication logic)\n", "   - Method -[AUTHORIZES]-> Permission (authorization checks)\n", "\n", "NAMING RULES:\n", "- PascalCase for Methods, Classes, Endpoints\n", "- Variables: show only variable name (userDto, not CreateUser.userDto)\n", "- Endpoints: include HTTP method (GET:/api/users, POST:/api/orders)\n", "- Tables: use singular form (User, not Users)\n", "- Filter out temp variables (i, j, temp, tmp, counter, index)\n", "\n", "CODE TO ANALYZE:\n", "{escaped_source_code}\n", "\n", "Return relationships in format:\n", "[SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName\n", "\n", "Examples:\n", "[Variable]:userDto -[FLOWS_TO]-> [Variable]:userEntity\n", "[Method]:CreateUser -[WRITES_TO]-> [Table]:User\n", "[Method]:GetUser -[CALLS]-> [Method]:FindById\n", "[Variable]:orderData -[TRANSFORMS_TO]-> [Variable]:orderEntity\n", "[Class]:UserController -[EXPOSES]-> [Endpoint]:GET:/api/users\n", "[Method]:ValidateUser -[VALIDATES_WITH]-> [Validator]:UserValidator\n", "\"\"\"\n", "            \n", "            response = llm.invoke(transformation_prompt)\n", "            content = response.content if hasattr(response, 'content') else str(response)\n", "            \n", "            doc = Document(page_content=content, metadata={'class_name': class_name})\n", "            \n", "            transformer = LLMGraphTransformer(\n", "                llm=llm,\n", "                allowed_nodes=['variable', 'method', 'table', 'class', 'endpoint', 'database', 'rule', 'validator', 'externalservice', 'operation', 'businessrule', 'user', 'permission'],\n", "                allowed_relationships=[\n", "                    # Variable transformations & flows\n", "                    ('variable', 'flows_to', 'variable'),\n", "                    ('variable', 'transforms_to', 'variable'),\n", "                    ('method', 'produces', 'variable'),\n", "                    ('variable', 'validates_against', 'rule'),\n", "                    \n", "                    # Database & persistence operations\n", "                    ('method', 'reads_from', 'table'),\n", "                    ('method', 'writes_to', 'table'),\n", "                    ('method', 'queries', 'database'),\n", "                    ('variable', 'persists_to', 'table'),\n", "                    \n", "                    # API & endpoint relationships\n", "                    ('class', 'exposes', 'endpoint'),\n", "                    ('method', 'maps_to', 'endpoint'),\n", "                    ('endpoint', 'accepts', 'variable'),\n", "                    ('endpoint', 'returns', 'variable'),\n", "                    \n", "                    # Business operations & method calls\n", "                    ('method', 'calls', 'method'),\n", "                    ('method', 'invokes', 'externalservice'),\n", "                    ('variable', 'triggers', 'operation'),\n", "                    ('method', 'processes', 'businessrule'),\n", "                    \n", "                    # Data validation & security\n", "                    ('variable', 'validates_with', 'validator'),\n", "                    ('method', 'authenticates', 'user'),\n", "                    ('method', 'authorizes', 'permission')\n", "                ],\n", "                strict_mode=False,  # Allow more flexibility for comprehensive extraction\n", "                node_properties=False,\n", "                relationship_properties=False,\n", "            )\n", "            \n", "            graph_docs = transformer.convert_to_graph_documents([doc])\n", "            \n", "            for gd in graph_docs:\n", "                for rel in gd.relationships:\n", "                    s_type = rel.source.type.title()\n", "                    t_type = rel.target.type.title()\n", "                    s_node = extract_clean_name(rel.source.id, s_type.lower())\n", "                    t_node = extract_clean_name(rel.target.id, t_type.lower())\n", "                    rel_type = rel.type.upper()\n", "                    \n", "                    # Filter out temp variables\n", "                    if s_type == 'Variable' and is_temp_variable(s_node):\n", "                        continue\n", "                    if t_type == 'Variable' and is_temp_variable(t_node):\n", "                        continue\n", "                    \n", "                    # Skip if nodes are empty or same\n", "                    if not s_node or not t_node or s_node == t_node:\n", "                        continue\n", "                    \n", "                    relationships.append({\n", "                        'source_node': s_node,\n", "                        'source_type': s_type,\n", "                        'destination_node': t_node,\n", "                        'destination_type': t_type,\n", "                        'relationship': rel_type,\n", "                        'class_context': class_name,\n", "                        'stage': 'variable_transformations'\n", "                    })\n", "                    \n", "                    # Store variable context in memory for variables\n", "                    for node_name, node_type in [(s_node, s_type), (t_node, t_type)]:\n", "                        if node_type == 'Variable':\n", "                            memory['variable_contexts'][node_name] = {\n", "                                'context': class_name,\n", "                                'context_type': 'class',\n", "                                'stage': '5_transformations'\n", "                            }\n", "                    \n", "                    # Add [USES] relationships from Method → Variable\n", "                    if s_type == 'Method' and t_type == 'Variable':\n", "                        uses_rel = {\n", "                            'source_node': s_node,\n", "                            'source_type': 'Method',\n", "                            'destination_node': t_node,\n", "                            'destination_type': 'Variable',\n", "                            'relationship': 'USES',\n", "                            'class_context': class_name,\n", "                            'stage': 'variable_transformations'\n", "                        }\n", "                        if uses_rel not in relationships:\n", "                            relationships.append(uses_rel)\n", "            \n", "        except Exception as llm_error:\n", "            print(f\"⚠️ LLM transformation error for {class_name}: {llm_error}\")\n", "            continue\n", "    \n", "    return relationships\n", "\n", "# Execute Stage 5\n", "transformation_relationships = extract_improved_variable_transformations(enhanced_class_registry, memory)\n", "df_transformations = pd.DataFrame(transformation_relationships)\n", "\n", "# Update memory with Stage 5 results\n", "memory['stage_5_results'] = {\n", "    'relationships': len(df_transformations),\n", "    'variable_transformations': len([r for r in transformation_relationships if 'TRANSFORMS_TO' in r.get('relationship', '')]),\n", "    'variable_flows': len([r for r in transformation_relationships if 'FLOWS_TO' in r.get('relationship', '')]),\n", "    'method_produces': len([r for r in transformation_relationships if 'PRODUCES' in r.get('relationship', '')]),\n", "    'db_operations': len([r for r in transformation_relationships if r.get('relationship') in ['READS_FROM', 'WRITES_TO']]),\n", "    'method_calls': len([r for r in transformation_relationships if 'CALLS' in r.get('relationship', '')])\n", "}\n", "\n", "# Add to memory cache for cross-stage reference\n", "for rel in transformation_relationships:\n", "    edge_key = f\"{rel['source_node']}-{rel['relationship']}-{rel['destination_node']}\"\n", "    if edge_key not in memory['validated_edges']:\n", "        memory['validated_edges'].add(edge_key)\n", "        \n", "        # Cache transformation patterns\n", "        if rel['relationship'] in ['FLOWS_TO', 'TRANSFORMS_TO', 'PRODUCES']:\n", "            cache_key = f\"{rel['source_node']}_{rel['relationship']}\"\n", "            memory['transformation_cache'].setdefault(cache_key, []).append({\n", "                'target': rel['destination_node'],\n", "                'stage': '5_improved',\n", "                'class_context': rel.get('class_context', '')\n", "            })\n", "        \n", "        # Track variable flows\n", "        if rel['destination_type'] == 'Variable':\n", "            var_key = rel['destination_node']\n", "            memory['variable_flows'].setdefault(var_key, []).append({\n", "                'stage': '5_improved',\n", "                'relationship': rel['relationship'],\n", "                'source': rel['source_node'],\n", "                'transformation_type': rel['relationship']\n", "            })\n", "\n", "save_memory(memory)\n", "\n", "print(f'✅ Stage 5 Complete: {len(df_transformations)} variable transformation relationships extracted')\n", "print(f'🔄 Transformations: {memory[\"stage_5_results\"][\"variable_transformations\"]}, Flows: {memory[\"stage_5_results\"][\"variable_flows\"]}')\n", "print(f'📊 Produces: {memory[\"stage_5_results\"][\"method_produces\"]}, DB Ops: {memory[\"stage_5_results\"][\"db_operations\"]}')\n", "print(f'📞 Method Calls: {memory[\"stage_5_results\"][\"method_calls\"]}')"]}, {"cell_type": "code", "execution_count": 132, "id": "8e057ac2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔧 FIXING FILE-<PERSON>LA<PERSON> RELATIONSHIPS AND NAMING ISSUES...\n", "📊 Memory stats: 4061 validated edges\n", "📁 Stage 2 - Hierarchy relationships: 160\n", "📎 File-Class relationships from df_hierarchy: 72\n", "🔗 Adding missing file-class relationships...\n", "📁 Found files: ['Application', 'TriggerCollector', 'ALMConfigController', 'AlmController', 'ALMConfigReq', 'ALMToolReq', 'Authentication', 'DataResponse', 'ALMConfigService', 'ALMConfigServiceImplementation', 'AlmService', 'ALMServiceImplementation', 'ConfigurationSettingService', 'ConfigurationSettingServiceImplementation', 'DateUtil', 'ConstantVariable', 'ProjectCollector', 'Configuration', 'DataConfig', 'MongoAggregate', 'ALMConfiguration', 'BaseModel', 'ChangeHistoryModel', 'ComponentVelocityList', 'ConfigurationSetting', 'ConfigurationToolInfoMetric', 'CustomFields', 'IterationModel', 'IterationOutModel', 'MetricsModel', 'MonogOutMetrics', 'ScoreCardSprintData', 'TransitionModel', 'VelocityList', 'ALMConfigRepo', 'ChangeHisortyRepo', 'ConfigurationSettingRep', 'IterationRepo', 'MetricRepo', 'TransitionRepo', 'ALMClientImplementation', 'ChartCalculations', 'Customfieldnames', 'DeleteJiraIssues', 'EffortAndChangeItemInfo', 'IssueHierarchy', 'IterationInfo', 'JIRAApplication', 'JiraAuthentication', 'JIRAClient', 'JiraExceptions', 'MetricsInfo', 'RallyAuthentication', 'ReleaseInfo', 'SprintWiseCalculation', 'TransitionInfo', 'TransitionMetrices', 'BacklogCalculation', 'BuildCalculations', 'CommonFunctions', 'Constant', 'CryptoUtils', 'DefectCalculations', 'EncryptionDecryptionAES', 'EncryptorAesGcmPassword', 'RestClient', 'SprintProgress', 'SprintProgressCalculations', 'StoryProgressModel', 'StoryProgressSprintwise', 'TaskRiskSprint', 'TeamQualityUtils', 'VelocityCalculations']\n", "🏛️ Found classes: ['Almclientimplementation', 'Almconfigcontroller', 'Almconfigreq', 'Almconfigserviceimplementation', 'Almconfiguration', 'Almcontroller', 'Almserviceimplementation', 'Almtoolreq', 'Application', 'Auth', 'Authentication', 'Backlogcalculation', 'Basemodel', 'Buildcalculations', 'Changehistorymodel', 'Chartcalculations', 'Commonfunctions', 'Componentvelocitylist', 'Configuration', 'Configurationsetting', 'Configurationsettingserviceimplementation', 'Configurationtoolinfometric', 'Constant', 'Constantvariable', 'Cryptoutils', 'Customfieldnames', 'Customfields', 'Dataconfig', 'Dataresponse', 'Dateutil', 'Defectcalculations', 'Deletejiraissues', 'Effortandchangeiteminfo', 'Encryptiondecryptionaes', 'Encryptoraesgcmpassword', 'Issuehierarchy', 'Iterationinfo', 'Iterationmodel', 'Iterationoutmodel', 'Jiraapplication', 'Jiraauthentication', 'Jiraexceptions', 'Logdatecomparator', 'Metricsinfo', 'Metricsmodel', 'Mongoaggregate', 'Monogoutmetrics', 'Projectcollector', 'Rallyauthentication', 'Releaseinfo', 'Restclient', 'Scorecardsprintdata', 'Sprintcomparatort', 'Sprintprogress', 'Sprintprogresscalculations', 'Sprintwisecalculation', 'Storyprogressmodel', 'Storyprogresssprintwise', 'Taskrisksprint', 'Teamqualityutils', 'Transitioncomparator', 'Transitioninfo', 'Transitionmetrices', 'Transitionmodel', 'Triggercollector', 'Valuecomparator', 'Velocitycalculations', 'Velocitylist']\n", "📎 Created 63 file-class mappings\n", "  Application -[DECLARES]-> Application\n", "  Triggercollector -[DECLARES]-> Triggercollector\n", "  Almconfigcontroller -[DECLARES]-> Almconfigcontroller\n", "  Almcontroller -[DECLARES]-> Almcontroller\n", "  Almconfigreq -[DECLARES]-> Almconfigreq\n", "  Almtoolreq -[DECLARES]-> Almtoolreq\n", "  Authentication -[DECLARES]-> Authentication\n", "  Dataresponse -[DECLARES]-> Dataresponse\n", "  Almconfigserviceimplementation -[DECLARES]-> Almconfigserviceimplementation\n", "  Almserviceimplementation -[DECLARES]-> Almserviceimplementation\n", "  Configurationsettingserviceimplementation -[DECLARES]-> Configurationsettingserviceimplementation\n", "  Dateutil -[DECLARES]-> Dateutil\n", "  Constantvariable -[DECLARES]-> Constantvariable\n", "  Projectcollector -[DECLARES]-> Projectcollector\n", "  Configuration -[DECLARES]-> Configuration\n", "  Dataconfig -[DECLARES]-> Dataconfig\n", "  Mongoaggregate -[DECLARES]-> Mongoaggregate\n", "  Almconfiguration -[DECLARES]-> Almconfiguration\n", "  Basemodel -[DECLARES]-> Basemodel\n", "  Changehistorymodel -[DECLARES]-> Changehistorymodel\n", "  Componentvelocitylist -[DECLARES]-> Componentvelocitylist\n", "  Configurationsetting -[DECLARES]-> Configurationsetting\n", "  Configurationtoolinfometric -[DECLARES]-> Configurationtoolinfometric\n", "  Customfields -[DECLARES]-> Customfields\n", "  Iterationmodel -[DECLARES]-> Iterationmodel\n", "  Iterationoutmodel -[DECLARES]-> Iterationoutmodel\n", "  Metricsmodel -[DECLARES]-> Metricsmodel\n", "  Monogoutmetrics -[DECLARES]-> Monogoutmetrics\n", "  Scorecardsprintdata -[DECLARES]-> Scorecardsprintdata\n", "  Transitionmodel -[DECLARES]-> Transitionmodel\n", "  Velocitylist -[DECLARES]-> Velocitylist\n", "  Almclientimplementation -[DECLARES]-> Almclientimplementation\n", "  Chartcalculations -[DECLARES]-> Chartcalculations\n", "  Customfieldnames -[DECLARES]-> Customfieldnames\n", "  Deletejiraissues -[DECLARES]-> Deletejiraissues\n", "  Effortandchangeiteminfo -[DECLARES]-> Effortandchangeiteminfo\n", "  Issuehierarchy -[DECLARES]-> Issuehierarchy\n", "  Iterationinfo -[DECLARES]-> Iterationinfo\n", "  Jiraapplication -[DECLARES]-> Jiraapplication\n", "  Jiraauthentication -[DECLARES]-> Jiraauthentication\n", "  Jiraexceptions -[DECLARES]-> Jiraexceptions\n", "  Metricsinfo -[DECLARES]-> Metricsinfo\n", "  Rallyauthentication -[DECLARES]-> Rallyauthentication\n", "  Releaseinfo -[DECLARES]-> Releaseinfo\n", "  Sprintwisecalculation -[DECLARES]-> Sprintwisecalculation\n", "  Transitioninfo -[DECLARES]-> Transitioninfo\n", "  Transitionmetrices -[DECLARES]-> Transitionmetrices\n", "  Backlogcalculation -[DECLARES]-> Backlogcalculation\n", "  Buildcalculations -[DECLARES]-> Buildcalculations\n", "  Commonfunctions -[DECLARES]-> Commonfunctions\n", "  Constant -[DECLARES]-> Constant\n", "  Cryptoutils -[DECLARES]-> Cryptoutils\n", "  Defectcalculations -[DECLARES]-> Defectcalculations\n", "  Encryptiondecryptionaes -[DECLARES]-> Encryptiondecryptionaes\n", "  Encryptoraesgcmpassword -[DECLARES]-> Encryptoraesgcmpassword\n", "  Restclient -[DECLARES]-> Restclient\n", "  Sprintprogress -[DECLARES]-> Sprintprogress\n", "  Sprintprogresscalculations -[DECLARES]-> Sprintprogresscalculations\n", "  Storyprogressmodel -[DECLARES]-> Storyprogressmodel\n", "  Storyprogresssprintwise -[DECLARES]-> Storyprogresssprintwise\n", "  Taskrisksprint -[DECLARES]-> Taskrisksprint\n", "  Teamqualityutils -[DECLARES]-> Teamqualityutils\n", "  Velocitycalculations -[DECLARES]-> Velocitycalculations\n", "🧹 Applying comprehensive naming fixes and normalization...\n", "✅ Consolidation complete: 3920 relationships\n", "🗑️ Clearing Neo4j database...\n", "🚀 Uploading 3920 relationships to Neo4j...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Creating Neo4j nodes: 100%|██████████| 3920/3920 [00:14<00:00, 261.61it/s]\n", "🔗 Creating Neo4j relationships: 100%|██████████| 3920/3920 [00:50<00:00, 77.86it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["💾 CSV saved to consolidated_lineage_v8_improved_fixed.csv\n", "\n", "📎 File-Class relationships in final dataset: 8\n", "  Almserviceimplementation -[DECLARES]-> Transitioncomparator\n", "  Almserviceimplementation -[DECLARES]-> Sprintcomparatort\n", "  Chartcalculations -[DECLARES]-> Transitioncomparator\n", "  Chartcalculations -[DECLARES]-> Sprintcomparatort\n", "  Defectcalculations -[DECLARES]-> Valuecomparator\n", "  Defectcalculations -[DECLARES]-> Sprintcomparatort\n", "  Sprintprogress -[DECLARES]-> Logdatecomparator\n", "  Sprintprogress -[DECLARES]-> Sprintcomparator\n", "\n", "🎉 PIPELINE COMPLETE!\n", "📊 Total relationships: 3920\n", "🏗️ Nodes created: 1938\n", "🔗 Relationships created: 3920\n", "🧠 Variable contexts tracked: 527\n", "\n", "✅ All improvements applied:\n", "   • PascalCase naming consistency\n", "   • Clean name extraction (removed file.class, classname.method patterns)\n", "   • Variable context separation\n", "   • Memory-enhanced LLM processing with build_enhanced_system_prompt_with_memory\n", "   • Added Stage 5: LLM-Enhanced Variable Transformations\n", "   • Improved final CSV generation\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# ========== STAGE 5: IMPROVED FINAL CONSOLIDATION & NEO4J UPLOAD ==========\n", "\n", "def apply_pascal_case(name):\n", "    \"\"\"Apply PascalCase to names with special handling for service/utils classes\"\"\"\n", "    if not name:\n", "        return name\n", "    \n", "    # General PascalCase conversion\n", "    words = name.replace('_', ' ').replace('-', ' ').split()\n", "    return ''.join(word.capitalize() for word in words if word)\n", "\n", "def consolidate_all_relationships_improved():\n", "    \"\"\"Consolidate all relationship data with improved naming\"\"\"\n", "    all_relationships = []\n", "\n", "    print(f\"📊 Memory stats: {len(memory['validated_edges'])} validated edges\")\n", "\n", "    # Stage 2: Folder-File relationships\n", "    print(f\"📁 Stage 2 - Hierarchy relationships: {len(df_hierarchy)}\")\n", "    for _, row in df_hierarchy.iterrows():\n", "        all_relationships.append({\n", "            'source_node': row['source_node'],\n", "            'source_type': row['source_type'],\n", "            'destination_node': row['destination_node'],\n", "            'destination_type': row['destination_type'],\n", "            'relationship': row['relationship'],\n", "            'stage': 'hierarchy'\n", "        })\n", "\n", "    \n", "    # ✅ Count file-class relationships already included in df_hierarchy\n", "    file_class_count = len(df_hierarchy[\n", "        (df_hierarchy['source_type'] == 'File') & \n", "        (df_hierarchy['destination_type'] == 'Class') & \n", "        (df_hierarchy['relationship'] == 'DECLARES')\n", "    ])\n", "    print(f\"📎 File-Class relationships from df_hierarchy: {file_class_count}\")\n", "    \n", "\n", "    # ✅ Stage 4B: LLM relationships (class-to-variables only)\n", "    for _, row in df_llm_lineage.iterrows():\n", "        all_relationships.append({\n", "            'source_node': row['source_node'],\n", "            'source_type': row['source_type'],\n", "            'destination_node': row['destination_node'],\n", "            'destination_type': row['destination_type'],\n", "            'relationship': row['relationship'],\n", "            'stage': 'llm_enhanced'\n", "        })\n", "    \n", "    # ADD MISSING FILE-CLA<PERSON> RELATIONSHIPS\n", "    print(\"🔗 Adding missing file-class relationships...\")\n", "    \n", "    # Extract unique classes from LLM data (note: LLM uses lowercase 'class')\n", "    # Extract unique classes from LLM data (check both lowercase and uppercase)\n", "    llm_classes_lower = df_llm_lineage[df_llm_lineage['source_type'] == 'class']['source_node'].unique()\n", "    llm_classes_upper = df_llm_lineage[df_llm_lineage['source_type'] == 'Class']['source_node'].unique()\n", "    llm_classes_dest_lower = df_llm_lineage[df_llm_lineage['destination_type'] == 'class']['destination_node'].unique()\n", "    llm_classes_dest_upper = df_llm_lineage[df_llm_lineage['destination_type'] == 'Class']['destination_node'].unique()\n", "    \n", "    # Combine all class sources\n", "    import numpy as np\n", "    all_classes = np.concatenate([llm_classes_lower, llm_classes_upper, llm_classes_dest_lower, llm_classes_dest_upper])\n", "    llm_classes = np.unique(all_classes[all_classes != ''])  # Remove duplicates and empty strings\n", "    \n", "    # Extract files from hierarchy data\n", "    hierarchy_files = df_hierarchy[df_hierarchy['destination_type'] == 'File']['destination_node'].unique()\n", "    \n", "    print(f\"📁 Found files: {list(hierarchy_files)}\")\n", "    print(f\"🏛️ Found classes: {list(llm_classes)}\")\n", "    \n", "    # Create file-class mappings based on name similarity\n", "    file_class_mappings = []\n", "    for file_name in hierarchy_files:\n", "        file_base = file_name.replace('.java', '').lower()\n", "        \n", "        # Find matching class (case-insensitive)\n", "        for class_name in llm_classes:\n", "            if class_name.lower() == file_base:\n", "                # Apply PascalCase to both for consistency\n", "                file_pascal = apply_pascal_case(file_name.replace('.java', ''))\n", "                class_pascal = apply_pascal_case(class_name)\n", "                \n", "                file_class_mappings.append({\n", "                    'source_node': file_pascal,\n", "                    'source_type': 'File',\n", "                    'destination_node': class_pascal,\n", "                    'destination_type': 'Class',\n", "                    'relationship': 'DECLARES',\n", "                    'stage': 'file_class_mapping'\n", "                })\n", "                break\n", "    \n", "    print(f\"📎 Created {len(file_class_mappings)} file-class mappings\")\n", "    for mapping in file_class_mappings:\n", "        print(f\"  {mapping['source_node']} -[DECLARES]-> {mapping['destination_node']}\")\n", "        all_relationships.append(mapping)\n", "    \n", "    # Stage 5: Variable Transformations\n", "    for _, row in df_transformations.iterrows():\n", "        all_relationships.append({\n", "            'source_node': row['source_node'],\n", "            'source_type': row['source_type'],\n", "            'destination_node': row['destination_node'],\n", "            'destination_type': row['destination_type'],\n", "            'relationship': row['relationship'],\n", "            'stage': 'variable_transformations'\n", "        })\n", "    \n", "    df_combined = pd.DataFrame(all_relationships)\n", "    \n", "    # Helper functions for comprehensive naming normalization\n", "    def normalize_endpoint_name(endpoint):\n", "        \"\"\"Normalize endpoint names to standard format\"\"\"\n", "        if not endpoint:\n", "            return endpoint\n", "        \n", "        # Ensure HTTP method is included\n", "        if not any(method in endpoint.upper() for method in ['GET:', 'POST:', 'PUT:', 'DELETE:', 'PATCH:']):\n", "            # Default to GET if no method specified\n", "            endpoint = f\"GET:{endpoint}\" if not endpoint.startswith('/') else f\"GET:{endpoint}\"\n", "        \n", "        return endpoint\n", "    \n", "    def singularize_table_name(table_name):\n", "        \"\"\"Convert table names to singular form\"\"\"\n", "        if not table_name:\n", "            return table_name\n", "        \n", "        # Simple singularization rules\n", "        if table_name.endswith('s') and len(table_name) > 1:\n", "            # Handle common cases\n", "            if table_name.lower().endswith('ies'):\n", "                return table_name[:-3] + 'y'\n", "            elif table_name.lower().endswith('es'):\n", "                return table_name[:-2]\n", "            else:\n", "                return table_name[:-1]\n", "        \n", "        return table_name\n", "    \n", "    # Apply comprehensive final naming fixes and normalization\n", "    def apply_final_naming_fixes(row):\n", "        \"\"\"Apply final naming fixes to ensure consistency\"\"\"\n", "        source_node = row['source_node']\n", "        dest_node = row['destination_node']\n", "        source_type = row['source_type']\n", "        dest_type = row['destination_type']\n", "        \n", "        # Apply PascalCase to classes, methods, files, folders\n", "        if source_type in ['Class', 'Method', 'File', 'Folder', 'Interface']:\n", "            source_node = apply_pascal_case(extract_clean_name(source_node, source_type.lower()))\n", "        \n", "        if dest_type in ['Class', 'Method', 'File', 'Folder', 'Interface']:\n", "            dest_node = apply_pascal_case(extract_clean_name(dest_node, dest_type.lower()))\n", "        \n", "        # Handle variables - show only variable name\n", "        if dest_type == 'Variable':\n", "            dest_node = extract_clean_name(dest_node, 'variable')\n", "        \n", "        # Handle tables - apply PascalCase\n", "        if dest_type == 'Table':\n", "            dest_node = extract_clean_name(dest_node, 'table')\n", "        \n", "        return source_node, dest_node\n", "    \n", "    # Apply comprehensive naming fixes and normalization\n", "    print(\"🧹 Applying comprehensive naming fixes and normalization...\")\n", "    for idx, row in df_combined.iterrows():\n", "        source_node, dest_node = apply_final_naming_fixes(row)\n", "        df_combined.at[idx, 'source_node'] = source_node\n", "        df_combined.at[idx, 'destination_node'] = dest_node\n", "    \n", "    # Remove duplicates\n", "    df_combined.drop_duplicates(\n", "        subset=[\"source_node\", \"source_type\", \"destination_node\", \"destination_type\", \"relationship\"],\n", "        inplace=True\n", "    )\n", "    \n", "    # Remove invalid relationships\n", "    df_combined = df_combined[\n", "        (df_combined['source_node'].notna()) & (df_combined['destination_node'].notna()) &\n", "        (df_combined['source_node'] != df_combined['destination_node']) &\n", "        (df_combined['source_node'].str.lower() != 'none') & \n", "        (df_combined['destination_node'].str.lower() != 'none') &\n", "        (df_combined['source_node'] != '') & \n", "        (df_combined['destination_node'] != '')\n", "    ]\n", "    \n", "    print(f\"✅ Consolidation complete: {len(df_combined)} relationships\")\n", "    return df_combined\n", "\n", "def upload_to_neo4j_improved(df_consolidated):\n", "    \"\"\"Upload consolidated lineage to Neo4j with improved variable handling\"\"\"\n", "    nodes_created = set()\n", "    \n", "    print(f\"🚀 Uploading {len(df_consolidated)} relationships to Neo4j...\")\n", "    \n", "    # Create Nodes with proper variable context\n", "    for _, row in tqdm(df_consolidated.iterrows(), desc=\"Creating Neo4j nodes\", total=len(df_consolidated)):\n", "        for node_name, node_type in [(row['source_node'], row['source_type']), (row['destination_node'], row['destination_type'])]:\n", "            if (node_name, node_type) not in nodes_created:\n", "                \n", "                # Special handling for variables\n", "                if node_type.lower() == 'variable':\n", "                    var_context = memory['variable_contexts'].get(node_name, {})\n", "                    display_name = node_name  # Only variable name\n", "                    context = var_context.get('context', 'Unknown')\n", "                    context_type = var_context.get('context_type', 'method')\n", "                else:\n", "                    display_name = node_name\n", "                    context = None\n", "                    context_type = None\n", "                \n", "                query = f\"\"\"\n", "                MERGE (n:{node_type} {{name: $name}})\n", "                SET n.display_name = $display_name,\n", "                    n.context = $context,\n", "                    n.context_type = $context_type,\n", "                    n.node_type = $node_type\n", "                \"\"\"\n", "                try:\n", "                    graph.query(query, {\n", "                        'name': node_name,\n", "                        'display_name': display_name,\n", "                        'context': context,\n", "                        'context_type': context_type,\n", "                        'node_type': node_type\n", "                    })\n", "                    nodes_created.add((node_name, node_type))\n", "                except Exception as e:\n", "                    print(f\"⚠️ Node creation error [{node_name}, {node_type}]: {e}\")\n", "\n", "    # Create Relationships\n", "    relationships_created = 0\n", "    for _, row in tqdm(df_consolidated.iterrows(), desc=\"🔗 Creating Neo4j relationships\", total=len(df_consolidated)):\n", "        try:\n", "            query = f\"\"\"\n", "            MATCH (s:{row['source_type']} {{name: $source_name}})\n", "            MATCH (t:{row['destination_type']} {{name: $target_name}})\n", "            MERGE (s)-[r:{row['relationship']}]->(t)\n", "            SET r.stage = $stage\n", "            \"\"\"\n", "            graph.query(query, {\n", "                'source_name': row['source_node'],\n", "                'target_name': row['destination_node'],\n", "                'stage': row['stage']\n", "            })\n", "            relationships_created += 1\n", "        except Exception as e:\n", "            print(f\"⚠️ Relationship creation error: {e}\")\n", "\n", "    return len(nodes_created), relationships_created\n", "\n", "# Execute Final Consolidation with File-Class Fixes\n", "print(\"🔧 FIXING FILE-CLASS RELATIONSHIPS AND NAMING ISSUES...\")\n", "df_consolidated = consolidate_all_relationships_improved()\n", "\n", "# Clear Neo4j database before upload\n", "print(\"🗑️ Clearing Neo4j database...\")\n", "graph.query(\"MATCH (n) DETACH DELETE n\")\n", "\n", "# Upload to Neo4j\n", "nodes_count, rels_count = upload_to_neo4j_improved(df_consolidated)\n", "\n", "# Save improved CSV\n", "df_consolidated.to_csv('consolidated_lineage_v8_improved_fixed.csv', index=False)\n", "print(f\"💾 CSV saved to consolidated_lineage_v8_improved_fixed.csv\")\n", "\n", "# Show file-class relationships in final dataset\n", "file_class_rels = df_consolidated[\n", "    (df_consolidated['source_type'] == 'File') & \n", "    (df_consolidated['destination_type'] == 'Class') & \n", "    (df_consolidated['relationship'] == 'DECLARES')\n", "]\n", "print(f\"\\n📎 File-Class relationships in final dataset: {len(file_class_rels)}\")\n", "for _, row in file_class_rels.iterrows():\n", "    print(f\"  {row['source_node']} -[DECLARES]-> {row['destination_node']}\")\n", "\n", "# Update Memory with final stats\n", "memory['final_stats'] = {\n", "    'total_relationships': len(df_consolidated),\n", "    'nodes_created': nodes_count,\n", "    'relationships_created': rels_count,\n", "    'variable_contexts': len(memory['variable_contexts']),\n", "    'stages_completed': 6,\n", "    'improvements_applied': [\n", "        'PascalCase naming consistency',\n", "        'Clean name extraction (removed file.class, classname.method patterns)',\n", "        'Variable context separation',\n", "        'Memory-enhanced LLM processing with build_enhanced_system_prompt_with_memory',\n", "        'Added Stage 5: LLM-Enhanced Variable Transformations',\n", "        'Improved final CSV generation'\n", "    ]\n", "}\n", "save_memory(memory)\n", "\n", "print(f\"\\n🎉 PIPELINE COMPLETE!\")\n", "print(f\"📊 Total relationships: {len(df_consolidated)}\")\n", "print(f\"🏗️ Nodes created: {nodes_count}\")\n", "print(f\"🔗 Relationships created: {rels_count}\")\n", "print(f\"🧠 Variable contexts tracked: {len(memory['variable_contexts'])}\")\n", "print(f\"\\n✅ All improvements applied:\")\n", "for improvement in memory['final_stats']['improvements_applied']:\n", "    print(f\"   • {improvement}\")\n", " "]}, {"cell_type": "code", "execution_count": null, "id": "014d2661", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}