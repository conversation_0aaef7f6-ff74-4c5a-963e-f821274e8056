package AppTwo;

public class OrderUtils {

    public static Order convertDtoToEntity(OrderDto dto) {
        Order order = new Order();
        order.setItem(dto.getItem()); // dto.item FLOWS_TO order.item
        order.setQuantity(dto.getQuantity()); // dto.quantity FLOWS_TO order.quantity
        return order;
    }

    public static double calculateTotal(double price, int quantity) {
        double total = price * quantity; // price, quantity FLOWS_TO total
        double taxedTotal = total * 1.18; // total TRANSFORMS_TO taxedTotal
        return taxedTotal; // taxedTotal PRODUCES output
    }
}