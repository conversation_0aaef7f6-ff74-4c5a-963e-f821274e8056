/**
 * <AUTHOR>
 *
 */
package com.bolt.dashboard.core;

import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ALMConfiguration;
import com.bolt.dashboard.core.model.CollaborativeMailModel;
import com.bolt.dashboard.core.model.CollectorLastRun;
import com.bolt.dashboard.core.repository.ALMConfigRepo;
import com.bolt.dashboard.core.repository.CollectorLastRunRepo;
import com.bolt.dashboard.core.repository.MailSetupRepo;
import com.bolt.dashboard.util.EmailNotification;

public class ConstantVariable {
    public static final String NAME = "name";
    public static final String DATE = "date";
    public static final String VERSION = "version";
    public static final String MSR = "msr";
    public static final String VALUE = "val";
    public static final String FORMATTED_VALUE = "frmt_val";
    public static final String KEY = "key";
    public static final String ALERT_TEXT = "alert_text";
    public static final String DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ssZ";
    public static final String JOBS_URL_FOR_DB = "/api/json?tree=result,duration,fullDisplayName,timestamp";
    public static final String SMTP_TOADDRESS = "<EMAIL>";
    public static final String MAIL_SMTP_AUTH = "mail.smtp.auth";
    public static final String MAIL_SMTP_ENABLE = "mail.smtp.starttls.enable";
    public static final String MAIL_SMTP_HOST = "mail.smtp.host";
    public static final String MAIL_SMTP_PORT = "mail.smtp.port";
    public static final String MAIL_SMTP_SSL_TRUST = "mail.smtp.ssl.trust";
    public static final String MAIL_SMTP_SSL_PROTOCOLS = "mail.smtp.ssl.protocols";
    public static final String TRUE_STRING = "true";
    public static final String BRILLIO_HOST = "smtp.office365.com";
    public static final String BRILLIO_PORT = "587";
    public static final String BOLT_USER = "<EMAIL>";
    public static final String BOLT_PASS = "brillio@2017";
    public static final String SONAR_METRICS = "&metrics=ncloc,classes,sqale_index,line_coverage,violations,critical_violations,minor_violations,info_violations,major_violations,blocker_violations,complexity,class_complexity,file_complexity,function_complexity,coverage,file_cycles,duplicated_blocks,violations_density,coverage,uncovered_lines";
    public static final String SONAR_METRICS_NEW1 = "major_violations,blocker_violations,complexity,class_complexity,file_complexity,function_complexity,coverage,duplicated_blocks,uncovered_lines,false_positive_issues,new_technical_debt,test_success_density,test_failures";
    public static final String SONAR_METRICS_NEW2="ncloc,classes,sqale_index,line_coverage,violations,critical_violations,minor_violations,info_violations,security_hotspots,security_review_rating,branch_coverage,tests,test_execution_time,test_errors";
    public static final String SONAR_METRICS_NEW3="cognitive_complexity,new_duplicated_blocks,new_duplicated_lines,new_duplicated_lines_density,duplicated_lines_density,reliability_rating,reliability_remediation_effort,vulnerabilities,security_rating,security_remediation_effort,new_code_smells,sqale_rating";
    public static final String SONARCOLLECTORSTARTDATE="2015-01-01";
    public static final String BUILD_URL_PATTERN = "/api/json";
    public static final String ALM_BACKLOG = "BackLog";
    public static final String ALM_CREATED = "created";
    public static final String FALSECONST = "false";
    public static final String ALM_RESOLUTIONDATE = "resolutiondate";
    public static final String ALM_TIMEORIGINALESTIMATE = "timeoriginalestimate";
    public static final String KEYWORD_ASSIGNEE = "assignee";
    public static final String KYWRD_STATUS = "status";
    public static final String KYWRD_ISSUETYPE = "issuetype";
    public static final String KYWRD_VALUE = "value";
    public static final String KYWRD_TIMESPENT = "aggregatetimespent";
    public static final String KYWRD_TIMEESTIMATE = "timeestimate";
    public static final String KYWRD_STORY = "Story";
    public static final String KYWRD_PRIORITY = "priority";
    public static final String KYWRD_UNAME = "uname";
    public static final String JIRA_SPRINT_FIELD = "customfield_10007";
    public static final String JIRA_SPRINT_FIELD_BRILLIO = "customfield_10001";
    public static final String JIRA_STATUSES = "statuses";
    public static final String updated = "updated";
    public static final String TIMETRACKING = "timetracking";
    public static final String COLLECTOR_STATUS_SUCCESS="SUCCESS";
    public static final String COLLECTOR_STATUS_FAILURE="FAIL";
    static Map<String, String[]> lastRunMap = new LinkedHashMap<>();
    public static final String SECRET_KEY="abccdazie13!!!!wzxwtaydbxb123!!@dbdq";
    public static final String SALT="zxwtaydbxb123!!@dbdq";
    public static String pAlmTypeConst="pAlmType";
    public static String lowerCaseActiveConst="active";
    public static String upperCaseActiveConst="ACTIVE";
    public static String lowerCaseMetricsConst="metrics";
    public static String mixedCaseMetricsConst="Metrics";
    public static String withDrawnConst="WITHDRAWN";
    public static String pNameConst ="pName";
    public static List<String> activeStates = Arrays.asList(upperCaseActiveConst, lowerCaseActiveConst, "Active");
    public static List<String> closedStates = Arrays.asList("CLOSED", "closed", "Closed");
    public static final String[] projectNameArr= {"pName","projectName","proName"};
    
    public static final String[] getAPIsAdmin = {  "/portfolioViewConfig", "/teamIndexConfig", "/getProjectUsers",
			"/jenkinsBuildFailurePatternFetchDataConfig","/buildJobList", "/fetchJobDetails",
			"/getChartConfigForProjectConfig","/HealthGetState", "gettingStackGradingData","/qualityConfig",
			"/stackByProject","/highLightFetchDataProj","/HealthGetState","/almconfigDetailsConfig","/goalsConfig",
			"/projectHealthGetDataConfig","/immediateSchedule","/configurationProjectConfig","/featureConfigForConfig",
			"/getGitlabBuildValueStream","/gettingStackGradingData","/kanbanWeeklyTrend"};

    public static final String[] getAPIsUser= {"/featureConfig","/getComponents","/taskRisk",
    		"/getUsersProjectDetails","/getLastRun","/getAlmType","/engScoreHome","/codeQualityHome","/issueBrakeUp",
    		"/getCurrentProjectDetails","/activeSprints","/configurationProject","/getRelease","/highLightReelFetchData",
    		"/almconfigDetails","/getChartConfigForProject","/getIterations","/getComponentWiseVelocityChart",
    		"/engIndexExcel","/engScorecardExcel","/getTowerProj","/quality","/sprintProgressHome","/burndown",
    		"/goals","/portfolioDetails","/teamIndexConfigLastRecord","/getTeamByProject","/buildDetailsHome",
			 "/collaborativemailsearch","/getBuildValueStream","/jenkinsBuildFailurePatternFetchData","/engScoreByProjectName",
			 "/coverageSummary","/defectsSummaryHome","/projectHealthGetData","/projectCoverage","/scmDetails",
			 "/groomingTable","/getComponontWiseSprintWiseStories","/storyAgeing","/getProjectDetails","/projectMembers",
			 "/getComponentWiseIssueHierarchy","/defectInsightData","/defectTrendAndClassification","/stackByProject",
			 "/getDefectPareto","/getProductionSlippage","/getDefectDensity","/getDefectBacklog","/retrospectiveDetailsByProject",
			 "/azureLogData","/getStoryProgress","/repoCoverageStatus","/buildDetails","/collaborativemaildelete",
			 "/engScoreCardByProject","/engScoreCardSubjetiveByProject","/storyAgeing2","/scmMergeRequestData","/kanbanCycleTimeMetrics",
			 "/kanbanCumulativeChart","/kanbanThroughputMetrics","/kanbanDefectTrend","/kanbanProductionSlippage","/kanbanDefectDenisty","/kanbanWeeks","/defectClassification","/doDashboard"};
	
    public static final String[] postUpddateDeleteApis= {"/almconfig","/jenkinsBuildFailure","/goalsCoverage",
			"/SaveJobDetails","/saveChartDetails","/featureConfig","/goals",
			"/projectHealthSaveData","/highLightSaveData","/portfolioViewConfig",
			"/configuration","/portfolioConfigSaveData","/DeleteStackGradingData",
			"/StackGradingSystem","/Stack","/teamIndexConfig","/updateMemberDetails",
			"/addNewMember", "/deleteTeamMember","/updateAssociatedUser"
			};
    
    public static final String[] postAPIWithQueryParamUser= {"/engIndexExcel"};
    public static final List<String> postAPIWithQueryParamUserList= Arrays.asList(postAPIWithQueryParamUser);
    public static final String[] postDeleteApiUser= {"/engScorecardFormFillEdit","/engScorecardFormFill",
    		"/collaborativemail","/saveAllActionTracker","/retrospectiveData","/retrospectiveDelete","/engScoreSave"};
    public static final String[] aPIbyUserNameArr= {"/updatePassword","/getProjects","/getUser"};
    public static final List<String> postAPIbyUserName= Arrays.asList(aPIbyUserNameArr);
    public static final List<String> postDeleteApiListUser=Arrays.asList(postDeleteApiUser);
    public static final List<String> apiList = Arrays.asList(postUpddateDeleteApis);
    public static final List<String> genUserGetApi = Arrays.asList(getAPIsAdmin);
    public static final List<String> genUserGetApiUser = Arrays.asList(getAPIsUser);
    
//    public static final String[] executiveAccess= {"/featureConfig","/configurationProject","/getUsersProjectDetails",
//    		"/projectHealthGetData","/getLastRun","/almconfigDetails","/getChartConfigForProject","/quality","/getRelease",
//    		"/goalsCopy","/jenkinsBuildFailurePatternFetchData","/getGitlabBuildValueStream","/getAlmType"
//    		};
//    public static final List<String> executiveAccessList=Arrays.asList(executiveAccess);
	
    public static final String[] adminApiAccess = { "/getUsers", "/deleteUsers", "/saveUser", "/getADDetails",
			"/Savedata", "/engDefaultData", "/engConfig", "/engConfigSave", "/licData", "/updateLicData","/buildJobList",
			"/retrieveMailConfiguration", "/saveMailConfiguration", "/getSSOConfigs", "/saveSSOConfiguration","/updateUser"};
    public static final String[] apiAccessToAll= {"/LoginUser","/tokenRefresh","/v2/api-docs","/swagger","/checkhealth","/getSSOConfigs","/getSSOConfig"
	};
    public static final List<String> apiAccessToAllList=Arrays.asList(apiAccessToAll);
    public static final String[] apiAccessToAllWithAuth= {"getServerResources","/getAllResources","/getCategoryInsights",
    		"/getOpenClosedInsights","/getSLA","/getInflowTrend","getAllIncidents","/engScoreByTowerName",
    		"/engScoreAll","/getUsersData","/engConfigByTower","/downloadEngIndexTemp","/doDashboard"
   	};
       public static final List<String> apiAccessToAllWithAuthList=Arrays.asList(apiAccessToAllWithAuth);
    public static final List<String> adminAcessAPI = Arrays.asList(adminApiAccess);
    
    
    public static long getPreviousDate() {
	Date date = new Date();
	// Or where ever you get it from
	Date daysAgo = new DateTime(date).minusDays(365).toDate();
	return daysAgo.getTime();

    }

    public static long timestamp(Object obj,String project) {
    	
	if (obj != null) {
		AnnotationConfigApplicationContext ctx = null;
		ctx = DataConfig.getContext();
		ALMConfigRepo almRepo=ctx.getBean(ALMConfigRepo.class);
		ALMConfiguration almConfig=almRepo.findByProjectName(project).get(0);
		String timezone="Asia/Kolkata";
		if(almConfig!=null) {
			timezone=almConfig.getTimeZone();
		}
	    String string = obj.toString();
	    if(!string.contains("Z")) {
	    string = string.split("\\.")[0];
	    }
	   
	    
	    DateTimeZone zoneUTC = DateTimeZone.forID(timezone);

	    DateTime dt = new DateTime(string, zoneUTC);
	   
	    return dt.getMillis();
	}
	return 0;
    }

    public static Map<String, String[]> getLastRun(String projectName, String type, long timeStamp, String result) {
	boolean flag = false;
	CollectorLastRun mainModel = null;
	AnnotationConfigApplicationContext ctx = null;
	ctx = DataConfig.getContext();
	MailSetupRepo mailSetupRepo = ctx.getBean(MailSetupRepo.class);
//	if(result.equalsIgnoreCase("fail")) {
//		CollaborativeMailModel mail = new CollaborativeMailModel();
//		//mail.setToAddress("<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>");
//		mail.setToAddress(mailSetupRepo.findAll().get(0).getNotification());
//		mail.setFileName("");
//		mail.setComment("Hi Team,<br /> <br /> BOLT "+type+ " collector failed for <b>"+ projectName + "</b>. <br /> <br /> <b> Thanks & Regards, <br /> Team BOLT </b>" );
//		mail.setSubject("BOLT collector failed");
//		EmailNotification email = new EmailNotification();
//		email.sendMail(mail);
//	}
	
	CollectorLastRunRepo repo = DataConfig.getContext().getBean(CollectorLastRunRepo.class);
	mainModel = repo.findByProjectName(projectName);
	String[] resultVal = new String[2];
	if ((mainModel == null)) {
	    mainModel = new CollectorLastRun();
	    mainModel.setProjectName(projectName);
	    mainModel.setTimeStamp(new Date().getTime());
	    resultVal[0] = result;
	    resultVal[1] = String.valueOf(timeStamp);
	    lastRunMap.put(type, resultVal);

	    mainModel.setLastRun(lastRunMap);
	    repo.save(mainModel);

	} else {
	    Map<String, String[]> tempMapName = new LinkedHashMap<>();
	    mainModel.setTimeStamp(new Date().getTime());
	    Map<String, String[]> mapName = mainModel.getLastRun();

	    Set<Entry<String, String[]>> srSet = mapName.entrySet();
	    for (Map.Entry<String, String[]> each : srSet) {
	
		if (each.getKey().equals(type)) {
		    flag = true;
		    resultVal[0] = result;
		    resultVal[1] = String.valueOf(timeStamp);
		    tempMapName.put(type, resultVal);
		}

	    }
	    if (!flag) {
		resultVal[0] = result;
		resultVal[1] = String.valueOf(timeStamp);
		tempMapName.put(type, resultVal);
	    }
	    mapName.putAll(tempMapName);
	    mainModel.setLastRun(mapName);
	    repo.save(mainModel);

	}
	return null;

    }

    private ConstantVariable() {
    }

}