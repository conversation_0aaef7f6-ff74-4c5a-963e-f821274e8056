package AppTwo;

import org.springframework.web.bind.annotation.*;
import AppOne.User;

@RestController
@RequestMapping("/order")
public class OrderService {

    private OrderRepository orderRepository = new OrderRepository();

    @PostMapping("/create")
    public Order createOrder(@RequestBody OrderDto orderDto) {
        Order order = OrderUtils.convertDtoToEntity(orderDto);
        order.setStatus("PENDING");
        orderRepository.save(order); // writes to Order table

        User user = orderDto.getUser(); // retrieves User DTO reference
        String userCode = AppOne.UserUtils.generateUserCode(user); // calls AppOne utility (cross-app)
        order.setUserCode(userCode); // userCode FLOWS_TO order.userCode
        return order;
    }
}