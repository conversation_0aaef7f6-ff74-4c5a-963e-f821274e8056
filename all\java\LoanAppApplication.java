package com.morganstanley.loanApp;

import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;

import javax.sql.DataSource;
import java.sql.SQLException;

@SpringBootApplication
@EnableConfigurationProperties
public class LoanAppApplication {

	@Autowired
	@Qualifier("dataSource")
	private DataSource dataSource; // primary

	@Autowired
	@Qualifier("storeProcDataSource")
	private DataSource secondDataSource;


	@PostConstruct
	public void printDsInfo() throws SQLException {
		System.out.println("Primary DataSource connection URL: " + dataSource.getConnection().getMetaData().getURL());
		System.out.println("Secondary DataSource connection URL: " + secondDataSource.getConnection().getMetaData().getURL());
	}
	public static void main(String[] args) {
		 SpringApplication.run(LoanAppApplication.class, args);

	}

}
