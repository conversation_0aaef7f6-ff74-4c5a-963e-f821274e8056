package com.bolt.dashboard.core.model;

import java.util.ArrayList;
import java.util.List;

import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;



public class VelocityList {
	 
	 
	 public ArrayList<Double> chartDataCommitted = new ArrayList<Double>();
	 public ArrayList<Double> chartDataAfter = new ArrayList<Double>();
	 public ArrayList<Double> chartDataCompleted =new ArrayList<Double>();
	 public ArrayList<Double> chartDataFinalCommited =  new ArrayList<Double>();
	 public ArrayList<Double> chartDataRemoved =  new ArrayList<Double>();
	 public ArrayList<Double>  chartDataEffort = new ArrayList<Double>();
	 public ArrayList<Double> chartDataDefects = new ArrayList<Double>();
	 public ArrayList<Double>  chartDataCapacity = new ArrayList<Double>();
	 public ArrayList<ArrayList<IssueList>>  chartIssuesComp = new ArrayList<ArrayList<IssueList>>();
	 public ArrayList<ArrayList<IssueList>>  chartIssuesComm = new ArrayList<ArrayList<IssueList>>();
	 public ArrayList<ArrayList<IssueList>>  chartIssuesCommAfter = new ArrayList<ArrayList<IssueList>>();
	 public ArrayList<ArrayList<IssueList>>  chartIssuesRefined = new ArrayList<ArrayList<IssueList>>();
	 public ArrayList<ArrayList<IssueList>>  chartIssuesRemoved = new ArrayList<ArrayList<IssueList>>();
	 public ArrayList<String> catagories = new ArrayList<String>();
	 public ArrayList<Double> finalCommitedStories =  new ArrayList<Double>(); 
	 public ArrayList<Double> finalCommitedDefetcs =  new ArrayList<Double>();
	 public ArrayList<Double> completedStories =  new ArrayList<Double>(); 
	 public ArrayList<Double> completedDefects = new ArrayList<Double>();
	 public ArrayList<Long> startDate=new ArrayList<Long>();
	 public ArrayList<Long> endDate=new ArrayList<Long>();
	 
	 
	 
	 @JsonIgnore
	 @JsonProperty(value = "workItemArr")
	 public ArrayList<String> workItemArr = new ArrayList<String>();
	 public String activeSprint;
	 
	 @JsonIgnore
	 @JsonProperty(value = "workItemArr")
	public ArrayList<String> getWorkItemArr() {
		return workItemArr;
	}
	public void setWorkItemArr(ArrayList<String> workItemArr) {
		this.workItemArr = workItemArr;
	}
	 
	 
	 
	 
}
