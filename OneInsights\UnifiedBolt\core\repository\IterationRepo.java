package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.IterationModel;

public interface IterationRepo extends CrudRepository<IterationModel, ObjectId> {

	IterationModel findBySNameAndPName(String sName, String pName);
	IterationModel findBySNameAndPNameAndPAlmType(String sName, String pName,String pAlmType);

	List<IterationModel> findByPName(String pName);
	IterationModel findBySIdAndPName(int getsId, String projectName);

}
