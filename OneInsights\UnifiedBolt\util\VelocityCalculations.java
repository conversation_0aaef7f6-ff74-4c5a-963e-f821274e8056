package com.bolt.dashboard.util;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collector;
import java.util.stream.Collectors;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.model.ALMConfiguration;
import com.bolt.dashboard.core.model.IssueList;
import com.bolt.dashboard.core.model.IterationOutModel;
import com.bolt.dashboard.core.model.MonogOutMetrics;
import com.bolt.dashboard.core.model.ScoreCardSprintData;
import com.bolt.dashboard.core.model.TransitionModel;

public class VelocityCalculations {

	private ArrayList<IterationOutModel> workingBacklog;
	
	private List<String> velocityFields;
	private List<String> closestates = null;
	private int tempSpRefined;
	private int tempSpRemoved;
	private int storiesCompleted;
	private int defetcsCompleted;
	private List<IssueList> issueList = null;
	private List<String> workItemArr2 = null;
	private ALMConfiguration almConfig = null;
	
	private List<ScoreCardSprintData> scoreCardSprintList = new ArrayList<>();
	private List<IssueList> refinedIssuList = null;
	private List<IssueList> removedIssuList = null;
	private long enDateSprint = 0;

	public List<ScoreCardSprintData> calcVelocity(List<IterationOutModel> iterations, ALMConfiguration temp) {
		almConfig = temp;

		workingBacklog = new ArrayList<>();
		ArrayList<IterationOutModel> workingSprints = new ArrayList<>();
		velocityFields = Arrays.asList(almConfig.getVelocityFields());
		closestates = Arrays.asList(almConfig.getCloseState());
		 List<String> taskNames = Arrays.asList(almConfig.getTaskName());
		ScoreCardSprintData scoreCardSprintPrev = null;

		for (IterationOutModel auth : iterations) {

			if (null == auth.getState() || (null != auth.getState())) {
				workingBacklog.add(auth);
			}
		}

		for (IterationOutModel auth : iterations) {
			if (null != auth.getState() && auth.getStDate() != 0 && !auth.getState().toLowerCase().contains("future")
					&& !auth.getState().toLowerCase().contains("backlog") && auth.getsId() != 0) {
				workingSprints.add(auth);
				workingSprints.sort(Comparator.comparing(IterationOutModel::getStDate));

			}
		}
		
		for (int i = workingSprints.size()- 30 >= 0 ? workingSprints.size()-30:0; i < workingSprints.size() ; i++) {
			double totalStoryPoints = 0;
			
			double totalCompletedStoryPoints = 0;
			double totalSPAfter = 0;
			double totalSpRefined = 0;
			double totalSPremoved = 0;
			double spillOverLastMonth=0;
			double currentSprintBugs = 0;
			double capacityPerSprint = 0;
			double actualEffort = 0;
			double tempSp = 0;
			tempSpRefined = 0;
			tempSpRemoved = 0;

			storiesCompleted = 0;
			defetcsCompleted = 0;

			workItemArr2 = new ArrayList<>();
			

			if (workingSprints.get(i).getMetrics() != null) {
				for (MonogOutMetrics m : workingSprints.get(i).getMetrics()) {
					if (taskNames.contains(m.getType())) {
						if (null != m.getEffort()) {
							actualEffort += m.getEffort();
						} else {
							actualEffort += 0;
						}

					}
				}
			}
			refinedIssuList = new ArrayList<>();
			removedIssuList = new ArrayList<>();
			currentSprintBugs = workingSprints.get(i).getTotClosedDefects();
			long endDatePrev= enDateSprint;
			
			if (workingSprints.get(i).getCompletedDate() == 0) {

				enDateSprint = workingSprints.get(i).getEndDate();
			} else {
				enDateSprint = workingSprints.get(i).getCompletedDate();
			}
	  
			
			totalStoryPoints = callSP("start", workingSprints.get(i));
			
			
			ScoreCardSprintData scoreCardSprint = new ScoreCardSprintData();
			scoreCardSprint.setCommitedSp(totalStoryPoints);
			scoreCardSprint.setSprintName(workingSprints.get(i).getsName());
			scoreCardSprint.setSprintId(workingSprints.get(i).getsId());
			scoreCardSprint.setStartDate(workingSprints.get(i).getStDate());

			scoreCardSprint.setEndDate(enDateSprint);
			scoreCardSprint.setIssuesCommited(issueList);
			
			totalSpRefined = tempSpRefined;
			totalSPremoved = tempSpRemoved;
			scoreCardSprint.setState(workingSprints.get(i).getState());

			totalSPAfter = callSP("end", workingSprints.get(i));
			scoreCardSprint.setCommitedAfterSp(totalSPAfter);
			scoreCardSprint.setIssuesCommitedAfter(issueList);
			totalSpRefined += tempSpRefined;
			totalSPremoved += tempSpRemoved;
 
			totalCompletedStoryPoints = calcClosedSP(workingSprints.get(i));
			scoreCardSprint.setCompletedSp(totalCompletedStoryPoints);
 			scoreCardSprint.setIssuesComplted(issueList);
			scoreCardSprint.setCompletedStories(storiesCompleted);
            
			
			if(workingSprints.get(i).getTeamSize() != null ) {
				capacityPerSprint = 10 * 6.5 * (int) workingSprints.get(i).getTeamSize();
			}else {
				capacityPerSprint = 0;
			}
			
			scoreCardSprint.setRefinedSp(totalSpRefined);
			scoreCardSprint.setRemovedSp(totalSPremoved);
			scoreCardSprint.setIssuesRefined(refinedIssuList);
			
			long effortEstimated=0;
			long effortSpent=0;
			for(IssueList iss:refinedIssuList) {
				effortEstimated+=iss.getOriginalEst();
				effortSpent+=iss.getEffortSpent();
			}
			scoreCardSprint.setOriginalEstimate(effortEstimated/3600);
			scoreCardSprint.setEffort(effortSpent/3600);
			scoreCardSprint.setCommittedStories(refinedIssuList.size());

			scoreCardSprint.setIssuesRemoved(removedIssuList);
		
		
			
			scoreCardSprint.setDefects(workingSprints.get(i).getTotClosedDefects());

			scoreCardSprint.setCapacity(capacityPerSprint);
			//scoreCardSprint.setEffort(Math.floor(actualEffort / 3600));
			scoreCardSprint.setCompletedDefects(defetcsCompleted);
			List<IssueList> refinedDefects = new ArrayList<>();
			refinedIssuList.forEach(issue -> {

				if (issue.getType().equals(almConfig.getDefectName())) {
					refinedDefects.add(issue);
				}
			});
			scoreCardSprint.setRefinedDefects(refinedDefects.size());
			scoreCardSprint.setDefects(currentSprintBugs);
			 if(i>0 && scoreCardSprint!=null && scoreCardSprintPrev!=null) {
				    
				 spillOverLastMonth= callSPSpillOver(scoreCardSprintPrev, scoreCardSprint);
				   
				   
				    scoreCardSprint.setSpillOverSp(spillOverLastMonth);
				    scoreCardSprint.setIssueSpillover(issueList);
			   }
			
			
			scoreCardSprintPrev=scoreCardSprint;
			scoreCardSprintList.add(scoreCardSprint);
		}

		return scoreCardSprintList;

	}

	private double callSPSpillOver(ScoreCardSprintData scoreCardSprintPrev, ScoreCardSprintData scoreCardSprint) {
		double tempSp = 0;
		List<IssueList> issuesPrev= scoreCardSprintPrev.getIssuesRefined();
		
		List<IssueList> issuesCurrent=scoreCardSprint.getIssuesRefined();
		
		List<String> wIds=new ArrayList<>();
		for(IssueList iss: issuesCurrent) {
			wIds.add(iss.getwId());
			
		}
		List<IssueList> tempList= new ArrayList<>();
		for(IssueList iss: issuesPrev) {
			if(wIds.contains(iss.getwId())) {
				tempList.add(iss);
			}
			
		}
		//issuesPrev = issuesPrev.stream().filter(p->equals(wIds.contains(p.getwId()))).collect(Collectors.toList());
		
		  issueList=tempList;
		  
			for(IssueList iss: issueList) {
				tempSp+=iss.getStoryPoints();
			}
		   
		  
		
		return tempSp;
	}

	public double callSP(String flag, IterationOutModel sp) {
		double tempSp = 0;
		tempSpRefined = 0;
		tempSpRemoved = 0;
		issueList = new ArrayList<>();

		long date;
		if (flag.equals("start")) {
			date = sp.getStDate();
		} else {
			if (sp.getState().toLowerCase().contains(ConstantVariable.lowerCaseActiveConst)) {
				date = new Date().getTime();
			} else {

				date = sp.getEndDate();

			}

		}

		for (IterationOutModel it : workingBacklog) {

			ArrayList<String> keys, dateMapValues;
			ArrayList<Long> dateMapKeys=new ArrayList<Long>();
			ArrayList<Long> storyPointMapKeys=new ArrayList<Long>();
			Map storyPointMap;
			Map<Long, String> dateMap;
			if (it.getMetrics() != null) {

				for (MonogOutMetrics ele : it.getMetrics()) {
					
					Collections.sort(dateMapKeys);
					if (ele.getAllocatedDate() != null && (velocityFields.indexOf(ele.getType()) > -1)
							&& ele.getStoryPoints() != null && (workItemArr2.indexOf(ele.getwId()) < 0)) {
						dateMap = ele.getAllocatedDate();
						dateMapKeys = new ArrayList<>(ele.getAllocatedDate().keySet());
						storyPointMapKeys = new ArrayList<>(ele.getStoryPoints().keySet());
						storyPointMap = ele.getStoryPoints();
						Collections.sort(storyPointMapKeys);
						for (int i = 0; i < dateMapKeys.size(); i++) {
							List<String> dateValues = Arrays.asList(dateMap.get(dateMapKeys.get(i)).split(","));
							if (i == dateMapKeys.size() - 1 && dateMapKeys.get(i) < date && (dateValues.contains(String.valueOf(sp.getsId())))) {

								tempSp = tempSp + storyLoop(storyPointMapKeys, sp, storyPointMap, issueList, ele, date,
										false, dateMapKeys.get(i));
								storyLoopRefined(true, storyPointMapKeys, sp, storyPointMap, ele, date,
										dateMapKeys.get(i));
							} else if ((dateMapKeys.get(i) < date) && (dateValues.contains(String.valueOf(sp.getsId())))
									&& (dateMapKeys.get(i + 1) > date)) {
								tempSp = tempSp + storyLoop(storyPointMapKeys, sp, storyPointMap, issueList, ele, date,
										false, dateMapKeys.get(i));
								storyLoopRefined(false, storyPointMapKeys, sp, storyPointMap, ele, date,
										dateMapKeys.get(i));
							}

						}
					}

				}
			}

		}
		return tempSp;
	}

	public double calcClosedSP(IterationOutModel sp) {
		double tempSp = 0;
		issueList = new ArrayList<>();

		long date;

		if (sp.getState().toLowerCase().contains("active")) {
			date = new Date().getTime();
		} else {
			if (sp.getCompletedDate() != 0) {
				date = sp.getCompletedDate();
			} else {
				date = sp.getEndDate();
			}
		}

		ArrayList<String> wIdArr = new ArrayList<>();
		for (IterationOutModel it : workingBacklog) {

			ArrayList<String> keys;
			ArrayList<Long> dateMapKeys, storyPointMapKeys;
			Map storyPointMap;
			Map<Long, String> dateMap;

			if (it.getMetrics() != null) {

				for (MonogOutMetrics ele : it.getMetrics()) {
					
					if ((ele.getsId() != 0) && ele.getAllocatedDate() != null && (velocityFields.indexOf(ele.getType()) > -1)
							&& ele.getStoryPoints() != null ) {
					
							dateMap = ele.getAllocatedDate();
							dateMapKeys = new ArrayList<>(ele.getAllocatedDate().keySet());
							Collections.sort(dateMapKeys);

							storyPointMapKeys = new ArrayList<>(ele.getStoryPoints().keySet());
							storyPointMap = ele.getStoryPoints();

							Collections.sort(storyPointMapKeys);

							for (int i = 0; i < dateMapKeys.size(); i++) {
								List<String> dateValues = Arrays.asList(dateMap.get(dateMapKeys.get(i)).split(","));
								if (i == dateMapKeys.size() - 1 && dateMapKeys.get(i) < date
										&& dateValues.contains(String.valueOf(sp.getsId()))) {
									TransitionModel trans = filterTrans(ele.getTransitions(), date);
									if (closestates.indexOf(trans.getCrState()) > -1 && trans.getMdfDate() < date
											&& trans.getMdfDate() > sp.getStDate()
											&& trans.getMdfDate() > dateMapKeys.get(i)
											&& wIdArr.indexOf(ele.getwId()) <= -1) {
										wIdArr.add(ele.getwId());
										long alloc = 0;
										tempSp = tempSp + storyLoop(storyPointMapKeys, sp, storyPointMap, issueList,
												ele, date, true, alloc);

									}

								} else if (dateMapKeys.get(i) < date && dateValues.indexOf(sp.getsId() + "") > -1
										&& dateMapKeys.get(i + 1) > date) {
									String wid = ele.getwId();
									TransitionModel trans = filterTrans(ele.getTransitions(), date);
									if (closestates.indexOf(trans.getCrState()) > -1 && trans.getMdfDate() < date
											&& trans.getMdfDate() > sp.getStDate()
											&& trans.getMdfDate() > dateMapKeys.get(i)
											&& wIdArr.indexOf(ele.getwId()) <= -1) {

										wIdArr.add(ele.getwId());
										long alloc = 0;
										tempSp = tempSp + storyLoop(storyPointMapKeys, sp, storyPointMap, issueList,
												ele, date, true, alloc);

									}

								}

							}
						}
					}
				}
		}
		return tempSp;
	}

	double storyLoop(List<Long> keys, IterationOutModel it, Map story, List<IssueList> issueList, MonogOutMetrics m,
			long date, boolean colsedflag, Long allocDate) {
		double temp = 0;
		for (int i = 0; i < keys.size(); i++) {
			if (keys.get(i) <= date && i == keys.size() - 1) {
				temp = temp + (double) story.get(keys.get(i));
				if (m.getType() != null && m.getType().equalsIgnoreCase(almConfig.getStoryName()) && colsedflag) {
					storiesCompleted++;
				}
				if (m.getType() != null && m.getType().equalsIgnoreCase(almConfig.getDefectName()) && colsedflag) {
					defetcsCompleted++;
				}
				IssueList iss = new IssueList();
				iss.setStoryPoints((double) story.get(keys.get(i)));
				iss.setwId(m.getwId());
				iss.setAssignee(m.getAssgnTo());
				iss.setState(m.getState());
				iss.setType(m.getType());
				iss.setAllocatedDate(m.getAllocatedDate());
				iss.setTransitions(m.getTransitions());
				iss.setCommitedAftertDate(allocDate);
				iss.setCreatedDate(m.getCreateDate());
			
				iss.setOriginalEst(m.getOrgEst()!=null?m.getOrgEst():0);
				iss.setEffortSpent(m.getEffort()!=null?m.getEffort():0);
				issueList.add(iss);
				workItemArr2.add(m.getwId());
			} else if (keys.get(i) <= date && keys.get(i + 1) > date) {
				temp = temp + (double) story.get(keys.get(i));
				if (m.getType() != null && m.getType().equalsIgnoreCase(almConfig.getStoryName()) && colsedflag) {
					storiesCompleted++;
				}
				if (m.getType() != null && m.getType().equalsIgnoreCase(almConfig.getDefectName()) && colsedflag) {
					defetcsCompleted++;
				}
				IssueList iss = new IssueList();
				iss.setStoryPoints((double) story.get(keys.get(i)));
				iss.setwId(m.getwId());
				iss.setAssignee(m.getAssgnTo());
				iss.setState(m.getState());
				iss.setType(m.getType());
				iss.setAllocatedDate(m.getAllocatedDate());
				iss.setTransitions(m.getTransitions());
				iss.setCommitedAftertDate(allocDate);
				iss.setCreatedDate(m.getCreateDate());
				iss.setOriginalEst(m.getOrgEst()!=null?m.getOrgEst():0);
				iss.setEffortSpent(m.getEffort()!=null?m.getEffort():0);
				issueList.add(iss);
				workItemArr2.add(m.getwId());
			}
		}
		return temp;
	}

	TransitionModel filterTrans(List<TransitionModel> tr, long date) {
		List<TransitionModel> newTr = new ArrayList<>();
		if (null != tr) {
			for (int i = 0; i < tr.size(); i++) {
				if (tr.get(i).getMdfDate() < date) {
					newTr.add(tr.get(i));
				}
			}
		}

		if (!newTr.isEmpty()) {
			return newTr.get(newTr.size() - 1);
		} else {
			return new TransitionModel();
		}
	}

	void storyLoopRefined(boolean isRefined, List<Long> keys, IterationOutModel it, Map story, MonogOutMetrics m, long date, Long allocateDate) {
		long endDate = it.getEndDate();
		List<TransitionModel> tr = m.getTransitions();
		if (tr != null) {
			tr.sort(Comparator.comparing(TransitionModel::getMdfDate));
		}
		boolean checkRemoved = false;
			if (it.getState().toLowerCase().contains(ConstantVariable.lowerCaseActiveConst)) {
				endDate = new Date().getTime();
			} else {
				if (it.getCompletedDate() != 0) {
					endDate = it.getCompletedDate();
				} else {
					endDate = it.getEndDate();
				}
			}
		List<String> checkRemoveResult = null;
		
		
		if(!isRefined) {
			checkRemoveResult = checkRemoved(it, m, allocateDate, endDate);
		}
		
		if(((checkRemoveResult !=null && checkRemoveResult.get(0).equals(ConstantVariable.FALSECONST)) || checkRemoveResult ==null) && closestates.indexOf("Withdrawn") > -1) {
				checkRemoveResult = checkWithdrawn( m, endDate);
			
		}
		
			
		if(checkRemoveResult !=null) {
			checkRemoved = !checkRemoveResult.get(0).equals(ConstantVariable.FALSECONST);
		}
		
		

		if (checkRemoved) {
			endDate = Long.parseLong(checkRemoveResult.get(1));
		}
		IssueList issue = new IssueList();

		issue.setwId(m.getwId());
		issue.setAssignee(m.getAssgnTo());
		issue.setState(m.getState());
		issue.setType(m.getType());
		issue.setAllocatedDate(m.getAllocatedDate());
		issue.setTransitions(m.getTransitions());
		issue.setCreatedDate(m.getCreateDate());
		issue.setOriginalEst(m.getOrgEst()!=null?m.getOrgEst():0);
		issue.setEffortSpent(m.getEffort()!=null?m.getEffort():0);
		

		for (int i = 0; i < keys.size(); i++) {
			issue.setStoryPoints((double) story.get(keys.get(i)));
			if (keys.get(i) <= endDate && i == keys.size() - 1) {

				if (checkRemoved) {
					removedIssuList.add(issue);
					tempSpRemoved += (double) story.get(keys.get(i));
					workItemArr2.add(m.getwId());
					break;
				} else {
					refinedIssuList.add(issue);
					workItemArr2.add(m.getwId());
					tempSpRefined+=(double) story.get(keys.get(i));
					break;
				}
			} else if (keys.get(i) < endDate && !checkRemoved && keys.get(i + 1) > endDate) {
				refinedIssuList.add(issue);
				workItemArr2.add(m.getwId());
				tempSpRefined+=(double) story.get(keys.get(i));
				break;
			} else if (checkRemoved && keys.get(i) < endDate && keys.get(i + 1) > endDate) {
				removedIssuList.add(issue);
				tempSpRemoved += (double) story.get(keys.get(i));
				workItemArr2.add(m.getwId());
				break;
			}
		}

	}

	private List<String> checkRemoved(IterationOutModel it, MonogOutMetrics m, Long allocateDate, long endDate) {
		
		Map<Long, String> dateMap = m.getAllocatedDate();
		ArrayList<Long> dateMapKeys = new ArrayList<>(m.getAllocatedDate().keySet());
		List<String> result = new ArrayList<>();
		result.add(ConstantVariable.FALSECONST);

		Collections.sort(dateMapKeys);
		int index = dateMapKeys.indexOf(allocateDate);

		for (int i = index + 1; i < dateMapKeys.size(); i++) {
			// String Sprints[]=dateMap.get(dateMapKeys.get(i))
			List<String> dateValues = Arrays.asList(dateMap.get(dateMapKeys.get(i)).split(","));
			if (dateMapKeys.get(i) < endDate && !(dateValues.contains(String.valueOf(it.getsId())))) {
				result.add(0, "true");
				result.add(1, String.valueOf(dateMapKeys.get(i)));
				return result;
			}

		}
		return result;
	}
	
	private List<String> checkWithdrawn(MonogOutMetrics m, long endDate){
		
		
		List<String> result = new ArrayList<>();
		List<TransitionModel> transitions= m.getTransitions();
		if(transitions !=null) {
			transitions.sort(Comparator.comparing(TransitionModel::getMdfDate));
			transitions = transitions.stream().filter(ele->ele.getMdfDate() <=endDate).collect(Collectors.toList());
			int index=-1;
			for(int i=0;i<transitions.size();i++) {
				if(transitions.get(i).getCrState().equalsIgnoreCase("Withdrawn")) {
					index=i;
				}
			}
			
			if(!transitions.isEmpty() && index == transitions.size()-1) {
				result.add(0, "true");
				result.add(1, String.valueOf(transitions.get(index).getMdfDate()));
			}else {
				result.add("false");
			}
		}else {
			result.add("false");
		}
		
		return result;
	}

}
