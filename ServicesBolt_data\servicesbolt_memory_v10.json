{"class_registry": {"Application": {"fqcn": "com.bolt.dashboard.Application", "package": "com.bolt.dashboard", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\Application.java", "imports": ["org.quartz.SchedulerException", "org.springframework.boot.autoconfigure.SpringBootApplication", "org.springframework.boot.builder.SpringApplicationBuilder", "org.springframework.boot.context.web.SpringBootServletInitializer", "org.springframework.cache.annotation.EnableCaching", "org.springframework.context.annotation.Bean", "org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder", "com.bolt.dashboard.config.RestApiConfig", "com.bolt.dashboard.config.WebMVCConfig", "com.bolt.dashboard.core.config.DataConfig"], "endpoints": [], "db_entities": [], "source_code": "/**\n * \n */\npackage com.bolt.dashboard;\n\nimport org.quartz.SchedulerException;\nimport org.springframework.boot.autoconfigure.SpringBootApplication;\nimport org.springframework.boot.builder.SpringApplicationBuilder;\nimport org.springframework.boot.context.web.SpringBootServletInitializer;\nimport org.springframework.cache.annotation.EnableCaching;\nimport org.springframework.context.annotation.Bean;\nimport org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;\n\nimport com.bolt.dashboard.config.RestApiConfig;\nimport com.bolt.dashboard.config.WebMVCConfig;\nimport com.bolt.dashboard.core.config.DataConfig;\n\n@SpringBootApplication\n@EnableCaching\npublic class Application extends SpringBootServletInitializer {\n    @Override\n    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {\n        return application.sources(Application.class, RestApiConfig.class, WebMVCConfig.class, DataConfig.class);\n    }\n    @Bean\n\tBCryptPasswordEncoder passwordEncoder(){\n\t\treturn new BCryptPasswordEncoder();\n\t}\n    public static void main(String[] args) throws SchedulerException {\n\n        new Application().configure(new SpringApplicationBuilder(Application.class)).run(args);\n        new TriggerCollector().getDataFromTools(true);\n    }\n}\n"}, "TriggerCollector": {"fqcn": "com.bolt.dashboard.TriggerCollector", "package": "com.bolt.dashboard", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\TriggerCollector.java", "imports": ["java.util.List", "org.apache.logging.log4j.LogManager", "org.apache.logging.log4j.Logger", "org.quartz.CronScheduleBuilder", "org.quartz.JobBuilder", "org.quartz.JobDetail", "org.quartz.JobKey", "org.quartz.Scheduler", "org.quartz.SchedulerException", "org.quartz.Trigger", "org.quartz.TriggerBuilder", "org.quartz.impl.StdSchedulerFactory", "org.springframework.context.annotation.AnnotationConfigApplicationContext", "org.springframework.stereotype.Component", "com.bolt.dashboard.core.ProjectCollector", "com.bolt.dashboard.core.config.DataConfig", "com.bolt.dashboard.core.model.PortfolioConfig", "com.bolt.dashboard.core.repository.PortfolioConfigRepo", "com.bolt.dashboard.jira.ALMClientImplementation"], "endpoints": [], "db_entities": [], "source_code": "package com.bolt.dashboard;\n\nimport java.util.List;\n\nimport org.apache.logging.log4j.LogManager;\nimport org.apache.logging.log4j.Logger;\nimport org.quartz.CronScheduleBuilder;\nimport org.quartz.JobBuilder;\nimport org.quartz.JobDetail;\nimport org.quartz.JobKey;\nimport org.quartz.Scheduler;\nimport org.quartz.SchedulerException;\nimport org.quartz.Trigger;\nimport org.quartz.TriggerBuilder;\nimport org.quartz.impl.StdSchedulerFactory;\nimport org.springframework.context.annotation.AnnotationConfigApplicationContext;\nimport org.springframework.stereotype.Component;\n\nimport com.bolt.dashboard.core.ProjectCollector;\nimport com.bolt.dashboard.core.config.DataConfig;\nimport com.bolt.dashboard.core.model.PortfolioConfig;\nimport com.bolt.dashboard.core.repository.PortfolioConfigRepo;\nimport com.bolt.dashboard.jira.ALMClientImplementation;\n\n@Component\npublic class TriggerCollector {\n\tprivate static final Logger LOGGER = LogManager.getLogger(ALMClientImplementation.class);\n\tAnnotationConfigApplicationContext ctx=null;\n\tpublic void getDataFromTools(Boolean flag) throws SchedulerException {\n\t\tctx = DataConfig.getContext();\n\t\tPortfolioConfigRepo portfolio = ctx.getBean(PortfolioConfigRepo.class);\n\t\tList<PortfolioConfig> data = portfolio.findAll();\n\t\tScheduler scheduler = new StdSchedulerFactory().getScheduler();\n\t\tfor (PortfolioConfig config : data) {\n\t\t\tif (config.getProjectName() != null && config.getCronExpression() != null && config.getSchedulerEnabled()!=null && config.getSchedulerEnabled()) {\n\t\t\t\tString jobkey = config.getProjectName();\n\t\t\t\tJobKey job = new JobKey(jobkey, \"BOLT_SCHEDULER\");\n\t\t\t\t\n\t\t\t\t// Use this Part to delete and rerun, If Already scheduler is Running\n\t\t\t\tif (scheduler.checkExists(job)){\n\t\t\t\t    scheduler.deleteJob(job);\n\t\t\t\t    LOGGER.info(\"Deleted the job...!\");\n\t\t\t\t}else {\n\t\t\t\t\tLOGGER.info(\"else the job...!\");\n\t\t\t\t}\n\t\t\t\tJobDetail jobA = JobBuilder.newJob(ProjectCollector.class).withIdentity(job).build();\n\n\t\t\t\tjobA.getJobDataMap().put(\"projectName\", config.getProjectName());\n\n\t\t\t\tTrigger trigger = TriggerBuilder.newTrigger().withIdentity(jobkey, \"BOLT_SCHEDULER\")\n\t\t\t\t\t\t.withSchedule(CronScheduleBuilder.cronSchedule(config.getCronExpression())).build();\n\n\t\t\t\tscheduler.scheduleJob(jobA, trigger);\n\n\t\t\t}\n\n\t\t}\n\n\t\tscheduler.start();\n\t}\n}\n"}, "ALMConfigController": {"fqcn": "com.bolt.dashboard.api.ALMConfigController", "package": "com.bolt.dashboard.api", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\api\\ALMConfigController.java", "imports": ["org.apache.logging.log4j.LogManager", "org.apache.logging.log4j.Logger", "org.springframework.beans.factory.annotation.Autowired", "org.springframework.http.ResponseEntity", "org.springframework.web.bind.annotation.RequestBody", "org.springframework.web.bind.annotation.RequestMapping", "org.springframework.web.bind.annotation.RestController", "com.bolt.dashboard.core.model.ALMConfiguration", "com.bolt.dashboard.request.ALMConfigReq", "com.bolt.dashboard.response.DataResponse", "com.bolt.dashboard.service.ALMConfigService"], "endpoints": [], "db_entities": [], "source_code": "/**\n * \n */\npackage com.bolt.dashboard.api;\n\nimport static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;\nimport static org.springframework.web.bind.annotation.RequestMethod.GET;\nimport static org.springframework.web.bind.annotation.RequestMethod.POST;\n\nimport org.apache.logging.log4j.LogManager;\n import org.apache.logging.log4j.Logger;\nimport org.springframework.beans.factory.annotation.Autowired;\nimport org.springframework.http.ResponseEntity;\nimport org.springframework.web.bind.annotation.RequestBody;\nimport org.springframework.web.bind.annotation.RequestMapping;\nimport org.springframework.web.bind.annotation.RestController;\n\nimport com.bolt.dashboard.core.model.ALMConfiguration;\nimport com.bolt.dashboard.request.ALMConfigReq;\nimport com.bolt.dashboard.response.DataResponse;\nimport com.bolt.dashboard.service.ALMConfigService;\n\n\n@RestController\npublic class ALMConfigController {\n\tprivate static final Logger LOG = LogManager.getLogger(ALMConfigController.class);\n\t@Autowired\n\tprivate ALMConfigService almConfigService;\n\n\t@Autowired\n\tpublic ALMConfigController(ALMConfigService almConfigService) {\n\t\tthis.almConfigService = almConfigService;\n\t}\n\t\t\n\n\t\t@RequestMapping(value = \"/almconfig\", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)\n\t\tpublic ResponseEntity<ALMConfiguration> saveALMConfig(@RequestBody ALMConfigReq req) {\n\t\t\t//ALMConfigReq almConfigReq = req;\n\n\t\t\tLOG.info(\"Inside ALMConfigController and org  :  \" + req.getProjectName());\n\t\t\treturn ResponseEntity.status(201).body(almConfigService.saveALMConfig(req.toDetailsAddSetting(req)));\n\t}\n\n\t@RequestMapping(value = \"/almconfigDetails\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic DataResponse<ALMConfiguration> retrieveList(String projectName) {\n\t\treturn almConfigService.retrieveALMConfig(projectName);\n\n\t}\n\t\n\t\n\t@RequestMapping(value = \"/almconfigDetailsConfig\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic DataResponse<ALMConfiguration> retrieveAlmConfig(String projectName) {\n\t\treturn almConfigService.retrieveALMConfig(projectName);\n\n\t}\n\n}\n"}, "AlmController": {"fqcn": "com.bolt.dashboard.api.AlmController", "package": "com.bolt.dashboard.api", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\api\\AlmController.java", "imports": ["java.util.Iterator", "java.util.List", "java.util.Map", "java.util.Set", "org.springframework.beans.factory.annotation.Autowired", "org.springframework.web.bind.annotation.RequestMapping", "org.springframework.web.bind.annotation.RequestMethod", "org.springframework.web.bind.annotation.RequestParam", "org.springframework.web.bind.annotation.RestController", "com.bolt.dashboard.core.config.DataConfig", "com.bolt.dashboard.core.model.ChangeHistoryModel", "com.bolt.dashboard.core.model.ComponentBurnDown", "com.bolt.dashboard.core.model.ComponentGroomingTable", "com.bolt.dashboard.core.model.ComponentIssueBreakup", "com.bolt.dashboard.core.model.ComponentSprintWiseStories", "com.bolt.dashboard.core.model.ComponentStoryAgeing", "com.bolt.dashboard.core.model.ComponentStoryProgress", "com.bolt.dashboard.core.model.ComponentTaskRisk", "com.bolt.dashboard.core.model.ComponentVelocityList", "com.bolt.dashboard.core.model.ConfigurationSetting", "com.bolt.dashboard.core.model.ConfigurationToolInfoMetric", "com.bolt.dashboard.core.model.DefectInsightData", "com.bolt.dashboard.core.model.EffortHistoryModel", "com.bolt.dashboard.core.model.IterationModel", "com.bolt.dashboard.core.model.IterationOutModel", "com.bolt.dashboard.core.model.MetricAgeData", "com.bolt.dashboard.core.model.MetricsModel", "com.bolt.dashboard.core.model.MonogOutMetrics", "com.bolt.dashboard.core.model.ProjectModel", "com.bolt.dashboard.core.model.ReleaseDetails", "com.bolt.dashboard.core.model.TransitionModel", "com.bolt.dashboard.core.repository.ConfigurationSettingRep", "com.bolt.dashboard.jira.ChartCalculations", "com.bolt.dashboard.service.AlmService", "com.bolt.dashboard.util.BurnDownDataSprint", "com.bolt.dashboard.util.DefectBacklog", "com.bolt.dashboard.util.DefectDensity", "com.bolt.dashboard.util.DefectParetoModel", "com.bolt.dashboard.util.DefectProductionSlippage", "com.bolt.dashboard.util.StoryProgressSprintwise", "com.bolt.dashboard.util.TaskRiskSprint"], "endpoints": [], "db_entities": [], "source_code": "package com.bolt.dashboard.api;\n\nimport static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;\nimport static org.springframework.web.bind.annotation.RequestMethod.GET;\n\nimport java.util.Iterator;\nimport java.util.List;\nimport java.util.Map;\nimport java.util.Set;\n\nimport org.springframework.beans.factory.annotation.Autowired;\nimport org.springframework.web.bind.annotation.RequestMapping;\nimport org.springframework.web.bind.annotation.RequestMethod;\nimport org.springframework.web.bind.annotation.RequestParam;\nimport org.springframework.web.bind.annotation.RestController;\n\nimport com.bolt.dashboard.core.config.DataConfig;\nimport com.bolt.dashboard.core.model.ChangeHistoryModel;\nimport com.bolt.dashboard.core.model.ComponentBurnDown;\nimport com.bolt.dashboard.core.model.ComponentGroomingTable;\nimport com.bolt.dashboard.core.model.ComponentIssueBreakup;\nimport com.bolt.dashboard.core.model.ComponentSprintWiseStories;\nimport com.bolt.dashboard.core.model.ComponentStoryAgeing;\nimport com.bolt.dashboard.core.model.ComponentStoryProgress;\nimport com.bolt.dashboard.core.model.ComponentTaskRisk;\nimport com.bolt.dashboard.core.model.ComponentVelocityList;\nimport com.bolt.dashboard.core.model.ConfigurationSetting;\nimport com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;\nimport com.bolt.dashboard.core.model.DefectInsightData;\nimport com.bolt.dashboard.core.model.EffortHistoryModel;\nimport com.bolt.dashboard.core.model.IterationModel;\nimport com.bolt.dashboard.core.model.IterationOutModel;\nimport com.bolt.dashboard.core.model.MetricAgeData;\nimport com.bolt.dashboard.core.model.MetricsModel;\nimport com.bolt.dashboard.core.model.MonogOutMetrics;\nimport com.bolt.dashboard.core.model.ProjectModel;\nimport com.bolt.dashboard.core.model.ReleaseDetails;\nimport com.bolt.dashboard.core.model.TransitionModel;\nimport com.bolt.dashboard.core.repository.ConfigurationSettingRep;\nimport com.bolt.dashboard.jira.ChartCalculations;\nimport com.bolt.dashboard.service.AlmService;\nimport com.bolt.dashboard.util.BurnDownDataSprint;\nimport com.bolt.dashboard.util.DefectBacklog;\nimport com.bolt.dashboard.util.DefectDensity;\nimport com.bolt.dashboard.util.DefectParetoModel;\nimport com.bolt.dashboard.util.DefectProductionSlippage;\nimport com.bolt.dashboard.util.StoryProgressSprintwise;\nimport com.bolt.dashboard.util.TaskRiskSprint;\n\n@RestController\npublic class AlmController {\n\t\n\tprivate AlmService service;\n\n\t@Autowired\n\tpublic AlmController(AlmService service) {\n\t\tthis.service = service;\n\t}\n\n\t/*\n\t * public static void main(String[] args) { ApplicationContext ctx =\n\t * DataConfig.getContext(); service = new\n\t * ALMServiceImplementation(ctx.getBean(TransitionRepo.class),\n\t * ctx.getBean(ProjectIterationRepo.class),\n\t * ctx.getBean(EffortHistoryRepo.class),\n\t * ctx.getBean(ChangeHisortyRepo.class), ctx.getBean(ProjectRepo.class),\n\t * ctx.getBean(ReleaseRepo.class)); new\n\t * AlmController(service).getSlaData(\"RDC\", \"JIRA\", 1524156068000L); }\n\t */\n\t\n\t@RequestMapping(value = \"/storyAgeing\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic  List<ComponentStoryAgeing>  storyAgeing(@RequestParam(\"pName\") String projName,@RequestParam(\"almType\") String almType){\n\t\t \n\t\treturn this.service.getStoryAgeingData(projName,almType);\n\t}\n\t\n\t@RequestMapping(value = \"/groomingTable\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic List<ComponentGroomingTable> groomingTable(@RequestParam(\"pName\") String projName,@RequestParam(\"almType\") String almType){\n\t\t \n\t\treturn this.service.getGroomingTable(projName,almType);\n\t}\n\t\n\t@RequestMapping(value = \"/delDuplicate\", method =RequestMethod.GET)\n\tpublic String delDuplicate(@RequestParam(\"pName\") String projName) {\n\t\treturn service.delDuplicate(projName);\n\t}\n\t\n\t@RequestMapping(value = \"/sprintProgressHome\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic List<Map<String,List<String>>> getSprintProgressHome(@RequestParam(\"pName\") String projName,@RequestParam(\"almType\") String almType){\n\t\treturn this.service.getSprintProgressHome(projName,almType);\n\t}\n\t@RequestMapping(value = \"/defectInsightData\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic Map<String, List<DefectInsightData>>  defectInsightData(@RequestParam(\"pName\") String projName,@RequestParam(\"almType\") String almType,\n\t\t\t@RequestParam(\"componentFlag\") boolean componentFlag){\n\t\t \n\t\treturn this.service.getDefectInsightData(projName,almType,componentFlag);\n\t}\n\t\n\t@RequestMapping(value = \"/defectTrendAndClassification\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic Map  defectTrendAndClassification(@RequestParam(\"pName\") String projName,@RequestParam(\"almType\") String almType,@RequestParam(\"componentBasedFlag\") boolean componentBased){\n\t\t \n\t\treturn this.service.getDefectTrendAndClassification(projName,almType,componentBased);\n\t}\n\t\n\t@RequestMapping(value = \"/defectClassification\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic Map  defectClassification(@RequestParam(\"pName\") String projName,@RequestParam(\"almType\") String almType,@RequestParam(\"componentBasedFlag\") boolean componentBased){\n\t\t \n\t\treturn this.service.getDefectClassification(projName,almType);\n\t}\n\t\n\t@RequestMapping(value = \"/issueBrakeUp\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic List<ComponentIssueBreakup> getIssueBrakeUp(@RequestParam(\"pName\") String projName,@RequestParam(\"almType\") String almType){\n\t\t\n\t\treturn this.service.getIssueBrakeUp(projName,almType);\n\t}\n\t\n\t@RequestMapping(value = \"/getStoryProgress\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic List<ComponentStoryProgress> getStoryProgress(@RequestParam(\"pName\") String projName,@RequestParam(\"almType\") String almType){\n\t\t\n\t\treturn service.getStoryProgress(projName,almType);\n\t}\n\t\n\t@RequestMapping(value = \"/defectsSummaryHome\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic List<Map<String,String>> getDefectsSummaryHome(@RequestParam(\"pName\") String projName,@RequestParam(\"almType\") String almType){\n\t\t \n\t\n\t\t\n\t\treturn this.service.getDefectsSummaryHome(projName,almType);\n\t}\n\t@RequestMapping(value = \"/taskRisk\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic  List<ComponentTaskRisk> getTaskRiskStoryPoint(@RequestParam(\"pName\") String projName,@RequestParam(\"almType\") String almType,@RequestParam(\"storyPointBasedFlag\") boolean storyPointBased){\n\t\t \n\t\n\t\t\n\t\treturn this.service.getTaskRisk(projName,almType,storyPointBased);\n\t}\n\t@RequestMapping(value = \"/burndown\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic List<ComponentBurnDown>  burndownCalculation(@RequestParam(\"pName\") String projName,@RequestParam(\"almType\") String almType,@RequestParam(\"storyPointBasedFlag\") boolean storyPointBased){\n\t\t\n\t\treturn this.service.burndownCalculation(projName,almType,storyPointBased);\n\t}\n\t\n\t@RequestMapping(value = \"/getProductionSlippage\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic Map<String,List<DefectProductionSlippage>> getProductionSlippage(@RequestParam(\"pName\") String projName,@RequestParam(\"almType\") String almType, @RequestParam(\"componentBasedFlag\") boolean componentBased){\t\n\t\treturn this.service.getProductionSlippage(projName,almType,componentBased);\n\t}\n\t@RequestMapping(value = \"/getDefectDensity\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic Map<String,List<DefectDensity>> getDefectDensity(@RequestParam(\"pName\") String projName,@RequestParam(\"almType\") String almType, @RequestParam(\"componentBasedFlag\") boolean componentBased){\t\n\t\treturn this.service.getDefectDensity(projName,almType,componentBased);\n\t}\n\t@RequestMapping(value = \"/getDefectBacklog\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic Map<String,DefectBacklog> getDefectBacklog(@RequestParam(\"pName\") String projName,@RequestParam(\"almType\") String almType, @RequestParam(\"componentBasedFlag\") boolean componentBased){\t\n\t\treturn this.service.getDefectBacklog(projName,almType,componentBased);\n\t}\n\t\n\t@RequestMapping(value = \"/getDefectPareto\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic Map<String,List<DefectParetoModel>> getDefectPareto(@RequestParam(\"pName\") String projName,@RequestParam(\"almType\") String almType, @RequestParam(\"componentBasedFlag\") boolean componentBased){\t\n\t\treturn this.service.defectParetoCalculation(projName,almType,componentBased);\n\t}\n\t\n\t\n\t\n\t@RequestMapping(value = \"/activeSprints\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic List<String> getActiveSprints(@RequestParam(\"pName\") String projName,@RequestParam(\"almType\") String almType){\n\t\treturn this.service.getActiveSprints(projName,almType);\n\t}\n\n\t\n\t\n\t@RequestMapping(value = \"/delAllIssues\", method =RequestMethod.GET)\n\tpublic String delAllIsues(@RequestParam(\"pName\") String projName) {\n\t\treturn service.delAllIssues(projName);\n\t}\n\n\t@RequestMapping(value = \"/getMetric\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic MonogOutMetrics getMetricsDatas(@RequestParam(\"wId\") String wId) {\n\t\treturn service.getMetricDetails(wId);\n\t}\n\t@RequestMapping(value = \"/getAllTransitions\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic List<TransitionModel> getAllTransitions(@RequestParam(\"pName\") String pName) {\n\t\treturn service.getAllTransitions(pName);\n\t}\n\n\t@RequestMapping(value = \"/getProjectMetrics\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic List<MonogOutMetrics> getProjectMetrics(@RequestParam(\"pName\") String pName) {\n\t\treturn service.getAllMetrics(pName);\n\t}\n\n\t@RequestMapping(value = \"/getChangeItems\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic List<ChangeHistoryModel> getChangesItems(@RequestParam(\"wId\") String wId) {\n\t\treturn service.getChangesItems(wId);\n\t}\n\n\t@RequestMapping(value = \"/getTransitions\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic List<TransitionModel> getTransitionsData(@RequestParam(\"wId\") String wId) {\n\t\treturn service.getTransitionsData(wId);\n\t}\n\n\t@RequestMapping(value = \"/getIteration\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic IterationOutModel getIterationData(@RequestParam(\"pName\") String pName,\n\t\t\t@RequestParam(\"itrName\") String itrName) {\n\t\treturn service.getIterationData(pName, itrName, getAlmType(pName));\n\t}\n\n\t@RequestMapping(value = \"/getEfort\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic List<EffortHistoryModel> getEffortData(@RequestParam(\"wId\") String wId) {\n\t\treturn service.getEffortData(wId);\n\t}\n\n\t@RequestMapping(value = \"/getProjectDetails\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic List<IterationOutModel> getProjectDetials(@RequestParam(\"pName\") String pName,\n\t\t\t@RequestParam(\"almType\") String almType) {\n\t\treturn service.getProjectDetails(pName, almType);\n\t}\n\t@RequestMapping(value = \"/getCurrentProjectDetails\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic Map <Integer,List<IterationOutModel>> getCurrentProjectDetials(@RequestParam(\"pName\") String pName,\n\t\t\t@RequestParam(\"almType\") String almType) {\n\t\treturn service.getCurrentProjectDetails(pName, almType);\n\t}\n\t\n\t\n\t@RequestMapping(value = \"/getCurrentIter\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic List<IterationOutModel> getCurrentIter(@RequestParam(\"pName\") String pName,\n\t\t\t@RequestParam(\"almType\") String almType) {\n\t\treturn service.getCrtItr(pName, almType);\n\t}\n\t@RequestMapping(value = \"/getIterations\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic List<IterationModel> getIterations(@RequestParam(\"pName\") String pName) {\n\t\treturn service.getAllIterations(pName);\n\t}\n\n\t@RequestMapping(value = \"/getDefectCount\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic ProjectModel getDefectCount(@RequestParam(\"pName\") String pName, @RequestParam(\"almType\") String almType) {\n\t\treturn service.getDefectCounts(pName, almType);\n\t}\n\n\t@RequestMapping(value = \"/getRelease\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic ReleaseDetails getRelease(@RequestParam(\"pName\") String projectName) {\n\t\treturn service.getRelease(projectName);\n\t}\n\n\t@RequestMapping(value = \"/getUnReleaseData\", produces = APPLICATION_JSON_VALUE, method = RequestMethod.GET)\n\tpublic List<MetricsModel> getUnReleaseData(@RequestParam(\"project\") String project,\n\t\t\t@RequestParam(\"almType\") String almType) {\n\t\tList<MetricsModel> response = service.getUnReleaseData(project, almType);\n\t\treturn response;\n\t}\n\n\t@RequestMapping(value = \"/getDefects\", produces = APPLICATION_JSON_VALUE, method = RequestMethod.GET)\n\tpublic List<MetricsModel> getDefects(@RequestParam(\"project\") String projectName,\n\t\t\t@RequestParam(\"almType\") String almType, @RequestParam(\"defect\") String defect) {\n\t\tList<MetricsModel> response = service.getDefects(projectName, almType, defect);\n\t\treturn response;\n\t}\n\n\t@RequestMapping(value = \"/slaData\", produces = APPLICATION_JSON_VALUE, method = RequestMethod.GET)\n\tpublic List<IterationOutModel> getSlaData(@RequestParam(\"project\") String projectName,\n\t\t\t@RequestParam(\"almType\") String almType, @RequestParam(\"createDate\") long createDate) {\n\t\treturn service.getSlaData(projectName, almType, createDate);\n\t}\n\t@RequestMapping(value = \"/assigneeAlm\", produces = APPLICATION_JSON_VALUE, method = RequestMethod.GET)\n\tpublic List<IterationOutModel> getAssigneeIssues(@RequestParam(\"project\") String projectName,\n\t\t\t@RequestParam(\"almType\") String almType, @RequestParam(\"members\") String[] members) {\n\t\treturn service.getAssigneeIssues(projectName, almType, members);\n\t}\n\n\t@RequestMapping(value = \"/dateIteration\", produces = APPLICATION_JSON_VALUE, method = RequestMethod.GET)\n\tpublic List<IterationOutModel> getDateIterations(@RequestParam(\"project\") String projectName,\n\t\t\t@RequestParam(\"almType\") String almType, @RequestParam(\"startDate\") long startDate,\n\t\t\t@RequestParam(\"endDate\") long endDate) {\n\t\treturn service.getDateIterations(projectName, almType, startDate, endDate);\n\t}\n\n\t@RequestMapping(value = \"/getProdDefects\", produces = APPLICATION_JSON_VALUE, method = RequestMethod.GET)\n\tpublic List<MetricsModel> getProdDefects(@RequestParam(\"project\") String projectName,\n\t\t\t@RequestParam(\"almType\") String almType, @RequestParam(\"defect\") String defect,\n\t\t\t@RequestParam(\"whereFound\") String whereFound, @RequestParam(\"whenFound\") String whenFound) {\n\t\tList<MetricsModel> response = service.getProdDefects(projectName, almType, defect, whereFound, whenFound);\n\t\treturn response;\n\t}\n\n\t@SuppressWarnings(\"rawtypes\")\n\t@RequestMapping(value = \"/getAlmType\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic String getAlmType(@RequestParam(\"pName\") String pName) {\n\t\tString almType = \"\";\n\t\tConfigurationSetting config = DataConfig.getContext().getBean(ConfigurationSettingRep.class)\n\t\t\t\t.findByProjectName(pName).get(0);\n\t\tSet<ConfigurationToolInfoMetric> metric = config.getMetrics();\n\t\tIterator iter = metric.iterator();\n\t\twhile (iter.hasNext()) {\n\t\t\tObject configuration1 = iter.next();\n\t\t\tConfigurationToolInfoMetric metric1 = (ConfigurationToolInfoMetric) configuration1;\n\t\t\tif (\"Jira\".equals(metric1.getToolName())\n\t\t\t\t\t|| (\"Jira Defects\".equals(metric1.getToolName()) && !((metric1.getUrl()).equals(\"\")))) {\n\t\t\t\talmType = \"JIRA\";\n\t\t\t\tbreak;\n\t\t\t} else if (\"Jira Defects\".equals(metric1.getToolName()) && !((metric1.getUrl()).equals(\"\"))) {\n\t\t\t\talmType = \"JIRA DEFECTS\";\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\t else if (\"JIRA-KANBAN\".equals(metric1.getToolName()) && !((metric1.getUrl()).equals(\"\"))) {\n\t\t\t\t\talmType = \"JIRA-KANBAN\";\n\t\t\t\t\tbreak;\n\t\t\t\t}else {\n\t\t\t\talmType = \"TFS\";\n\t\t\t}\n\t\t}\n\t\treturn almType;\n\t}\n\n\t\n\t@RequestMapping(value = \"/getVelocityChart\", produces = APPLICATION_JSON_VALUE, method = RequestMethod.GET)\n\tpublic List<ComponentVelocityList> getVelocityChart(@RequestParam(\"project\") String projectName) {\n\t\treturn service.getComponentVelocityChart(projectName,false);\n\t}\n\t\n\t\n\t@RequestMapping(value = \"/getIssueHierarchy\", produces = APPLICATION_JSON_VALUE, method = RequestMethod.GET)\n\tpublic Map<String, List> getIssueHierarchy(@RequestParam(\"project\") String projectName) {\n\t\tMap<String, List> response=  service.getIssueHierarchyChart(projectName);\n\t\treturn response;\n\t}\n\t\n\t\n\t@RequestMapping(value = \"/getComponentWiseIssueHierarchy\", produces = APPLICATION_JSON_VALUE, method = RequestMethod.GET)\n\tpublic Map<String, Map> getComponentWiseIssueHierarchy(@RequestParam(\"project\") String projectName) {\n\t\tMap<String, Map> response=  service.getComponentWiseIssueHierarchyChart(projectName);\n\t\treturn response;\n\t}\n\t\n\t\n\t@RequestMapping(value = \"/getComponentWiseVelocityChart\", produces = APPLICATION_JSON_VALUE, method = RequestMethod.GET)\n\tpublic List<ComponentVelocityList> getComponentWiseVelocityChart(@RequestParam(\"project\") String projectName) {\n\t\tList<ComponentVelocityList> resp=service.getComponentVelocityChart(projectName,true);\n\t\treturn resp;\n\t}\n\t\n\t\n\t@RequestMapping(value = \"/getComponontWiseSprintWiseStories\", produces = APPLICATION_JSON_VALUE, method = RequestMethod.GET)\n\tpublic List<ComponentSprintWiseStories> getComponontWiseSprintWiseStories(@RequestParam(\"project\") String projectName) {\n\t\tList<ComponentSprintWiseStories> response = service.getComponentsSprintStories(projectName);\n\t\treturn response;\n\t}\n\t\n\t@RequestMapping(value = \"/getComponents\", produces = APPLICATION_JSON_VALUE, method = RequestMethod.GET)\n\tpublic List<String> getComponents(@RequestParam(\"project\") String projectName) {\n\t\tList<String> response = service.getComponentsChart(projectName);\n\t\treturn response;\n\t}\n\t\n\t@RequestMapping(value = \"/updateComponent\", produces = APPLICATION_JSON_VALUE, method = RequestMethod.POST)\n\tpublic void updateComponent(@RequestParam(\"project\") String projectName) {\n\t\tservice.updateComponentsOfTaskandSubtask(projectName);\n\t\t\n\t}\n\t\n\t@RequestMapping(value = \"/saveEngScore\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic String saveEngScore(@RequestParam(\"pName\")String projectName,@RequestParam(\"month\")String month,@RequestParam(\"engScore\")double engScore){\n\t\n\t\treturn service.saveEngScore(projectName,month,engScore);\n\t\t\n\t}\n\t\n\t@RequestMapping(value = \"/getFeatureMetrics\", method = GET, produces = APPLICATION_JSON_VALUE)\n\tpublic List<MonogOutMetrics>  getFeatureMetrics(@RequestParam(\"pName\") String pName,\n\t\t\t@RequestParam(\"almType\") String almType) {\n\t\treturn service.getFeatureMetrics(pName, almType);\n\t}\n\t\n\t/*\n\t * public String getAlmType(String projectName) { \n\t * ConfigurationSetting config =\n\t * DataConfig.getContext().getBean(ConfigurationSettingRep.class)\n\t * .findByProjectName(projectName); Set<ConfigurationToolInfoMetric> metric\n\t * = config.getMetrics(); Iterator iter = metric.iterator(); while\n\t * (iter.hasNext()) { Object configuration1 = iter.next();\n\t * ConfigurationToolInfoMetric metric1 = (ConfigurationToolInfoMetric)\n\t * configuration1; if (\"Jira\".equals(metric1.getToolName()) || (\n\t * \"Jira Defects\".equals(metric1.getToolName()) && metric1.getUrl() == \"\"))\n\t * { almType = \"JIRA\"; break; } else if (\"Jira Defects\"\n\t * .equals(metric1.getToolName()) && metric1.getUrl() != \"\") { almType =\n\t * \"JIRA DEFECTS\"; break; } else { almType = \"TFS\"; } }\n\t */\n}\n"}, "ALMConfigReq": {"fqcn": "com.bolt.dashboard.request.ALMConfigReq", "package": "com.bolt.dashboard.request", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "imports": ["org.springframework.beans.BeanUtils", "com.bolt.dashboard.core.model.ALMConfiguration"], "endpoints": [], "db_entities": [], "source_code": "/**\n * \n */\npackage com.bolt.dashboard.request;\n\nimport org.springframework.beans.BeanUtils;\n\nimport com.bolt.dashboard.core.model.ALMConfiguration;\n\npublic class ALMConfigReq {\n\tprivate String storyName;\n\tprivate String priorityName;\n\tprivate String projectName;\n\tprivate String defectName;\n\tprivate String releaseName;\n\tprivate String[] taskName;\n\tprivate String[] closeState;\n\tprivate String[] newState;\n\tprivate String[] progressState;\n\tprivate String[] criticalPriority;\n\tprivate String[] highPriority;\n\tprivate String[] medPriority;\n\tprivate String[] lowPriority;\n\tprivate String[] tracksSet;\n\tprivate String[] rejectionPhase;\n\tprivate String[] reopenPhase;\n\tprivate String[] testingPhase;\n\tprivate String[] productionPhase;\n\tprivate double personHours;\n\tprivate String timeZone;\n\tprivate String[] velocityFields; \n\tprivate String environment;\n\tprivate boolean safeEnabled;\n\tprivate String ccrLabel;\n\tprivate String[] cycleTimeStates;\n\tprivate String[] throughputStates;\n\tprivate String firstSprint;\n\tpublic String getCcrLabel() {\n\t\treturn ccrLabel;\n\t}\n\n\tpublic void setCcrLabel(String ccrLabel) {\n\t\tthis.ccrLabel = ccrLabel;\n\t}\n\n\tpublic String[] getCycleTimeStates() {\n\t\treturn cycleTimeStates;\n\t}\n\n\tpublic void setCycleTimeStates(String[] cycleTimeStates) {\n\t\tthis.cycleTimeStates = cycleTimeStates;\n\t}\n\n\tpublic String[] getThroughputStates() {\n\t\treturn throughputStates;\n\t}\n\n\tpublic void setThroughputStates(String[] throughputStates) {\n\t\tthis.throughputStates = throughputStates;\n\t}\n\n\tpublic String[] getRejectionPhase() {\n\t\treturn rejectionPhase;\n\t}\n\n\tpublic void setRejectionPhase(String[] rejectionPhase) {\n\t\tthis.rejectionPhase = rejectionPhase;\n\t}\n\n\tpublic String[] getReopenPhase() {\n\t\treturn reopenPhase;\n\t}\n\n\tpublic void setReopenPhase(String[] reopenPhase) {\n\t\tthis.reopenPhase = reopenPhase;\n\t}\n\n\tpublic String[] getTestingPhase() {\n\t\treturn testingPhase;\n\t}\n\n\tpublic void setTestingPhase(String[] testingPhase) {\n\t\tthis.testingPhase = testingPhase;\n\t}\n\n\tpublic String[] getProductionPhase() {\n\t\treturn productionPhase;\n\t}\n\n\tpublic void setProductionPhase(String[] productionPhase) {\n\t\tthis.productionPhase = productionPhase;\n\t}\n\n\tprivate String trendType;\n\n\tpublic String getStoryName() {\n\t\treturn storyName;\n\t}\n\n\tpublic String getPriorityName() {\n\t\treturn priorityName;\n\t}\n\n\tpublic void setPriorityName(String priorityName) {\n\t\tthis.priorityName = priorityName;\n\t}\n\n\tpublic String[] getTaskName() {\n\t\treturn taskName;\n\t}\n\n\tpublic void setTaskName(String[] taskName) {\n\t\tthis.taskName = taskName;\n\t}\n\n\tpublic String getReleaseName() {\n\t\treturn releaseName;\n\t}\n\n\tpublic void setReleaseName(String releaseName) {\n\t\tthis.releaseName = releaseName;\n\t}\n\n\tpublic String[] getCloseState() {\n\t\treturn closeState;\n\t}\n\n\tpublic void setCloseState(String[] closeState) {\n\t\tthis.closeState = closeState;\n\t}\n\n\tpublic String[] getCriticalPriority() {\n\t\treturn criticalPriority;\n\t}\n\n\tpublic void setCriticalPriority(String[] criticalPriority) {\n\t\tthis.criticalPriority = criticalPriority;\n\t}\n\n\tpublic String[] getHighPriority() {\n\t\treturn highPriority;\n\t}\n\n\tpublic void setHighPriority(String[] highPriority) {\n\t\tthis.highPriority = highPriority;\n\t}\n\n\tpublic String[] getMedPriority() {\n\t\treturn medPriority;\n\t}\n\n\tpublic void setMedPriority(String[] medPriority) {\n\t\tthis.medPriority = medPriority;\n\t}\n\n\tpublic String[] getLowPriority() {\n\t\treturn lowPriority;\n\t}\n\n\tpublic void setLowPriority(String[] lowPriority) {\n\t\tthis.lowPriority = lowPriority;\n\t}\n\n\tpublic void setStoryName(String storyName) {\n\t\tthis.storyName = storyName;\n\t}\n\n\tpublic String getProjectName() {\n\t\treturn projectName;\n\t}\n\n\tpublic void setProjectName(String projectName) {\n\t\tthis.projectName = projectName;\n\t}\n\n\tpublic String getDefectName() {\n\t\treturn defectName;\n\t}\n\n\tpublic void setDefectName(String defectName) {\n\t\tthis.defectName = defectName;\n\t}\n\n\tpublic String getTrendType() {\n\t\treturn trendType;\n\t}\n\n\tpublic void setTrendType(String trendType) {\n\t\tthis.trendType = trendType;\n\t}\n\n\tpublic String[] getTracksSet() {\n\t\treturn tracksSet;\n\t}\n\n\tpublic void setTracksSet(String[] tracksSet) {\n\t\tthis.tracksSet = tracksSet;\n\t}\n\t\n\t\n\n\tpublic String[] getNewState() {\n\t\treturn newState;\n\t}\n\n\tpublic void setNewState(String[] newState) {\n\t\tthis.newState = newState;\n\t}\n\n\tpublic String[] getProgressState() {\n\t\treturn progressState;\n\t}\n\n\tpublic void setProgressState(String[] progressState) {\n\t\tthis.progressState = progressState;\n\t}\n\n\tpublic ALMConfiguration toDetailsAddSetting(ALMConfigReq req) {\n\t\tALMConfiguration details = new ALMConfiguration();\n\n\t\tBeanUtils.copyProperties(req, details);\n\t\treturn details;\n\t}\n\n\tpublic double getPersonHours() {\n\t\treturn personHours;\n\t}\n\n\tpublic void setPersonHours(double personHours) {\n\t\tthis.personHours = personHours;\n\t}\n\n\tpublic String getTimeZone() {\n\t\treturn timeZone;\n\t}\n\n\tpublic void setTimeZone(String timeZone) {\n\t\tthis.timeZone = timeZone;\n\t}\n\n\tpublic String[] getVelocityFields() {\n\t\treturn velocityFields;\n\t}\n\n\tpublic void setVelocityFields(String[] velocityFields) {\n\t\tthis.velocityFields = velocityFields;\n\t}\n\n\tpublic String getEnvironment() {\n\t\treturn environment;\n\t}\n\n\tpublic void setEnvironment(String environment) {\n\t\tthis.environment = environment;\n\t}\n\n\tpublic boolean isSafeEnabled() {\n\t\treturn safeEnabled;\n\t}\n\n\tpublic void setSafeEnabled(boolean safeEnabled) {\n\t\tthis.safeEnabled = safeEnabled;\n\t}\n\n\tpublic String getFirstSprint() {\n\t\treturn firstSprint;\n\t}\n\n\tpublic void setFirstSprint(String firstSprint) {\n\t\tthis.firstSprint = firstSprint;\n\t}\n\t\n\t\n\n}\n"}, "ALMToolReq": {"fqcn": "com.bolt.dashboard.request.ALMToolReq", "package": "com.bolt.dashboard.request", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMToolReq.java", "imports": [], "endpoints": [], "db_entities": [], "source_code": "package com.bolt.dashboard.request;\n\n/**\n * \n * <AUTHOR>\n *\n */\npublic class ALMToolReq {\n    private String almType;\n\n    /**\n     * @return the almType\n     */\n    public String getAlmType() {\n        return almType;\n    }\n\n    /**\n     * @param almType\n     *            the almType to set\n     */\n    public void setAlmType(String almType) {\n        this.almType = almType;\n    }\n\n}"}, "Authentication": {"fqcn": "com.bolt.dashboard.response.Authentication", "package": "com.bolt.dashboard.response", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\response\\Authentication.java", "imports": ["java.net.URI", "java.net.URISyntaxException", "javax.ws.rs.client.Client", "javax.ws.rs.client.ClientBuilder", "javax.ws.rs.client.Invocation", "javax.ws.rs.client.WebTarget", "javax.ws.rs.core.MediaType", "javax.ws.rs.core.Response", "org.glassfish.jersey.client.authentication.HttpAuthenticationFeature"], "endpoints": [], "db_entities": [], "source_code": "/**\n * \n */\n/*\n * package com.bolt.dashboard.response;\n * \n * import java.net.URI; import java.net.URISyntaxException;\n * \n * import javax.ws.rs.client.Client; import javax.ws.rs.client.ClientBuilder;\n * import javax.ws.rs.client.Invocation; import javax.ws.rs.client.WebTarget;\n * import javax.ws.rs.core.MediaType; import javax.ws.rs.core.Response;\n * \n * import org.glassfish.jersey.client.authentication.HttpAuthenticationFeature;\n * \n *//**\n   * <AUTHOR>\n   *\n   *//*\n     * public class Authentication { public static Auth AUTHNTICATION ; public\n     * static Response authenticationOfservice(String username,String password){\n     * return Auth.authenticate(username, password, new Auth()); }\n     * \n     * }\n     * \n     * final class Auth{ Response response = null; boolean authenticationStatus=\n     * false; public Auth(){\n     * \n     * }\n     * \n     * public static Response authenticate(String username,String password,Auth\n     * auth){ HttpAuthenticationFeature feature =\n     * HttpAuthenticationFeature.basic(username, password); final Client client\n     * = ClientBuilder.newClient(); client.register(feature); try { WebTarget\n     * webTarget = client.target(new\n     * URI(\"http://localhost:8081/api/tfsbuild?proName=BOLT_Dashboard\"));\n     * Invocation.Builder invocationBuilder =\n     * webTarget.request(MediaType.APPLICATION_JSON); auth.response =\n     * invocationBuilder.get(); } catch (URISyntaxException e) { \n     * Auto-generated catch block e.printStackTrace(); }\n     * \n     * return auth.response; } }\n     */"}, "DataResponse": {"fqcn": "com.bolt.dashboard.response.DataResponse", "package": "com.bolt.dashboard.response", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\response\\DataResponse.java", "imports": [], "endpoints": [], "db_entities": [], "source_code": "package com.bolt.dashboard.response;\n\npublic class DataResponse<T> {\n    private final T result;\n    private final long lastUpdated;\n\n    public DataResponse(T result, long lastUpdated) {\n        this.result = result;\n        this.lastUpdated = lastUpdated;\n    }\n\n    public T getResult() {\n        return result;\n    }\n\n    public long getLastUpdated() {\n        return lastUpdated;\n    }\n}\n"}, "ALMConfigService": {"fqcn": "com.bolt.dashboard.service.ALMConfigService", "package": "com.bolt.dashboard.service", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\service\\ALMConfigService.java", "imports": ["java.util.List", "com.bolt.dashboard.core.model.ALMConfiguration", "com.bolt.dashboard.response.DataResponse"], "endpoints": [], "db_entities": [], "source_code": "/**\n * \n */\npackage com.bolt.dashboard.service;\n\nimport java.util.List;\n\nimport com.bolt.dashboard.core.model.ALMConfiguration;\n\nimport com.bolt.dashboard.response.DataResponse;\n\n/**\n * <AUTHOR>\n *\n */\npublic interface ALMConfigService {\n\n\tALMConfiguration saveALMConfig(ALMConfiguration retrospective);\n\n\tDataResponse<ALMConfiguration> retrieveALMConfig(String projectName);\n\n}\n"}, "ALMConfigServiceImplementation": {"fqcn": "com.bolt.dashboard.service.ALMConfigServiceImplementation", "package": "com.bolt.dashboard.service", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\service\\ALMConfigServiceImplementation.java", "imports": ["org.apache.logging.log4j.LogManager", "org.apache.logging.log4j.Logger", "org.springframework.beans.factory.annotation.Autowired", "org.springframework.cache.annotation.CacheEvict", "org.springframework.cache.annotation.Cacheable", "org.springframework.stereotype.Service", "com.bolt.dashboard.core.model.ALMConfiguration", "com.bolt.dashboard.core.repository.ALMConfigRepo", "com.bolt.dashboard.response.DataResponse"], "endpoints": [], "db_entities": [], "source_code": "/**\n * \n */\npackage com.bolt.dashboard.service;\n\nimport org.apache.logging.log4j.LogManager;\n import org.apache.logging.log4j.Logger;\nimport org.springframework.beans.factory.annotation.Autowired;\nimport org.springframework.cache.annotation.CacheEvict;\nimport org.springframework.cache.annotation.Cacheable;\nimport org.springframework.stereotype.Service;\n\nimport com.bolt.dashboard.core.model.ALMConfiguration;\nimport com.bolt.dashboard.core.repository.ALMConfigRepo;\nimport com.bolt.dashboard.response.DataResponse;\n\n/**\n * <AUTHOR>\n *\n */\n@Service\npublic class ALMConfigServiceImplementation implements ALMConfigService {\n\tprivate ALMConfigRepo almConfigRepo;\n\tprivate static final Logger LOG = LogManager.getLogger(ALMConfigServiceImplementation.class);\n\n\t@Autowired\n\tpublic ALMConfigServiceImplementation(ALMConfigRepo repo) {\n\t\tthis.almConfigRepo = repo;\n\t}\n\n\t@Override\n//\t@CacheEvict(value=\"retrieveALMConfig\", key =\"'retrieveALMConfig'+#req.getProjectName()\", cacheManager=\"timeoutCacheManager\")\n\tpublic ALMConfiguration saveALMConfig(ALMConfiguration req) {\n\n\t\tif (almConfigRepo.findByProjectName(req.getProjectName()) != null) {\n\t\t\talmConfigRepo.deleteByProjectName(req.getProjectName());\n\t\t}\n\t\tLOG.info(\"ALM Configuration saved successfully...\");\n\t\treturn almConfigRepo.save(req);\n\n\t}\n\n\t@Override\n//\t@Cacheable(value=\"retrieveALMConfig\", key =\"'retrieveALMConfig'+#projectName\", cacheManager=\"timeoutCacheManager\")\n\tpublic DataResponse<ALMConfiguration> retrieveALMConfig(String projectName) {\n\t\tlong lastUpdate = 1;\n\t\tALMConfiguration result = almConfigRepo.findByProjectName(projectName).get(0);\n\n\t\treturn new DataResponse<ALMConfiguration>(result, lastUpdate);\n\t}\n\n}\n"}, "AlmService": {"fqcn": "com.bolt.dashboard.service.AlmService", "package": "com.bolt.dashboard.service", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\service\\AlmService.java", "imports": ["java.util.List", "java.util.Map", "com.bolt.dashboard.core.model.ChangeHistoryModel", "com.bolt.dashboard.core.model.ComponentBurnDown", "com.bolt.dashboard.core.model.ComponentGroomingTable", "com.bolt.dashboard.core.model.ComponentIssueBreakup", "com.bolt.dashboard.core.model.ComponentSprintWiseStories", "com.bolt.dashboard.core.model.ComponentStoryAgeing", "com.bolt.dashboard.core.model.ComponentStoryProgress", "com.bolt.dashboard.core.model.ComponentTaskRisk", "com.bolt.dashboard.core.model.ComponentVelocityList", "com.bolt.dashboard.core.model.DefectInsightData", "com.bolt.dashboard.core.model.EffortHistoryModel", "com.bolt.dashboard.core.model.IterationModel", "com.bolt.dashboard.core.model.IterationOutModel", "com.bolt.dashboard.core.model.MetricAgeData", "com.bolt.dashboard.core.model.MetricsModel", "com.bolt.dashboard.core.model.MonogOutMetrics", "com.bolt.dashboard.core.model.ProjectModel", "com.bolt.dashboard.core.model.ReleaseDetails", "com.bolt.dashboard.core.model.TransitionModel", "com.bolt.dashboard.util.BurnDownDataSprint", "com.bolt.dashboard.util.DefectBacklog", "com.bolt.dashboard.util.DefectDensity", "com.bolt.dashboard.util.DefectParetoModel", "com.bolt.dashboard.util.DefectProductionSlippage", "com.bolt.dashboard.util.StoryProgressSprintwise", "com.bolt.dashboard.util.TaskRiskSprint"], "endpoints": [], "db_entities": [], "source_code": "package com.bolt.dashboard.service;\n\nimport java.util.List;\nimport java.util.Map;\n\nimport com.bolt.dashboard.core.model.ChangeHistoryModel;\nimport com.bolt.dashboard.core.model.ComponentBurnDown;\nimport com.bolt.dashboard.core.model.ComponentGroomingTable;\nimport com.bolt.dashboard.core.model.ComponentIssueBreakup;\nimport com.bolt.dashboard.core.model.ComponentSprintWiseStories;\nimport com.bolt.dashboard.core.model.ComponentStoryAgeing;\nimport com.bolt.dashboard.core.model.ComponentStoryProgress;\nimport com.bolt.dashboard.core.model.ComponentTaskRisk;\nimport com.bolt.dashboard.core.model.ComponentVelocityList;\nimport com.bolt.dashboard.core.model.DefectInsightData;\nimport com.bolt.dashboard.core.model.EffortHistoryModel;\nimport com.bolt.dashboard.core.model.IterationModel;\nimport com.bolt.dashboard.core.model.IterationOutModel;\nimport com.bolt.dashboard.core.model.MetricAgeData;\nimport com.bolt.dashboard.core.model.MetricsModel;\nimport com.bolt.dashboard.core.model.MonogOutMetrics;\nimport com.bolt.dashboard.core.model.ProjectModel;\nimport com.bolt.dashboard.core.model.ReleaseDetails;\nimport com.bolt.dashboard.core.model.TransitionModel;\nimport com.bolt.dashboard.util.BurnDownDataSprint;\nimport com.bolt.dashboard.util.DefectBacklog;\nimport com.bolt.dashboard.util.DefectDensity;\nimport com.bolt.dashboard.util.DefectParetoModel;\nimport com.bolt.dashboard.util.DefectProductionSlippage;\nimport com.bolt.dashboard.util.StoryProgressSprintwise;\nimport com.bolt.dashboard.util.TaskRiskSprint;\n\n/**\n * <AUTHOR>\n *\n */\npublic interface AlmService {\n\n\tMonogOutMetrics getMetricDetails(String wId);\n\n\tList<MonogOutMetrics> getAllMetrics(String pName);\n\n\tList<ChangeHistoryModel> getChangesItems(String wId);\n\n\tList<TransitionModel> getTransitionsData(String wId);\n\n\tIterationOutModel getIterationData(String pName, String itrName, String almType);\n\n\tList<EffortHistoryModel> getEffortData(String wId);\n\n\tList<IterationOutModel> getProjectDetails(String pName, String almType);\n\n\tProjectModel getDefectCounts(String pName, String almType);\n\n\tList<IterationOutModel> getCrtItr(String pName, String almType);\n\n\tReleaseDetails getRelease(String projectName);\n\n\tList<MetricsModel> getUnReleaseData(String projectName, String almType);\n\n\tList<MetricsModel> getDefects(String projectName, String almType, String defect);\n\n\tList<IterationOutModel> getSlaData(String projectName, String almType, long createDate);\n\n\tList<IterationOutModel> getAssigneeIssues(String projectName, String almType, String[] members);\n\n\tList<IterationOutModel> getDateIterations(String projectName, String almType, long startDate, long endDate);\n\n\tList<MetricsModel> getProdDefects(String projectName, String almType, String defect, String whereFound,\n\t\t\tString whenFound);\n\n\tString delDuplicate(String projName);\n\n\tMap<Integer, List<IterationOutModel>> getCurrentProjectDetails(String pName, String almType);\n\n\tString delAllIssues(String projName);\n\n\tList<TransitionModel> getAllTransitions(String pName);\n\n\tList<ComponentVelocityList> getComponentVelocity(String projectName, boolean b);\n\n\tList<ComponentSprintWiseStories> getComponentsSprint(String projectName, boolean flag);\n\n\tMap<String, List> getIssueHierarchy(String projectName);\n\n\tMap<String, Map> getComponentWiseIssueHierarchy(String projectName);\n\n\tList<String> getComponents(String projectName, boolean b);\n\n\tvoid updateComponentsOfTaskandSubtask(String projectName);\n\n\tList<MonogOutMetrics> getFeatureMetrics(String pName, String almType);\n\n\tList<Map<String, List<String>>> getSprintProgressHome(String projName, String almType);\n\n\tList<Map<String, String>> getDefectsSummaryHome(String projName, String almType);\n\n\tList<ComponentTaskRisk> getTaskRisk(String projName, String almType, boolean storyPointBased);\n\n\tList<String> getActiveSprints(String projName, String almType);\n\n\tList<ComponentIssueBreakup> getIssueBrakeUp(String projName, String almType);\n\n\tList<ComponentStoryProgress> getStoryProgress(String projName, String almType);\n\n\tList<ComponentBurnDown> burndownCalculation(String projName, String almType, boolean storyPointBased);\n\n\tMap<String, List<DefectInsightData>> getDefectInsightData(String projName, String almType, boolean componentFlag);\n\n\tMap<String, List<DefectParetoModel>> defectParetoCalculation(String projName, String almType,\n\t\t\tboolean componentBased);\n\n\tMap<String, List<DefectProductionSlippage>> getProductionSlippage(String projName, String almType,\n\t\t\tboolean componentBased);\n\n\tMap<String, List<DefectDensity>> getDefectDensity(String projName, String almType, boolean componentBased);\n\n\tMap<String, DefectBacklog> getDefectBacklog(String projName, String almType, boolean componentBased);\n\n\tMap getDefectTrendAndClassification(String projName, String almType, boolean componentBased);\n\n\t List<ComponentStoryAgeing> getStoryAgeingData(String projName, String almType);\n\n\t List<ComponentGroomingTable> getGroomingTable(String projName, String almType);\n\n\tList<IterationModel> getAllIterations(String pName);\n\n\tMap getDefectClassification(String projName, String almType);\n\n\tString saveEngScore(String projectName, String month, double engScore);\n\t\n\tList<ComponentVelocityList> getComponentVelocityChart(String projectName, boolean b);\n\n\tList<ComponentSprintWiseStories> getComponentsSprintStories(String projectName);\n\n\tMap<String, List> getIssueHierarchyChart(String projectName);\n\n\tMap<String, Map> getComponentWiseIssueHierarchyChart(String projectName);\n\n\tList<String> getComponentsChart(String projectName);\n\t\n\n\n}\n"}, "ALMServiceImplementation": {"fqcn": "com.bolt.dashboard.service.ALMServiceImplementation", "package": "com.bolt.dashboard.service", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\service\\ALMServiceImplementation.java", "imports": ["java.util.ArrayList", "java.util.Arrays", "java.util.Collections", "java.util.Comparator", "java.util.Date", "java.util.HashMap", "java.util.Iterator", "java.util.List", "java.util.ListIterator", "java.util.Map", "java.util.Set", "java.util.SortedMap", "java.util.TreeMap", "java.util.stream.Collectors", "org.apache.logging.log4j.LogManager", "org.apache.logging.log4j.Logger", "org.springframework.beans.BeanUtils", "org.springframework.beans.factory.annotation.Autowired", "org.springframework.cache.annotation.Cacheable", "org.springframework.data.mongodb.core.MongoTemplate", "org.springframework.data.mongodb.core.aggregation.Aggregation", "org.springframework.data.mongodb.core.query.Criteria", "org.springframework.data.mongodb.core.query.Query", "org.springframework.stereotype.Service", "com.bolt.dashboard.core.ConstantVariable", "com.bolt.dashboard.core.config.DataConfig", "com.bolt.dashboard.core.config.MongoAggregate", "com.bolt.dashboard.core.model.ALMConfiguration", "com.bolt.dashboard.core.model.ChangeHistoryModel", "com.bolt.dashboard.core.model.ComponentBurnDown", "com.bolt.dashboard.core.model.ComponentGroomingTable", "com.bolt.dashboard.core.model.ComponentIssueBreakup", "com.bolt.dashboard.core.model.ComponentSprintWiseStories", "com.bolt.dashboard.core.model.ComponentStoryAgeing", "com.bolt.dashboard.core.model.ComponentStoryProgress", "com.bolt.dashboard.core.model.ComponentTaskRisk", "com.bolt.dashboard.core.model.ComponentVelocityList", "com.bolt.dashboard.core.model.ConfigurationSetting", "com.bolt.dashboard.core.model.ConfigurationToolInfoMetric", "com.bolt.dashboard.core.model.DefectInsightData", "com.bolt.dashboard.core.model.EffortHistoryModel", "com.bolt.dashboard.core.model.IssueList", "com.bolt.dashboard.core.model.IterationModel", "com.bolt.dashboard.core.model.IterationOutModel", "com.bolt.dashboard.core.model.MetricsModel", "com.bolt.dashboard.core.model.MonogOutMetrics", "com.bolt.dashboard.core.model.ProjectModel", "com.bolt.dashboard.core.model.ReleaseDetails", "com.bolt.dashboard.core.model.ScoreCardSprintData", "com.bolt.dashboard.core.model.TransitionModel", "com.bolt.dashboard.core.model.VelocityList", "com.bolt.dashboard.core.repository.ALMConfigRepo", "com.bolt.dashboard.core.repository.ChangeHisortyRepo", "com.bolt.dashboard.core.repository.ConfigurationSettingRep", "com.bolt.dashboard.core.repository.EffortHistoryRepo", "com.bolt.dashboard.core.repository.IterationRepo", "com.bolt.dashboard.core.repository.MetricRepo", "com.bolt.dashboard.core.repository.ProjectIterationRepo", "com.bolt.dashboard.core.repository.ProjectRepo", "com.bolt.dashboard.core.repository.ReleaseRepo", "com.bolt.dashboard.core.repository.TransitionRepo", "com.bolt.dashboard.jira.ChartCalculations", "com.bolt.dashboard.jira.JIRAApplication", "com.bolt.dashboard.jira.JiraExceptions", "com.bolt.dashboard.util.BacklogCalculation", "com.bolt.dashboard.util.DefectBacklog", "com.bolt.dashboard.util.DefectCalculations", "com.bolt.dashboard.util.DefectDensity", "com.bolt.dashboard.util.DefectParetoModel", "com.bolt.dashboard.util.DefectProductionSlippage", "com.bolt.dashboard.util.ProjectHomeCalculation", "com.bolt.dashboard.util.SprintProgress", "com.bolt.dashboard.util.SprintProgressCalculations", "com.bolt.dashboard.util.VelocityCalculations", "com.google.common.collect.Lists"], "endpoints": [], "db_entities": [], "source_code": "package com.bolt.dashboard.service;\n\nimport static org.springframework.data.mongodb.core.aggregation.Aggregation.group;\nimport static org.springframework.data.mongodb.core.aggregation.Aggregation.match;\nimport static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;\nimport static org.springframework.data.mongodb.core.aggregation.Aggregation.unwind;\n\nimport java.util.ArrayList;\nimport java.util.Arrays;\nimport java.util.Collections;\nimport java.util.Comparator;\nimport java.util.Date;\nimport java.util.HashMap;\nimport java.util.Iterator;\nimport java.util.List;\nimport java.util.ListIterator;\nimport java.util.Map;\nimport java.util.Set;\nimport java.util.SortedMap;\nimport java.util.TreeMap;\nimport java.util.stream.Collectors;\n\nimport org.apache.logging.log4j.LogManager;\nimport org.apache.logging.log4j.Logger;\nimport org.springframework.beans.BeanUtils;\nimport org.springframework.beans.factory.annotation.Autowired;\nimport org.springframework.cache.annotation.Cacheable;\nimport org.springframework.data.mongodb.core.MongoTemplate;\nimport org.springframework.data.mongodb.core.aggregation.Aggregation;\nimport org.springframework.data.mongodb.core.query.Criteria;\nimport org.springframework.data.mongodb.core.query.Query;\nimport org.springframework.stereotype.Service;\n\nimport com.bolt.dashboard.core.ConstantVariable;\nimport com.bolt.dashboard.core.config.DataConfig;\nimport com.bolt.dashboard.core.config.MongoAggregate;\nimport com.bolt.dashboard.core.model.ALMConfiguration;\nimport com.bolt.dashboard.core.model.ChangeHistoryModel;\nimport com.bolt.dashboard.core.model.ComponentBurnDown;\nimport com.bolt.dashboard.core.model.ComponentGroomingTable;\nimport com.bolt.dashboard.core.model.ComponentIssueBreakup;\nimport com.bolt.dashboard.core.model.ComponentSprintWiseStories;\nimport com.bolt.dashboard.core.model.ComponentStoryAgeing;\nimport com.bolt.dashboard.core.model.ComponentStoryProgress;\nimport com.bolt.dashboard.core.model.ComponentTaskRisk;\nimport com.bolt.dashboard.core.model.ComponentVelocityList;\nimport com.bolt.dashboard.core.model.ConfigurationSetting;\nimport com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;\nimport com.bolt.dashboard.core.model.DefectInsightData;\nimport com.bolt.dashboard.core.model.EffortHistoryModel;\nimport com.bolt.dashboard.core.model.IssueList;\nimport com.bolt.dashboard.core.model.IterationModel;\nimport com.bolt.dashboard.core.model.IterationOutModel;\nimport com.bolt.dashboard.core.model.MetricsModel;\nimport com.bolt.dashboard.core.model.MonogOutMetrics;\nimport com.bolt.dashboard.core.model.ProjectModel;\nimport com.bolt.dashboard.core.model.ReleaseDetails;\nimport com.bolt.dashboard.core.model.ScoreCardSprintData;\nimport com.bolt.dashboard.core.model.TransitionModel;\nimport com.bolt.dashboard.core.model.VelocityList;\nimport com.bolt.dashboard.core.repository.ALMConfigRepo;\nimport com.bolt.dashboard.core.repository.ChangeHisortyRepo;\nimport com.bolt.dashboard.core.repository.ConfigurationSettingRep;\nimport com.bolt.dashboard.core.repository.EffortHistoryRepo;\nimport com.bolt.dashboard.core.repository.IterationRepo;\nimport com.bolt.dashboard.core.repository.MetricRepo;\nimport com.bolt.dashboard.core.repository.ProjectIterationRepo;\nimport com.bolt.dashboard.core.repository.ProjectRepo;\nimport com.bolt.dashboard.core.repository.ReleaseRepo;\nimport com.bolt.dashboard.core.repository.TransitionRepo;\nimport com.bolt.dashboard.jira.ChartCalculations;\nimport com.bolt.dashboard.jira.JIRAApplication;\nimport com.bolt.dashboard.jira.JiraExceptions;\nimport com.bolt.dashboard.util.BacklogCalculation;\nimport com.bolt.dashboard.util.DefectBacklog;\nimport com.bolt.dashboard.util.DefectCalculations;\nimport com.bolt.dashboard.util.DefectDensity;\nimport com.bolt.dashboard.util.DefectParetoModel;\nimport com.bolt.dashboard.util.DefectProductionSlippage;\nimport com.bolt.dashboard.util.ProjectHomeCalculation;\nimport com.bolt.dashboard.util.SprintProgress;\nimport com.bolt.dashboard.util.SprintProgressCalculations;\nimport com.bolt.dashboard.util.VelocityCalculations;\nimport com.google.common.collect.Lists;\n\n@Service\npublic class ALMServiceImplementation implements AlmService {\n\n\tprivate TransitionRepo transitionRepo;\n\tprivate EffortHistoryRepo effortHistoryRepo;\n\tprivate ChangeHisortyRepo changeHisortyRepo;\n\tprivate ProjectIterationRepo authorRepo;\n\tprivate ProjectRepo projectRepo;\n\tprivate ReleaseRepo releaseRepo;\n\tprivate IterationRepo iterationRepo;\n\tprivate MetricRepo metricRepo;\n\tprivate ALMConfigRepo almConfigRepo;\n\tprivate MongoAggregate agg = null;\n\tMongoTemplate mongoTemplate = null;\n\t\n//\t@Autowired\n//    GuavaCacheManager cacheManager;\n\t\n\tList<IterationOutModel> workingBacklog = new ArrayList<IterationOutModel>();\n\tList<IterationOutModel> workingSprints = new ArrayList<IterationOutModel>();\n\tList<String> velocityFields;\n\tList<String> closestates;\n\tList<String> taskNames;\n\tALMConfiguration almConfig;\n\tVelocityList vlist;\n\tArrayList<IssueList> issueList;\n\tList<String> wIdArr;\n\tdouble tempSpRefined = 0;\n\tdouble tempSpRemoved = 0;\n\tdouble finalStoriesCommited = 0;\n\tdouble storiesCompleted = 0;\n\tdouble defetcsCompleted = 0;\n\tArrayList<IssueList> refinedIssuList;\n\tArrayList<IssueList> removedIssuList;\n\tprivate static final Logger LOGGER = LogManager.getLogger(ALMServiceImplementation.class);\n\n\tprivate ChartCalculations chartService;\n\n\t@Autowired\n\tpublic ALMServiceImplementation(TransitionRepo transitionRepo, ProjectIterationRepo authorRepo,\n\t\t\tEffortHistoryRepo effortHistoryRepo, ChangeHisortyRepo changeHisortyRepo, ProjectRepo projectRepo,\n\t\t\tReleaseRepo repo, IterationRepo iterationRepo, MetricRepo metricRepo, ALMConfigRepo almConfig) {\n\t\tthis.transitionRepo = transitionRepo;\n\t\tthis.effortHistoryRepo = effortHistoryRepo;\n\t\tthis.changeHisortyRepo = changeHisortyRepo;\n\t\tthis.authorRepo = authorRepo;\n\t\tthis.projectRepo = projectRepo;\n\t\tthis.releaseRepo = repo;\n\t\tthis.agg = new MongoAggregate();\n\t\tthis.iterationRepo = iterationRepo;\n\t\tthis.metricRepo = metricRepo;\n\t\tthis.agg = new MongoAggregate();\n\t\tthis.almConfigRepo = almConfig;\n\t\tchartService = new ChartCalculations();\n\n\t\ttry {\n\t\t\tmongoTemplate = DataConfig.getInstance().mongoTemplate();\n\t\t} catch (Exception e) {\n\t\t\tLOGGER.info(e);\n\t\t}\n\n\t}\n\t\n//\tpublic void evictAllCaches(String cacheName) {\n//\t\tcacheManager.getCache(cacheName).clear();\n//\t}\n\t\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getMetricDetails'+#wId\", cacheManager=\"timeoutCacheManager\")\n\tpublic MonogOutMetrics getMetricDetails(String wId) {\n\t\tMonogOutMetrics result = agg.getMetric(null, wId).get(0);\n\t\treturn result;\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getMetricDetails'+#pName\", cacheManager=\"timeoutCacheManager\")\n\tpublic List<MonogOutMetrics> getAllMetrics(String pName) {\n\t\tList<MonogOutMetrics> result = agg.getMetric(pName, null);\n\t\treturn result;\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getChangesItems'+#wId\", cacheManager=\"timeoutCacheManager\")\n\tpublic List<ChangeHistoryModel> getChangesItems(String wId) {\n\t\tList<ChangeHistoryModel> result = Lists.newArrayList(changeHisortyRepo.findByWId(wId));\n\t\treturn result;\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getTransitionsData'+#wId\", cacheManager=\"timeoutCacheManager\")\n\tpublic List<TransitionModel> getTransitionsData(String wId) {\n\t\treturn Lists.newArrayList(transitionRepo.findByWId(wId));\n\t\t\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getIterationData'+#pName+#itrName+#almType\", cacheManager=\"timeoutCacheManager\")\n\tpublic IterationOutModel getIterationData(String pName, String itrName, String almType) {\n\t\treturn authorRepo.findByPNameAndSNameAndPAlmType(pName, itrName, almType);\n\t\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getEffortData'+#wId\", cacheManager=\"timeoutCacheManager\")\n\tpublic List<EffortHistoryModel> getEffortData(String wId) {\n\t\treturn effortHistoryRepo.findByWId(wId);\n\t\t\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getProjectDetials'+#pName+#almType\", cacheManager=\"timeoutCacheManager\")\n\tpublic List<IterationOutModel> getProjectDetails(String pName, String almType) {\n\t\tString almTy = getAlmType(pName);\n\t\treturn populateAuthor(pName, almTy);\n\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getFeatureMetrics'+#projectName+#almType\", cacheManager=\"timeoutCacheManager\")\n\tpublic List<MonogOutMetrics> getFeatureMetrics(String pName, String almType) {\n\t\treturn agg.aggregateMetrics(pName, almType);\n\t\t \n\t}\n\n\tpublic List<IterationOutModel> populateAuthor(String pName, String almType) {\n\t\tList<IterationOutModel> dataModel = authorRepo.findByPNameAndPAlmType(pName, almType);\n\t\tif (dataModel == null || dataModel.isEmpty()) {\n\t\t\tagg.aggregate(pName, almType);\n\t\t\tdataModel = authorRepo.findByPNameAndPAlmType(pName, almType);\n\t\t}\n\t\treturn dataModel;\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getCurrentIter'+#pName+#almType\", cacheManager=\"timeoutCacheManager\")\n\tpublic List<IterationOutModel> getCrtItr(String pName, String almType) {\n\t\t\n\t\tList<IterationOutModel> dataList = agg.getCurrentItr(pName, almType, ConstantVariable.activeStates);\n\t\tif (dataList.isEmpty()) {\n\t\t\tdataList = new ArrayList<>();\n\t\t\n\t\t\tList<IterationOutModel> data = agg.getCurrentItr(pName, almType, ConstantVariable.closedStates);\n\t\t\tif (!data.isEmpty()) {\n\t\t\t\tdataList.add(data.get(0));\n\t\t\t}\n\t\t}\n\t\treturn dataList;\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getDefectCount'+#pName+#almType\", cacheManager=\"timeoutCacheManager\")\n\tpublic ProjectModel getDefectCounts(String pName, String almType) {\n\t\treturn projectRepo.findByProjectNameAndAlmType(pName, almType);\n\t\t\n\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getRelease'+#projectName\", cacheManager=\"timeoutCacheManager\")\n\tpublic ReleaseDetails getRelease(String projectName) {\n\t\tList<ReleaseDetails> releases = releaseRepo.findByProjectName(projectName);\n\t\treturn releases.get(0);\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getUnReleaseData'+#projectName+#almType\", cacheManager=\"timeoutCacheManager\")\n\tpublic List<MetricsModel> getUnReleaseData(String projectName, String almType) {\n\n\t\tQuery query = new Query();\n\n\t\tquery.addCriteria(Criteria.where(\"targetRelease\").exists(true)\n\t\t\t\t.andOperator(Criteria.where(ConstantVariable.pNameConst).is(projectName), Criteria.where(ConstantVariable.pAlmTypeConst).is(almType)));\n\n\t\tList<MetricsModel> response = new ArrayList<>();\n\t\tresponse = mongoTemplate.find(query, MetricsModel.class, ConstantVariable.mixedCaseMetricsConst);\n\n\t\treturn response;\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getDefects'+#projectName+#almType+#defect\", cacheManager=\"timeoutCacheManager\")\n\tpublic List<MetricsModel> getDefects(String projectName, String almType, String defect) {\n\t\tQuery query = new Query();\n\t\tquery.addCriteria(Criteria.where(ConstantVariable.pNameConst).is(projectName).and(ConstantVariable.pAlmTypeConst).is(almType).and(\"type\").is(defect));\n\t\treturn mongoTemplate.find(query, MetricsModel.class, ConstantVariable.mixedCaseMetricsConst);\n\t\t\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getProdDefects'+#projectName+#almType+#defect+#whereFound+#whenFound\", cacheManager=\"timeoutCacheManager\")\n\tpublic List<MetricsModel> getProdDefects(String projectName, String almType, String defect, String whereFound,\n\t\t\tString whenFound) {\n\t\tQuery query = new Query();\n\t\tquery.addCriteria(Criteria.where(ConstantVariable.pNameConst).is(projectName).and(ConstantVariable\n\t\t\t\t.pAlmTypeConst).is(almType).and(\"type\").is(defect)\n\t\t\t\t.orOperator(Criteria.where(\"whereFound\").is(whereFound), Criteria.where(\"whenFound\").is(whenFound)));\n\t\n\t\treturn mongoTemplate.find(query, MetricsModel.class, ConstantVariable.mixedCaseMetricsConst);\n\t\t\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getDateIterations'+#projectName+#almType+#startDate+#endDate\", cacheManager=\"timeoutCacheManager\")\n\tpublic List<IterationOutModel> getDateIterations(String projectName, String almType, long startDate, long endDate) {\n\t\tQuery query = new Query();\n\t\tquery.addCriteria(Criteria.where(ConstantVariable.pNameConst).is(projectName).and(ConstantVariable.pAlmTypeConst)\n\t\t\t\t.is(almType).and(\"stDate\")\n\t\t\t\t.gte(startDate).and(\"endDate\").lte(endDate));\n\t\tList<IterationOutModel> response = new ArrayList<>();\n\t\tresponse = mongoTemplate.find(query, IterationOutModel.class, \"Iterations\");\n\t\treturn response;\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getSlaData'+#projectName+#almType+#createDate\", cacheManager=\"timeoutCacheManager\")\n\tpublic List<IterationOutModel> getSlaData(String projectName, String almType, long createDate) {\n\t\tList<IterationOutModel> data = null;\n\t\tAggregation aggregation = newAggregation(match(Criteria.where(ConstantVariable.pNameConst).is(projectName)), unwind(ConstantVariable.lowerCaseMetricsConst),\n\t\t\t\tmatch(Criteria.where(\"metrics.createDate\").gt(createDate)),\n\t\t\t\tgroup(\"_id\").addToSet(\"$metrics\").as(ConstantVariable.lowerCaseMetricsConst));\n\n\t\ttry {\n\t\t\tdata = DataConfig.getInstance().mongoTemplate().aggregate(aggregation, \"Author\", IterationOutModel.class)\n\t\t\t\t\t.getMappedResults();\n\t\t} catch (Exception e) {\n\t\t\t\n\t\t\tLOGGER.error(e.getCause());\n\t\t}\n\n\t\t/*\n\t\t * MatchOperation filterSName = Aggregation.match(new\n\t\t * Criteria(\"pName\").is(projectName)); UnwindOperation unwinnd =\n\t\t * Aggregation.unwind(\"metrices\"); MatchOperation metricSearch =\n\t\t * Aggregation.match(new Criteria(\"metrices.createDate\").gt(createDate));\n\t\t * GroupOperation group = Aggregation.group(\"metrices\"); String\n\t\t * GroupOperationBuilder = null; GroupOperation groupAppend =\n\t\t * group.addToSet(\"$metrics\"); Aggregation mtrAggr =\n\t\t * Aggregation.newAggregation(filterSName, unwinnd, metricSearch, groupAppend);\n\t\t * try { data = DataConfig.getInstance().mongoTemplate().aggregate(mtrAggr,\n\t\t * \"Author\", MonogOutMetrics.class) .getMappedResults(); } catch (Exception e) {\n\t\t *  e.printStackTrace(); }\n\t\t */\n\n\t\t/*\n\t\t * Query query = new Query();\n\t\t * query.addCriteria(Criteria.where(\"pName\").is(projectName).and(\n\t\t * \"pAlmType\").is(almType).and(\"createDate\").gt(createDate)); List<MetricsModel>\n\t\t * response = new ArrayList<>(); response = mongoTemplate.find(query,\n\t\t * MetricsModel.class, \"Metrics\");\n\t\t */\n\t\treturn data;\n\t}\n\n\t// Verizon : Filter based on assigned members\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getAssigneeIssues'+#projectName+#almType+#members\", cacheManager=\"timeoutCacheManager\")\n\tpublic List<IterationOutModel> getAssigneeIssues(String projectName, String almType, String[] members) {\n\t\tList<IterationOutModel> data = null;\n\t\tAggregation aggregation = newAggregation(match(Criteria.where(ConstantVariable.pNameConst).is(projectName)), unwind(ConstantVariable.lowerCaseMetricsConst),\n\t\t\t\tmatch(Criteria.where(\"metrics.assgnTo\").in(members)),\n\t\t\t\tgroup(\"_id\").first(\"$sName\").as(\"sName\").first(\"$sId\").as(\"sId\").first(\"$stDate\").as(\"stDate\")\n\t\t\t\t\t\t.first(\"$endDate\").as(\"endDate\").first(\"$completedDate\").as(\"completedDate\").first(\"$velocity\")\n\t\t\t\t\t\t.as(\"velocity\").first(\"$teamSize\").as(\"teamSize\").first(\"$techDebt\").as(\"techDebt\")\n\t\t\t\t\t\t.first(\"$totalStoryPoints\").as(\"totalStoryPoints\").first(\"$totOpenDefetct\").as(\"totOpenDefetct\")\n\t\t\t\t\t\t.first(\"$totDefects\").as(\"totDefects\").first(\"$totClosedDefects\").as(\"totClosedDefects\")\n\t\t\t\t\t\t.first(\"$state\").as(\"state\").first(\"$pName\").as(ConstantVariable.pNameConst).first(\"$pAlmType\").as(ConstantVariable.pAlmTypeConst)\n\t\t\t\t\t\t.addToSet(\"$metrics\").as(ConstantVariable.lowerCaseMetricsConst));\n\n\t\ttry {\n\t\t\tdata = DataConfig.getInstance().mongoTemplate().aggregate(aggregation, \"Author\", IterationOutModel.class)\n\t\t\t\t\t.getMappedResults();\n\t\t} catch (Exception e) {\n\t\t\tLOGGER.error(e.getMessage());\n\t\t}\n\t\treturn data;\n\t}\n\n\t@Override\n//\t@CacheEvict(value=\"ALMService\", allEntries=true)\n\tpublic String delDuplicate(String projName) {\n\t\t\n\t\ttry {\n\t\t\treturn (new JIRAApplication().deleteJiraIssues(projName));\n\t\t} catch (JiraExceptions e) {\n\t\t\t\n\t\t\tLOGGER.error(e.getMessage());\n\t\t\treturn \"Failed\";\n\t\t}\n\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getCurrentProjectDetials'+#pName+#almType\", cacheManager=\"timeoutCacheManager\")\n\tpublic Map<Integer, List<IterationOutModel>> getCurrentProjectDetails(String pName, String almType) {\n\t\t\n\t\tMongoAggregate mongoAggr = new MongoAggregate();\n\t\tint totalCount = (int) mongoAggr.getTotalSprintCount(pName, almType);\n\t\tMap<Integer, List<IterationOutModel>> outMap = new HashMap<>();\n\t\tList<IterationOutModel> currentAuthor = authorRepo.findByPNameAndPAlmTypeAndState(pName, almType, ConstantVariable.upperCaseActiveConst);\n\t\tif (currentAuthor.isEmpty()) {\n\t\t\tIterationOutModel temp = authorRepo.findOneByPNameAndPAlmTypeOrderByEndDateDesc(pName, almType);\n\t\t\tcurrentAuthor.add(temp);\n\t\t}\n\t\toutMap.put(totalCount, currentAuthor);\n\n\t\treturn outMap;\n\t}\n\n\t@Override\n//\t@CacheEvict(value=\"ALMService\", allEntries=true)\n\tpublic String delAllIssues(String projName) {\n\t\t\n\t\t// List<TransitionModel> transitions=transitionRepo.findByPName(projName);\n\t\t// if(!transitions.isEmpty())\n\t\ttransitionRepo.delete(transitionRepo.findByPName(projName));\n\n\t\teffortHistoryRepo.delete(effortHistoryRepo.findByPName(projName));\n\t\tchangeHisortyRepo.delete(changeHisortyRepo.findByPName(projName));\n\t\tauthorRepo.delete(authorRepo.findByPName(projName));\n\n\t\titerationRepo.delete(iterationRepo.findByPName(projName));\n\n\t\tmetricRepo.delete(metricRepo.findByPName(projName));\n\t\treleaseRepo.delete(releaseRepo.findByProjectName(projName));\n\t\tprojectRepo.delete(projectRepo.findByProjectName(projName));\n\t\tLOGGER.info(\"Collections deleted for Project \" + projName);\n\t\treturn \"Collections deleted for Project \" + projName;\n\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getAllTransitions'+#pName\", cacheManager=\"timeoutCacheManager\")\n\tpublic List<TransitionModel> getAllTransitions(String pName) {\n\t\t\n\t\tList<TransitionModel> result = transitionRepo.findByPName(pName);\n\t\treturn result;\n\t}\n\n\t@Override\n\tpublic List<ComponentVelocityList> getComponentVelocity(String projectName, boolean flag) {\n\t\tList<ComponentVelocityList> response = new ArrayList<ComponentVelocityList>();\n\t\tList<IterationOutModel> authorList = authorRepo.findByPName(projectName);\n\t\tCollections.sort(authorList, new SprintComparatort());\n\n\t\tList<ScoreCardSprintData> v = getVelocityChart(authorList, projectName);\n\t\tComponentVelocityList c = new ComponentVelocityList();\n\t\tc.setComponent(\"All\");\n\t\tc.setVelocityList(v);\n\t\tresponse.add(c);\n\t\tif (flag) {\n\t\t\tArrayList<String> componentList = getComponentList(authorList);\n\n\t\t\tfor (String component : componentList) {\n\t\t\t\tList<IterationOutModel> tempAuthorList = new ArrayList<IterationOutModel>();\n\t\t\t\tfor (IterationOutModel auth : authorList) {\n\n\t\t\t\t\tIterationOutModel author = new IterationOutModel();\n\t\t\t\t\tBeanUtils.copyProperties(auth, author);\n\n\t\t\t\t\tList<MonogOutMetrics> metrics = new ArrayList<MonogOutMetrics>();\n\t\t\t\t\tif (auth.getMetrics() != null)\n\t\t\t\t\t\tfor (MonogOutMetrics m : auth.getMetrics()) {\n\t\t\t\t\t\t\tif (m.getComponents() != null && m.getComponents().get(0) != null\n\t\t\t\t\t\t\t\t\t&& m.getComponents().get(0).equals(component)) {\n\n\t\t\t\t\t\t\t\tmetrics.add(m);\n\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\tif (metrics.size() > 0) {\n\t\t\t\t\t\tauthor.setMetrics(metrics);\n\t\t\t\t\t\ttempAuthorList.add(author);\n\t\t\t\t\t}\n\n\t\t\t\t}\n\t\t\t\tCollections.sort(tempAuthorList, new SprintComparatort());\n\t\t\t\tv = getVelocityChart(tempAuthorList, projectName);\n\t\t\t\tc = new ComponentVelocityList();\n\t\t\t\tc.setComponent(component);\n\t\t\t\tc.setVelocityList(v);\n\t\t\t\tresponse.add(c);\n\n\t\t\t}\n\t\t}\n\t\treturn response;\n\t}\n\n\t@Override\n\tpublic List<ComponentSprintWiseStories> getComponentsSprint(String projectName, boolean flag) {\n\t\tList<ComponentSprintWiseStories> response = new ArrayList<ComponentSprintWiseStories>();\n\n\t\tList<IterationModel> sprints = iterationRepo.findByPName(projectName);\n\t\tList<MetricsModel> metricList = metricRepo.findByPNameAndType(projectName, \"Story\");\n\t\tList<TransitionModel> transitionList = (List<TransitionModel>) transitionRepo.findAll();\n\t\tList<IterationOutModel> authorList = authorRepo.findByPName(projectName);\n\t\tCollections.sort(authorList, new SprintComparatort());\n\n\t\tList<IterationOutModel> itr = getSprintWiseStories(projectName, metricList, transitionList, authorList);\n\t\tComponentSprintWiseStories c = new ComponentSprintWiseStories();\n\t\tc.setComponent(\"All\");\n\t\tc.setIterationOutModel(itr);\n\t\tresponse.add(c);\n\t\tif (flag) {\n\t\t\tArrayList<String> componentList = getComponentList(authorList);\n\n\t\t\tfor (String component : componentList) {\n\t\t\t\tList<IterationOutModel> tempAuthorList = new ArrayList<IterationOutModel>();\n\t\t\t\tList<MetricsModel> allMetricsOfComponet = new ArrayList<MetricsModel>();\n\t\t\t\tfor (IterationOutModel auth : authorList) {\n\n\t\t\t\t\tIterationOutModel author = new IterationOutModel();\n\t\t\t\t\tBeanUtils.copyProperties(auth, author);\n\n\t\t\t\t\tList<MonogOutMetrics> metrics = new ArrayList<MonogOutMetrics>();\n\t\t\t\t\tif (auth.getMetrics() != null)\n\t\t\t\t\t\tfor (MonogOutMetrics m : auth.getMetrics()) {\n\t\t\t\t\t\t\tif (m.getComponents() != null && m.getComponents().get(0) != null\n\t\t\t\t\t\t\t\t\t&& m.getComponents().get(0).equals(component)) {\n\n\t\t\t\t\t\t\t\tmetrics.add(m);\n\t\t\t\t\t\t\t\tMetricsModel temp = new MetricsModel();\n\t\t\t\t\t\t\t\tBeanUtils.copyProperties(m, temp);\n\t\t\t\t\t\t\t\tallMetricsOfComponet.add(temp);\n\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\tif (metrics.size() > 0) {\n\t\t\t\t\t\tauthor.setMetrics(metrics);\n\t\t\t\t\t\ttempAuthorList.add(author);\n\t\t\t\t\t}\n\n\t\t\t\t}\n\t\t\t\tCollections.sort(tempAuthorList, new SprintComparatort());\n\t\t\t\t// if(component.equals(\"NP-01 Service Transparency-Consumer\")) {\n\t\t\t\t// System.out.println(\"sdsdsd\");\n\t\t\t\t// }\n\t\t\t\titr = getSprintWiseStories(projectName, allMetricsOfComponet, transitionList, tempAuthorList);\n\t\t\t\tc = new ComponentSprintWiseStories();\n\t\t\t\tc.setComponent(component);\n\t\t\t\tc.setIterationOutModel(itr);\n\t\t\t\tresponse.add(c);\n\n\t\t\t}\n\t\t}\n\t\treturn response;\n\t}\n\n\t@Override\n\tpublic Map<String, List> getIssueHierarchy(String projectName) {\n\t\t\n\n\t\tList<MetricsModel> allMetrics = metricRepo.findByPName(projectName);\n\t\tList<IterationOutModel> authorList = authorRepo.findByPName(projectName);\n\t\tArrayList<String> componentList = getComponentList(authorList);\n\n\t\tMap<String, List> resp = new IssuesHierarchy().getHierarchyData(allMetrics);\n\t\treturn resp;\n\t}\n\n\tpublic ArrayList<String> getComponentList(List<IterationOutModel> authorList) {\n\n\t\tArrayList<String> componentList = new ArrayList<String>();\n\t\tfor (IterationOutModel auth : authorList) {\n\n\t\t\tif (auth.getMetrics() != null) {\n\t\t\t\tfor (MonogOutMetrics m : auth.getMetrics()) {\n\t\t\t\t\tif (m.getComponents() != null && m.getComponents().get(0) != null) {\n\t\t\t\t\t\tString component = m.getComponents().get(0);\n\t\t\t\t\t\tif (componentList.indexOf(component) < 0) {\n\t\t\t\t\t\t\tcomponentList.add(component);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn componentList;\n\t}\n\n\t@Override\n\tpublic Map<String, Map> getComponentWiseIssueHierarchy(String projectName) {\n\n\t\tList<IterationOutModel> authorList = authorRepo.findByPName(projectName);\n\t\tList<MetricsModel> metrics = metricRepo.findByPName(projectName);\n\t\tArrayList<String> componentList = getComponentList(authorList);\n\n\t\tMap<String, Map> response = new HashMap<String, Map>();\n\n\t\tMap<String, List> resp = new IssuesHierarchy().getHierarchyData(metrics);\n\t\tresponse.put(\"All\", resp);\n\n\t\tcomponentList.forEach(component -> {\n\t\t\tList<MetricsModel> filteredMetrics = metrics.stream().filter(m -> {\n\t\t\t\tif (m != null && m.getComponents() != null && m.getComponents().size() > 0\n\t\t\t\t\t\t&& m.getComponents().get(0).equalsIgnoreCase(component)) {\n\t\t\t\t\treturn true;\n\t\t\t\t} else {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}).collect(Collectors.toList());\n\t\t\tMap<String, List> resp1 = new IssuesHierarchy().getHierarchyData(filteredMetrics);\n\t\t\tresponse.put(component, resp1);\n\n\t\t});\n\n\t\treturn response;\n\t}\n\n\t@Override\n\tpublic List<String> getComponents(String projectName, boolean b) {\n\t\tList<IterationOutModel> authorList = authorRepo.findByPName(projectName);\n\t\tList<String> copmonents = getComponentList(authorList);\n\t\tCollections.sort(copmonents);\n\t\treturn copmonents;\n\t}\n\n\tpublic List<IterationOutModel> getSprintWiseStories(String projectName, List<MetricsModel> metricList,\n\t\t\tList<TransitionModel> transitionList, List<IterationOutModel> sprints) {\n\n\t\talmConfig = almConfigRepo.findByProjectName(projectName).get(0);\n\t\tList<String> closestates = Arrays.asList(almConfig.getCloseState());\n\n\t\tMap<String, List<TransitionModel>> transitionGrouped = new HashMap<>();\n\n\t\tfor (TransitionModel p : transitionList) {\n\t\t\tif (!transitionGrouped.containsKey(p.getwId())) {\n\t\t\t\ttransitionGrouped.put(p.getwId(), new ArrayList<>());\n\t\t\t}\n\t\t\ttransitionGrouped.get(p.getwId()).add(p);\n\t\t}\n\n\t\tIterator<MetricsModel> metricitr = metricList.iterator();\n\t\tList<MonogOutMetrics> aggregatedMetricList = new ArrayList<MonogOutMetrics>();\n\t\twhile (metricitr.hasNext()) {\n\t\t\tMetricsModel m = metricitr.next();\n\t\t\tMonogOutMetrics agr = new MonogOutMetrics();\n\t\t\tBeanUtils.copyProperties(m, agr);\n\t\t\tagr.setTransitions(transitionGrouped.get(m.getwId()));\n\t\t\taggregatedMetricList.add(agr);\n\n\t\t}\n\n\t\tList<IterationOutModel> sprintwise = new ArrayList<IterationOutModel>();\n\n\t\tListIterator<IterationOutModel> spIterator = sprints.listIterator();\n\t\tArrayList<String> wIdArr = new ArrayList<String>();\n\t\twhile (spIterator.hasNext()) {\n\n\t\t\tint tempStory = 0;\n\t\t\tIterationOutModel sprint = spIterator.next();\n\t\t\tIterationOutModel sprintAndstories = new IterationOutModel();\n\t\t\tList<MonogOutMetrics> spStories = new ArrayList<MonogOutMetrics>();\n\t\t\tif (null != sprint.getStDate() && sprint.getStDate() != 0 && sprint.getsId() != 0) {\n\n\t\t\t\tsprintAndstories.setsName(sprint.getsName());\n\t\t\t\tsprintAndstories.setsId(sprint.getsId());\n\t\t\t\tsprintAndstories.setStDate(sprint.getStDate());\n\n\t\t\t\tif (0 != sprint.getCompletedDate()) {\n\t\t\t\t\tsprintAndstories.setEndDate(sprint.getCompletedDate());\n\t\t\t\t} else {\n\t\t\t\t\tsprintAndstories.setEndDate(sprint.getEndDate());\n\t\t\t\t}\n\n\t\t\t\t// if (sprint.getsName().equalsIgnoreCase(\"NP PI-2.1\")) {\n\t\t\t\t// System.out.println(\"dsfsdfsd\");\n\t\t\t\t// }\n\t\t\t\tIterator<MonogOutMetrics> stIterator = aggregatedMetricList.iterator();\n\t\t\t\twhile (stIterator.hasNext()) {\n\t\t\t\t\tMonogOutMetrics story = stIterator.next();\n\n\t\t\t\t\tlong endDate;\n\t\t\t\t\tif (sprint.getState().toLowerCase().contains(ConstantVariable.lowerCaseActiveConst)) {\n\t\t\t\t\t\tendDate = new Date().getTime();\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\tendDate = sprint.getEndDate();\n\n\t\t\t\t\t}\n\t\t\t\t\t// if (story.getwId().equalsIgnoreCase(\"NTSBW-875\")) {\n\t\t\t\t\t// System.out.println(story.getwId());\n\t\t\t\t\t// }\n\t\t\t\t\tif (null != story.getAllocatedDate()\n\t\t\t\t\t\t\t&& story.getType().toLowerCase().equals(almConfig.getStoryName().toLowerCase())) {\n\t\t\t\t\t\tArrayList<Long> dates = new ArrayList<Long>(story.getAllocatedDate().keySet());\n\t\t\t\t\t\tArrayList<String> values = new ArrayList<String>(story.getAllocatedDate().values());\n\t\t\t\t\t\tfor (int m = dates.size() - 1; m >= 0; m--) {\n\t\t\t\t\t\t\tif (values.get(m).contains(sprint.getsId() + \"\")) {\n\t\t\t\t\t\t\t\tif (m == dates.size() - 1 && dates.get(m) < sprint.getStDate()) {\n\t\t\t\t\t\t\t\t\tMonogOutMetrics s = new MonogOutMetrics();\n\t\t\t\t\t\t\t\t\tBeanUtils.copyProperties(story, s);\n\t\t\t\t\t\t\t\t\tspStories.add(s);\n\t\t\t\t\t\t\t\t} else if (dates.get(m) < sprint.getStDate() && dates.get(m + 1) > sprint.getStDate()\n\t\t\t\t\t\t\t\t\t\t&& dates.get(m + 1) > endDate) {\n\t\t\t\t\t\t\t\t\tMonogOutMetrics s = new MonogOutMetrics();\n\t\t\t\t\t\t\t\t\tspStories.add(s);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tlong completedDate;\n\t\t\t\t\t\tif (sprint.getState().toLowerCase().contains(ConstantVariable.lowerCaseActiveConst)) {\n\t\t\t\t\t\t\tcompletedDate = new Date().getTime();\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tif (sprint.getCompletedDate() != 0) {\n\t\t\t\t\t\t\t\tcompletedDate = sprint.getCompletedDate();\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tcompletedDate = sprint.getEndDate();\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (null != story.getAllocatedDate() && wIdArr.indexOf(story.getwId()) <= -1) {\n\n\t\t\t\t\t\t\tfor (int i = 0; i < dates.size(); i++) {\n\t\t\t\t\t\t\t\tList<String> dateMapValues = Arrays.asList(values.get(i).split(\",\"));\n\t\t\t\t\t\t\t\tif (i == dates.size() - 1) {\n\t\t\t\t\t\t\t\t\tif (dates.get(i) < completedDate\n\t\t\t\t\t\t\t\t\t\t\t&& dateMapValues.indexOf(sprint.getsId() + \"\") > -1) {\n\t\t\t\t\t\t\t\t\t\tString wid = story.getwId();\n\t\t\t\t\t\t\t\t\t\tList<TransitionModel> tr = story.getTransitions();\n\t\t\t\t\t\t\t\t\t\tif (tr != null && tr.size() > 0) {\n\t\t\t\t\t\t\t\t\t\t\tCollections.sort(tr, new TransitionComparator());\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tTransitionModel trans = filterTrans(story.getTransitions(), completedDate);\n\t\t\t\t\t\t\t\t\t\tif (closestates.indexOf(trans.getCrState()) > -1\n\t\t\t\t\t\t\t\t\t\t\t\t&& trans.getMdfDate() < completedDate\n\t\t\t\t\t\t\t\t\t\t\t\t&& trans.getMdfDate() > sprint.getStDate()\n\t\t\t\t\t\t\t\t\t\t\t\t&& wIdArr.indexOf(story.getwId()) <= -1) {\n\t\t\t\t\t\t\t\t\t\t\ttempStory++;\n\t\t\t\t\t\t\t\t\t\t\twIdArr.add(story.getwId());\n\t\t\t\t\t\t\t\t\t\t\t// if(sprint.getsId() == 351) {\n\t\t\t\t\t\t\t\t\t\t\t// System.out.println(story.getwId());\n\t\t\t\t\t\t\t\t\t\t\t// }\n\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tif (dates.get(i) < completedDate && dates.get(i + 1) > completedDate\n\t\t\t\t\t\t\t\t\t\t\t&& dateMapValues.indexOf(String.valueOf(sprint.getsId())) > -1) {\n\t\t\t\t\t\t\t\t\t\tString wid = story.getwId();\n\t\t\t\t\t\t\t\t\t\tList<TransitionModel> tr = story.getTransitions();\n\t\t\t\t\t\t\t\t\t\tif (tr != null && tr.size() > 0) {\n\t\t\t\t\t\t\t\t\t\t\tCollections.sort(tr, new TransitionComparator());\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\tTransitionModel trans = filterTrans(story.getTransitions(), completedDate);\n\t\t\t\t\t\t\t\t\t\tif (closestates.indexOf(trans.getCrState()) > -1\n\t\t\t\t\t\t\t\t\t\t\t\t&& trans.getMdfDate() < completedDate\n\t\t\t\t\t\t\t\t\t\t\t\t&& trans.getMdfDate() > sprint.getStDate()\n\t\t\t\t\t\t\t\t\t\t\t\t&& wIdArr.indexOf(story.getwId()) <= -1) {\n\t\t\t\t\t\t\t\t\t\t\ttempStory++;\n\t\t\t\t\t\t\t\t\t\t\twIdArr.add(story.getwId());\n\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\t\t\tsprintAndstories.setMetrics(spStories);\n\t\t\tsprintAndstories.setClosedStories(tempStory);\n\t\t\tsprintwise.add(sprintAndstories);\n\n\t\t}\n\t\treturn sprintwise;\n\t}\n\n\tTransitionModel filterTrans(List<TransitionModel> tr, long date) {\n\t\tList<TransitionModel> newTr = new ArrayList<TransitionModel>();\n\t\tif (null != tr) {\n\t\t\tfor (int i = 0; i < tr.size(); i++) {\n\t\t\t\tif (tr.get(i).getMdfDate() < date) {\n\t\t\t\t\tnewTr.add(tr.get(i));\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (newTr.size() > 0) {\n\t\t\treturn newTr.get(newTr.size() - 1);\n\t\t} else {\n\t\t\treturn new TransitionModel();\n\t\t}\n\t}\n\n\tpublic List<ScoreCardSprintData> getVelocityChart(List<IterationOutModel> authorList, String projectName) {\n\n\t\tList<ALMConfiguration> almConfigurations = almConfigRepo.findByProjectName(projectName);\n\t\tVelocityCalculations velocity = new VelocityCalculations();\n\t\treturn velocity.calcVelocity(authorList, almConfigurations.get(0));\n\n\t}\n\n\tpublic double callSP(String flag, IterationOutModel sp) {\n\t\tdouble tempSp = 0;\n\t\ttempSpRefined = 0;\n\t\ttempSpRemoved = 0;\n\t\tissueList = new ArrayList<IssueList>();\n\n\t\tlong date;\n\t\tif (flag.equals(\"start\")) {\n\t\t\tdate = sp.getStDate();\n\t\t} else {\n\t\t\tif (sp.getState().toLowerCase().contains(ConstantVariable.lowerCaseActiveConst)) {\n\t\t\t\tdate = new Date().getTime();\n\t\t\t} else {\n\n\t\t\t\tdate = sp.getEndDate();\n\n\t\t\t}\n\n\t\t}\n\t\t// if (sp.getsName().equals(\"NP PI-1.6\") ) {\n\t\t// System.out.println(\"dsfsdfsd\");\n\t\t// }\n\n\t\tfor (IterationOutModel it : workingBacklog) {\n\n\t\t\tArrayList<String> keys, dateMapValues;\n\t\t\tArrayList<Long> dateMapKeys, storyPointMapKeys;\n\t\t\tMap storyPointMap;\n\t\t\tMap<Long, String> dateMap;\n\t\t\tif (it.getMetrics() != null)\n\t\t\t\tfor (MonogOutMetrics ele : it.getMetrics()) {\n\t\t\t\t\tdateMap = ele.getAllocatedDate();\n\t\t\t\t\tdateMapKeys = new ArrayList<Long>(ele.getAllocatedDate().keySet());\n\t\t\t\t\tCollections.sort(dateMapKeys);\n\t\t\t\t\t// if (ele.getwId().equals(\"NTSBW-305\")) {\n\t\t\t\t\t// System.out.println(\"dfdf\");\n\t\t\t\t\t// }\n\n\t\t\t\t\tif (ele.getAllocatedDate() != null && (velocityFields.indexOf(ele.getType()) > -1)\n\t\t\t\t\t\t\t&& ele.getStoryPoints() != null && (vlist.workItemArr.indexOf(ele.getwId()) < 0)) {\n\n\t\t\t\t\t\tstoryPointMapKeys = new ArrayList<Long>(ele.getStoryPoints().keySet());\n\t\t\t\t\t\tstoryPointMap = ele.getStoryPoints();\n\t\t\t\t\t\tCollections.sort(storyPointMapKeys);\n\t\t\t\t\t\tfor (int i = 0; i < dateMapKeys.size(); i++) {\n\t\t\t\t\t\t\tList<String> dateValues = Arrays.asList(dateMap.get(dateMapKeys.get(i)).split(\",\"));\n\t\t\t\t\t\t\tif (i == dateMapKeys.size() - 1 && dateMapKeys.get(i) < date\n\t\t\t\t\t\t\t\t\t&& (dateValues.contains(String.valueOf(sp.getsId())))) {\n\t\t\t\t\t\t\t\ttempSp = tempSp\n\t\t\t\t\t\t\t\t\t\t+ storyLoop(storyPointMapKeys, sp, storyPointMap, issueList, ele, date, false);\n\t\t\t\t\t\t\t\tstoryLoopRefined(true, storyPointMapKeys, sp, storyPointMap, issueList, ele, date);\n\t\t\t\t\t\t\t} else if ((dateMapKeys.get(i) < date) && (dateValues.contains(String.valueOf(sp.getsId())))\n\t\t\t\t\t\t\t\t\t&& (dateMapKeys.get(i + 1) > date)) {\n\t\t\t\t\t\t\t\ttempSp = tempSp\n\t\t\t\t\t\t\t\t\t\t+ storyLoop(storyPointMapKeys, sp, storyPointMap, issueList, ele, date, false);\n\t\t\t\t\t\t\t\tstoryLoopRefined(false, storyPointMapKeys, sp, storyPointMap, issueList, ele, date);\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t}\n\t\treturn tempSp;\n\t}\n\n\tpublic double calcClosedSP(IterationOutModel sp) {\n\t\tdouble tempSp = 0;\n\t\tissueList = new ArrayList<IssueList>();\n\n\t\tlong date;\n\n\t\tif (sp.getState().toLowerCase().contains(ConstantVariable.lowerCaseActiveConst)) {\n\t\t\tdate = new Date().getTime();\n\t\t} else {\n\t\t\tif (sp.getCompletedDate() != 0) {\n\t\t\t\tdate = sp.getCompletedDate();\n\t\t\t} else {\n\t\t\t\tdate = sp.getEndDate();\n\t\t\t}\n\t\t}\n\n\t\twIdArr = new ArrayList<String>();\n\t\tfor (IterationOutModel it : workingBacklog) {\n\n\t\t\tArrayList<String> keys;\n\t\t\tArrayList<Long> dateMapKeys, storyPointMapKeys;\n\t\t\tMap storyPointMap;\n\t\t\tMap<Long, String> dateMap;\n\t\t\tif (it.getMetrics() != null)\n\t\t\t\tfor (MonogOutMetrics ele : it.getMetrics()) {\n\t\t\t\t\tdateMap = ele.getAllocatedDate();\n\t\t\t\t\tdateMapKeys = new ArrayList<Long>(ele.getAllocatedDate().keySet());\n\t\t\t\t\tCollections.sort(dateMapKeys);\n\t\t\t\t\tif (ele.getsId() != 0) {\n\t\t\t\t\t\t// if(ele.getwId().equals(\"SBW-792\")) {\n\t\t\t\t\t\t// System.out.println(\"dsdsd\");\n\t\t\t\t\t\t// }\n\t\t\t\t\t\tif (ele.getAllocatedDate() != null && (velocityFields.indexOf(ele.getType()) > -1)\n\t\t\t\t\t\t\t\t&& ele.getStoryPoints() != null) {\n\n\t\t\t\t\t\t\tstoryPointMapKeys = new ArrayList<Long>(ele.getStoryPoints().keySet());\n\t\t\t\t\t\t\tstoryPointMap = ele.getStoryPoints();\n\n\t\t\t\t\t\t\tCollections.sort(storyPointMapKeys);\n\n\t\t\t\t\t\t\tfor (int i = 0; i < dateMapKeys.size(); i++) {\n\t\t\t\t\t\t\t\tList<String> dateValues = Arrays.asList(dateMap.get(dateMapKeys.get(i)).split(\",\"));\n\t\t\t\t\t\t\t\tif (i == dateMapKeys.size() - 1 && dateMapKeys.get(i) < date\n\t\t\t\t\t\t\t\t\t\t&& dateValues.contains(String.valueOf(sp.getsId()))) {\n\t\t\t\t\t\t\t\t\tString wid = ele.getwId();\n\t\t\t\t\t\t\t\t\tTransitionModel trans = filterTrans(ele.getTransitions(), date);\n\t\t\t\t\t\t\t\t\tif (closestates.indexOf(trans.getCrState()) > -1 && trans.getMdfDate() < date\n\t\t\t\t\t\t\t\t\t\t\t&& trans.getMdfDate() > sp.getStDate()\n\t\t\t\t\t\t\t\t\t\t\t&& trans.getMdfDate() > dateMapKeys.get(i)\n\t\t\t\t\t\t\t\t\t\t\t&& wIdArr.indexOf(ele.getwId()) <= -1) {\n\t\t\t\t\t\t\t\t\t\twIdArr.add(ele.getwId());\n\t\t\t\t\t\t\t\t\t\ttempSp = tempSp + storyLoop(storyPointMapKeys, sp, storyPointMap, issueList,\n\t\t\t\t\t\t\t\t\t\t\t\tele, date, true);\n\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t} else if (dateMapKeys.get(i) < date && dateValues.indexOf(sp.getsId() + \"\") > -1\n\t\t\t\t\t\t\t\t\t\t&& dateMapKeys.get(i + 1) > date) {\n\t\t\t\t\t\t\t\t\tString wid = ele.getwId();\n\t\t\t\t\t\t\t\t\tTransitionModel trans = filterTrans(ele.getTransitions(), date);\n\t\t\t\t\t\t\t\t\tif (closestates.indexOf(trans.getCrState()) > -1 && trans.getMdfDate() < date\n\t\t\t\t\t\t\t\t\t\t\t&& trans.getMdfDate() > sp.getStDate()\n\t\t\t\t\t\t\t\t\t\t\t&& trans.getMdfDate() > dateMapKeys.get(i)\n\t\t\t\t\t\t\t\t\t\t\t&& wIdArr.indexOf(ele.getwId()) <= -1) {\n\n\t\t\t\t\t\t\t\t\t\twIdArr.add(ele.getwId());\n\t\t\t\t\t\t\t\t\t\ttempSp = tempSp + storyLoop(storyPointMapKeys, sp, storyPointMap, issueList,\n\t\t\t\t\t\t\t\t\t\t\t\tele, date, true);\n\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t}\n\t\treturn tempSp;\n\t}\n\n\tdouble storyLoop(List<Long> keys, IterationOutModel it, Map story, List<IssueList> issueList, MonogOutMetrics m,\n\t\t\tlong date, boolean colsedflag) {\n\t\tdouble temp = 0;\n\t\tfor (int i = 0; i < keys.size(); i++) {\n\t\t\tif (keys.get(i) <= date && i == keys.size() - 1) {\n\t\t\t\ttemp = temp + (double) story.get(keys.get(i));\n\t\t\t\tif (m.getType() != null && m.getType().equalsIgnoreCase(almConfig.getStoryName()) && colsedflag) {\n\t\t\t\t\tstoriesCompleted++;\n\t\t\t\t}\n\t\t\t\tif (m.getType() != null && m.getType().equalsIgnoreCase(almConfig.getDefectName()) && colsedflag) {\n\t\t\t\t\tdefetcsCompleted++;\n\t\t\t\t}\n\t\t\t\tIssueList iss = new IssueList();\n\t\t\t\tiss.setStoryPoints((double) story.get(keys.get(i)));\n\t\t\t\tiss.setwId(m.getwId());\n\t\t\t\tiss.setAssignee(m.getAssgnTo());\n\t\t\t\tiss.setState(m.getState());\n\t\t\t\tiss.setType(m.getType());\n\t\t\t\tiss.setSortId(m.getwId().split(\"-\")[1]);\n\t\t\t\tissueList.add(iss);\n\t\t\t\tvlist.workItemArr.add(m.getwId());\n\t\t\t} else if (keys.get(i) <= date && keys.get(i + 1) > date) {\n\t\t\t\ttemp = temp + (double) story.get(keys.get(i));\n\t\t\t\tif (m.getType() != null && m.getType().equalsIgnoreCase(almConfig.getStoryName()) && colsedflag) {\n\t\t\t\t\tstoriesCompleted++;\n\t\t\t\t}\n\t\t\t\tif (m.getType() != null && m.getType().equalsIgnoreCase(almConfig.getDefectName()) && colsedflag) {\n\t\t\t\t\tdefetcsCompleted++;\n\t\t\t\t}\n\t\t\t\tIssueList iss = new IssueList();\n\t\t\t\tiss.setStoryPoints((double) story.get(keys.get(i)));\n\t\t\t\tiss.setwId(m.getwId());\n\t\t\t\tiss.setAssignee(m.getAssgnTo());\n\t\t\t\tiss.setState(m.getState());\n\t\t\t\tiss.setType(m.getType());\n\t\t\t\tiss.setSortId(m.getwId().split(\"-\")[1]);\n\t\t\t\tissueList.add(iss);\n\t\t\t\tvlist.workItemArr.add(m.getwId());\n\t\t\t}\n\t\t}\n\t\treturn temp;\n\t}\n\n\tdouble storyLoopRefined(boolean isRefined, List<Long> keys, IterationOutModel it, Map story,\n\t\t\tList<IssueList> issueList, MonogOutMetrics m, long date) {\n\t\tdouble temp = 0;\n\n\t\tList<TransitionModel> tr = m.getTransitions();\n\t\tif (tr != null) {\n\t\t\tCollections.sort(tr, new TransitionComparator());\n\t\t}\n\n\t\tfor (int i = 0; i < keys.size(); i++) {\n\t\t\tif (keys.get(i) <= date && i == keys.size() - 1) {\n\n\t\t\t\tif (tr != null && tr.size() > 0) {\n\n\t\t\t\t\tif (!tr.get(tr.size() - 1).getCrState().toUpperCase().equals(ConstantVariable.withDrawnConst) && isRefined) {\n\t\t\t\t\t\ttempSpRefined += (double) story.get(keys.get(i));\n\t\t\t\t\t\tif (m.getType().equals(almConfig.getStoryName())) {\n\t\t\t\t\t\t\tfinalStoriesCommited++;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tIssueList issue = new IssueList();\n\t\t\t\t\t\tissue.setStoryPoints((double) story.get(keys.get(i)));\n\t\t\t\t\t\tissue.setwId(m.getwId());\n\t\t\t\t\t\tissue.setAssignee(m.getAssgnTo());\n\t\t\t\t\t\tissue.setState(m.getState());\n\t\t\t\t\t\tissue.setType(m.getType());\n\t\t\t\t\t\tissue.setSortId(m.getwId().split(\"-\")[1]);\n\t\t\t\t\t\trefinedIssuList.add(issue);\n\t\t\t\t\t\tvlist.workItemArr.add(m.getwId());\n\t\t\t\t\t} else {\n\t\t\t\t\t\ttempSpRemoved += (double) story.get(keys.get(i));\n\t\t\t\t\t\tif (m.getType().equals(almConfig.getStoryName())) {\n\t\t\t\t\t\t\tfinalStoriesCommited++;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tIssueList issue = new IssueList();\n\t\t\t\t\t\tissue.setStoryPoints((double) story.get(keys.get(i)));\n\t\t\t\t\t\tissue.setwId(m.getwId());\n\t\t\t\t\t\tissue.setAssignee(m.getAssgnTo());\n\t\t\t\t\t\tissue.setState(m.getState());\n\t\t\t\t\t\tissue.setType(m.getType());\n\t\t\t\t\t\tissue.setSortId(m.getwId().split(\"-\")[1]);\n\t\t\t\t\t\tremovedIssuList.add(issue);\n\t\t\t\t\t\tvlist.workItemArr.add(m.getwId());\n\t\t\t\t\t}\n\t\t\t\t} else if (m.getsName() != null && vlist.activeSprint != null\n\t\t\t\t\t\t&& vlist.activeSprint.equals(m.getsName())) {\n\t\t\t\t\ttempSpRefined += (double) story.get(keys.get(i));\n\t\t\t\t\tif (m.getType().equals(almConfig.getStoryName())) {\n\t\t\t\t\t\tfinalStoriesCommited++;\n\t\t\t\t\t}\n\t\t\t\t\tIssueList issue = new IssueList();\n\t\t\t\t\tissue.setStoryPoints((double) story.get(keys.get(i)));\n\t\t\t\t\tissue.setwId(m.getwId());\n\t\t\t\t\tissue.setAssignee(m.getAssgnTo());\n\t\t\t\t\tissue.setState(m.getState());\n\t\t\t\t\tissue.setType(m.getType());\n\t\t\t\t\tissue.setSortId(m.getwId().split(\"-\")[1]);\n\t\t\t\t\trefinedIssuList.add(issue);\n\t\t\t\t\tvlist.workItemArr.add(m.getwId());\n\t\t\t\t} else {\n\t\t\t\t\ttempSpRemoved += (double) story.get(keys.get(i));\n\t\t\t\t\tIssueList issue = new IssueList();\n\t\t\t\t\tissue.setStoryPoints((double) story.get(keys.get(i)));\n\t\t\t\t\tissue.setwId(m.getwId());\n\t\t\t\t\tissue.setAssignee(m.getAssgnTo());\n\t\t\t\t\tissue.setState(m.getState());\n\t\t\t\t\tissue.setType(m.getType());\n\t\t\t\t\tissue.setSortId(m.getwId().split(\"-\")[1]);\n\t\t\t\t\tremovedIssuList.add(issue);\n\t\t\t\t\tvlist.workItemArr.add(m.getwId());\n\t\t\t\t}\n\t\t\t} else if (keys.get(i) <= date && keys.get(i + 1) > date) {\n\t\t\t\tif (tr != null && tr.size() > 0) {\n\n\t\t\t\t\tif (!tr.get(tr.size() - 1).getCrState().toUpperCase().equals(ConstantVariable.withDrawnConst)\n\t\t\t\t\t\t\t&& !m.getState().toUpperCase().equals(ConstantVariable.withDrawnConst) && isRefined) {\n\t\t\t\t\t\ttempSpRefined += (double) story.get(keys.get(i));\n\t\t\t\t\t\tif (m.getType().equals(almConfig.getStoryName())) {\n\t\t\t\t\t\t\tfinalStoriesCommited++;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tIssueList issue = new IssueList();\n\t\t\t\t\t\tissue.setStoryPoints((double) story.get(keys.get(i)));\n\t\t\t\t\t\tissue.setwId(m.getwId());\n\t\t\t\t\t\tissue.setAssignee(m.getAssgnTo());\n\t\t\t\t\t\tissue.setState(m.getState());\n\t\t\t\t\t\tissue.setType(m.getType());\n\t\t\t\t\t\tissue.setSortId(m.getwId().split(\"-\")[1]);\n\t\t\t\t\t\trefinedIssuList.add(issue);\n\t\t\t\t\t\tvlist.workItemArr.add(m.getwId());\n\t\t\t\t\t} else {\n\t\t\t\t\t\ttempSpRemoved += (double) story.get(keys.get(i));\n\t\t\t\t\t\tIssueList issue = new IssueList();\n\t\t\t\t\t\tissue.setStoryPoints((double) story.get(keys.get(i)));\n\t\t\t\t\t\tissue.setwId(m.getwId());\n\t\t\t\t\t\tissue.setAssignee(m.getAssgnTo());\n\t\t\t\t\t\tissue.setState(m.getState());\n\t\t\t\t\t\tissue.setType(m.getType());\n\t\t\t\t\t\tissue.setSortId(m.getwId().split(\"-\")[1]);\n\t\t\t\t\t\tremovedIssuList.add(issue);\n\t\t\t\t\t\tvlist.workItemArr.add(m.getwId());\n\t\t\t\t\t}\n\t\t\t\t} else if (vlist.activeSprint.equals(m.getsName())) {\n\t\t\t\t\ttempSpRefined += (double) story.get(keys.get(i));\n\t\t\t\t\tif (m.getType().equals(almConfig.getStoryName())) {\n\t\t\t\t\t\tfinalStoriesCommited++;\n\t\t\t\t\t}\n\t\t\t\t\tIssueList issue = new IssueList();\n\t\t\t\t\tissue.setStoryPoints((double) story.get(keys.get(i)));\n\t\t\t\t\tissue.setwId(m.getwId());\n\t\t\t\t\tissue.setAssignee(m.getAssgnTo());\n\t\t\t\t\tissue.setState(m.getState());\n\t\t\t\t\tissue.setType(m.getType());\n\t\t\t\t\tissue.setSortId(m.getwId().split(\"-\")[1]);\n\t\t\t\t\trefinedIssuList.add(issue);\n\t\t\t\t\tvlist.workItemArr.add(m.getwId());\n\t\t\t\t} else {\n\t\t\t\t\ttempSpRemoved += (double) story.get(keys.get(i));\n\t\t\t\t\tIssueList issue = new IssueList();\n\t\t\t\t\tissue.setStoryPoints((double) story.get(keys.get(i)));\n\t\t\t\t\tissue.setwId(m.getwId());\n\t\t\t\t\tissue.setAssignee(m.getAssgnTo());\n\t\t\t\t\tissue.setState(m.getState());\n\t\t\t\t\tissue.setType(m.getType());\n\t\t\t\t\tissue.setSortId(m.getwId().split(\"-\")[1]);\n\t\t\t\t\tremovedIssuList.add(issue);\n\t\t\t\t\tvlist.workItemArr.add(m.getwId());\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn temp;\n\t}\n\n\tclass TransitionComparator implements Comparator {\n\n\t\t@Override\n\t\tpublic int compare(Object o1, Object o2) {\n\t\t\tTransitionModel t1 = (TransitionModel) o1;\n\t\t\tTransitionModel t2 = (TransitionModel) o2;\n\n\t\t\tif (t1.getMdfDate() != null && t2.getMdfDate() != null) {\n\t\t\t\tif (t1.getMdfDate().equals(t2.getMdfDate()))\n\t\t\t\t\treturn 0;\n\t\t\t\telse if (t1.getMdfDate() > t2.getMdfDate())\n\t\t\t\t\treturn 1;\n\t\t\t\telse\n\t\t\t\t\treturn -1;\n\t\t\t} else if (t1.getMdfDate() == null) {\n\t\t\t\treturn 1;\n\t\t\t} else {\n\t\t\t\treturn -1;\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\tclass SprintComparatort implements Comparator {\n\n\t\t@Override\n\t\tpublic int compare(Object o1, Object o2) {\n\t\t\tIterationOutModel s1 = (IterationOutModel) o1;\n\t\t\tIterationOutModel s2 = (IterationOutModel) o2;\n\n\t\t\tif (s1.getStDate() != null && s2.getStDate() != null) {\n\t\t\t\tif (s1.getStDate().equals(s2.getStDate()))\n\t\t\t\t\treturn 0;\n\t\t\t\telse if (s1.getStDate() > s2.getStDate())\n\t\t\t\t\treturn 1;\n\t\t\t\telse\n\t\t\t\t\treturn -1;\n\t\t\t} else if (s1.getStDate() == null) {\n\t\t\t\treturn 1;\n\t\t\t} else {\n\t\t\t\treturn -1;\n\t\t\t}\n\n\t\t}\n\n\t}\n\t\n\t@Override\n//\t@CacheEvict(value=\"ALMService\", allEntries=true)\n\tpublic void updateComponentsOfTaskandSubtask(String projectName) {\n\t\tMongoAggregate mongoaggr = new MongoAggregate();\n\t\tmongoaggr.updateComponentForTaskAndSubtasks(projectName);\n\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getSprintProgressHome'+#projName +#almType\", cacheManager=\"timeoutCacheManager\")\n\tpublic List<Map<String, List<String>>> getSprintProgressHome(String projName, String almType) {\n\t\t\n\t\tProjectHomeCalculation projHomeCalc = new ProjectHomeCalculation();\n\t\tList<Map<String, List<String>>> result = projHomeCalc.getSprintProgressData(projName, almType);\n\t\treturn result;\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getDefectsSummaryHome'+#projName +#almType\", cacheManager=\"timeoutCacheManager\")\n\tpublic List<Map<String, String>> getDefectsSummaryHome(String projName, String almType) {\n\t\t\n\t\tProjectHomeCalculation projHomeCalc = new ProjectHomeCalculation();\n\t\tthis.almConfig = this.almConfigRepo.findByProjectName(projName).get(0);\n\t\tList<Map<String, String>> result = projHomeCalc.getDefectsSummaryData(projName, almType, this.almConfig);\n\t\treturn result;\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getTaskRisk'+#projName +#almType+#storyPointBased\", cacheManager=\"timeoutCacheManager\")\n\tpublic List<ComponentTaskRisk> getTaskRisk(String projName, String almType, boolean storyPointBased) {\n\t\t\n\t\tList<ComponentTaskRisk> result = new SprintProgress().getTaskRisk(projName, almType, storyPointBased);\n\t\treturn result;\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getActiveSprints'+#projName +#almType\", cacheManager=\"timeoutCacheManager\")\n\tpublic List<String> getActiveSprints(String projName, String almType) {\n\t\tQuery query = new Query();\n\t\tquery.addCriteria(Criteria.where(ConstantVariable.pNameConst).is(projName).and(ConstantVariable.pAlmTypeConst).is(almType).and(\"state\").in(ConstantVariable.activeStates));\n\t\tList<IterationModel> iterations = mongoTemplate.find(query, IterationModel.class, \"Iterations\");\n\t\tif (iterations.size() > 0) {\n\t\t\tList<String> response = new ArrayList<String>();\n\t\t\tIterator<IterationModel> itr = iterations.iterator();\n\t\t\twhile (itr.hasNext()) {\n\t\t\t\tresponse.add(itr.next().getsName());\n\t\t\t}\n\t\t\treturn response;\n\t\t}\n\t\treturn null;\n\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getIssueBrakeUp'+#projName +#almType\", cacheManager=\"timeoutCacheManager\")\n\tpublic List<ComponentIssueBreakup> getIssueBrakeUp(String projName, String almType) {\n\t\tSprintProgressCalculations spCalc = new SprintProgressCalculations();\n\t\tList<ComponentIssueBreakup> result = spCalc.getissueBreakup(projName, almType);\n\t\treturn result;\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getStoryProgress'+#projName +#almType\", cacheManager=\"timeoutCacheManager\")\n\tpublic List<ComponentStoryProgress> getStoryProgress(String projName, String almType) {\n\t\tSprintProgressCalculations spCalc = new SprintProgressCalculations();\n\t\tList<ComponentStoryProgress> result = spCalc.getStoryProgress(projName, almType);\n\t\treturn result;\n\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'burndownCalculation'+#projName +#almType+#storyPointBased\", cacheManager=\"timeoutCacheManager\")\n\tpublic List<ComponentBurnDown> burndownCalculation(String projName, String almType, boolean storyPointBased) {\n\t\t\n\t\tSprintProgress spCalc = new SprintProgress();\n\t\tList<ComponentBurnDown> result = spCalc.getBurndown(projName, almType, storyPointBased);\n\t\treturn result;\n\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getDefectInsightData'+#projName +#almType +#compFlag\", cacheManager=\"timeoutCacheManager\")\n\tpublic Map<String, List<DefectInsightData>> getDefectInsightData(String projName, String almType,boolean compFlag) {\n\t\t\n\t\tMap<String, List<DefectInsightData>> result = new DefectCalculations().calculateDefectInsightDataComponent(projName, almType,compFlag);\n\t\treturn result;\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'defectParetoCalculation'+#projName +#almType+#componentBased\", cacheManager=\"timeoutCacheManager\")\n\tpublic Map<String, List<DefectParetoModel>> defectParetoCalculation(String projName, String almType,\n\t\t\tboolean componentBased) {\n\t\tMap<String, List<DefectParetoModel>> result = new DefectCalculations().getParetoDataComp(projName, almType, componentBased);\n\t\treturn result;\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getProductionSlippage'+#projName +#almType+#componentBased\", cacheManager=\"timeoutCacheManager\")\n\tpublic Map<String, List<DefectProductionSlippage>> getProductionSlippage(String projName, String almType,\n\t\t\tboolean componentBased) {\n\t\tMap<String, List<DefectProductionSlippage>> result = new DefectCalculations().getDefectProducationSlippageComp(projName, almType, componentBased);\n\t\treturn result;\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getDefectDensity'+#projName +#almType+#componentBased\", cacheManager=\"timeoutCacheManager\")\n\tpublic Map<String, List<DefectDensity>> getDefectDensity(String projName, String almType, boolean componentBased) {\n\t\tMap<String, List<DefectDensity>> result = new DefectCalculations().getDefectDensityComp(projName, almType, componentBased);\n\t\treturn result;\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getDefectBacklog'+#projName +#almType+#componentBased\", cacheManager=\"timeoutCacheManager\")\n\tpublic Map<String, DefectBacklog> getDefectBacklog(String projName, String almType, boolean componentBased) {\n\n\t\tMap<String, DefectBacklog> result = new DefectCalculations().getDefectBacklogComponent(projName, almType, componentBased);\n\t\treturn result;\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getDefectTrendAndClassification'+#projName +#almType +#componentBased\", cacheManager=\"timeoutCacheManager\")\n\tpublic Map getDefectTrendAndClassification(String projName, String almType, boolean componentBased) {\n\t\t\n\t\tMap result = new DefectCalculations().componentSprintDefectTrend(projName, almType,componentBased);\n\t\treturn result;\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getDefectClassification'+#projName +#almType\", cacheManager=\"timeoutCacheManager\")\n\tpublic Map getDefectClassification(String projName, String almType) {\n\t\t\n\t\tMap result = new DefectCalculations().defectClassification(projName, almType);\n\t\treturn result;\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getStoryAgeingData'+#projName+#almType\", cacheManager=\"timeoutCacheManager\")\n\tpublic List<ComponentStoryAgeing> getStoryAgeingData(String projName, String almType) {\n\t\t\n\t\tList<ComponentStoryAgeing> result = new BacklogCalculation().caluclateStoryAgeing(projName, almType);\n\t\treturn result;\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getGroomingTable'+#projName+#almType\", cacheManager=\"timeoutCacheManager\")\n\tpublic  List<ComponentGroomingTable> getGroomingTable(String projName, String almType) {\n\t\tList<ComponentGroomingTable> result = new BacklogCalculation().calculateGroomingTable(projName, almType);\n\t\treturn result;\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getIterations'+#pName\", cacheManager=\"timeoutCacheManager\")\n\tpublic List<IterationModel> getAllIterations(String pName) {\n\t\t\n\t\tList<IterationModel> result = iterationRepo.findByPName(pName);\n\t\treturn result;\n\t}\n\n\tpublic String getAlmType(String pName) {\n\t\tString almType = \"\";\n\t\tConfigurationSetting config = DataConfig.getContext().getBean(ConfigurationSettingRep.class)\n\t\t\t\t.findByProjectName(pName).get(0);\n\t\tSet<ConfigurationToolInfoMetric> metric = config.getMetrics();\n\t\tIterator iter = metric.iterator();\n\t\twhile (iter.hasNext()) {\n\t\t\tObject configuration1 = iter.next();\n\t\t\tConfigurationToolInfoMetric metric1 = (ConfigurationToolInfoMetric) configuration1;\n//\t\t\tif (\"Jira\".equals(metric1.getToolName())\n//\t\t\t\t\t|| (\"Jira Defects\".equals(metric1.getToolName()) && metric1.getUrl() == \"\")) {\n//\t\t\t\talmType = \"JIRA\";\n//\t\t\t\tbreak;\n//\t\t\t} else if (\"Jira Defects\".equals(metric1.getToolName()) && metric1.getUrl() != \"\") {\n//\t\t\t\talmType = \"JIRA DEFECTS\";\n//\t\t\t\tbreak;\n//\t\t\t} else {\n//\t\t\t\talmType = \"TFS\";\n//\t\t\t}\n\t\t\tif (\"ALM\".equals(metric1.getToolType())) {\n\t\t\t\talmType = metric1.getToolName().toUpperCase();\n\t\t\t}\n\t\t}\n\t\treturn almType;\n\t}\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'saveEngScore'+#projectName+#month+#engScore\", cacheManager=\"timeoutCacheManager\")\n\tpublic String saveEngScore(String projectName, String month, double engScore) {\n\t\t\n\t\ttry {\n\t\t\tList<ProjectModel> proj=projectRepo.findByProjectName(projectName);\n            SortedMap<String,Double> engScrMap=proj.get(0).getEngScores();\n            if(engScrMap==null){\n\t           engScrMap=new TreeMap<String,Double>();\n                }\n\t\t\t     engScrMap.put(month, engScore);\n\t\t\t     proj.get(0).setEngScores(engScrMap);\n\t\t\t    projectRepo.save(proj);\n\t\t\t     return \"Success\";\n\t\t} catch (Exception e) {\n\t\t\tLOGGER.info(e);\n\t\t\treturn \"Failure\";\n\t\t}\n\t\t\n\t}\n\n\t@Override\n\t@Cacheable(value=\"ALMService\", key =\"'getComponentVelocity'+#projectName\", cacheManager=\"timeoutCacheManager\")\n\tpublic List<ComponentVelocityList> getComponentVelocityChart(String projectName, boolean b) {\n\t\tLOGGER.info(\"No cache\");\n\t\tList<ComponentVelocityList> result = chartService.getComponentVelocity(projectName,true);\n\t\treturn result;\n\t}\n\n\t@Override\n\t@Cacheable(value=\"ALMService\", key =\"'getComponentsSprintStories'+#projectName\", cacheManager=\"timeoutCacheManager\")\n\tpublic List<ComponentSprintWiseStories> getComponentsSprintStories(String projectName) {\n\t\tLOGGER.info(\"No cache\");\n\t\tList<ComponentSprintWiseStories> result = chartService.getComponentsSprintStories(projectName);\n\t\treturn result;\n\t}\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getIssueHierarchy'+#projectName\", cacheManager=\"timeoutCacheManager\")\n\tpublic Map<String, List> getIssueHierarchyChart(String projectName) {\n\t\t\n\t\tMap<String, List> result = chartService.getIssueHierarchy(projectName);\n\t\treturn result;\n\t}\n\n\t@Override\n\t@Cacheable(value=\"ALMService\", key =\"'getComponentWiseIssueHierarchy'+#projectName\", cacheManager=\"timeoutCacheManager\")\n\tpublic Map<String, Map> getComponentWiseIssueHierarchyChart(String projectName) {\n\t\tLOGGER.info(\"No cache\");\n\t\tMap<String, Map> result = chartService.getComponentWiseIssueHierarchy(projectName);\n\t\treturn result;\n\t}\n\n\n\t@Override\n@Cacheable(value=\"ALMService\", key =\"'getComponents'+#projectName\", cacheManager=\"timeoutCacheManager\")\n\tpublic List<String> getComponentsChart(String projectName) {\n\t\t\n\t\tList<String> result = chartService.getComponents(projectName);\n\t\treturn result;\n\t}\n\n}\n"}, "ConfigurationSettingService": {"fqcn": "com.bolt.dashboard.service.ConfigurationSettingService", "package": "com.bolt.dashboard.service", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\service\\ConfigurationSettingService.java", "imports": ["com.bolt.dashboard.core.model.ConfigurationSetting", "com.bolt.dashboard.response.DataResponse"], "endpoints": [], "db_entities": [], "source_code": "package com.bolt.dashboard.service;\n\nimport com.bolt.dashboard.core.model.ConfigurationSetting;\nimport com.bolt.dashboard.response.DataResponse;\n\npublic interface ConfigurationSettingService {\n    DataResponse<Iterable<ConfigurationSetting>> getConfig();\n\n    ConfigurationSetting addConfig(ConfigurationSetting req);\n\n    public int deleteConfig(ConfigurationSetting configurationSetting);\n\n\tboolean deleteAllCollections(String projectName);\n\n\tboolean deleteProject(String projectName);\n\n\t ConfigurationSetting getConfigProject(String pName);\n\n}\n"}, "ConfigurationSettingServiceImplementation": {"fqcn": "com.bolt.dashboard.service.ConfigurationSettingServiceImplementation", "package": "com.bolt.dashboard.service", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\service\\ConfigurationSettingServiceImplementation.java", "imports": ["java.util.Date", "java.util.List", "org.apache.logging.log4j.LogManager", "org.apache.logging.log4j.Logger", "org.springframework.beans.factory.annotation.Autowired", "org.springframework.stereotype.Service", "com.bolt.dashboard.core.ConstantVariable", "com.bolt.dashboard.core.model.BuildFailurePatternForProjectInJenkinsModel", "com.bolt.dashboard.core.model.ConfigurationSetting", "com.bolt.dashboard.core.model.ConfigurationToolInfoMetric", "com.bolt.dashboard.core.repository.ALMConfigRepo", "com.bolt.dashboard.core.repository.BuildFailurePatternForProjectRepo", "com.bolt.dashboard.core.repository.BuildToolRep", "com.bolt.dashboard.core.repository.ChartConfigRepo", "com.bolt.dashboard.core.repository.CodeCoverageRepository", "com.bolt.dashboard.core.repository.CodeQualityRep", "com.bolt.dashboard.core.repository.ConfigurationSettingRep", "com.bolt.dashboard.core.repository.GoalSettingRep", "com.bolt.dashboard.core.repository.HealthDataRepo", "com.bolt.dashboard.core.repository.PortfolioConfigRepo", "com.bolt.dashboard.core.repository.ProjectHealthRep", "com.bolt.dashboard.core.repository.SCMToolRepository", "com.bolt.dashboard.core.repository.UserAssociationRep", "com.bolt.dashboard.response.DataResponse", "com.bolt.dashboard.util.EncryptionDecryptionAES"], "endpoints": [], "db_entities": [], "source_code": "package com.bolt.dashboard.service;\n\nimport java.util.Date;\nimport java.util.List;\n\nimport org.apache.logging.log4j.LogManager;\nimport org.apache.logging.log4j.Logger;\nimport org.springframework.beans.factory.annotation.Autowired;\nimport org.springframework.stereotype.Service;\n\nimport com.bolt.dashboard.core.ConstantVariable;\nimport com.bolt.dashboard.core.model.BuildFailurePatternForProjectInJenkinsModel;\nimport com.bolt.dashboard.core.model.ConfigurationSetting;\nimport com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;\nimport com.bolt.dashboard.core.repository.ALMConfigRepo;\nimport com.bolt.dashboard.core.repository.BuildFailurePatternForProjectRepo;\nimport com.bolt.dashboard.core.repository.BuildToolRep;\nimport com.bolt.dashboard.core.repository.ChartConfigRepo;\nimport com.bolt.dashboard.core.repository.CodeCoverageRepository;\nimport com.bolt.dashboard.core.repository.CodeQualityRep;\nimport com.bolt.dashboard.core.repository.ConfigurationSettingRep;\nimport com.bolt.dashboard.core.repository.GoalSettingRep;\nimport com.bolt.dashboard.core.repository.HealthDataRepo;\nimport com.bolt.dashboard.core.repository.PortfolioConfigRepo;\nimport com.bolt.dashboard.core.repository.ProjectHealthRep;\nimport com.bolt.dashboard.core.repository.SCMToolRepository;\nimport com.bolt.dashboard.core.repository.UserAssociationRep;\nimport com.bolt.dashboard.response.DataResponse;\nimport com.bolt.dashboard.util.EncryptionDecryptionAES;\n\n@Service\npublic class ConfigurationSettingServiceImplementation implements ConfigurationSettingService {\n    private ConfigurationSettingRep configurationSettingRepository;\n    private AlmService almService;\n    private BuildToolRep buildRepo;\n    private BuildFailurePatternForProjectRepo buildFailurePatternRepo;\n    private CodeCoverageRepository codeCoverageRepo;\n    private CodeQualityRep codeQualityRep;\n    private HealthDataRepo healthRepo;\n    private SCMToolRepository scmRepo;\n    private ALMConfigRepo almConfigRepo;\n    private ChartConfigRepo chartConfigRepo;\n    private GoalSettingRep goalSettingRepo;\n    private PortfolioConfigRepo portfolioRepo;\n    private ProjectHealthRep projectHealthRepo;\n    private UserAssociationRep userAssociation;\n    @Autowired\n    public ConfigurationSettingServiceImplementation(ConfigurationSettingRep configurationSettingRepository,\n\t\t\tAlmService almService, BuildToolRep buildRepo, BuildFailurePatternForProjectRepo buildFailurePatternRepo,\n\t\t\tCodeCoverageRepository codeCoverageRepo, CodeQualityRep codeQualityRep, HealthDataRepo healthRepo,\n\t\t\tSCMToolRepository scmRepo, ALMConfigRepo almConfigRepo, ChartConfigRepo chartConfigRepo,\n\t\t\tGoalSettingRep goalSettingRepo, PortfolioConfigRepo portfolioRepo, ProjectHealthRep projectHealthRepo,\n\t\t\tUserAssociationRep userAssociation) {\n\t\tsuper();\n\t\tthis.configurationSettingRepository = configurationSettingRepository;\n\t\tthis.almService = almService;\n\t\tthis.buildRepo = buildRepo;\n\t\tthis.buildFailurePatternRepo = buildFailurePatternRepo;\n\t\tthis.codeCoverageRepo = codeCoverageRepo;\n\t\tthis.codeQualityRep = codeQualityRep;\n\t\tthis.healthRepo = healthRepo;\n\t\tthis.scmRepo = scmRepo;\n\t\tthis.almConfigRepo = almConfigRepo;\n\t\tthis.chartConfigRepo = chartConfigRepo;\n\t\tthis.goalSettingRepo = goalSettingRepo;\n\t\tthis.portfolioRepo = portfolioRepo;\n\t\tthis.projectHealthRepo = projectHealthRepo;\n\t\tthis.userAssociation = userAssociation;\n\t}\n\n\t\n    \n\tprivate static final Logger LOG = LogManager.getLogger(ConfigurationSettingServiceImplementation.class);\n\n    \n//    public ConfigurationSettingServiceImplementation(ConfigurationSettingRep configurationSettingRepository,AlmService almService,BuildToolRep buildRepo,\n//    \t\tBuildFailurePatternForProjectRepo buildFailurePatternRepo,CodeCoverageRepository codeCoverageRepo,\n//    \t\tCodeQualityRep codeQualityRep,HealthDataRepo healthRepo,\n//    \t\tSCMToolRepository scmRepo) {\n//        this.configurationSettingRepository = configurationSettingRepository;\n//    }\n\n    @Override\n//    @Cacheable(value=\"getConfig\", key =\"'getConfig'\", cacheManager=\"timeoutCacheManager\")\n    public DataResponse<Iterable<ConfigurationSetting>> getConfig() {\n        long lastUpdated = 1;\n        Iterable<ConfigurationSetting> result = configurationSettingRepository.findAll();\n\n        return new DataResponse<Iterable<ConfigurationSetting>>(result, lastUpdated);\n    }\n\n    @Override\n    public ConfigurationSetting addConfig(ConfigurationSetting req) {\n        Date date = new Date();\n        deleteConfig(req);\n        long timeStamp = date.getTime();\n        req.setTimestamp((long) timeStamp);\n        req.setProjectName(req.getProjectName());\n        if (configurationSettingRepository.findByProjectName(req.getProjectName()) != null) {\n            configurationSettingRepository.deleteByProjectName(req.getProjectName());\n        }\n        return configurationSettingRepository.save(req);\n    }\n\n    @Override\n    public int deleteConfig(ConfigurationSetting configurationSetting) {\n        LOG.info(\"deleted\");\n        return configurationSettingRepository.deleteByProjectName(configurationSetting.getProjectName());\n    }\n\n\t@Override\n\tpublic boolean deleteAllCollections(String projectName) {\n\t\t\n\t\talmService.delAllIssues(projectName);\n\t\tbuildRepo.delete(buildRepo.findByName(projectName));\n\t\tList<BuildFailurePatternForProjectInJenkinsModel> failurePattern=buildFailurePatternRepo.findByProjectName(projectName);\n\t\tif(!failurePattern.isEmpty()) {\n\t\t\tBuildFailurePatternForProjectInJenkinsModel failurePatternObj=failurePattern.get(0);\n\t\tfailurePatternObj.getPatternMetrics().forEach((value)->{\n\t\t\tvalue.setPatternCount(0);\n\t\t});\n\t\tbuildFailurePatternRepo.save(failurePatternObj);\n\t\t}\n\t\tcodeCoverageRepo.delete(codeCoverageRepo.findByProjectName(projectName));\n\t\tcodeQualityRep.delete(codeQualityRep.findByName(projectName));\n\t\thealthRepo.delete(healthRepo.findByProjectName(projectName));\n\t\tscmRepo.delete(scmRepo.findByProjectName(projectName));\n\t\treturn true;\n\t}\n\n\t@Override\n\tpublic boolean deleteProject(String projectName) {\n\t\t\n\t\tdeleteAllCollections(projectName);\n\t\talmConfigRepo.delete(almConfigRepo.findByProjectName(projectName));\n\t\tbuildFailurePatternRepo.delete(buildFailurePatternRepo.findByProjectName(projectName));\n\t\tgoalSettingRepo.delete(goalSettingRepo.findByProjectName(projectName));\n\t\tportfolioRepo.delete(portfolioRepo.findByProjectName(projectName));\n\t\tprojectHealthRepo.delete(projectHealthRepo.findByProjectName(projectName));\n\t\tuserAssociation.delete(userAssociation.findByPName(projectName));\n\t\tchartConfigRepo.delete(chartConfigRepo.findByPName(projectName));\n\t\tconfigurationSettingRepository.delete(configurationSettingRepository.findByProjectName(projectName));\n\t\t\n\t\treturn false;\n\t}\n\n\t@Override\n//\t@Cacheable(value=\"getConfigProject\", key =\"'getConfigProject'+#pName\", cacheManager=\"timeoutCacheManager\")\n\tpublic ConfigurationSetting getConfigProject(String pName) {\n\t\t\n\t\t\n\t\tConfigurationSetting config=configurationSettingRepository.findByProjectName(pName).get(0);\n\t\tif(config.getMetrics()!=null)\n\t\tfor(ConfigurationToolInfoMetric metric: config.getMetrics()) {\n\t\t\tmetric.setPassword(EncryptionDecryptionAES.decrypt(metric.getPassword(), ConstantVariable.SECRET_KEY));\n\t\t  }\n\t\t\t\t\n\t\t\t\treturn config;\n\t}\n\n}\n"}, "DateUtil": {"fqcn": "com.bolt.dashboard.util.DateUtil", "package": "com.bolt.dashboard.util", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\util\\DateUtil.java", "imports": ["java.text.SimpleDateFormat", "java.util.Calendar", "java.util.Date", "java.util.HashMap", "java.util.Map", "org.springframework.stereotype.Component"], "endpoints": [], "db_entities": [], "source_code": "package com.bolt.dashboard.util;\n\nimport java.text.SimpleDateFormat;\nimport java.util.Calendar;\nimport java.util.Date;\nimport java.util.HashMap;\nimport java.util.Map;\n\nimport org.springframework.stereotype.Component;\n\n@Component\npublic class DateUtil {\n\n    public String getDateInFormat(String format, Date dateInput) {\n        String pattern = format;\n        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);\n\n        return simpleDateFormat.format(dateInput);\n\n    }\n\n    public Map<String, String> getLastWeekWorkingDateRange() {\n        Map<String, String> map = new HashMap<>();\n        Date date = new Date();\n        Calendar c = Calendar.getInstance();\n        c.setTime(date);\n        c.setFirstDayOfWeek(2);// set Monday as First day of the week\n        int i = c.get(Calendar.DAY_OF_WEEK) - c.getFirstDayOfWeek();\n        c.add(Calendar.DATE, -i - 7);\n        Date start = c.getTime();\n        c.add(Calendar.DATE, 4);\n        Date end = c.getTime();\n        map.put(\"start\", getDateInFormat(\"yyyy/MM/dd\", start));\n        map.put(\"end\", getDateInFormat(\"yyyy/MM/dd\", end));\n        return map;\n\n    }\n\n}\n"}}, "dto_mappings": {}, "validated_edges": ["ALMConfigReq-DECLARES-getCcrLabel", "ALMServiceImplementation-HAS_FIELD-Vlist", "DateUtil-DECLARES-DateUtil", "AlmController-DECLARES-getIssueHierarchy", "Almservice-DECLARES-getComponentsSprint", "ALMConfigReq-HAS_FIELD-Throughputstates", "Almservice-DECLARES-getMetricDetails", "ConfigurationSettingServiceImplementation-HAS_FIELD-Buildrepo", "ALMServiceImplementation-DECLARES-getDefects", "ALMConfigReq-DECLARES-getCriticalPriority", "Auth-HAS_FIELD-Response", "ConfigurationSettingServiceImplementation-HAS_FIELD-Healthrepo", "getDataFromTools-USES-Joba", "Application-DECLARES-passwordEncoder", "ALMServiceImplementation-DECLARES-updateComponentsOfTaskandSubtask", "Service-CONTAINS-ConfigurationSettingService", "ALMConfigController-DECLARES-retrieveALMConfig", "Almservice-DECLARES-getGroomingTable", "ALMServiceImplementation-DECLARES-getStoryProgress", "ALMConfigReq-HAS_FIELD-Storyname", "Almservice-DECLARES-getDefectsSummaryHome", "AlmController-DECLARES-getStoryProgress", "getDateIterations-USES-Response", "ALMServiceImplementation-HAS_FIELD-Changehisortyrepo", "AlmController-DECLARES-defectClassification", "getComponents-USES-Response", "ALMConfigReq-DECLARES-getTaskName", "ALMConfigReq-HAS_FIELD-Timezone", "ALMServiceImplementation-DECLARES-getDefectsSummaryHome", "ALMServiceImplementation-DECLARES-getChangesItems", "Almservice-DECLARES-getUnReleaseData", "ALMServiceImplementation-DECLARES-getRelease", "ServicesBolt-CONTAINS-Response", "Util-CONTAINS-DateUtil", "AlmController-DECLARES-getActiveSprints", "AlmController-DECLARES-AlmController", "getConfigProject-USES-Config", "Almservice-DECLARES-getComponentsChart", "compare-USES-S2", "AlmController-DECLARES-getProjectMetrics", "Almservice-DECLARES-getDefectTrendAndClassification", "ALMConfigReq-DECLARES-setMedPriority", "ALMServiceImplementation-DECLARES-getComponentWiseIssueHierarchyChart", "ALMServiceImplementation-DECLARES-getComponentList", "ALMServiceImplementation-HAS_FIELD-Almconfigrepo", "ALMConfigReq-HAS_FIELD-Criticalpriority", "ALMConfigServiceImplementation-HAS_FIELD-Almconfigrepo", "getDefects-USES-Response", "ALMConfigReq-HAS_FIELD-Testingphase", "ALMConfigReq-DECLARES-getTestingPhase", "ALMServiceImplementation-HAS_FIELD-Mongotemplate", "ALMServiceImplementation-HAS_FIELD-Closestates", "getLastWeekWorkingDateRange-USES-Start", "ALMConfigReq-DECLARES-setCycleTimeStates", "Application-DECLARES-main", "AlmController-DECLARES-getDefectsSummaryHome", "Service-CONTAINS-AlmService", "ALMConfigServiceImplementation-DECLARES-retrieveALMConfig", "ALMServiceImplementation-DECLARES-getSprintWiseStories", "ALMServiceImplementation-HAS_FIELD-Iterationrepo", "ALMServiceImplementation-HAS_FIELD-Projectrepo", "ALMServiceImplementation-HAS_FIELD-Workingsprints", "ALMServiceImplementation-DECLARES-getDefectDensity", "ALMServiceImplementation-DECLARES-getProjectDetails", "ALMServiceImplementation-DECLARES-callSP", "Almservice-DECLARES-getProductionSlippage", "ConfigurationSettingServiceImplementation-DECLARES-deleteProject", "ALMConfigReq-DECLARES-setLowPriority", "getAlmType-USES-Almtype", "ALMConfigReq-DECLARES-setRejectionPhase", "ALMServiceImplementation-HAS_FIELD-Tasknames", "Almservice-DECLARES-getAllTransitions", "SprintComparatort-DECLARES-compare", "Configurationsettingservice-DECLARES-getConfigProject", "AlmController-DECLARES-getTaskRiskStoryPoint", "AlmController-DECLARES-getSlaData", "Almservice-DECLARES-getProjectDetails", "Almservice-DECLARES-getActiveSprints", "ALMConfigReq-HAS_FIELD-Highpriority", "ALMConfigReq-DECLARES-getDefectName", "getProdDefects-USES-Response", "ALMConfigReq-HAS_FIELD-Ccrlabel", "AlmController-DECLARES-getProjectDetials", "ALMServiceImplementation-DECLARES-getDefectInsightData", "ALMServiceImplementation-DECLARES-getCurrentProjectDetails", "ALMConfigReq-HAS_FIELD-Defectname", "ConfigurationSettingServiceImplementation-HAS_FIELD-Buildfailurepatternrepo", "ALMConfigReq-DECLARES-setVelocityFields", "ConfigurationSettingServiceImplementation-HAS_FIELD-Almservice", "ALMConfigReq-DECLARES-getProgressState", "Almservice-DECLARES-getStoryProgress", "ALMConfigReq-DECLARES-toDetailsAddSetting", "ALMConfigReq-HAS_FIELD-Medpriority", "DateUtil-DECLARES-getDateInFormat", "ALMServiceImplementation-DECLARES-defectParetoCalculation", "Almservice-DECLARES-getSlaData", "ALMServiceImplementation-DECLARES-getFeatureMetrics", "ALMServiceImplementation-DECLARES-getEffortData", "getComponontWiseSprintWiseStories-USES-Response", "Almservice-DECLARES-getDefectDensity", "Configurationsettingservice-DECLARES-deleteConfig", "ALMConfigReq-DECLARES-setReleaseName", "ConfigurationSettingServiceImplementation-DECLARES-getConfig", "ALMConfigReq-HAS_FIELD-Tracksset", "AlmController-DECLARES-getFeatureMetrics", "Almservice-DECLARES-getCurrentProjectDetails", "Configurationsettingservice-DECLARES-getConfig", "ALMServiceImplementation-DECLARES-getComponentsChart", "AlmController-DECLARES-delAllIsues", "Almservice-DECLARES-getDefectCounts", "Almservice-DECLARES-getRelease", "Almservice-DECLARES-getDefectClassification", "Almservice-DECLARES-getDefects", "DataResponse-DECLARES-getResult", "Almservice-DECLARES-defectParetoCalculation", "ALMConfigReq-DECLARES-getLowPriority", "ALMToolReq-HAS_FIELD-Almtype", "ALMConfigReq-HAS_FIELD-Reopenphase", "ALMServiceImplementation-DECLARES-SprintComparatort", "ConfigurationSettingServiceImplementation-DECLARES-deleteConfig", "ALMServiceImplementation-DECLARES-getAllTransitions", "ALMServiceImplementation-DECLARES-getCrtItr", "ALMServiceImplementation-DECLARES-populateAuthor", "AlmController-DECLARES-getTransitionsData", "Application-DECLARES-Application", "ALMServiceImplementation-DECLARES-getComponentsSprintStories", "ALMConfigReq-DECLARES-setPersonHours", "getAlmType-USES-Config", "compare-USES-T2", "Almservice-DECLARES-getIssueHierarchy", "ALMConfigReq-DECLARES-setCcrLabel", "getAlmType-USES-Metric", "ALMServiceImplementation-HAS_FIELD-Refinedissulist", "ALMServiceImplementation-DECLARES-getTaskRisk", "ALMServiceImplementation-DECLARES-getDefectTrendAndClassification", "AlmController-DECLARES-getIterations", "Almservice-DECLARES-getComponentVelocityChart", "getIssueHierarchy-USES-Response", "ALMConfigReq-DECLARES-setCriticalPriority", "getDataFromTools-USES-Trigger", "AlmController-HAS_FIELD-Service", "AlmController-DECLARES-getChangesItems", "AlmController-DECLARES-getVelocityChart", "ALMServiceImplementation-DECLARES-getSprintProgressHome", "Almservice-DECLARES-getComponents", "Request-CONTAINS-ALMToolReq", "ALMConfigServiceImplementation-DECLARES-ALMConfigServiceImplementation", "ALMConfigReq-DECLARES-setProjectName", "Almservice-DECLARES-getComponentWiseIssueHierarchy", "ALMConfigReq-DECLARES-isSafeEnabled", "Almservice-DECLARES-getAssigneeIssues", "ConfigurationSettingServiceImplementation-HAS_FIELD-Log", "ConfigurationSettingServiceImplementation-HAS_FIELD-Userassociation", "ALMConfigReq-DECLARES-setPriorityName", "ServicesBolt-CONTAINS-Api", "ALMConfigReq-HAS_FIELD-Velocityfields", "ALMServiceImplementation-DECLARES-getAlmType", "AlmController-DECLARES-getAllTransitions", "Almservice-DECLARES-delDuplicate", "deleteAllCollections-USES-Failurepatternobj", "AlmController-DECLARES-getDefectCount", "ALMServiceImplementation-DECLARES-getProductionSlippage", "ServicesBolt-CONTAINS-Application", "ALMServiceImplementation-DECLARES-getAssigneeIssues", "DataResponse-HAS_FIELD-Lastupdated", "ALMConfigReq-DECLARES-setHighPriority", "Almservice-DECLARES-getStoryAgeingData", "ALMConfigReq-HAS_FIELD-Personhours", "ALMConfigReq-DECLARES-setProgressState", "ALMConfigReq-DECLARES-setTimeZone", "TriggerCollector-HAS_FIELD-Ctx", "AlmController-DECLARES-getComponontWiseSprintWiseStories", "ALMServiceImplementation-HAS_FIELD-Metricrepo", "ALMConfigController-HAS_FIELD-Log", "ALMConfigReq-HAS_FIELD-Lowpriority", "ALMConfigReq-HAS_FIELD-Trendtype", "ALMServiceImplementation-DECLARES-getIterationData", "ALMConfigReq-HAS_FIELD-Firstsprint", "ALMConfigReq-DECLARES-getFirstSprint", "ServicesBolt-CONTAINS-Request", "ALMServiceImplementation-DECLARES-getIssueBrakeUp", "ALMServiceImplementation-DECLARES-burndownCalculation", "ALMConfigReq-DECLARES-setThroughputStates", "ALMConfigController-DECLARES-retrieveList", "ALMServiceImplementation-DECLARES-getComponentVelocityChart", "ALMServiceImplementation-HAS_FIELD-Logger", "ALMConfigReq-HAS_FIELD-Taskname", "Auth-HAS_FIELD-Authenticationstatus", "getDateInFormat-USES-Format", "getAlmType-USES-Metric1", "ALMServiceImplementation-DECLARES-getGroomingTable", "AlmController-DECLARES-getComponentWiseVelocityChart", "ALMServiceImplementation-DECLARES-getTransitionsData", "AlmController-DECLARES-getEffortData", "ALMConfigReq-DECLARES-getReopenPhase", "Almservice-DECLARES-getDefectBacklog", "ALMConfigReq-DECLARES-getCloseState", "TriggerCollector-DECLARES-getDataFromTools", "ALMServiceImplementation-DECLARES-getProdDefects", "ALMConfigReq-HAS_FIELD-Safeenabled", "AlmController-DECLARES-getIterationData", "ALMConfigController-DECLARES-ALMConfigController", "Auth-DECLARES-Authenticate", "ALMServiceImplementation-DECLARES-delAllIssues", "ALMServiceImplementation-DECLARES-getIssueHierarchyChart", "AlmController-DECLARES-storyAgeing", "ALMConfigReq-DECLARES-getThroughputStates", "ALMServiceImplementation-HAS_FIELD-Transitionrepo", "AlmController-DECLARES-getDefectBacklog", "Almservice-DECLARES-getEffortData", "ALMServiceImplementation-DECLARES-getStoryAgeingData", "Service-CONTAINS-ALMConfigService", "Almservice-DECLARES-getTaskRisk", "Configurationsettingservice-DECLARES-addConfig", "ALMConfigReq-DECLARES-getMedPriority", "getDataFromTools-USES-Job", "ALMServiceImplementation-DECLARES-TransitionComparator", "ALMServiceImplementation-HAS_FIELD-Tempspremoved", "Almservice-DECLARES-getAllIterations", "DataResponse-DECLARES-DataResponse", "ALMServiceImplementation-HAS_FIELD-Authorrepo", "ALMConfigReq-HAS_FIELD-Environment", "ALMServiceImplementation-DECLARES-getAllIterations", "ALMConfigController-HAS_FIELD-Almconfigservice", "AlmController-DECLARES-getRelease", "ALMConfigReq-DECLARES-getCycleTimeStates", "Almservice-DECLARES-getIterationData", "ALMServiceImplementation-HAS_FIELD-Releaserepo", "AlmController-DECLARES-defectTrendAndClassification", "ALMConfigReq-DECLARES-setReopenPhase", "ALMConfigReq-HAS_FIELD-Projectname", "retrieveALMConfig-USES-Lastupdate", "AlmController-DECLARES-getSprintProgressHome", "Almservice-DECLARES-getIssueHierarchyChart", "Almservice-DECLARES-getAllMetrics", "TriggerCollector-DECLARES-TriggerCollector", "setAlmType-USES-Almtype", "Configurationsettingservice-DECLARES-deleteProject", "ALMServiceImplementation-DECLARES-getDefectBacklog", "ALMConfigReq-HAS_FIELD-Progressstate", "ALMToolReq-DECLARES-getAlmType", "ALMServiceImplementation-HAS_FIELD-Widarr", "getDataFromTools-USES-Jobkey", "ALMServiceImplementation-DECLARES-getComponentsSprint", "ALMConfigReq-DECLARES-setTrendType", "ALMConfigReq-HAS_FIELD-Priorityname", "ALMServiceImplementation-DECLARES-calcClosedSP", "AlmController-DECLARES-getProductionSlippage", "addConfig-USES-Timestamp", "getDataFromTools-USES-Scheduler", "Almservice-DECLARES-burndownCalculation", "ALMConfigReq-DECLARES-getEnvironment", "ServicesBolt-CONTAINS-Service", "ConfigurationSettingServiceImplementation-DECLARES-deleteAllCollections", "Service-CONTAINS-ConfigurationSettingServiceImplementation", "Almservice-DECLARES-saveEngScore", "AlmController-DECLARES-updateComponent", "ALMConfigReq-DECLARES-getReleaseName", "ALMServiceImplementation-HAS_FIELD-Velocityfields", "getDateInFormat-USES-Simpledateformat", "ALMServiceImplementation-DECLARES-getComponentVelocity", "ALMConfigReq-DECLARES-getHighPriority", "ALMConfigReq-DECLARES-getPersonHours", "TransitionComparator-DECLARES-compare", "ALMServiceImplementation-HAS_FIELD-Almconfig", "getDataFromTools-USES-Ctx", "ALMConfigReq-DECLARES-getStoryName", "ALMConfigReq-DECLARES-getTrendType", "ALMServiceImplementation-DECLARES-getAllMetrics", "ConfigurationSettingServiceImplementation-HAS_FIELD-Projecthealthrepo", "Almservice-DECLARES-getIssueBrakeUp", "ALMConfigReq-HAS_FIELD-Cycletimestates", "AlmController-DECLARES-getIssueBrakeUp", "ALMConfigReq-DECLARES-setTracksSet", "ALMServiceImplementation-DECLARES-delDuplicate", "ALMServiceImplementation-DECLARES-getComponents", "ALMServiceImplementation-HAS_FIELD-Storiescompleted", "ALMServiceImplementation-DECLARES-getUnReleaseData", "Response-CONTAINS-Authentication", "ALMConfigReq-HAS_FIELD-Newstate", "ALMConfigReq-DECLARES-setProductionPhase", "getDataFromTools-USES-Portfolio", "TriggerCollector-HAS_FIELD-Logger", "ALMServiceImplementation-HAS_FIELD-Efforthistoryrepo", "ConfigurationSettingServiceImplementation-HAS_FIELD-Codecoveragerepo", "ALMToolReq-DECLARES-ALMToolReq", "ALMConfigServiceImplementation-DECLARES-saveALMConfig", "AlmController-DECLARES-getComponentWiseIssueHierarchy", "AlmController-DECLARES-groomingTable", "ALMServiceImplementation-DECLARES-saveEngScore", "AlmController-DECLARES-saveEngScore", "ALMServiceImplementation-HAS_FIELD-Finalstoriescommited", "ConfigurationSettingServiceImplementation-HAS_FIELD-Almconfigrepo", "AlmController-DECLARES-delDuplicate", "Almservice-DECLARES-getChangesItems", "Almservice-DECLARES-getDateIterations", "ALMServiceImplementation-DECLARES-getDefectCounts", "ALMConfigReq-HAS_FIELD-Productionphase", "Application-DECLARES-configure", "Almservice-DECLARES-getSprintProgressHome", "ALMServiceImplementation-HAS_FIELD-Chartservice", "DateUtil-DECLARES-getLastWeekWorkingDateRange", "ALMServiceImplementation-HAS_FIELD-Removedissulist", "ConfigurationSettingServiceImplementation-HAS_FIELD-Portfoliorepo", "AlmController-DECLARES-burndownCalculation", "Service-CONTAINS-ALMConfigServiceImplementation", "Service-CONTAINS-ALMServiceImplementation", "ServicesBolt-CONTAINS-Util", "ALMConfigReq-DECLARES-setDefectName", "Almservice-DECLARES-getFeatureMetrics", "Almservice-DECLARES-getComponentWiseIssueHierarchyChart", "ALMServiceImplementation-HAS_FIELD-Tempsprefined", "ConfigurationSettingServiceImplementation-HAS_FIELD-Goalsettingrepo", "ALMServiceImplementation-DECLARES-storyLoopRefined", "ALMServiceImplementation-DECLARES-getSlaData", "ALMServiceImplementation-DECLARES-storyLoop", "AlmController-DECLARES-getUnReleaseData", "ALMToolReq-DECLARES-setAlmType", "compare-USES-S1", "ALMConfigServiceImplementation-HAS_FIELD-Log", "ServicesBolt-CONTAINS-TriggerCollector", "Almservice-DECLARES-getProdDefects", "ALMConfigReq-HAS_FIELD-Closestate", "Authentication-DECLARES-Authenticationofservice", "ALMConfigReq-DECLARES-getRejectionPhase", "AlmController-DECLARES-getCurrentIter", "Almservice-DECLARES-getDefectInsightData", "Authentication-HAS_FIELD-Authntication", "getUnReleaseData-USES-Response", "AlmController-DECLARES-getDefectDensity", "AlmController-DECLARES-getProdDefects", "ALMConfigReq-HAS_FIELD-Releasename", "addConfig-USES-Date", "ALMConfigReq-DECLARES-getProductionPhase", "Api-CONTAINS-ALMConfigController", "getAlmType-USES-Configuration1", "getComponentWiseVelocityChart-USES-Resp", "ALMConfigReq-DECLARES-setCloseState", "ALMServiceImplementation-DECLARES-getActiveSprints", "ConfigurationSettingServiceImplementation-DECLARES-addConfig", "ConfigurationSettingServiceImplementation-HAS_FIELD-Codequalityrep", "ALMServiceImplementation-DECLARES-getDefectClassification", "getDateInFormat-USES-Pattern", "ALMConfigReq-DECLARES-setNewState", "Configurationsettingservice-DECLARES-deleteAllCollections", "AlmController-DECLARES-getAssigneeIssues", "AlmController-DECLARES-getMetricsDatas", "Almconfigservice-DECLARES-saveALMConfig", "Almservice-DECLARES-getComponentVelocity", "Api-CONTAINS-AlmController", "ALMServiceImplementation-DECLARES-getDateIterations", "ALMServiceImplementation-HAS_FIELD-Agg", "Almservice-DECLARES-getComponentsSprintStories", "ALMConfigReq-DECLARES-getProjectName", "ALMConfigReq-DECLARES-setFirstSprint", "ALMServiceImplementation-DECLARES-filterTrans", "ConfigurationSettingServiceImplementation-HAS_FIELD-Scmrepo", "getLastWeekWorkingDateRange-USES-End", "ALMConfigReq-HAS_FIELD-Rejectionphase", "AlmController-DECLARES-getDateIterations", "getConfig-USES-Lastupdated", "ALMServiceImplementation-HAS_FIELD-Defetcscompleted", "AlmController-DECLARES-getComponents", "compare-USES-T1", "ALMConfigReq-DECLARES-getTimeZone", "AlmController-DECLARES-getDefects", "Response-CONTAINS-DataResponse", "AlmController-DECLARES-getAlmType", "getComponentWiseIssueHierarchy-USES-Response", "ALMServiceImplementation-DECLARES-getIssueHierarchy", "Almservice-DECLARES-delAllIssues", "Almconfigservice-DECLARES-retrieveALMConfig", "AlmController-DECLARES-getCurrentProjectDetials", "ALMServiceImplementation-DECLARES-getComponentWiseIssueHierarchy", "ConfigurationSettingServiceImplementation-HAS_FIELD-Configurationsettingrepository", "AlmController-DECLARES-defectInsightData", "ALMServiceImplementation-HAS_FIELD-Workingbacklog", "ALMConfigReq-DECLARES-setTaskName", "ALMConfigReq-DECLARES-setStoryName", "getDateIterations-USES-Query", "Request-CONTAINS-ALMConfigReq", "ALMConfigReq-DECLARES-getPriorityName", "ALMConfigController-DECLARES-saveALMConfig", "getLastWeekWorkingDateRange-USES-Date", "Almservice-DECLARES-getCrtItr", "ConfigurationSettingServiceImplementation-DECLARES-getConfigProject", "ALMConfigReq-DECLARES-getVelocityFields", "ALMConfigReq-DECLARES-getNewState", "ALMServiceImplementation-HAS_FIELD-Issuelist", "ALMServiceImplementation-DECLARES-ALMServiceImplementation", "ALMConfigReq-DECLARES-getTracksSet", "DataResponse-DECLARES-getLastUpdated", "ConfigurationSettingServiceImplementation-HAS_FIELD-Chartconfigrepo", "AlmController-DECLARES-getDefectPareto", "deleteAllCollections-USES-Failurepattern", "Almservice-DECLARES-getTransitionsData", "ALMServiceImplementation-DECLARES-getVelocityChart", "ALMServiceImplementation-DECLARES-getMetricDetails", "ALMConfigReq-DECLARES-setEnvironment", "Almservice-DECLARES-updateComponentsOfTaskandSubtask", "ConfigurationSettingServiceImplementation-DECLARES-ConfigurationSettingServiceImplementation", "ALMConfigReq-DECLARES-ALMConfigReq", "ALMConfigReq-DECLARES-setTestingPhase", "ALMConfigReq-DECLARES-setSafeEnabled"], "code_index": {"Application": {"methods": ["Configure"], "variables": [], "annotations": ["SpringBootApplication", "EnableCaching", "Override", "<PERSON>"]}, "TriggerCollector": {"methods": ["GetDataFromTools"], "variables": [["<PERSON><PERSON>", "LOGGER"], ["AnnotationConfigApplicationContext", "ctx"], ["PortfolioConfigRepo", "portfolio"], ["Scheduler", "scheduler"], ["String", "jobkey"], ["<PERSON><PERSON><PERSON>", "job"], ["JobDetail", "jobA"], ["<PERSON><PERSON>", "trigger"]], "annotations": ["Component"]}, "ALMConfigController": {"methods": [], "variables": [["<PERSON><PERSON>", "LOG"], ["ALMConfigService", "almConfigService"], ["ALMConfigReq", "almConfigReq"]], "annotations": ["RestController", "Autowired", "Autowired", "RequestMapping", "RequestBody", "RequestMapping", "RequestMapping"]}, "AlmController": {"methods": ["DelDuplicate", "DefectTrendAndClassification", "DefectClassification", "DelAllIsues", "GetMetricsDatas", "GetIterationData", "GetDefectCount", "GetRelease", "GetAlmType", "UpdateComponent", "SaveEngScore", "GetAlmType"], "variables": [["AlmService", "service"], ["ApplicationContext", "ctx"], ["return", "response"], ["return", "response"], ["return", "response"], ["String", "almType"], ["ConfigurationSetting", "config"], ["Iterator", "iter"], ["Object", "configuration1"], ["ConfigurationToolInfoMetric", "metric1"], ["return", "almType"], ["return", "response"], ["return", "response"], ["return", "resp"], ["return", "response"], ["return", "response"], ["ConfigurationSetting", "config"], ["Iterator", "iter"], ["Object", "configuration1"], ["ConfigurationToolInfoMetric", "metric1"]], "annotations": ["RestController", "Autowired", "RequestMapping", "RequestParam", "RequestParam", "RequestMapping", "RequestParam", "RequestParam", "RequestMapping", "RequestParam", "RequestMapping", "RequestParam", "RequestParam", "RequestMapping", "RequestParam", "RequestParam", "RequestParam", "RequestMapping", "RequestParam", "RequestParam", "RequestParam", "RequestMapping", "RequestParam", "RequestParam", "RequestParam", "RequestMapping", "RequestParam", "RequestParam", "RequestMapping", "RequestParam", "RequestParam", "RequestMapping", "RequestParam", "RequestParam", "RequestMapping", "RequestParam", "RequestParam", "RequestParam", "RequestMapping", "RequestParam", "RequestParam", "RequestParam", "RequestMapping", "RequestParam", "RequestParam", "RequestParam", "RequestMapping", "RequestParam", "RequestParam", "RequestParam", "RequestMapping", "RequestParam", "RequestParam", "RequestParam", "RequestMapping", "RequestParam", "RequestParam", "RequestParam", "RequestMapping", "RequestParam", "RequestParam", "RequestMapping", "RequestParam", "RequestMapping", "RequestParam", "RequestMapping", "RequestParam", "RequestMapping", "RequestParam", "RequestMapping", "RequestParam", "RequestMapping", "RequestParam", "RequestMapping", "RequestParam", "RequestParam", "RequestMapping", "RequestParam", "RequestMapping", "RequestParam", "RequestParam", "RequestMapping", "RequestParam", "RequestParam", "RequestMapping", "RequestParam", "RequestParam", "RequestMapping", "RequestParam", "RequestMapping", "RequestParam", "RequestParam", "RequestMapping", "RequestParam", "RequestMapping", "RequestParam", "RequestParam", "RequestMapping", "RequestParam", "RequestParam", "RequestParam", "RequestMapping", "RequestParam", "RequestParam", "RequestParam", "RequestMapping", "RequestParam", "RequestParam", "RequestParam", "RequestMapping", "RequestParam", "RequestParam", "RequestParam", "RequestParam", "RequestMapping", "RequestParam", "RequestParam", "RequestParam", "RequestParam", "RequestParam", "SuppressWarnings", "RequestMapping", "RequestParam", "RequestMapping", "RequestParam", "RequestMapping", "RequestParam", "RequestMapping", "RequestParam", "RequestMapping", "RequestParam", "RequestMapping", "RequestParam", "RequestMapping", "RequestParam", "RequestMapping", "RequestParam", "RequestMapping", "RequestParam", "RequestParam", "RequestParam", "RequestMapping", "RequestParam", "RequestParam"]}, "ALMConfigReq": {"methods": ["GetCcrLabel", "SetCcrLabel", "SetCycleTimeStates", "SetThroughputStates", "SetRejectionPhase", "SetReopenPhase", "SetTestingPhase", "SetProductionPhase", "GetStoryName", "GetPriorityName", "SetPriorityName", "SetTaskName", "GetReleaseName", "SetReleaseName", "SetCloseState", "SetCriticalPriority", "SetHighPriority", "SetMedPriority", "SetLowPriority", "SetStoryName", "GetProjectName", "SetProjectName", "GetDefectName", "SetDefectName", "GetTrendType", "SetTrendType", "SetTracksSet", "SetNewState", "SetProgressState", "ToDetailsAddSetting", "GetPersonHours", "SetPersonHours", "GetTimeZone", "SetTimeZone", "SetVelocityFields", "GetEnvironment", "SetEnvironment", "IsSafeEnabled", "SetSafeEnabled", "GetFirstSprint", "SetFirstSprint"], "variables": [["String", "story<PERSON>ame"], ["String", "priorityName"], ["String", "projectName"], ["String", "<PERSON><PERSON><PERSON>"], ["String", "releaseName"], ["double", "personHours"], ["String", "timeZone"], ["String", "environment"], ["boolean", "safeEnabled"], ["String", "ccrLabel"], ["String", "firstSprint"], ["return", "ccrLabel"], ["return", "cycleTimeStates"], ["return", "throughputStates"], ["return", "rejectionPhase"], ["return", "reopenPhase"], ["return", "testingPhase"], ["return", "productionPhase"], ["String", "trendType"], ["return", "story<PERSON>ame"], ["return", "priorityName"], ["return", "taskName"], ["return", "releaseName"], ["return", "closeState"], ["return", "criticalPriority"], ["return", "highPriority"], ["return", "medPriority"], ["return", "lowPriority"], ["return", "projectName"], ["return", "<PERSON><PERSON><PERSON>"], ["return", "trendType"], ["return", "tracksSet"], ["return", "newState"], ["return", "progressState"], ["ALMConfiguration", "details"], ["return", "details"], ["return", "personHours"], ["return", "timeZone"], ["return", "velocityFields"], ["return", "environment"], ["return", "safeEnabled"], ["return", "firstSprint"]], "annotations": []}, "ALMToolReq": {"methods": ["GetAlmType", "SetAlmType"], "variables": [["String", "almType"], ["return", "almType"]], "annotations": ["author", "return", "param"]}, "Authentication": {"methods": [], "variables": [["<PERSON><PERSON>", "AUTHNTICATION"], ["Response", "response"], ["boolean", "authenticationStatus"], ["HttpAuthenticationFeature", "feature"], ["Builder", "invocationBuilder"]], "annotations": ["author"]}, "DataResponse": {"methods": ["GetResult", "GetLastUpdated"], "variables": [["T", "result"], ["long", "lastUpdated"], ["return", "result"], ["return", "lastUpdated"]], "annotations": []}, "ALMConfigService": {"methods": [], "variables": [], "annotations": ["author"]}, "ALMConfigServiceImplementation": {"methods": ["SaveALMConfig"], "variables": [["ALMConfigRepo", "almConfigRepo"], ["<PERSON><PERSON>", "LOG"], ["long", "lastUpdate"], ["ALMConfiguration", "result"]], "annotations": ["author", "Service", "Autowired", "Override", "CacheE<PERSON><PERSON>", "Override", "Cacheable"]}, "AlmService": {"methods": [], "variables": [], "annotations": ["author"]}, "ALMServiceImplementation": {"methods": ["EvictAllCaches", "GetMetricDetails", "GetIterationData", "GetDefectCounts", "GetRelease", "DelDuplicate", "DelAll<PERSON>ssues", "CallSP", "CalcClosedSP", "Compare", "Compare", "UpdateComponentsOfTaskandSubtask", "GetDefectTrendAndClassification", "GetDefectClassification", "GetAlmType", "SaveEngScore"], "variables": [["TransitionRepo", "transitionRepo"], ["EffortHistoryRepo", "effortHistoryRepo"], ["ChangeHisortyRepo", "changeHisortyRepo"], ["ProjectIterationRepo", "<PERSON><PERSON><PERSON><PERSON>"], ["ProjectRepo", "projectRepo"], ["ReleaseRepo", "releaseRepo"], ["IterationRepo", "iterationRepo"], ["MetricRepo", "metricRepo"], ["ALMConfigRepo", "almConfigRepo"], ["MongoAggregate", "agg"], ["MongoTemplate", "mongoTemplate"], ["GuavaCacheManager", "cacheManager"], ["ALMConfiguration", "almConfig"], ["VelocityList", "vlist"], ["double", "tempSpRefined"], ["double", "tempSpRemoved"], ["double", "finalStoriesCommited"], ["double", "storiesCompleted"], ["double", "defetcsCompleted"], ["<PERSON><PERSON>", "LOGGER"], ["ChartCalculations", "chartService"], ["MonogOutMetrics", "result"], ["return", "result"], ["return", "result"], ["return", "result"], ["String", "almTy"], ["return", "dataModel"], ["return", "dataList"], ["Query", "query"], ["return", "response"], ["Query", "query"], ["Query", "query"], ["Query", "query"], ["return", "response"], ["Aggregation", "aggregation"], ["MatchOperation", "filterSName"], ["UnwindOperation", "unwinnd"], ["MatchOperation", "metricSearch"], ["GroupOperation", "group"], ["GroupOperation", "groupAppend"], ["Aggregation", "mtrAggr"], ["Query", "query"], ["return", "data"], ["Aggregation", "aggregation"], ["return", "data"], ["MongoAggregate", "mongoAggr"], ["int", "totalCount"], ["IterationOutModel", "temp"], ["return", "outMap"], ["return", "result"], ["ComponentVelocityList", "c"], ["IterationOutModel", "author"], ["return", "response"], ["ComponentSprintWiseStories", "c"], ["IterationOutModel", "author"], ["MetricsModel", "temp"], ["return", "response"], ["return", "resp"], ["String", "component"], ["return", "componentList"], ["return", "true"], ["return", "false"], ["return", "response"], ["return", "copmonents"], ["MetricsModel", "m"], ["MonogOutMetrics", "agr"], ["int", "tempStory"], ["IterationOutModel", "sprint"], ["IterationOutModel", "sprintAndstories"], ["MonogOutMetrics", "story"], ["long", "endDate"], ["int", "m"], ["MonogOutMetrics", "s"], ["MonogOutMetrics", "s"], ["long", "completedDate"], ["int", "i"], ["String", "wid"], ["TransitionModel", "trans"], ["String", "wid"], ["TransitionModel", "trans"], ["return", "sprintwise"], ["int", "i"], ["VelocityCalculations", "velocity"], ["double", "tempSp"], ["long", "date"], ["Map", "storyPointMap"], ["int", "i"], ["return", "tempSp"], ["double", "tempSp"], ["long", "date"], ["Map", "storyPointMap"], ["int", "i"], ["String", "wid"], ["TransitionModel", "trans"], ["String", "wid"], ["TransitionModel", "trans"], ["return", "tempSp"], ["double", "temp"], ["int", "i"], ["IssueList", "iss"], ["IssueList", "iss"], ["return", "temp"], ["double", "temp"], ["int", "i"], ["IssueList", "issue"], ["IssueList", "issue"], ["IssueList", "issue"], ["IssueList", "issue"], ["IssueList", "issue"], ["IssueList", "issue"], ["IssueList", "issue"], ["IssueList", "issue"], ["return", "temp"], ["TransitionModel", "t1"], ["TransitionModel", "t2"], ["return", "0"], ["return", "1"], ["return", "1"], ["IterationOutModel", "s1"], ["IterationOutModel", "s2"], ["return", "0"], ["return", "1"], ["return", "1"], ["MongoAggregate", "mongoaggr"], ["ProjectHomeCalculation", "projHomeCalc"], ["return", "result"], ["ProjectHomeCalculation", "projHomeCalc"], ["return", "result"], ["return", "result"], ["Query", "query"], ["return", "response"], ["return", "null"], ["SprintProgressCalculations", "spCalc"], ["return", "result"], ["SprintProgressCalculations", "spCalc"], ["return", "result"], ["SprintProgress", "spCalc"], ["return", "result"], ["return", "result"], ["return", "result"], ["return", "result"], ["return", "result"], ["return", "result"], ["Map", "result"], ["return", "result"], ["Map", "result"], ["return", "result"], ["return", "result"], ["return", "result"], ["return", "result"], ["String", "almType"], ["ConfigurationSetting", "config"], ["Iterator", "iter"], ["Object", "configuration1"], ["ConfigurationToolInfoMetric", "metric1"], ["return", "almType"], ["return", "result"], ["return", "result"], ["return", "result"], ["return", "result"], ["return", "result"]], "annotations": ["Service", "Autowired", "Autowired", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "CacheE<PERSON><PERSON>", "Override", "Cacheable", "Override", "CacheE<PERSON><PERSON>", "Override", "Cacheable", "Override", "Override", "Override", "Override", "Override", "Override", "Override", "Override", "CacheE<PERSON><PERSON>", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable", "Override", "Cacheable"]}, "ConfigurationSettingService": {"methods": ["DeleteConfig"], "variables": [], "annotations": []}, "ConfigurationSettingServiceImplementation": {"methods": ["AddConfig", "DeleteConfig", "DeleteAllCollections", "DeleteProject", "GetConfigProject"], "variables": [["ConfigurationSettingRep", "configurationSettingRepository"], ["AlmService", "almService"], ["BuildToolRep", "buildRepo"], ["BuildFailurePatternForProjectRepo", "buildFailurePatternRepo"], ["CodeCoverageRepository", "codeCoverageRepo"], ["CodeQualityRep", "codeQualityRep"], ["HealthDataRepo", "healthRepo"], ["SCMToolRepository", "scmRepo"], ["ALMConfigRepo", "almConfigRepo"], ["ChartConfigRepo", "chartConfigRepo"], ["GoalSettingRep", "goalSettingRepo"], ["PortfolioConfigRepo", "portfolioRepo"], ["ProjectHealthRep", "projectHealthRepo"], ["UserAssociationRep", "userAssociation"], ["<PERSON><PERSON>", "LOG"], ["long", "lastUpdated"], ["Date", "date"], ["long", "timeStamp"], ["BuildFailurePatternForProjectInJenkinsModel", "failurePatternObj"], ["return", "true"], ["return", "false"], ["ConfigurationSetting", "config"], ["return", "config"]], "annotations": ["Service", "Autowired", "Override", "Cacheable", "Override", "Override", "Override", "Override", "Override", "Cacheable"]}, "DateUtil": {"methods": ["GetDateInFormat"], "variables": [["String", "pattern"], ["SimpleDateFormat", "simpleDateFormat"], ["Date", "date"], ["Calendar", "c"], ["int", "i"], ["Date", "start"], ["Date", "end"], ["return", "map"]], "annotations": ["Component"]}}, "variable_flows": {}, "method_signatures": {"Configure": {"class": "Application", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\Application.java", "stage": "stage_3_registry"}, "GetDataFromTools": {"class": "TriggerCollector", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\TriggerCollector.java", "stage": "stage_3_registry"}, "DelDuplicate": {"class": "ALMServiceImplementation", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\service\\ALMServiceImplementation.java", "stage": "stage_3_registry"}, "DefectTrendAndClassification": {"class": "AlmController", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\api\\AlmController.java", "stage": "stage_3_registry"}, "DefectClassification": {"class": "AlmController", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\api\\AlmController.java", "stage": "stage_3_registry"}, "DelAllIsues": {"class": "AlmController", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\api\\AlmController.java", "stage": "stage_3_registry"}, "GetMetricsDatas": {"class": "AlmController", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\api\\AlmController.java", "stage": "stage_3_registry"}, "GetIterationData": {"class": "ALMServiceImplementation", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\service\\ALMServiceImplementation.java", "stage": "stage_3_registry"}, "GetDefectCount": {"class": "AlmController", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\api\\AlmController.java", "stage": "stage_3_registry"}, "GetRelease": {"class": "ALMServiceImplementation", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\service\\ALMServiceImplementation.java", "stage": "stage_3_registry"}, "GetAlmType": {"class": "ALMServiceImplementation", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\service\\ALMServiceImplementation.java", "stage": "stage_3_registry"}, "UpdateComponent": {"class": "AlmController", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\api\\AlmController.java", "stage": "stage_3_registry"}, "SaveEngScore": {"class": "ALMServiceImplementation", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\service\\ALMServiceImplementation.java", "stage": "stage_3_registry"}, "GetCcrLabel": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "SetCcrLabel": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "SetCycleTimeStates": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "SetThroughputStates": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "SetRejectionPhase": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "SetReopenPhase": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "SetTestingPhase": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "SetProductionPhase": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "GetStoryName": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "GetPriorityName": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "SetPriorityName": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "SetTaskName": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "GetReleaseName": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "SetReleaseName": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "SetCloseState": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "SetCriticalPriority": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "SetHighPriority": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "SetMedPriority": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "SetLowPriority": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "SetStoryName": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "GetProjectName": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "SetProjectName": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "GetDefectName": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "SetDefectName": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "GetTrendType": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "SetTrendType": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "SetTracksSet": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "SetNewState": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "SetProgressState": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "ToDetailsAddSetting": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "GetPersonHours": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "SetPersonHours": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "GetTimeZone": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "SetTimeZone": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "SetVelocityFields": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "GetEnvironment": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "SetEnvironment": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "IsSafeEnabled": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "SetSafeEnabled": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "GetFirstSprint": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "SetFirstSprint": {"class": "ALMConfigReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMConfigReq.java", "stage": "stage_3_registry"}, "SetAlmType": {"class": "ALMToolReq", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\request\\ALMToolReq.java", "stage": "stage_3_registry"}, "GetResult": {"class": "DataResponse", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\response\\DataResponse.java", "stage": "stage_3_registry"}, "GetLastUpdated": {"class": "DataResponse", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\response\\DataResponse.java", "stage": "stage_3_registry"}, "SaveALMConfig": {"class": "ALMConfigServiceImplementation", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\service\\ALMConfigServiceImplementation.java", "stage": "stage_3_registry"}, "EvictAllCaches": {"class": "ALMServiceImplementation", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\service\\ALMServiceImplementation.java", "stage": "stage_3_registry"}, "GetMetricDetails": {"class": "ALMServiceImplementation", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\service\\ALMServiceImplementation.java", "stage": "stage_3_registry"}, "GetDefectCounts": {"class": "ALMServiceImplementation", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\service\\ALMServiceImplementation.java", "stage": "stage_3_registry"}, "DelAllIssues": {"class": "ALMServiceImplementation", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\service\\ALMServiceImplementation.java", "stage": "stage_3_registry"}, "CallSP": {"class": "ALMServiceImplementation", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\service\\ALMServiceImplementation.java", "stage": "stage_3_registry"}, "CalcClosedSP": {"class": "ALMServiceImplementation", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\service\\ALMServiceImplementation.java", "stage": "stage_3_registry"}, "Compare": {"class": "ALMServiceImplementation", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\service\\ALMServiceImplementation.java", "stage": "stage_3_registry"}, "UpdateComponentsOfTaskandSubtask": {"class": "ALMServiceImplementation", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\service\\ALMServiceImplementation.java", "stage": "stage_3_registry"}, "GetDefectTrendAndClassification": {"class": "ALMServiceImplementation", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\service\\ALMServiceImplementation.java", "stage": "stage_3_registry"}, "GetDefectClassification": {"class": "ALMServiceImplementation", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\service\\ALMServiceImplementation.java", "stage": "stage_3_registry"}, "DeleteConfig": {"class": "ConfigurationSettingServiceImplementation", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\service\\ConfigurationSettingServiceImplementation.java", "stage": "stage_3_registry"}, "AddConfig": {"class": "ConfigurationSettingServiceImplementation", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\service\\ConfigurationSettingServiceImplementation.java", "stage": "stage_3_registry"}, "DeleteAllCollections": {"class": "ConfigurationSettingServiceImplementation", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\service\\ConfigurationSettingServiceImplementation.java", "stage": "stage_3_registry"}, "DeleteProject": {"class": "ConfigurationSettingServiceImplementation", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\service\\ConfigurationSettingServiceImplementation.java", "stage": "stage_3_registry"}, "GetConfigProject": {"class": "ConfigurationSettingServiceImplementation", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\service\\ConfigurationSettingServiceImplementation.java", "stage": "stage_3_registry"}, "GetDateInFormat": {"class": "DateUtil", "file_path": "C:\\Shaik\\sample\\ServicesBolt\\util\\DateUtil.java", "stage": "stage_3_registry"}}, "transformation_cache": {}, "variable_contexts": {}, "stage_2_results": {"relationships": 20, "folders": 5, "files": 15, "file_class_relationships": 13}, "ast_relationships": 643, "ast_name_mapping": {"application": "Application", "configure": "configure", "passwordencoder": "passwordEncoder", "main": "main", "triggercollector": "TriggerCollector", "getdatafromtools": "getDataFromTools", "almconfigcontroller": "ALMConfigController", "savealmconfig": "saveALMConfig", "retrievelist": "retrieveList", "retrievealmconfig": "retrieveALMConfig", "almcontroller": "AlmController", "storyageing": "storyAgeing", "groomingtable": "groomingTable", "delduplicate": "delDuplicate", "getsprintprogresshome": "getSprintProgressHome", "defectinsightdata": "defectInsightData", "defecttrendandclassification": "defectTrendAndClassification", "defectclassification": "defectClassification", "getissuebrakeup": "getIssueBrakeUp", "getstoryprogress": "getStoryProgress", "getdefectssummaryhome": "getDefectsSummaryHome", "gettaskriskstorypoint": "getTaskRiskStoryPoint", "burndowncalculation": "burndownCalculation", "getproductionslippage": "getProductionSlippage", "getdefectdensity": "getDefectDensity", "getdefectbacklog": "getDefectBacklog", "getdefectpareto": "getDefectPareto", "getactivesprints": "getActiveSprints", "delallisues": "delAllIsues", "getmetricsdatas": "getMetricsDatas", "getalltransitions": "getAllTransitions", "getprojectmetrics": "getProjectMetrics", "getchangesitems": "getChangesItems", "gettransitionsdata": "getTransitionsData", "getiterationdata": "getIterationData", "geteffortdata": "getEffortData", "getprojectdetials": "getProjectDetials", "getcurrentprojectdetials": "getCurrentProjectDetials", "getcurrentiter": "getCurrentIter", "getiterations": "getIterations", "getdefectcount": "getDefectCount", "getrelease": "getRelease", "getunreleasedata": "getUnReleaseData", "getdefects": "getDefects", "getsladata": "getSlaData", "getassigneeissues": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getdateiterations": "getDateIterations", "getproddefects": "getProdDefects", "getalmtype": "getAlmType", "getvelocitychart": "getVelocityChart", "getissuehierarchy": "getIssueHierarchy", "getcomponentwiseissuehierarchy": "getComponentWiseIssueHierarchy", "getcomponentwisevelocitychart": "getComponentWiseVelocityChart", "getcomponontwisesprintwisestories": "getComponontWiseSprintWiseStories", "getcomponents": "getComponents", "updatecomponent": "updateComponent", "saveengscore": "saveEngScore", "getfeaturemetrics": "getFeatureMetrics", "almconfigreq": "ALMConfigReq", "getccrlabel": "getCcrLabel", "setccrlabel": "setCcrLabel", "getcycletimestates": "getCycleTimeStates", "setcycletimestates": "setCycleTimeStates", "getthroughputstates": "getThroughputStates", "setthroughputstates": "setThroughputStates", "getrejectionphase": "getRejectionPhase", "setrejectionphase": "setRejectionPhase", "getreopenphase": "getReopenPhase", "setreopenphase": "setReopenPhase", "gettestingphase": "getTestingPhase", "settestingphase": "setTestingPhase", "getproductionphase": "getProductionPhase", "setproductionphase": "setProductionPhase", "getstoryname": "getStoryName", "getpriorityname": "getPriorityName", "setpriorityname": "setPriorityName", "gettaskname": "getTaskName", "settaskname": "setTaskName", "getreleasename": "getReleaseName", "setreleasename": "setReleaseName", "getclosestate": "getCloseState", "setclosestate": "setCloseState", "getcriticalpriority": "getCriticalPriority", "setcriticalpriority": "setCriticalPriority", "gethighpriority": "getHighPriority", "sethighpriority": "setHighPriority", "getmedpriority": "getMedPriority", "setmedpriority": "setMedPriority", "getlowpriority": "getLowPriority", "setlowpriority": "setLowPriority", "setstoryname": "setStoryName", "getprojectname": "getProjectName", "setprojectname": "setProjectName", "getdefectname": "getDefectName", "setdefectname": "setDefectName", "gettrendtype": "getTrendType", "settrendtype": "setTrendType", "gettracksset": "getTracksSet", "settracksset": "setTracksSet", "getnewstate": "getNewState", "setnewstate": "setNewState", "getprogressstate": "getProgressState", "setprogressstate": "setProgressState", "todetailsaddsetting": "toDetailsAddSetting", "getpersonhours": "getPersonHours", "setpersonhours": "setPersonHours", "gettimezone": "getTimeZone", "settimezone": "setTimeZone", "getvelocityfields": "getVelocityFields", "setvelocityfields": "setVelocityFields", "getenvironment": "getEnvironment", "setenvironment": "setEnvironment", "issafeenabled": "isSafeEnabled", "setsafeenabled": "setSafeEnabled", "getfirstsprint": "getFirstSprint", "setfirstsprint": "setFirstSprint", "almtoolreq": "ALMToolReq", "setalmtype": "setAlmType", "dataresponse": "DataResponse", "getresult": "getResult", "getlastupdated": "getLastUpdated", "almconfigserviceimplementation": "ALMConfigServiceImplementation", "getmetricdetails": "getMetricDetails", "getallmetrics": "getAllMetrics", "getprojectdetails": "getProjectDetails", "getdefectcounts": "getDefectCounts", "getcrtitr": "getCrtItr", "getcurrentprojectdetails": "getCurrentProjectDetails", "delallissues": "delAllIssues", "getcomponentvelocity": "getComponentVelocity", "getcomponentssprint": "getComponentsSprint", "updatecomponentsoftaskandsubtask": "updateComponentsOfTaskandSubtask", "gettaskrisk": "getTaskRisk", "getdefectinsightdata": "getDefectInsightData", "defectparetocalculation": "defectParetoCalculation", "getdefecttrendandclassification": "getDefectTrendAndClassification", "getstoryageingdata": "getStoryAgeingData", "getgroomingtable": "getGroomingTable", "getalliterations": "getAllIterations", "getdefectclassification": "getDefectClassification", "getcomponentvelocitychart": "getComponentVelocityChart", "getcomponentssprintstories": "getComponentsSprintStories", "getissuehierarchychart": "getIssueHierarchyChart", "getcomponentwiseissuehierarchychart": "getComponentWiseIssueHierarchyChart", "getcomponentschart": "getComponentsChart", "almserviceimplementation": "ALMServiceImplementation", "populateauthor": "populate<PERSON><PERSON><PERSON>", "getcomponentlist": "getComponentList", "getsprintwisestories": "getSprintWiseStories", "filtertrans": "filterTrans", "callsp": "callSP", "calcclosedsp": "calcClosedSP", "storyloop": "storyLoop", "storylooprefined": "storyLoopRefined", "transitioncomparator": "TransitionComparator", "compare": "compare", "sprintcomparatort": "SprintComparatort", "getconfig": "getConfig", "addconfig": "addConfig", "deleteconfig": "deleteConfig", "deleteallcollections": "deleteAllCollections", "deleteproject": "deleteProject", "getconfigproject": "getConfigProject", "configurationsettingserviceimplementation": "ConfigurationSettingServiceImplementation", "dateutil": "DateUtil", "getdateinformat": "getDateInFormat", "getlastweekworkingdaterange": "getLastWeekWorkingDateRange"}}