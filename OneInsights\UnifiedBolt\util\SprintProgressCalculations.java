package com.bolt.dashboard.util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.TreeMap;
import java.util.stream.Collectors;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ALMConfiguration;
import com.bolt.dashboard.core.model.ComponentIssueBreakup;
import com.bolt.dashboard.core.model.ComponentStoryProgress;
import com.bolt.dashboard.core.model.IterationModel;
import com.bolt.dashboard.core.model.IterationOutModel;
import com.bolt.dashboard.core.model.MetricsModel;
import com.bolt.dashboard.core.repository.ALMConfigRepo;
import com.bolt.dashboard.core.repository.MetricRepo;
import com.bolt.dashboard.jira.ChartCalculations;

public class SprintProgressCalculations {
	AnnotationConfigApplicationContext ctx = DataConfig.getContext();
	MetricRepo metricRepo = null;
	MongoTemplate mongo = null;
	List<IterationModel> allIterations = null;
	List<MetricsModel> allIssues = null;
	List<IterationOutModel> authorData = null;
	List<MetricsModel> storyData = null;
	List<MetricsModel> metricsTaskData = null;
	ALMConfiguration almConfig = null;
	List<IterationModel> iterationData = null;

	private ALMConfigRepo almConfigRepo;
	

	public SprintProgressCalculations() {
		if (mongo == null) {
			try {
				mongo = DataConfig.getInstance().mongoTemplate();
			} catch (Exception e) {
				LOGGER.error("Mongo error");
			}
		}
		almConfigRepo = ctx.getBean(ALMConfigRepo.class);

	}

	private static final Logger LOGGER = LogManager.getLogger(SprintProgressCalculations.class);

//	public static void main(String[] args) {
//		new SprintProgressCalculations().getissueBreakup("SP 1", "JIRA");
//	}
//	
	private void getInititialDetails(String projectName, String almType) {

		metricRepo = ctx.getBean(MetricRepo.class);
		allIssues = metricRepo.findByPNameAndPAlmType(projectName, almType);

		Query q = new Query();
		q.addCriteria(Criteria.where("pName").is(projectName).and("sName").nin("BACKLOG", "Backlog", "backlog",
				"FUTURE", "Future", "future", "BackLog"));
		q.with(new Sort(Sort.Direction.ASC, "stDate"));
		allIterations = mongo.find(q, IterationModel.class, "Iterations");

	}

	
	public List<ComponentIssueBreakup> getissueBreakup(String projectName, String almType) {
		List<ComponentIssueBreakup> response = new ArrayList<ComponentIssueBreakup>();
	
		getInititialDetails(projectName, almType);
		Map<Integer, String> spData = new HashMap<Integer, String>();
		for (int i = allIterations.size() - 1; i > 0 && i > allIterations.size() - 8; i--) {
			spData.put(allIterations.get(i).getsId(), allIterations.get(i).getsName());
		}ArrayList<String> componentList = new ArrayList<String>();
		componentList.add("All");

		componentList.addAll(new ChartCalculations().getComponents(projectName));
		Collections.sort(componentList);
		List<MetricsModel> metrics = null;
		for (String component : componentList) {
			ComponentIssueBreakup c = new ComponentIssueBreakup();
			List<Map> issueBreakupArr = new ArrayList<Map>();
			if (!component.equals("All")) {

				metrics = this.allIssues.stream()
						.filter(met -> (met.getComponents() != null
								? met.getComponents().get(0).equals(component)
								: false) && met.getAssgnTo() != null)
						.collect(Collectors.toList());
			} else {
				metrics = this.allIssues;

			}

		Map<String, List<MetricsModel>> groupedByType = metrics.stream()
				.collect(Collectors.groupingBy(MetricsModel::getType));

		for (Entry<String, List<MetricsModel>> issueType : groupedByType.entrySet()) {
			List<MetricsModel> issuesByType = issueType.getValue();
			String type = issueType.getKey();

			Map<Integer, List<MetricsModel>> sprints = issuesByType.stream()
					.collect(Collectors.groupingBy(MetricsModel::getsId));
			Map<String, List<MetricsModel>> states = issuesByType.stream()
					.collect(Collectors.groupingBy(MetricsModel::getState));

			List<String> statesList = new ArrayList<String>();
			List<Map> sprintInfo = new ArrayList<Map>();

			for (Entry<Integer, String> spr : spData.entrySet()) {

				String sprintName = spr.getValue();
				List<MetricsModel> issueBySprintAndType = sprints.get(spr.getKey());
				Map<String, Integer> countMap = new HashMap<String, Integer>();

				for (Entry<String, List<MetricsModel>> state : states.entrySet()) {
					countMap.put(state.getKey(), 0);
				}
				if (issueBySprintAndType != null) {
					for (MetricsModel issue : issueBySprintAndType) {
						int count = countMap.get(issue.getState());
						count++;
						countMap.put(issue.getState(), count);
					}
				}

				statesList = new ArrayList<String>();
				List<Integer> dataList = new ArrayList<Integer>();
				for (Entry<String, Integer> res : countMap.entrySet()) {
					statesList.add(res.getKey());
					dataList.add(res.getValue());
				}

				Map<Object, Object> info = new HashMap<>();
				info.put("sprintName", sprintName);
				info.put("data", dataList);
				sprintInfo.add(info);

			}
			Map<Object, Object> item = new HashMap<Object, Object>();
			item.put("type", type);
			item.put("states", statesList);
			item.put("sprintsInfo", sprintInfo);
			issueBreakupArr.add(item);

		}
		c.setComponent(component);
		c.setIterationOutModel(issueBreakupArr);
		response.add(c);
	}
	return response;
	}

	

	public List<ComponentStoryProgress> getStoryProgress(String projectName, String almType) {
		List<MetricsModel> filterStroyData;
		List<MetricsModel> filterMetricsData;
		List<ComponentStoryProgress> response = new ArrayList<ComponentStoryProgress>();
		List<String> sprintList = new ArrayList<String>();

		try {
			mongo = DataConfig.getInstance().mongoTemplate();
		} catch (Exception e) {
			LOGGER.error("Mongo error");
		}
		try {
			almConfig = almConfigRepo.findByProjectName(projectName).get(0);

			Query q = new Query();
			q.addCriteria(Criteria.where("pName").is(projectName).and("type").is(almConfig.getStoryName()).and("sName")
					.nin("BACKLOG", "Backlog", "backlog", "FUTURE", "Future", "future", "BackLog"));
			q.with(new Sort(Sort.Direction.ASC, "sName"));
			storyData = mongo.find(q, MetricsModel.class, "Metrics");
			q = new Query();
			q.addCriteria(Criteria.where("pName").is(projectName).and("type").in(almConfig.getTaskName()).and("sName")
//			q.addCriteria(Criteria.where("pName").is(projectName).and("sName")
					.nin("BACKLOG", "Backlog", "backlog", "FUTURE", "Future", "future", "BackLog"));
			q.with(new Sort(Sort.Direction.ASC, "sName"));
			metricsTaskData = mongo.find(q, MetricsModel.class, "Metrics");

			q = new Query();
			q.addCriteria(Criteria.where("pName").is(projectName).and("sName")
					.nin("BACKLOG", "Backlog", "backlog", "BackLog").and("state").nin("FUTURE", "Future", "future"));
			q.with(new Sort(Sort.Direction.ASC, "stDate"));
			iterationData = mongo.find(q, IterationModel.class, "Iterations");

//		for(List :authorData.)
//		IterationOutModel p = authorData.get(0);
			for (IterationModel item : iterationData) {
				if (item.getStDate() != 0)
					sprintList.add(item.getsName());
			}

			ArrayList<String> componentList = new ArrayList<String>();
			componentList.add("All");

			componentList.addAll(new ChartCalculations().getComponents(projectName));
			Collections.sort(componentList);

		boolean	flag = (componentList.size() > 1) ? true : false;
			for (String component : componentList) {
				ComponentStoryProgress c = new ComponentStoryProgress();

				List<StoryProgressSprintwise> storyProgressArr = new ArrayList<>();
				for (String sprint : sprintList) {
					List<StoryProgressModel> storyTasks = new ArrayList<>();

					if (flag && !component.equals("All")) {

						filterMetricsData = metricsTaskData.stream().filter(
								m -> (m.getComponents() != null ? m.getComponents().get(0).equals(component) : false)
										&& m.getsName().equals(sprint))
								.collect(Collectors.toList());
						filterStroyData = storyData.stream().filter(
								m -> (m.getComponents() != null ? m.getComponents().get(0).equals(component) : false)
										&& m.getsName().equals(sprint))
								.collect(Collectors.toList());

					} else {
						filterMetricsData = metricsTaskData.stream().filter(m -> m.getsName().equals(sprint))
								.collect(Collectors.toList());
						filterStroyData = storyData.stream().filter(m -> m.getsName().equals(sprint))
								.collect(Collectors.toList());
					}

					if (sprint.equals("NP PI-3.2")) {
						LOGGER.info("debug");
					}
					if (almConfig.getTrendType().equals("Story Point Map")) {

						// from the Story

						for (MetricsModel story : filterStroyData) {
							if (story != null) {
								Double storyPoints = 0.0;
								double successPercentage = 0;
								int sPsize = story.getStoryPoints().size();
								if (sPsize > 0) {
									Map<Long, Double> spData = story.getStoryPoints();
									TreeMap<Long, Double> sorted = new TreeMap<>();

									// Copy all data from hashMap into TreeMap
									sorted.putAll(spData);

									storyPoints = (Double) sorted.values().toArray()[sorted.size() - 1];
								}
								if (story.getwId().equals("MP360-156") == true) {
									LOGGER.info("debugger......!");
								}

								if (story.getSubtaskList() != null && !story.getSubtaskList().isEmpty()) {
									double successCount = 0;

									double subTasklength = 0;
									for (String subTask : story.getSubtaskList()) {
										for (MetricsModel value : filterMetricsData) {
											if ((subTask != null && value.getwId() != null)
													&& subTask.equals(value.getwId())) {
												if (Arrays.asList(almConfig.getTaskName()).contains(value.getType())) {
													subTasklength++;

													if (value.getState() != null
															&& Arrays.asList(almConfig.getCloseState())
																	.contains(value.getState())) {
														successCount++;
														successPercentage = ((successCount / subTasklength) * 100);
													}
												}
											}
										}
									}
									StoryProgressModel sp = new StoryProgressModel();
									sp.setwId(story.getwId());
									sp.setStoryPoint(storyPoints);
									sp.setSuccessPercentage(successPercentage);
									storyTasks.add(sp);

								} else if (story.getState() != null
										&& Arrays.asList(almConfig.getCloseState()).contains(story.getState())) {
									successPercentage = 100;
									StoryProgressModel sp = new StoryProgressModel();
									sp.setwId(story.getwId());
									sp.setStoryPoint(storyPoints);
									sp.setSuccessPercentage(successPercentage);
									storyTasks.add(sp);
								}
							}
						}
					} else {
						boolean temp = false;
						for (MetricsModel o : filterStroyData) {
							// If issues do not have subtasks, consider the linked tasks
							if (o.getSubtaskList() != null && !o.getSubtaskList().isEmpty()) {
								temp = true;
							}
													// For each story call a function to find progress of the subtasks/linked issue
							// completed

							double successPercentage = 0;
							Double storyPoints = 0.0;
							int sPsize = o.getStoryPoints().size();
							if (o.getwId().equals("MP360-156") == true) {
								LOGGER.info("debugger......!");
							}
							if (sPsize > 0) {
								
								Map<Long, Double> spData = o.getStoryPoints();
								TreeMap<Long, Double> sorted = new TreeMap<>();
								// Copy all data from hashMap into TreeMap
								sorted.putAll(spData);

								storyPoints = ((Double) sorted.values().toArray()[sorted.size() - 1]);
							}
//	              
							if (temp && o.getSubtaskList() != null) {
//
//							for (String item : o.getSubtaskList()) {
//								// "subtaskList" )
//								if (item != null) {
//								callOperation(item);

								if (o.getSubtaskList() != null && !o.getSubtaskList().isEmpty()) {
									int successCount = 0;
									int subTasklength = 0;
									for (String subTask : o.getSubtaskList()) {
										for (MetricsModel value : filterMetricsData) {
											if ((subTask != null && value.getwId() != null)
													&& subTask.equals(value.getwId())) {
												if (Arrays.asList(almConfig.getTaskName()).contains(value.getType())) {
													subTasklength++;
													if (value.getState() != null
															&& Arrays.asList(almConfig.getCloseState())
																	.contains(value.getState())) {
														successCount++;
														successPercentage = ((successCount / (double)subTasklength) * 100f);
														successPercentage = Math.floor(successPercentage * 100) / 100;
													}
												}
											}
										}
									}
								}
								StoryProgressModel sp = new StoryProgressModel();
								sp.setwId(o.getwId());
								sp.setStoryPoint(storyPoints != null ? storyPoints : 0);
								sp.setSuccessPercentage(successPercentage);
								storyTasks.add(sp);

//								}
//							}
							} else {
								double successCount = 0;
								// "inWardIssueLink";
//						 if(o.getTaskList()!=null)
//						for (String item : o.getTaskList()) {
//							if (item != null) {
//								callOperation(item);
								if (o.getTaskList() != null && !o.getTaskList().isEmpty()) {

									double tasklength = 0;
									for (String taskItem : o.getTaskList()) {
										for (MetricsModel value : filterMetricsData) {
											if ((taskItem != null && value.getwId() != null)
													&& taskItem.equals(value.getwId())) {
												if (Arrays.asList(almConfig.getTaskName()).contains(value.getType())) {
													tasklength++;
													if (value.getState() != null
															&& Arrays.asList(almConfig.getCloseState())
																	.contains(value.getState())) {
														successCount++;
														successPercentage = ((successCount / tasklength) * 100);
														successPercentage = Math.floor(successPercentage * 100) / 100;
													}
												}
											}
										}
									}
//								}
//							}
								} else if (o.getState() != null
										&& Arrays.asList(almConfig.getCloseState()).contains(o.getState())) {
									successPercentage = 100;
									StoryProgressModel sp = new StoryProgressModel();
									sp.setwId(o.getwId());
									sp.setStoryPoint(storyPoints);
									sp.setSuccessPercentage(successPercentage);
									storyTasks.add(sp);
								}

//	                if (totalRem !=null|| totalSpent) {
								double val = 0;
//					DecimalFormat df = new DecimalFormat("###.##");
//					if(totalRem + totalSpent>0)
//					 val = ((((totalSpent) / (totalRem + totalSpent)) * 100));
								StoryProgressModel sp = new StoryProgressModel();
								sp.setwId(o.getwId());
								sp.setStoryPoint(storyPoints != null ? storyPoints : 0);
//					sp.setSuccessPercentage(Double.valueOf(df.format(val)));
								sp.setSuccessPercentage(successPercentage);
								storyTasks.add(sp);

//	                }

							}
						}

					}
					storyProgressArr.add(new StoryProgressSprintwise(sprint, storyTasks));

				}
				c.setComponent(component);
				c.setStoryProgressList(storyProgressArr);
				response.add(c);
			}
		} catch (Exception e) {
			LOGGER.error(String.format("story progress error :  %s " , e.getMessage()));
		}

		return response;
	}

	// Progress of each issue linked to the story
	public void callOperation(String item) {
		if (metricsTaskData != null)
			for (MetricsModel val : metricsTaskData) {
//				if (item.equals(val.getwId()) && val.getActEst() != null) {
//					totalRem += val.getRemTime() != null ? val.getRemTime() : 0;
//					totalSpent += val.getEffort() != null ? val.getEffort() : 0;
//
//				}
			}
	}

}
