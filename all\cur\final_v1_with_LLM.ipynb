{"cells": [{"cell_type": "code", "execution_count": null, "id": "ca138d3e", "metadata": {}, "outputs": [], "source": ["#connect to Neo4j using langchain-community's Neo4jGraph class. Replace the URL, username, and password with your own Neo4j setup.\n", "from pathlib import Path\n", "from langchain_community.graphs import Neo4jGraph\n", "\n", "graph = Neo4jGraph(\n", "    url=\"bolt://localhost:7687\",\n", "    username=\"neo4j\",\n", "    password=\"Test@7889\",\n", "    database=\"Test\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8fb47074", "metadata": {}, "outputs": [], "source": ["# Create a set to track created folders\n", "base_path = Path(\"C:/Shaik/sample/java\")\n", "\n", "created_folders = set()\n", "\n", "for file_path in base_path.rglob(\"*.java\"):\n", "    relative_path = file_path.relative_to(base_path)\n", "    parts = relative_path.parts  # e.g. ('com', 'morganstanley', 'loanapp', 'controller', 'Foo.java')\n", "    \n", "    parent_folder = base_path.name  # \"java\"\n", "    parent_folder_path = \"\"\n", "\n", "    if parent_folder not in created_folders:\n", "        graph.query(\n", "            \"MERGE (f:Folder {name: $name})\",\n", "            {\"name\": parent_folder}\n", "        )\n", "        created_folders.add(parent_folder)\n", "\n", "    for part in parts[:-1]:\n", "        parent_folder_path = str(Path(parent_folder_path) / part) if parent_folder_path else part\n", "        if parent_folder_path not in created_folders:\n", "            graph.query(\n", "                \"MERGE (f:Folder {name: $name})\",\n", "                {\"name\": parent_folder_path}\n", "            )\n", "            created_folders.add(parent_folder_path)\n", "\n", "        graph.query(\n", "            \"\"\"\n", "            MATCH (p:Folder {name: $parent_name}), (c:Folder {name: $child_name})\n", "            MERGE (p)-[:CONTAINS]->(c)\n", "            \"\"\",\n", "            {\"parent_name\": parent_folder, \"child_name\": parent_folder_path}\n", "        )\n", "        parent_folder = parent_folder_path\n", "\n", "    file_name = parts[-1].lower()\n", "    graph.query(\n", "        \"MERGE (f:File {name: $name})\",\n", "        {\"name\": file_name}\n", "    )\n", "    graph.query(\n", "        \"\"\"\n", "        MATCH (p:Folder {name: $parent_name}), (f:File {name: $file_name})\n", "        MERGE (p)-[:CONTAINS]->(f)\n", "        \"\"\",\n", "        {\"parent_name\": parent_folder, \"file_name\": file_name}\n", "    )\n"]}, {"cell_type": "code", "execution_count": null, "id": "ef2fae87", "metadata": {}, "outputs": [], "source": ["# Load and split Java files into chunks\n", "from langchain_community.document_loaders import TextLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter, Language\n", "\n", "java_docs = []\n", "for path in base_path.rglob(\"*.java\"):\n", "    loader = TextLoader(str(path))\n", "    loaded_docs = loader.load()\n", "    for doc in loaded_docs:\n", "        doc.metadata[\"language\"] = \"java\"\n", "        doc.metadata[\"source\"] = str(path)\n", "    java_docs.extend(loaded_docs)\n", "\n", "splitter_java = RecursiveCharacterTextSplitter.from_language(\n", "    language=Language.JAVA,\n", "    chunk_size=2000,\n", "    chunk_overlap=200\n", ")\n", "\n", "split_docs = []\n", "for doc in java_docs:\n", "    chunks = splitter_java.split_documents([doc])\n", "    for idx, chunk in enumerate(chunks):\n", "        chunk.metadata[\"chunk_index\"] = idx\n", "        chunk.metadata[\"language\"] = \"java\"\n", "        chunk.metadata[\"source\"] = doc.metadata[\"source\"]\n", "        split_docs.append(chunk)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "4794559a", "metadata": {}, "outputs": [], "source": ["# Build system prompt for code and data lineage analysis\n", "import os\n", "\n", "def build_system_prompt(file_path: str) -> str:\n", "    file_name = os.path.basename(file_path).lower()\n", "    prompt = f\"\"\"\n", "You are a **code and data lineage analysis engine**. From the provided Java code chunk, extract **structured graph triples** in the format:\n", "\n", "[SourceNodeLabel]:SourceNodeName -[RELATION]-> [TargetNodeLabel]:TargetNodeName\n", "\n", "Context:\n", "- This code chunk is from the file: File:{file_name}\n", "- Always include the File node: File:{file_name} and connect classes/functions to this File node using DECLARES relationships.\n", "- Ignore package statements for folder nodes.\n", "- Include tables, columns, APIs if detected.\n", "- Capture function calls, inheritance, data lineage as usual.\n", "\n", "Layers:\n", "- File -> Class\n", "- Class -> Function/Method\n", "- Function/Method -> Function/Method (CALLS)\n", "- Class -> Class (INHERITS)\n", "- Function/Method -> Table (READS_FROM / WRITES_TO)\n", "- Function/Method -> API (CALLS_API)\n", "\n", "Node Types:\n", "- File, Class, Function, Table, Column, API\n", "\n", "Relationship Types:\n", "- DECLARES, CALLS, INHERITS, READS_FROM, WRITES_TO, CALLS_API\n", "\n", "Return only these triples, nothing else.\n", "\n", "Example:\n", "File:{file_name} -[DECLARES]-> Class:MyClass\n", "Class:MyClass -[DECLARES]-> Function:myMethod\n", "Function:myMethod -[CALLS]-> Function:helperMethod\n", "\"\"\"\n", "    return prompt\n"]}, {"cell_type": "code", "execution_count": null, "id": "ad06aad9", "metadata": {}, "outputs": [], "source": ["google_api_key = \"AIzaSyDY2qPx7-wQk90KjsCnnFgc88Qzb1lV5b0\""]}, {"cell_type": "code", "execution_count": null, "id": "60dcefa4", "metadata": {}, "outputs": [], "source": ["# Import necessary libraries for LLM and graph transformation\n", "from langchain_google_genai import ChatGoogleGenerativeAI\n", "from langchain_experimental.graph_transformers import LLMGraphTransformer\n", "\n", "llm = ChatGoogleGenerativeAI(\n", "    model=\"gemini-2.5-pro\",\n", "    temperature=0,\n", "    google_api_key=google_api_key\n", ")\n", "\n", "all_graph_documents = []\n", "\n", "for chunk in split_docs:\n", "    system_prompt = build_system_prompt(chunk.metadata[\"source\"])\n", "\n", "    transformer = LLMGraphTransformer(\n", "        llm=llm,\n", "        additional_instructions=system_prompt,\n", "        allowed_nodes=[\"File\", \"Class\", \"Function\", \"Table\", \"Column\", \"API\"],\n", "        allowed_relationships=[\n", "            (\"File\", \"DECLARES\", \"Class\"),\n", "            (\"Class\", \"DECLARES\", \"Function\"),\n", "            (\"Function\", \"CALLS\", \"Function\"),\n", "            (\"Class\", \"INHERITS\", \"Class\"),\n", "            (\"Function\", \"READS_FROM\", \"Table\"),\n", "            (\"Function\", \"WRITES_TO\", \"Table\"),\n", "            (\"Function\", \"CALLS_API\", \"API\"),\n", "        ],\n", "        strict_mode=True,\n", "        node_properties=False,\n", "        relationship_properties=False,\n", "    )\n", "\n", "    graph_docs = transformer.convert_to_graph_documents([chunk])\n", "    all_graph_documents.extend(graph_docs)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "ea0c5afa", "metadata": {}, "outputs": [], "source": ["graph_docs"]}, {"cell_type": "code", "execution_count": null, "id": "fd1164e1", "metadata": {}, "outputs": [], "source": ["# Merging LLM relationships into Neo4j\n", "from tqdm import tqdm\n", "\n", "for gd in tqdm(all_graph_documents, desc=\"Merging LLM relationships into Neo4j\"):\n", "    # Create/merge nodes\n", "    for node in gd.nodes:\n", "        node_name = node.id.strip().lower()\n", "        node_type = node.type\n", "\n", "        # Files already exist in Stage 1, do not recreate\n", "        if node_type == \"File\":\n", "            continue\n", "\n", "        # Create or match other nodes\n", "        graph.query(\n", "            f\"\"\"\n", "            MERGE (n:{node_type} {{name: $name}})\n", "            \"\"\",\n", "            {\"name\": node_name}\n", "        )\n", "\n", "    #Create/merge relationships\n", "    for rel in gd.relationships:\n", "        source_name = rel.source.id.strip().lower()\n", "        target_name = rel.target.id.strip().lower()\n", "        rel_type = rel.type\n", "\n", "        source_type = rel.source.type\n", "        target_type = rel.target.type\n", "\n", "        # Build and run relationship merge query\n", "        query = f\"\"\"\n", "        MATCH (s:{source_type} {{name: $source_name}})\n", "        MATCH (t:{target_type} {{name: $target_name}})\n", "        MERGE (s)-[r:{rel_type}]->(t)\n", "        \"\"\"\n", "        graph.query(\n", "            query,\n", "            {\"source_name\": source_name, \"target_name\": target_name}\n", "        )\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "a3016b26", "metadata": {}, "outputs": [], "source": ["# Clean up Neo4j database\n", "graph.query(\"MATCH (n) DETACH DELETE n\")\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}