{"cells": [{"cell_type": "code", "execution_count": null, "id": "c86c50cf", "metadata": {}, "outputs": [], "source": ["import os\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "import pandas as pd\n", "from tree_sitter import Language, Parser\n", "import tree_sitter_java as tsjava\n", "\n", "from langchain_community.document_loaders import TextLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter, Language as LC_Language\n", "from langchain_google_genai import ChatGoogleGenerativeAI\n", "from langchain_experimental.graph_transformers import LLMGraphTransformer\n", "from langchain_community.graphs import Neo4jGraph\n", "\n", "# Configurations\n", "BASE_PATH = Path(\"C:/Shaik/sample/java\")\n", "NEO4J_URI = \"bolt://localhost:7687\"\n", "NEO4J_USER = \"neo4j\"\n", "NEO4J_PASSWORD = \"Test@7889\"\n", "NEO4J_DB = \"astllm\"\n", "\n", "\n", "# LLM and Neo4j\n", "graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)\n", "\n", "# Tree-sitter Java parser setup\n", "JAVA_LANGUAGE = Language(tsjava.language())\n", "parser = Parser(JAVA_LANGUAGE)"]}, {"cell_type": "code", "execution_count": 3, "id": "ae982593", "metadata": {}, "outputs": [], "source": ["def extract_folder_file_hierarchy(base_path):\n", "    folder_records = []\n", "    file_records = []\n", "    base_path = os.path.abspath(base_path)\n", "    base_folder_name = os.path.basename(base_path)\n", "\n", "    folder_records.append({\n", "        \"source_node\": None,\n", "        \"source_type\": None,\n", "        \"destination_node\": base_folder_name,\n", "        \"destination_type\": \"Folder\",\n", "        \"relationship\": None,\n", "        \"file_path\": None\n", "    })\n", "\n", "    for root, dirs, files in os.walk(base_path):\n", "        rel_root = os.path.relpath(root, base_path)\n", "        if rel_root == \".\":\n", "            parent_folder = None\n", "            current_folder = base_folder_name\n", "        elif '/' not in rel_root and '\\\\' not in rel_root:\n", "            parent_folder = base_folder_name\n", "            current_folder = rel_root\n", "        else:\n", "            parent_folder = os.path.dirname(rel_root)\n", "            current_folder = os.path.basename(rel_root)\n", "\n", "        if current_folder:\n", "            folder_records.append({\n", "                \"source_node\": parent_folder,\n", "                \"source_type\": \"Folder\" if parent_folder else None,\n", "                \"destination_node\": current_folder,\n", "                \"destination_type\": \"Folder\",\n", "                \"relationship\": \"CONTAINS\" if parent_folder else None,\n", "                \"file_path\": None\n", "            })\n", "\n", "        for f in files:\n", "            if f.endswith(\".java\"):\n", "                file_path = os.path.join(root, f)\n", "                file_rel_path = os.path.relpath(file_path, base_path)\n", "                file_records.append({\n", "                    \"source_node\": current_folder,\n", "                    \"source_type\": \"Folder\",\n", "                    \"destination_node\": f,\n", "                    \"destination_type\": \"File\",\n", "                    \"relationship\": \"CONTAINS\",\n", "                    \"file_path\": file_rel_path\n", "                })\n", "\n", "    return folder_records, file_records"]}, {"cell_type": "code", "execution_count": 4, "id": "9c756c08", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extracted 8 folders and 12 files.\n"]}], "source": ["folder_records, file_records = extract_folder_file_hierarchy(BASE_PATH)\n", "print(f\"Extracted {len(folder_records)} folders and {len(file_records)} files.\")"]}, {"cell_type": "code", "execution_count": 5, "id": "110e6e23", "metadata": {}, "outputs": [], "source": ["\n", "def read_source_code(file_path):\n", "    with open(file_path, \"r\", encoding=\"utf-8\") as f:\n", "        return f.read().encode(\"utf-8\")\n", "\n", "\n", "def extract_ast_structure(node, source_code, file_path):\n", "    records = []\n", "    current_class = None\n", "    current_method = None\n", "\n", "    def traverse(n):\n", "        nonlocal current_class, current_method\n", "        if n.type == \"class_declaration\":\n", "            for child in n.children:\n", "                if child.type == \"identifier\":\n", "                    class_name = source_code[child.start_byte:child.end_byte].decode()\n", "                    records.append({\n", "                        \"source_node\": os.path.basename(file_path),\n", "                        \"source_type\": \"File\",\n", "                        \"destination_node\": class_name,\n", "                        \"destination_type\": \"Class\",\n", "                        \"relationship\": \"DECLARES\",\n", "                        \"file_path\": file_path\n", "                    })\n", "                    current_class = class_name\n", "            for child in n.children:\n", "                traverse(child)\n", "            current_class = None\n", "\n", "        elif n.type == \"method_declaration\":\n", "            for child in n.children:\n", "                if child.type == \"identifier\":\n", "                    method_name = source_code[child.start_byte:child.end_byte].decode()\n", "                    records.append({\n", "                        \"source_node\": current_class,\n", "                        \"source_type\": \"Class\",\n", "                        \"destination_node\": method_name,\n", "                        \"destination_type\": \"Method\",\n", "                        \"relationship\": \"DECLARES\",\n", "                        \"file_path\": file_path\n", "                    })\n", "                    current_method = method_name\n", "            for child in n.children:\n", "                traverse(child)\n", "            current_method = None\n", "\n", "        elif n.type == \"method_invocation\":\n", "            for child in n.children:\n", "                if child.type == \"identifier\":\n", "                    called_method = source_code[child.start_byte:child.end_byte].decode()\n", "                    records.append({\n", "                        \"source_node\": current_method,\n", "                        \"source_type\": \"Method\",\n", "                        \"destination_node\": called_method,\n", "                        \"destination_type\": \"Method\",\n", "                        \"relationship\": \"CALLS\",\n", "                        \"file_path\": file_path\n", "                    })\n", "            for child in n.children:\n", "                traverse(child)\n", "\n", "        elif n.type == \"variable_declarator\":\n", "            for child in n.children:\n", "                if child.type == \"identifier\":\n", "                    var_name = source_code[child.start_byte:child.end_byte].decode()\n", "                    source = current_method or current_class or os.path.basename(file_path)\n", "                    source_type = \"Method\" if current_method else \"Class\" if current_class else \"File\"\n", "                    rel = \"USES\" if current_method else \"HAS_FIELD\" if current_class else \"DECLARES\"\n", "                    records.append({\n", "                        \"source_node\": source,\n", "                        \"source_type\": source_type,\n", "                        \"destination_node\": var_name,\n", "                        \"destination_type\": \"Variable\",\n", "                        \"relationship\": rel,\n", "                        \"file_path\": file_path\n", "                    })\n", "            for child in n.children:\n", "                traverse(child)\n", "        else:\n", "            for child in n.children:\n", "                traverse(child)\n", "\n", "    traverse(node)\n", "    return records"]}, {"cell_type": "code", "execution_count": 13, "id": "c88260c7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extracted 244 AST lineage records.\n", "Prepared 262 AST + folder structure records for Neo4j ingestion.\n"]}], "source": ["\n", "ast_records = []\n", "for root, dirs, files in os.walk(BASE_PATH):\n", "    for file in files:\n", "        if file.endswith(\".java\"):\n", "            file_path = os.path.join(root, file)\n", "            source_code = read_source_code(file_path)\n", "            tree = parser.parse(source_code)\n", "            ast_records.extend(extract_ast_structure(tree.root_node, source_code, file_path))\n", "\n", "print(f\"Extracted {len(ast_records)} AST lineage records.\")\n", "\n", "\n", "# Create DataFrames for folder, file, and AST records\n", "df_folders = pd.DataFrame(folder_records)\n", "df_files = pd.DataFrame(file_records)\n", "df_ast = pd.DataFrame(ast_records)\n", "\n", "# Combine and clean: drop rows with null critical columns and strip whitespace\n", "df_combined_ast = pd.concat([df_folders, df_files, df_ast], ignore_index=True)\n", "df_combined_ast = df_combined_ast.dropna(subset=[\"source_node\", \"destination_node\", \"source_type\", \"destination_type\", \"relationship\"])\n", "\n", "for col in [\"source_node\", \"destination_node\", \"source_type\", \"destination_type\", \"relationship\"]:\n", "    df_combined_ast[col] = df_combined_ast[col].astype(str).str.strip()\n", "\n", "# **Normalize all key columns to lowercase to avoid duplicates**\n", "for col in [\"source_node\", \"destination_node\", \"source_type\", \"destination_type\", \"relationship\"]:\n", "    df_combined_ast[col] = df_combined_ast[col].str.lower()\n", "\n", "print(f\"Prepared {len(df_combined_ast)} AST + folder structure records for Neo4j ingestion.\")"]}, {"cell_type": "code", "execution_count": 14, "id": "17f35042", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "Index: 262 entries, 2 to 263\n", "Data columns (total 6 columns):\n", " #   Column            Non-Null Count  Dtype \n", "---  ------            --------------  ----- \n", " 0   source_node       262 non-null    object\n", " 1   source_type       262 non-null    object\n", " 2   destination_node  262 non-null    object\n", " 3   destination_type  262 non-null    object\n", " 4   relationship      262 non-null    object\n", " 5   file_path         256 non-null    object\n", "dtypes: object(6)\n", "memory usage: 14.3+ KB\n"]}], "source": ["df_combined_ast.info()"]}, {"cell_type": "code", "execution_count": 15, "id": "c25715e6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Split 12 Java files into 12 LLM chunks.\n"]}], "source": ["\n", "# Text splitting for LLM processing\n", "splitter = RecursiveCharacterTextSplitter.from_language(\n", "    language=LC_Language.JAVA,\n", "    chunk_size=4000,\n", "    chunk_overlap=200\n", ")\n", "\n", "java_docs = []\n", "for root, dirs, files in os.walk(BASE_PATH):\n", "    for file in files:\n", "        if file.endswith(\".java\"):\n", "            loader = TextLoader(os.path.join(root, file))\n", "            java_docs.extend(loader.load())\n", "\n", "split_docs = []\n", "for doc in java_docs:\n", "    split_docs.extend(splitter.split_documents([doc]))\n", "\n", "print(f\"Split {len(java_docs)} Java files into {len(split_docs)} LLM chunks.\")"]}, {"cell_type": "code", "execution_count": 16, "id": "fda3fd95", "metadata": {}, "outputs": [], "source": ["\n", "def build_system_prompt(file_path: str, ast_records_df: pd.DataFrame) -> str:\n", "    ast_subset = ast_records_df[ast_records_df[\"file_path\"] == file_path]\n", "    ast_context = \"\"\n", "    for _, row in ast_subset.iterrows():\n", "        ast_context += f\"{row['source_type'].capitalize()}:{row['source_node']} -[{row['relationship'].upper()}]-> {row['destination_type'].capitalize()}:{row['destination_node']}\\n\"\n", "\n", "    prompt = f\"\"\"\n", "You are a code and data lineage extraction engine.\n", "\n", "Given Java code and the following AST context:\n", "{ast_context}\n", "\n", "Extract lineage triples in the format:\n", "[SourceNodeType]:SourceNodeName -[RELATIONSHIP]-> [TargetNodeType]:TargetNodeName\n", "\n", "Valid Types:\n", "File, Class, Method, Variable, Table, API\n", "\n", "Valid Relationships:\n", "DECLARES, CONTAINS, CALLS, HAS_FIELD, USES, READS_FROM, WRITES_TO, CALLS_API, etc.\n", "\n", "Return triples only without explanations.\n", "\"\"\"\n", "    return prompt\n", "\n"]}, {"cell_type": "code", "execution_count": 27, "id": "0d503c32", "metadata": {}, "outputs": [], "source": ["google_api_key = \"AIzaSyBBxguitG2elmyMp1zSgqX5M4FCVdb85cs\"\n", "\n", "llm = ChatGoogleGenerativeAI(\n", "    model=\"gemini-2.5-pro\",\n", "    temperature=0,\n", "    google_api_key=google_api_key\n", ")"]}, {"cell_type": "code", "execution_count": 28, "id": "d0e23a01", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["LLM Extraction per chunk:   0%|          | 0/12 [00:00<?, ?it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["LLM Extraction per chunk: 100%|██████████| 12/12 [05:53<00:00, 29.49s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Collected 128 LLM lineage triples.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["\n", "\n", "all_llm_lineage = []\n", "\n", "for chunk in tqdm(split_docs, desc=\"LLM Extraction per chunk\"):\n", "    system_prompt = build_system_prompt(chunk.metadata.get(\"source\"), df_ast)\n", "\n", "    transformer = LLMGraphTransformer(\n", "        llm=llm,\n", "        additional_instructions=system_prompt,\n", "        allowed_nodes=[\"File\", \"Class\", \"Method\", \"Variable\", \"Table\", \"API\"],\n", "        allowed_relationships=[\n", "            (\"File\", \"DECLARES\", \"Class\"),\n", "            (\"Class\", \"DECLARES\", \"Method\"),\n", "            (\"Method\", \"CALLS\", \"Method\"),\n", "            (\"Class\", \"HAS_FIELD\", \"Variable\"),\n", "            (\"Method\", \"USES\", \"Variable\"),\n", "            (\"Method\", \"READS_FROM\", \"Table\"),\n", "            (\"Method\", \"WRITES_TO\", \"Table\"),\n", "            (\"Method\", \"CALLS_API\", \"API\")\n", "        ],\n", "        strict_mode=True,\n", "        node_properties=False,\n", "        relationship_properties=False,\n", "    )\n", "\n", "    graph_docs = transformer.convert_to_graph_documents([chunk])\n", "\n", "    for gd in graph_docs:\n", "        source_file = chunk.metadata.get(\"source\", None)  # get source path of chunk\n", "\n", "        for rel in gd.relationships:\n", "            all_llm_lineage.append({\n", "                \"source_node\": rel.source.id.strip().lower() if rel.source.id else None,\n", "                \"source_type\": rel.source.type.strip().lower() if rel.source.type else None,\n", "                \"destination_node\": rel.target.id.strip().lower() if rel.target.id else None,\n", "                \"destination_type\": rel.target.type.strip().lower() if rel.target.type else None,\n", "                \"relationship\": rel.type.strip().lower() if rel.type else None,\n", "                \"file_path\": source_file\n", "            })\n", "\n", "print(f\"Collected {len(all_llm_lineage)} LLM lineage triples.\")"]}, {"cell_type": "code", "execution_count": 29, "id": "7de4c2d0", "metadata": {}, "outputs": [], "source": ["\n", "df_llm_lineage = pd.DataFrame(all_llm_lineage).dropna(subset=[\n", "    \"source_node\", \"source_type\", \"destination_node\", \"destination_type\", \"relationship\"\n", "])"]}, {"cell_type": "code", "execution_count": 30, "id": "2b040a4a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 128 entries, 0 to 127\n", "Data columns (total 6 columns):\n", " #   Column            Non-Null Count  Dtype \n", "---  ------            --------------  ----- \n", " 0   source_node       128 non-null    object\n", " 1   source_type       128 non-null    object\n", " 2   destination_node  128 non-null    object\n", " 3   destination_type  128 non-null    object\n", " 4   relationship      128 non-null    object\n", " 5   file_path         128 non-null    object\n", "dtypes: object(6)\n", "memory usage: 6.1+ KB\n"]}], "source": ["df_llm_lineage.info()"]}, {"cell_type": "code", "execution_count": 31, "id": "184d5450", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 0 rows for userrepository.java:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>source_node</th>\n", "      <th>source_type</th>\n", "      <th>destination_node</th>\n", "      <th>destination_type</th>\n", "      <th>relationship</th>\n", "      <th>file_path</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [source_node, source_type, destination_node, destination_type, relationship, file_path]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}], "source": ["filtered_df = df_llm_lineage[df_llm_lineage[\"source_node\"].str.contains(\n", "    \"com/morganstanley/loanapp/repository/userrepository.java\",\n", "    case=False, na=False\n", ")]\n", "\n", "print(f\"Found {len(filtered_df)} rows for userrepository.java:\")\n", "display(filtered_df)"]}, {"cell_type": "code", "execution_count": 32, "id": "fddb83dc", "metadata": {}, "outputs": [], "source": ["\n", "# Normalize LLM dataframe columns to lowercase to match AST\n", "for col in [\"source_node\", \"destination_node\", \"source_type\", \"destination_type\", \"relationship\"]:\n", "    df_llm_lineage[col] = df_llm_lineage[col].str.strip().str.lower()\n"]}, {"cell_type": "code", "execution_count": 33, "id": "ceb3736f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Combined lineage DataFrame prepared with 148 rows (folder + file + LLM).\n"]}], "source": ["\n", "# Combine AST and LLM lineage\n", "df_combined = pd.concat([df_folders, df_files, df_llm_lineage], ignore_index=True)\n", "\n", "print(f\"Combined lineage DataFrame prepared with {len(df_combined)} rows (folder + file + LLM).\")\n", "\n"]}, {"cell_type": "code", "execution_count": 34, "id": "1babb204", "metadata": {}, "outputs": [], "source": ["\n", "# Optional: Remove duplicates before insertion\n", "df_combined = df_combined.drop_duplicates(\n", "    subset=[\"source_node\", \"source_type\", \"destination_node\", \"destination_type\", \"relationship\"]\n", ")\n"]}, {"cell_type": "code", "execution_count": 35, "id": "e3d75c6a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Combined lineage DataFrame prepared with 135 rows (AST + LLM).\n"]}], "source": ["print(f\"Combined lineage DataFrame prepared with {len(df_combined)} rows (AST + LLM).\")"]}, {"cell_type": "code", "execution_count": 36, "id": "95b5f2ca", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "Index: 135 entries, 0 to 147\n", "Data columns (total 6 columns):\n", " #   Column            Non-Null Count  Dtype \n", "---  ------            --------------  ----- \n", " 0   source_node       134 non-null    object\n", " 1   source_type       134 non-null    object\n", " 2   destination_node  135 non-null    object\n", " 3   destination_type  135 non-null    object\n", " 4   relationship      134 non-null    object\n", " 5   file_path         128 non-null    object\n", "dtypes: object(6)\n", "memory usage: 7.4+ KB\n"]}], "source": ["df_combined.info()"]}, {"cell_type": "code", "execution_count": null, "id": "2b356933", "metadata": {}, "outputs": [], "source": ["df_combined.to_csv(\"combined_lineage_v1.csv\", index=False)"]}, {"cell_type": "code", "execution_count": 37, "id": "c76d0e40", "metadata": {}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["# Clear existing graph data before ingestion\n", "graph.query(\"MATCH (n) DETACH DELETE n\")\n"]}, {"cell_type": "code", "execution_count": 38, "id": "2232605d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Pushing Combined Lineage to Neo4j:   0%|          | 0/135 [00:00<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Error at row 0: 'NoneType' object has no attribute 'capitalize'\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Pushing Combined Lineage to Neo4j: 100%|██████████| 135/135 [00:03<00:00, 43.67it/s]\n"]}], "source": ["\n", "# Push combined lineage to Neo4j with normalized casing for labels and relationships\n", "for idx, row in tqdm(df_combined.iterrows(), total=len(df_combined), desc=\"Pushing Combined Lineage to Neo4j\"):\n", "    try:\n", "        # Capitalize label names (Class, Method, etc.)\n", "        source_label = row[\"source_type\"].capitalize()\n", "        dest_label = row[\"destination_type\"].capitalize()\n", "        # Uppercase relationship types (DECLARES, CALLS, etc.)\n", "        rel_type = row[\"relationship\"].upper()\n", "\n", "        graph.query(\n", "            f\"\"\"\n", "            MERGE (s:{source_label} {{name: $source_node}})\n", "            MERGE (t:{dest_label} {{name: $destination_node}})\n", "            MERGE (s)-[:{rel_type}]->(t)\n", "            \"\"\",\n", "            {\n", "                \"source_node\": row[\"source_node\"],\n", "                \"destination_node\": row[\"destination_node\"]\n", "            }\n", "        )\n", "    except Exception as e:\n", "        print(f\"Error at row {idx}: {e}\")\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "28d0da3a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}