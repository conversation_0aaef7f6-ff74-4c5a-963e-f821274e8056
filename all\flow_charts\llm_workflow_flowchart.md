# 🧩 LLM workflow for Java Code Graph Extraction

```mermaid
flowchart TD
    A["🚀 Start: Import Libraries<br>langchain, Neo4j, pathlib"]
    A --> C["📂 Initialize File Processing"]
    C --> D{"📄 For Each Java File?"}
    D --> E["🗂️ Process File Path<br>Extract relative path<br>Split into parts"]
    E --> F["📁 Create Parent Folder<br>MERGE parent folder node<br>Add to created_folders"]
    F --> G["📁 Create Folder Hierarchy<br>For each path part:<br>• MERGE folder node<br>• Create CONTAINS relationship"]
    G --> H["📄 Create File Node<br>MERGE file node<br>Connect to parent folder"]
    H --> I{"🔄 More Files?"}
    I -- Yes --> D
    I -- No --> J["📚 Load Java Files<br>TextLoader for each .java file<br>Add language metadata"]
    J --> K["✂️ Split Documents<br>RecursiveCharacterTextSplitter<br>with Java language settings<br>chunk_size=2000, overlap=200"]
    K --> L["🧠 Build System Prompt<br>Create prompt for code analysis<br>Include file context and instructions"]
    L --> M["🔄 Initialize Google Gemini LLM<br>ChatGoogleGenerativeAI<br>model=\"gemini-2.5-pro\""]
    M --> N["🛠️ Configure LLMGraphTransformer<br>Set allowed nodes and relationships<br>Enable strict mode"]
    N --> O{"📄 For Each Document Chunk?"}
    O --> P["🧠 Generate Graph Triples<br>LLM analyzes code chunk<br>Extracts structured relationships"]
    P --> Q["📊 Store Graph Documents<br>Add to all_graph_documents"]
    Q --> R{"🔄 More Chunks?"}
    R -- Yes --> O
    R -- No --> X["🔌 Connect to Neo4j<br>Neo4jGraph with bolt://localhost:7687"]
    X --> Y["📤 Push to Neo4j<br>For each graph document:<br>• Create nodes<br>• Create relationships"]
    Y --> T["✅ Complete<br>All data pushed to Neo4j"]

    %% Styling
    style A fill:#e3f2fd,stroke:#1976d2,stroke-width:3px,color:#000
    style C fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#000
    style D fill:#fff8e1,stroke:#f57c00,stroke-width:2px,color:#000
    style E fill:#fff8e1,stroke:#f57c00,stroke-width:2px,color:#000
    style F fill:#fff8e1,stroke:#f57c00,stroke-width:2px,color:#000
    style G fill:#fff8e1,stroke:#f57c00,stroke-width:2px,color:#000
    style H fill:#fff8e1,stroke:#f57c00,stroke-width:2px,color:#000
    style I fill:#fff8e1,stroke:#f57c00,stroke-width:2px,color:#000
    style J fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#000
    style K fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#000
    style L fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    style M fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    style N fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    style O fill:#fff8e1,stroke:#f57c00,stroke-width:2px,color:#000
    style P fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    style Q fill:#fff8e1,stroke:#f57c00,stroke-width:2px,color:#000
    style R fill:#fff8e1,stroke:#f57c00,stroke-width:2px,color:#000
    style X fill:#e0f2f1,stroke:#00695c,stroke-width:2px,color:#000
    style Y fill:#e0f2f1,stroke:#00695c,stroke-width:2px,color:#000
    style T fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px,color:#000
```

## How It Works

### 1️⃣ Repository Structure Ingestion
- Uses Pathlib to traverse `*.java` files.
- Creates Folder and File nodes in Neo4j:
  - Nodes: `(:Folder {name})`, `(:File {name})`
  - Relationships: `(Folder)-[:CONTAINS]->(Folder|File)`
- **Purpose:** Provides directory → file context for each code file, enabling hierarchical navigation.

### 2️⃣ Code Chunking for LLM Processing
- Loads Java files using TextLoader.
- Splits into chunks (2000 chars, 200 overlap) using RecursiveCharacterTextSplitter.
- **Purpose:** Keeps chunks LLM-friendly while retaining enough context for dependency tracing.

### 3️⃣ System Prompt for Graph Triple Extraction
- Builds a precise system prompt instructing the LLM to extract triples:
  ```
  [SourceNodeLabel]:SourceNodeName -[RELATION]-> [TargetNodeLabel]:TargetNodeName
  ```
- Lineage structure:
  - File → Class (`DECLARES`)
  - Class → Function (`DECLARES`)
  - Function → Function (`CALLS`)
  - Class → Class (`INHERITS`)
  - Function → Table (`READS_FROM`/`WRITES_TO`)
  - Function → API (`CALLS_API`)
- **Purpose:** Converts unstructured code into structured semantic triples for Neo4j ingestion.

### 4️⃣ LLM Graph Transformation
- Uses:
  ```python
  LLMGraphTransformer(
      llm=llm,
      additional_instructions=system_prompt,
      allowed_nodes=[...],
      allowed_relationships=[...],
      strict_mode=True
  )
  ```
- Extracts `GraphDocument` with `.nodes` and `.relationships` from each chunk.
- **Purpose:** Transforms LLM output into a graph-friendly structure automatically.

### 5️⃣ Neo4j Graph Ingestion
- Skips creating File nodes again.
- Creates or merges other nodes (Class, Function, Table, API).
- Merges relationships with:
  ```cypher
  MATCH (s:SourceType {name: $source_name})
  MATCH (t:TargetType {name: $target_name})
  MERGE (s)-[:REL_TYPE]->(t)
  ```
- **Purpose:** Populates the code+data lineage graph in Neo4j systematically with high structural consistency.

---

### Key Strengths
- ✅ Uses LLM to extract semantic relationships beyond what static parsing alone captures.
- ✅ Generates clean, consistent triples, reducing noisy unstructured output.
- ✅ Uses strict allowed node/relationship filtering, improving accuracy.
- ✅ Combines directory context + semantic graph extraction.
- ✅ Modular, easily extensible to other languages.
