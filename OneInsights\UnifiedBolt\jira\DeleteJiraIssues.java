package com.bolt.dashboard.jira;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.config.MongoAggregate;
import com.bolt.dashboard.core.model.ChangeHistoryModel;
import com.bolt.dashboard.core.model.EffortHistoryModel;
import com.bolt.dashboard.core.model.IterationModel;
import com.bolt.dashboard.core.model.MetricsModel;
import com.bolt.dashboard.core.model.TransitionModel;
import com.bolt.dashboard.core.repository.ChangeHisortyRepo;
import com.bolt.dashboard.core.repository.EffortHistoryRepo;
import com.bolt.dashboard.core.repository.IterationRepo;
import com.bolt.dashboard.core.repository.MetricRepo;
import com.bolt.dashboard.core.repository.ProjectRepo;
import com.bolt.dashboard.core.repository.TransitionRepo;

public class DeleteJiraIssues {
	private static final Logger LOGGER = LogManager.getLogger(DeleteJiraIssues.class);
	  JiraAuthentication auth = new JiraAuthentication();   
	  private AnnotationConfigApplicationContext ctx = null;
	    MongoAggregate mongoAgr=new MongoAggregate();
	    List<String> widList=new ArrayList<>();
	    MetricsModel metrics = null;
	    MetricRepo metricsRepo=null;
	    String wid = "";
	public String handleDeletedIssues(String jiraUrl, String userName, String password, String projName, String key) throws JiraExceptions{
		ctx = DataConfig.getContext();
		metricsRepo=ctx.getBean(MetricRepo.class);
		  List<MetricsModel> allMetrics=metricsRepo.findByPName(projName);
		    Map<String, List<MetricsModel>> groupByWid = allMetrics.stream().collect(Collectors.groupingBy(MetricsModel::getwId));
		
		JSONObject jsonOutput = null;
		try {
			LOGGER.info("Handle Delted Issues Started for Project "+projName);
			if(key.equals("IN")){
				 jsonOutput = (JSONObject) new JSONParser()
						    .parse(auth.alternateJiraConnection(jiraUrl, userName, password, key, 0, 0, null,null,true,"").toString());
			}
			else{
				
			
		    jsonOutput = (JSONObject) new JSONParser()
			    .parse(auth.jiraConnection(jiraUrl, userName, password, key, 0, 0, null,true).toString());
			}
		    Long totalIssues = (long) jsonOutput.get("total");
		    int startAt=0,maxResults=100;
		    while (startAt < totalIssues) {
		    	LOGGER.info("total Issues : " + totalIssues + " , startAt :" + startAt);
		  if(key.equals("IN")){
			  jsonOutput = (JSONObject) new JSONParser().parse(
						auth.alternateJiraConnection(jiraUrl, userName, password, key, startAt, maxResults, null,null,true,"")
							.toString());
		  }else{
			  
		  
				jsonOutput = (JSONObject) new JSONParser().parse(
					auth.jiraConnection(jiraUrl, userName, password, key, startAt, maxResults, null,true)
						.toString());
		  }
				JSONArray jsonArrayOutput = (JSONArray) jsonOutput.get("issues");
				startAt += maxResults;
				for (Object obj : jsonArrayOutput) {
					 metrics = new MetricsModel();
					
					    JSONObject jsonNew = (JSONObject) new JSONParser()
						    .parse(new StringBuilder(obj.toString()).toString());
					    wid = jsonNew.get("key").toString();
					    widList.add(wid);
					    List<MetricsModel> tempMetrics=groupByWid.get(wid);
					    //allMetrics.removeIf((MetricsModel met) -> met.getwId().equals(wid));
					    if(tempMetrics !=null && tempMetrics.size()>1){
/*					    	long recentUpdated= tempMetrics.get(0).getUpdatedDate();
					    	MetricsModel recentModel=tempMetrics.get(0);
					    	for(MetricsModel tempModel:tempMetrics){
					    		if(tempModel.getUpdDate()>recentUpdated){
					    			recentUpdated=tempModel.getUpdDate();
					    			recentModel=tempModel;
					    		}	
				    	}*/	
					    	MetricsModel recentModel=tempMetrics.get(tempMetrics.size()-1);
					    	tempMetrics.remove(recentModel);
				    		metricsRepo.delete(tempMetrics);
}
					    	
					    
				}
		    }
		    
		    mongoAgr=new MongoAggregate();
		    //metricsRepo.delete(allMetrics);
		    mongoAgr.deleteIssues(widList,projName);
		  
		    return "Issues Handled";
	
		}
		catch (Exception e) {
			LOGGER.error(e.getMessage());
		   LOGGER.info(e.getMessage());
		   return "Failed to handle Issues";
		}	
	}
	
}
