package com.bolt.dashboard.core.model;

import java.util.List;
import java.util.Map;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "Author")
public class IterationOutModel extends BaseModel {
	private Long timeStamp;
	private String sName;
	private Long stDate;
	private Long endDate;
	private int velocity;
	private Number sPPlanned;
	private String relName;
	private Long relDate;
	private String techDebt;
	private Number teamSize;
	private Number pTestCase;
	private Number fAdd;
	private Number fDel;
	private Number fMod;
	private int totDefects;
	private int totClosedDefects;
	private int totOpenDefetct;
	private Number stPerSprint;
	private Number buildFailed;
	private String state;
	private String pName;
	private String projKey;
	private String pAlmType;
	private int sId;
	private long completedDate;
	private double totalStoryPoints;
	private Map<String, Integer> buildFail;
	private int closedStories;
	
	public int getClosedStories() {
		return closedStories;
	}
	public void setClosedStories(int closedStories) {
		this.closedStories = closedStories;
	}
	private List<MonogOutMetrics> metrics;
	public Long getTimeStamp() {
		return timeStamp;
	}
	public void setTimeStamp(Long timeStamp) {
		this.timeStamp = timeStamp;
	}
	public String getsName() {
		return sName;
	}
	public void setsName(String sName) {
		this.sName = sName;
	}
	public Long getStDate() {
		return stDate;
	}
	public void setStDate(Long stDate) {
		this.stDate = stDate;
	}
	public Long getEndDate() {
		return endDate;
	}
	public void setEndDate(Long endDate) {
		this.endDate = endDate;
	}
	public Number getVelocity() {
		return velocity;
	}
	public Number getsPPlanned() {
		return sPPlanned;
	}
	public void setsPPlanned(Number sPPlanned) {
		this.sPPlanned = sPPlanned;
	}
	public Long getRelDate() {
		return relDate;
	}
	public void setRelDate(Long relDate) {
		this.relDate = relDate;
	}
	public int getTotClosedDefects() {
		return totClosedDefects;
	}
	public void setTotClosedDefects(int totClosedDefects) {
		this.totClosedDefects = totClosedDefects;
	}
	public int getTotOpenDefetct() {
		return totOpenDefetct;
	}
	public void setTotOpenDefetct(int totOpenDefetct) {
		this.totOpenDefetct = totOpenDefetct;
	}
	public double getTotalStoryPoints() {
		return totalStoryPoints;
	}
	public void setTotalStoryPoints(double totalStoryPoints) {
		this.totalStoryPoints = totalStoryPoints;
	}
	public Map<String, Integer> getBuildFail() {
		return buildFail;
	}
	public void setBuildFail(Map<String, Integer> buildFail) {
		this.buildFail = buildFail;
	}
	public void setVelocity(int velocity) {
		this.velocity = velocity;
	}
	public void setTotDefects(int totDefects) {
		this.totDefects = totDefects;
	}
	public String getTechDebt() {
		return techDebt;
	}
	public void setTechDebt(String techDebt) {
		this.techDebt = techDebt;
	}
	public Number getTeamSize() {
		return teamSize;
	}
	public void setTeamSize(Number teamSize) {
		this.teamSize = teamSize;
	}
	public Number getpTestCase() {
		return pTestCase;
	}
	public void setpTestCase(Number pTestCase) {
		this.pTestCase = pTestCase;
	}
	public Number getfAdd() {
		return fAdd;
	}
	public void setfAdd(Number fAdd) {
		this.fAdd = fAdd;
	}
	public Number getfDel() {
		return fDel;
	}
	public void setfDel(Number fDel) {
		this.fDel = fDel;
	}
	public Number getfMod() {
		return fMod;
	}
	public void setfMod(Number fMod) {
		this.fMod = fMod;
	}
	public Number getTotDefects() {
		return totDefects;
	}
	public Number getStPerSprint() {
		return stPerSprint;
	}
	public void setStPerSprint(Number stPerSprint) {
		this.stPerSprint = stPerSprint;
	}
	public Number getBuildFailed() {
		return buildFailed;
	}
	public void setBuildFailed(Number buildFailed) {
		this.buildFailed = buildFailed;
	}
	public String getState() {
		return state;
	}
	public void setState(String state) {
		this.state = state;
	}
	public String getpName() {
		return pName;
	}
	public void setpName(String pName) {
		this.pName = pName;
	}
	public String getpAlmType() {
		return pAlmType;
	}
	public void setpAlmType(String pAlmType) {
		this.pAlmType = pAlmType;
	}
	public int getsId() {
		return sId;
	}
	public void setsId(int sId) {
		this.sId = sId;
	}
	public long getCompletedDate() {
		return completedDate;
	}
	public void setCompletedDate(long completedDate) {
		this.completedDate = completedDate;
	}
	public List<MonogOutMetrics> getMetrics() {
		return metrics;
	}
	public void setMetrics(List<MonogOutMetrics> metrics) {
		this.metrics = metrics;
	}
	public String getRelName() {
		return relName;
	}
	public void setRelName(String relName) {
		this.relName = relName;
	}
	public String getProjKey() {
		return projKey;
	}
	public void setProjKey(String projKey) {
		this.projKey = projKey;
	}		
}
