package com.bolt.dashboard.util;

import java.util.ArrayList;
import java.util.List;

import com.bolt.dashboard.core.model.IterationOutModel;
import com.bolt.dashboard.core.model.MonogOutMetrics;

public class CommonFunctions {
	
	public static ArrayList<String> getComponentList(List<IterationOutModel> authorList) {

		ArrayList<String> componentList = new ArrayList<String>();
		for (IterationOutModel auth : authorList) {
	
			if (auth.getMetrics() != null) {
				for (MonogOutMetrics m : auth.getMetrics()) {
					if (m.getComponents() != null && m.getComponents().get(0) != null) {
						String component = m.getComponents().get(0);
						if (componentList.indexOf(component) < 0) {
							componentList.add(component);
						}
					}
				}
			}
		}
		return componentList;
	}


	
	public String convertToDisplayValues(long time, int noOfDays) {
		
		String result = "";
		long seconds = time / 1000;
		long minutes = seconds / 60;
		long hours = minutes / 60;
		long leftMinutes=minutes%60;
		long days = hours / noOfDays;
		long leftHours = hours % 24;
		long week = days / 7;
		long leftDays = days % 7;

		if (week != 0) {
			result = String.valueOf(week) + "w ";

		}
		if (leftDays != 0) {
			result += String.valueOf(leftDays) + "d ";
		}

		if (leftHours != 0) {
			result += String.valueOf(leftHours) + "h ";
		}
		if(leftMinutes!=0) {
			result += String.valueOf(leftMinutes) + "m";
		}

		if (result.equals("")) {
			return "0h";
		}

		return result;

	}
	
	
	
	public String convertMilisToDisplayValuesDefect(long milis, int noOfDays) {
		
		String result = "";
		long seconds = milis / 1000;
		long minutes = seconds / 60;
		long hours = minutes / 60;
		long leftMinutes=minutes%60;
		long days = hours / noOfDays;
		long leftHours = hours % noOfDays;
		//long leftDays = days % 7;

		if (days != 0) {
			result += String.valueOf(days) + "d ";
		}

		if (leftHours != 0) {
			result += String.valueOf(leftHours) + "h ";
		}
		if(leftMinutes!=0) {
			result += String.valueOf(leftMinutes) + "m";
		}

		if (result.equals("")) {
			return "0h";
		}

		return result;

	}
	public String convertSecondsToStringDisplay(long seconds, int noOfDays) {
		
		String result = "";
		long minutes = seconds / 60;
		long hours = minutes / 60;
		long leftMinutes=minutes%60;
		long days = hours / noOfDays;
		long leftHours = hours % 24;
		

			result += String.valueOf(days) + "d ";
	

			result += String.valueOf(leftHours) + "h ";
		
			result += String.valueOf(leftMinutes) + "m";

		return result;

	}
	public String toHoursString(long seconds) {
		
		long minutes = seconds / 60;
		long hours = minutes / 60;
		return String.valueOf(hours);
	}
	public long toDaysString(long milis, int noOfDays) {
		
		long seconds=milis/1000;
		long minutes = seconds / 60;
		long hours = minutes / 60;
		long days= hours/noOfDays;
		return days;
	}
	
}
