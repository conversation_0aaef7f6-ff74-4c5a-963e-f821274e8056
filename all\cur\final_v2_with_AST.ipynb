{"cells": [{"cell_type": "code", "execution_count": null, "id": "76e7f8cf", "metadata": {}, "outputs": [], "source": ["# This script sets up the Tree-sitter parser for Java files and imports necessary libraries.\n", "import os\n", "from tree_sitter import Language, Parser\n", "import tree_sitter_java as tsjava\n", "\n", "JAVA_LANGUAGE = Language(tsjava.language())\n", "parser = Parser(JAVA_LANGUAGE)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "f61f912d", "metadata": {}, "outputs": [], "source": ["# Function to read source code from a file and return it as bytes\n", "def read_source_code(file_path: str) -> bytes:\n", "    with open(file_path, 'r', encoding='utf-8') as file:\n", "        source_code = file.read()\n", "    return source_code.encode('utf-8')\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "d0d84176", "metadata": {}, "outputs": [], "source": ["# Function to parse the source code and extract the structure using Tree-sitter\n", "def extract_structure(node, source_code, file_path):\n", "    extracted = []\n", "\n", "    def traverse(n, parent_class=None, parent_method=None):\n", "        text = source_code[n.start_byte:n.end_byte].decode(errors='ignore')\n", "\n", "        if n.type == \"class_declaration\":\n", "            class_name = None\n", "            for child in n.children:\n", "                if child.type == \"identifier\":\n", "                    class_name = source_code[child.start_byte:child.end_byte].decode()\n", "            extracted.append({\n", "                \"type\": \"class\",\n", "                \"name\": class_name,\n", "                \"file\": file_path\n", "            })\n", "            for child in n.children:\n", "                traverse(child, parent_class=class_name, parent_method=None)\n", "\n", "        elif n.type == \"method_declaration\":\n", "            method_name = None\n", "            for child in n.children:\n", "                if child.type == \"identifier\":\n", "                    method_name = source_code[child.start_byte:child.end_byte].decode()\n", "            extracted.append({\n", "                \"type\": \"method\",\n", "                \"name\": method_name,\n", "                \"contained_in_class\": parent_class,\n", "                \"file\": file_path\n", "            })\n", "            for child in n.children:\n", "                traverse(child, parent_class=parent_class, parent_method=method_name)\n", "\n", "        elif n.type == \"method_invocation\":\n", "            invoked_method = None\n", "            for child in n.children:\n", "                if child.type == \"identifier\":\n", "                    invoked_method = source_code[child.start_byte:child.end_byte].decode()\n", "            extracted.append({\n", "                \"type\": \"method_call\",\n", "                \"name\": invoked_method,\n", "                \"caller_method\": parent_method,\n", "                \"caller_class\": parent_class,\n", "                \"file\": file_path\n", "            })\n", "            for child in n.children:\n", "                traverse(child, parent_class=parent_class, parent_method=parent_method)\n", "\n", "        elif n.type == \"variable_declarator\":\n", "            var_name = None\n", "            for child in n.children:\n", "                if child.type == \"identifier\":\n", "                    var_name = source_code[child.start_byte:child.end_byte].decode()\n", "            extracted.append({\n", "                \"type\": \"variable\",\n", "                \"name\": var_name,\n", "                \"scope_class\": parent_class,\n", "                \"scope_method\": parent_method,\n", "                \"file\": file_path\n", "            })\n", "\n", "        else:\n", "            for child in n.children:\n", "                traverse(child, parent_class=parent_class, parent_method=parent_method)\n", "\n", "    traverse(node)\n", "    return extracted\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "e2d47838", "metadata": {}, "outputs": [], "source": ["# Import necessary libraries for Neo4j and data handling\n", "from tqdm import tqdm\n", "\n", "all_results = []\n", "folder_edges = []\n", "\n", "def parse_java_project(base_folder):\n", "    for root, dirs, files in os.walk(base_folder):\n", "        rel_root = os.path.relpath(root, base_folder)\n", "        parent_folder = os.path.dirname(rel_root) if rel_root != '.' else None\n", "\n", "        # Folder nodes\n", "        folder_edges.append({\n", "            \"type\": \"folder\",\n", "            \"path\": rel_root,\n", "            \"parent\": parent_folder if parent_folder != '.' else None\n", "        })\n", "\n", "        for file in tqdm(files):\n", "            if file.endswith(\".java\"):\n", "                file_path = os.path.join(root, file)\n", "                source_code = read_source_code(file_path)\n", "                tree = parser.parse(source_code)\n", "\n", "                structured = extract_structure(tree.root_node, source_code, file_path)\n", "                all_results.extend(structured)\n", "                print(f\"Parsed: {file_path}\")\n", "\n", "parse_java_project(\"java\")\n", "print(f\"Parsed {len(all_results)} elements across {len(folder_edges)} folders.\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "31ce2899", "metadata": {}, "outputs": [], "source": ["# save into CSV \n", "import pandas as pd\n", "\n", "df = pd.DataFrame(all_results)\n"]}, {"cell_type": "code", "execution_count": null, "id": "62b9b089", "metadata": {}, "outputs": [], "source": ["df.info()"]}, {"cell_type": "code", "execution_count": null, "id": "3aea7837", "metadata": {}, "outputs": [], "source": ["df.head(10)"]}, {"cell_type": "code", "execution_count": null, "id": "a8d3b853", "metadata": {}, "outputs": [], "source": ["#  Neo4j connection setup\n", "from neo4j import GraphDatabase\n", "\n", "uri = \"bolt://127.0.0.1:7687\"\n", "user = \"neo4j\"\n", "password = \"Test@7889\"\n", "\n", "driver = GraphDatabase.driver(uri, auth=(user, password))\n", "\n", "with driver.session(database=\"neo4j\") as session:\n", "    result = session.run(\"RETURN 1 AS test\")\n", "    print(result.single()[\"test\"])\n", "\n", "print(\"Connected to Neo4j.\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "8e0077e0", "metadata": {}, "outputs": [], "source": ["#push to Neo4j\n", "def push_to_neo4j(all_results, folder_edges):\n", "    with driver.session(database=\"neo4j\") as session:\n", "        # Insert folders and their hierarchy\n", "        for folder in folder_edges:\n", "            if not folder.get(\"path\"):\n", "                print(f\"Skipping folder with null path: {folder}\")\n", "                continue\n", "\n", "            session.run(\"\"\"\n", "                MERGE (f:Folder {path: $path})\n", "                FOREACH (_ IN CASE WHEN $parent IS NOT NULL THEN [1] ELSE [] END |\n", "                    MERGE (p:Folder {path: $parent})\n", "                    MERGE (p)-[:CONTAINS]->(f)\n", "                )\n", "            \"\"\", path=folder[\"path\"], parent=folder.get(\"parent\"))\n", "\n", "        # Insert files and code structure\n", "        for item in all_results:\n", "            file_name = os.path.basename(item[\"file\"])\n", "            file_path = os.path.relpath(item[\"file\"], \"java\")\n", "            folder_path = os.path.dirname(file_path)\n", "\n", "            try:\n", "                # Ensure File nodes always created\n", "                session.run(\"\"\"\n", "                    MERGE (fo:Folder {path: $folder_path})\n", "                    MERGE (fi:File {name: $file_name, path: $file_path})\n", "                    MERGE (fo)-[:CONTAINS]->(fi)\n", "                \"\"\", folder_path=folder_path, file_name=file_name, file_path=file_path)\n", "\n", "                # Insert based on type\n", "                if item[\"type\"] == \"class\":\n", "                    if not item.get(\"name\"):\n", "                        print(f\"Skipping class with null name: {item}\")\n", "                        continue\n", "\n", "                    session.run(\"\"\"\n", "                        MERGE (c:Class {name: $class_name})\n", "                        MERGE (fi:File {name: $file_name, path: $file_path})\n", "                        MERGE (fi)-[:CONTAINS]->(c)\n", "                    \"\"\", class_name=item[\"name\"], file_name=file_name, file_path=file_path)\n", "\n", "                elif item[\"type\"] == \"method\":\n", "                    if not item.get(\"contained_in_class\") or not item.get(\"name\"):\n", "                        print(f\"Skipping method with null values: {item}\")\n", "                        continue\n", "\n", "                    session.run(\"\"\"\n", "                        MERGE (c:Class {name: $class_name})\n", "                        MERGE (m:Method {name: $method_name})\n", "                        MERGE (c)-[:DECLARES]->(m)\n", "                    \"\"\", class_name=item[\"contained_in_class\"], method_name=item[\"name\"])\n", "\n", "                elif item[\"type\"] == \"method_call\":\n", "                    if not item.get(\"caller_method\") or not item.get(\"name\"):\n", "                        print(f\"Skipping method_call with null values: {item}\")\n", "                        continue\n", "\n", "                    session.run(\"\"\"\n", "                        MERGE (caller:Method {name: $caller_method})\n", "                        MERGE (callee:Method {name: $callee_method})\n", "                        MERGE (caller)-[:CALLS]->(callee)\n", "                    \"\"\", caller_method=item[\"caller_method\"], callee_method=item[\"name\"])\n", "\n", "                elif item[\"type\"] == \"variable\":\n", "                    if item.get(\"scope_method\"):\n", "                        # Local variable in method\n", "                        session.run(\"\"\"\n", "                            MERGE (m:Method {name: $method_name})\n", "                            MERGE (v:Variable {name: $var_name})\n", "                            MERGE (m)-[:USES]->(v)\n", "                        \"\"\", method_name=item[\"scope_method\"], var_name=item[\"name\"])\n", "                    elif item.get(\"scope_class\"):\n", "                        # Class field (global variable in class)\n", "                        session.run(\"\"\"\n", "                            MERGE (c:Class {name: $class_name})\n", "                            MERGE (v:Variable {name: $var_name})\n", "                            MERGE (c)-[:HAS_FIELD]->(v)\n", "                        \"\"\", class_name=item[\"scope_class\"], var_name=item[\"name\"])\n", "                    else:\n", "                        print(f\"Skipping variable with missing scope: {item}\")\n", "                        continue\n", "\n", "            except Exception as e:\n", "                print(f\"Error while processing {item} -> {e}\")\n", "\n", "\n", "\n", "push_to_neo4j(all_results, folder_edges)\n", "\n", "print(\"All valid nodes and relationships pushed to Neo4j.\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "6434cbe7", "metadata": {}, "outputs": [], "source": ["# Clear Neo4j database\n", "def clear_neo4j_database():\n", "    with driver.session(database=\"neo4j\") as session:\n", "        session.run(\"MATCH (n) DETACH DELETE n\")\n", "    print(\" All nodes and relationships have been deleted from the database.\")\n", "\n", "clear_neo4j_database()"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}