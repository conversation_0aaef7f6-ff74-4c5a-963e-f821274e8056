{"cells": [{"cell_type": "code", "execution_count": null, "id": "89d19d80", "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "from langchain_community.document_loaders import TextLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter, Language\n", "from langchain_google_genai import ChatGoogleGenerativeAI\n", "from langchain_experimental.graph_transformers import LLMGraphTransformer\n", "from langchain_community.graphs import Neo4jGraph\n", "from langchain_community.vectorstores import Chroma\n", "from langchain_openai import OpenAIEmbeddings\n", "from langchain_core.documents import Document\n", "\n", "# 1️⃣ Neo4j connection\n", "graph = Neo4jGraph(\n", "    url=\"bolt://localhost:7687\",\n", "    username=\"neo4j\",\n", "    password=\"password\"\n", ")\n", "\n", "# 2️⃣ Folder ➔ File Prepopulation (Hierarchy Ingestion)\n", "base_path = Path(\"C:/Shaik/sample/java\")\n", "for path in base_path.rglob(\"*.java\"):\n", "    rel_path = path.relative_to(base_path)\n", "    folders = rel_path.parts[:-1]\n", "    file_name = rel_path.name\n", "\n", "    parent = \"java\"\n", "    for folder in folders:\n", "        graph.query(\"\"\"\n", "            MERGE (p:Folder {name: $parent})\n", "            MERGE (c:Folder {name: $child})\n", "            MERGE (p)-[:CONTAINS]->(c)\n", "        \"\"\", {\"parent\": parent, \"child\": folder})\n", "        parent = folder\n", "\n", "    # Link file to its parent folder\n", "    graph.query(\"\"\"\n", "        MERGE (f:File {name: $file})\n", "        MERGE (p:Folder {name: $parent})\n", "        MERGE (p)-[:CONTAINS]->(f)\n", "    \"\"\", {\"parent\": parent, \"file\": file_name})\n", "\n", "print(\"✅ Folder ➔ File structure ingested into Neo4j.\")\n", "\n", "# 3️⃣ Load Java files\n", "java_docs = []\n", "for path in base_path.rglob(\"*.java\"):\n", "    loader = TextLoader(str(path))\n", "    loaded = loader.load()\n", "    for doc in loaded:\n", "        doc.metadata[\"source\"] = str(path)\n", "        doc.metadata[\"language\"] = \"java\"\n", "    java_docs.extend(loaded)\n", "\n", "# 4️⃣ Split using Java AST-aware splitter\n", "splitter_java = RecursiveCharacterTextSplitter.from_language(\n", "    language=Language.JAVA,\n", "    chunk_size=2000,\n", "    chunk_overlap=200\n", ")\n", "split_docs = splitter_java.split_documents(java_docs)\n", "print(f\"✅ {len(split_docs)} Java chunks prepared.\")\n", "\n", "# 5️⃣ Store embeddings for cross-chunk retrieval\n", "vectorstore = Chroma(\n", "    collection_name=\"code_chunks\",\n", "    embedding=OpenAIEmbeddings(),\n", "    persist_directory=\"./vector_db\"\n", ")\n", "vectorstore.add_documents(split_docs)\n", "print(\"✅ Chunks embedded and stored in vector DB.\")\n", "\n", "# 6️⃣ Gemini LLM initialization\n", "llm = ChatGoogleGenerativeAI(\n", "    model=\"gemini-1.5-pro-latest\",\n", "    google_api_key=\"YOUR_GOOGLE_API_KEY\"\n", ")\n", "\n", "# 7️⃣ System prompt for lineage\n", "system_prompt = \"\"\"\n", "You are a **code and data lineage analysis engine**. From the provided Java code chunk, extract **structured graph triples** in the format:\n", "\n", "[SourceNodeLabel]:SourceNodeName -[RELATION]-> [TargetNodeLabel]:TargetNodeName\n", "\n", "## Context:\n", "- Use the explicit Folder and File info provided in metadata.\n", "- Ignore package statements when building Folder nodes.\n", "- If database table names, column names, or API endpoints are detected, include them as nodes for lineage.\n", "- If functions or classes are reading from or writing to database tables or making API calls, capture them in the lineage relationships.\n", "\n", "## Layers:\n", "- File -> Class\n", "- Class -> Function/Method\n", "- Function/Method -> Function/Method (CALLS)\n", "- Class -> Class (INHERITS)\n", "- Function/Method -> Table (READS_FROM / WRITES_TO)\n", "- Function/Method -> API (CALLS_API)\n", "\n", "## Node Types:\n", "- File\n", "- Class\n", "- Function\n", "- Table\n", "- Column\n", "- API\n", "\n", "## Relationship Types:\n", "- DECLARES\n", "- CALLS\n", "- INHERITS\n", "- READS_FROM\n", "- WRITES_TO\n", "- CALLS_API\n", "\n", "## Instructions:\n", "- Return **only these triples, nothing else**.\n", "\"\"\"\n", "\n", "# 8️⃣ Initialize LLMGraphTransformer\n", "transformer = LLMGraphTransformer(\n", "    llm=llm,\n", "    additional_instructions=system_prompt,\n", "    allowed_nodes=[\"File\", \"Class\", \"Function\", \"Table\", \"Column\", \"API\"],\n", "    allowed_relationships=[\n", "        (\"File\", \"DECLARES\", \"Class\"),\n", "        (\"Class\", \"DECLARES\", \"Function\"),\n", "        (\"Function\", \"CALLS\", \"Function\"),\n", "        (\"Class\", \"INHERITS\", \"Class\"),\n", "        (\"Function\", \"READS_FROM\", \"Table\"),\n", "        (\"Function\", \"WRITES_TO\", \"Table\"),\n", "        (\"Function\", \"CALLS_API\", \"API\"),\n", "    ],\n", "    strict_mode=True,\n", "    node_properties=False,\n", "    relationship_properties=False,\n", ")\n", "\n", "# 9️⃣ Cross-chunk retrieval for multi-chunk context\n", "new_split_docs = []\n", "for doc in split_docs:\n", "    related = vectorstore.similarity_search(doc.page_content, k=4)\n", "    combined_content = \"\\n\\n\".join([d.page_content for d in related])\n", "    combined_doc = Document(page_content=combined_content, metadata=doc.metadata)\n", "    new_split_docs.append(combined_doc)\n", "\n", "print(f\"✅ {len(new_split_docs)} combined multi-chunk documents ready for LLM extraction.\")\n", "\n", "# 1️⃣0️⃣ Extract graph documents using Gemini\n", "graph_documents = transformer.convert_to_graph_documents(new_split_docs)\n", "print(f\"✅ Extracted {len(graph_documents)} graph documents from combined chunks.\")\n", "\n", "# 1️⃣1️⃣ De-duplicate nodes and relationships (clean, optional, handled internally by Neo4j but can post-process if needed)\n", "\n", "# 1️⃣2️⃣ Push to Neo4j\n", "graph.add_graph_documents(graph_documents)\n", "print(\"✅ Final Knowledge Graph successfully built in Neo4j with cross-chunk lineage, class, and function structure.\")\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}