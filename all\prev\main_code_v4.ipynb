{"cells": [{"cell_type": "code", "execution_count": null, "id": "de13997d", "metadata": {}, "outputs": [], "source": ["import os\n", "from pathlib import Path\n", "from langchain_community.document_loaders.generic import GenericLoader\n", "from langchain_community.document_loaders.parsers.language.language_parser import LanguageParser\n", "from langchain.text_splitter import Language, RecursiveCharacterTextSplitter\n", "from langchain_experimental.graph_transformers import LLMGraphTransformer\n", "from langchain_community.graphs import Neo4jGraph\n", "from langchain_core.documents import Document\n", "from langchain_document_loaders import TextLoader"]}, {"cell_type": "code", "execution_count": null, "id": "6f6e18c7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Java LanguageParser failed, falling back to TextLoader: __init__() takes exactly 1 argument (2 given)\n", "✅ Loaded 24 documents before splitting.\n", "✅ Split into 27 language-aware chunks ready for LLM graph extraction.\n"]}], "source": ["\n", "python_loader = GenericLoader.from_filesystem(\n", "    \"test\",\n", "    glob=\"**/*.py\",\n", "    parser=LanguageParser(language=Language.PYTHON)\n", ")\n", "python_docs = python_loader.load()\n", "for doc in python_docs:\n", "    doc.metadata[\"language\"] = \"python\"\n", "\n", "try:\n", "    java_loader = GenericLoader.from_filesystem(\n", "        \"test\",\n", "        glob=\"**/*.java\",\n", "        parser=LanguageParser(language=Language.JAVA)\n", "    )\n", "    java_docs = java_loader.load()\n", "    for doc in java_docs:\n", "        doc.metadata[\"language\"] = \"java\"\n", "except Exception as e:\n", "    print(\"Java LanguageParser failed, falling back to TextLoader:\", e)\n", "    java_docs = []\n", "    for path in Path(\"test\").rglob(\"*.java\"):\n", "        loader = TextLoader(str(path))\n", "        loaded = loader.load()\n", "        for doc in loaded:\n", "            doc.metadata[\"language\"] = \"java\"\n", "        java_docs.extend(loaded)\n", "\n", "docs = python_docs + java_docs\n", "print(f\"✅ Loaded {len(docs)} documents before splitting.\")\n", "\n", "splitter_py = RecursiveCharacterTextSplitter.from_language(\n", "    language=Language.PYTHON,\n", "    chunk_size=2000,\n", "    chunk_overlap=200\n", ")\n", "\n", "splitter_java = RecursiveCharacterTextSplitter.from_language(\n", "    language=Language.JAVA,\n", "    chunk_size=2000,\n", "    chunk_overlap=200\n", ")\n", "\n", "split_docs = []\n", "for doc in docs:\n", "    lang = doc.metadata.get(\"language\")\n", "    if lang == \"python\":\n", "        split_docs.extend(splitter_py.split_documents([doc]))\n", "    elif lang == \"java\":\n", "        split_docs.extend(splitter_java.split_documents([doc]))\n", "    else:\n", "        # fallback to language-agnostic splitting\n", "        split_docs.extend(RecursiveCharacterTextSplitter(\n", "            chunk_size=2000,\n", "            chunk_overlap=200\n", "        ).split_documents([doc]))\n", "\n", "docs = split_docs\n", "print(f\"✅ Split into {len(docs)} language-aware chunks ready for LLM graph extraction.\")\n"]}, {"cell_type": "code", "execution_count": 7, "id": "8cfc805e", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(metadata={'source': 'test\\\\python\\\\app.py', 'content_type': 'functions_classes', 'language': 'python'}, page_content='def cleanup_on_shutdown():\\n    try:\\n        if os.path.exists(\"db\"):\\n            import chromadb\\n            # Close any open ChromaDB instances\\n            client = chromadb.Client()\\n            client.reset()\\n    except Exception as e:\\n        logger.error(f\"Error during shutdown cleanup: {str(e)}\")'),\n", " Document(metadata={'source': 'test\\\\python\\\\app.py', 'content_type': 'functions_classes', 'language': 'python'}, page_content='def remove_readonly(func, path, _):\\n    \"\"\"Clear the readonly bit and reattempt the removal\"\"\"\\n    try:\\n        os.chmod(path, stat.S_IWRITE)\\n        func(path)\\n    except Exception as e:\\n        logger.error(f\"Error removing readonly file {path}: {str(e)}\")'),\n", " Document(metadata={'source': 'test\\\\python\\\\app.py', 'content_type': 'functions_classes', 'language': 'python'}, page_content='def cleanup_directories():\\n    \"\"\"Clean up directories with better error handling for Git repositories\"\"\"\\n    def force_remove_readonly(func, path, _):\\n        try:\\n            os.chmod(path, stat.S_IWRITE)\\n            func(path)\\n        except Exception as e:\\n            logger.warning(f\"Could not remove {path}: {str(e)}\")\\n\\n    try:\\n        # Force close any open database connections\\n        import sqlite3\\n        try:\\n            sqlite3.connect(\\'db/chroma.sqlite3\\').close()\\n        except:\\n            pass\\n\\n        # Force garbage collection to release file handles\\n        import gc\\n        gc.collect()\\n\\n        if os.path.exists(\"repo\"):\\n            logger.info(\"Removing existing repo directory\")\\n            shutil.rmtree(\"repo\", onerror=force_remove_readonly)\\n\\n        if os.path.exists(\"db\"):\\n            logger.info(\"Removing existing db directory\")\\n            # Wait a moment for any file handles to be released\\n            import time\\n            time.sleep(1)\\n            shutil.rmtree(\"db\", onerror=force_remove_readonly)\\n\\n    except Exception as e:\\n        logger.error(f\"Error during cleanup: {str(e)}\")\\n        # Continue execution even if cleanup fails\\n        pass'),\n", " Document(metadata={'source': 'test\\\\python\\\\app.py', 'content_type': 'functions_classes', 'language': 'python'}, page_content='def validate_github_url(url):\\n    \"\"\"Validate if the provided URL is a GitHub repository URL.\"\"\"\\n    if not isinstance(url, str):\\n        return False\\n    url = url.lower()\\n    return url.startswith((\\'http://github.com/\\', \\'https://github.com/\\')) and len(url.split(\\'/\\')) >= 5'),\n", " Document(metadata={'source': 'test\\\\python\\\\app.py', 'content_type': 'functions_classes', 'language': 'python'}, page_content='def initialize_chat_model():\\n    \"\"\"Initialize the chat model and vector store.\"\"\"\\n    try:\\n        embeddings = load_embedding()\\n        vectordb = Chroma(\\n            persist_directory=\"db\",\\n            embedding_function=embeddings\\n        )\\n        \\n        llm = ChatGoogleGenerativeAI(\\n            model=\"gemini-2.0-flash-lite\",\\n            temperature=0.6,\\n            google_api_key=GOOGLE_API_KEY,\\n            max_output_tokens=2048  # Add token limit\\n        )\\n        \\n        memory = ConversationBufferMemory(\\n            memory_key=\"chat_history\",\\n            return_messages=True,\\n            output_key=\"answer\"  # Specify output key\\n        )\\n        \\n        qa_chain = ConversationalRetrievalChain.from_llm(\\n            llm=llm,\\n            retriever=vectordb.as_retriever(\\n                search_type=\"mmr\",\\n                search_kwargs={\"k\": 4, \"fetch_k\": 20}  # Adjust retrieval parameters\\n            ),\\n            memory=memory,\\n            return_source_documents=True,  # Include source documents\\n            verbose=True\\n        )\\n        return qa_chain\\n    except Exception as e:\\n        logger.error(f\"Error initializing chat model: {str(e)}\")\\n        raise'),\n", " Document(metadata={'source': 'test\\\\python\\\\app.py', 'content_type': 'functions_classes', 'language': 'python'}, page_content=\"def index():\\n    return render_template('index.html')\"),\n", " Document(metadata={'source': 'test\\\\python\\\\app.py', 'content_type': 'functions_classes', 'language': 'python'}, page_content='def process_repository():\\n    try:\\n        repo_url = request.form.get(\\'question\\')\\n        \\n        if not repo_url:\\n            return jsonify({\"error\": \"Repository URL is required\"}), 400\\n        \\n        if not validate_github_url(repo_url):\\n            return jsonify({\"error\": \"Invalid GitHub repository URL\"}), 400\\n\\n        # Extract repo name from URL\\n        repo_name = repo_url.split(\\'/\\')[-1]\\n        if repo_name.endswith(\\'.git\\'):\\n            repo_name = repo_name[:-4]\\n\\n        # Clear existing repo and db if they exist\\n        cleanup_directories()\\n\\n        # Clone and process the repository\\n        repo_ingestion(repo_url)\\n        \\n        # Initialize the vector store\\n        try:\\n            os.system(\"python store_index.py\")\\n        except Exception as e:\\n            logger.error(f\"Error creating index: {str(e)}\")\\n            return jsonify({\"error\": \"Failed to create search index\"}), 500\\n\\n        return jsonify({\\n            \"success\": True, \\n            \"message\": \"Repository processed successfully\",\\n            \"repo_name\": repo_name\\n        })\\n\\n    except Exception as e:\\n        logger.error(f\"Error processing repository: {str(e)}\")\\n        cleanup_directories()  # Cleanup on error\\n        return jsonify({\"error\": \"Failed to process repository. Please try again.\"}), 500'),\n", " Document(metadata={'source': 'test\\\\python\\\\app.py', 'content_type': 'functions_classes', 'language': 'python'}, page_content='def chat():\\n    try:\\n        message = request.form.get(\\'msg\\')\\n        \\n        if not message:\\n            return jsonify({\"error\": \"Message is required\"}), 400\\n\\n        if not os.path.exists(\"db\"):\\n            return jsonify({\"error\": \"Please analyze a repository first\"}), 400\\n\\n        qa_chain = initialize_chat_model()\\n        result = qa_chain.invoke({\\n            \"question\": message\\n        })\\n        \\n        # Extract the answer from the result\\n        answer = result.get(\\'answer\\', \\'\\')\\n        if not answer:\\n            return jsonify({\"error\": \"No response generated\"}), 500\\n\\n        # Clean and format the response\\n        answer = answer.strip()\\n        return jsonify({\"answer\": answer})\\n\\n    except Exception as e:\\n        logger.error(f\"Error in chat endpoint: {str(e)}\")\\n        return jsonify({\"error\": \"An error occurred while processing your request\"}), 500'),\n", " Document(metadata={'source': 'test\\\\python\\\\app.py', 'content_type': 'functions_classes', 'language': 'python'}, page_content='def get_current_repo():\\n    try:\\n        if os.path.exists(\"repo\"):\\n            # Get the repository name from the cloned URL\\n            with open(\"repo/.git/config\", \"r\") as f:\\n                config = f.read()\\n                # Extract repo name from the URL\\n                for line in config.split(\\'\\\\n\\'):\\n                    if \\'url = \\' in line:\\n                        url = line.strip().split(\\'url = \\')[1]\\n                        repo_name = url.split(\\'/\\')[-1]\\n                        if repo_name.endswith(\\'.git\\'):\\n                            repo_name = repo_name[:-4]\\n                        return jsonify({\"repo_name\": repo_name})\\n        return jsonify({\"repo_name\": None})\\n    except Exception as e:\\n        logger.error(f\"Error getting repo name: {str(e)}\")\\n        return jsonify({\"error\": \"Failed to get repo name\"}), 500'),\n", " Document(metadata={'source': 'test\\\\python\\\\app.py', 'content_type': 'functions_classes', 'language': 'python'}, page_content='def clear_cache():\\n    try:\\n        def force_remove_readonly(func, path, _):\\n            os.chmod(path, stat.S_IWRITE)\\n            if os.path.exists(path):\\n                func(path)\\n\\n        # Close any open file handles in the db directory\\n        if os.path.exists(\"db\"):\\n            try:\\n                import gc\\n                gc.collect()  # Force garbage collection\\n                shutil.rmtree(\"db\", onerror=force_remove_readonly)\\n            except Exception as e:\\n                logger.warning(f\"Error removing db directory: {str(e)}\")\\n\\n        # Handle Git repository cleanup\\n        if os.path.exists(\"repo\"):\\n            try:\\n                # Force close Git repository\\n                import git\\n                try:\\n                    repo = git.Repo(\"repo\")\\n                    repo.git.gc()\\n                    repo.close()\\n                except:\\n                    pass\\n\\n                # Wait for handles to be released\\n                import time\\n                time.sleep(1)\\n\\n                # Remove read-only attributes recursively\\n                for root, dirs, files in os.walk(\"repo\", topdown=False):\\n                    for name in files:\\n                        try:\\n                            file_path = os.path.join(root, name)\\n                            os.chmod(file_path, stat.S_IWRITE)\\n                        except:\\n                            pass\\n                    for name in dirs:\\n                        try:\\n                            dir_path = os.path.join(root, name)\\n                            os.chmod(dir_path, stat.S_IWRITE)\\n                        except:\\n                            pass\\n\\n                shutil.rmtree(\"repo\", onerror=force_remove_readonly)\\n            except Exception as e:\\n                logger.warning(f\"Error removing repo directory: {str(e)}\")'),\n", " Document(metadata={'source': 'test\\\\python\\\\app.py', 'content_type': 'functions_classes', 'language': 'python'}, page_content='shutil.rmtree(\"repo\", onerror=force_remove_readonly)\\n            except Exception as e:\\n                logger.warning(f\"Error removing repo directory: {str(e)}\")\\n\\n        return jsonify({\"message\": \"Cache cleared successfully\"}), 200\\n    except Exception as e:\\n        logger.error(f\"Error clearing cache: {str(e)}\")\\n        return jsonify({\"error\": \"Failed to clear cache\"}), 500'),\n", " Document(metadata={'source': 'test\\\\python\\\\app.py', 'content_type': 'simplified_code', 'language': 'python'}, page_content='from flask import Flask, render_template, jsonify, request\\nfrom flask_cors import CORS\\nfrom flask_limiter import Limiter\\nfrom flask_limiter.util import get_remote_address\\nimport redis\\nfrom langchain_chroma import Chroma\\nfrom langchain_google_genai import ChatGoogleGenerativeAI\\nfrom langchain.memory import ConversationBufferMemory\\nfrom langchain.chains import ConversationalRetrievalChain\\nfrom src.helper import load_embedding, repo_ingestion\\nfrom dotenv import load_dotenv\\nimport os\\nimport logging\\nimport shutil\\nimport stat\\n\\n# Configure logging\\nlogging.basicConfig(level=logging.INFO)\\nlogger = logging.getLogger(__name__)\\n\\n# Ensure the application properly closes resources on shutdown\\n# Code for: def cleanup_on_shutdown():\\n\\n# Register the cleanup function\\nimport atexit\\natexit.register(cleanup_on_shutdown)\\n\\n# Code for: def remove_readonly(func, path, _):\\n\\n# Code for: def cleanup_directories():\\n\\n# Run cleanup at startup\\ncleanup_directories()\\n\\n# Load environment variables\\nload_dotenv()\\nGOOGLE_API_KEY = os.getenv(\\'GOOGLE_API_KEY\\')\\n\\nif not GOOGLE_API_KEY:\\n    raise ValueError(\"GOOGLE_API_KEY environment variable is not set\")\\n\\n# Initialize Flask app and limiter\\napp = Flask(__name__)\\nCORS(app)\\n\\n# Simple in-memory limiter configuration\\nlimiter = Limiter(\\n    app=app,\\n    key_func=get_remote_address,\\n    default_limits=[\"200 per day\", \"50 per hour\"],\\n    storage_options={\"STORAGE_BACKEND\": \"memory\"}\\n)\\n\\n# Code for: def validate_github_url(url):\\n\\n# Code for: def initialize_chat_model():\\n\\<EMAIL>(\\'/\\')\\n# Code for: def index():\\n\\<EMAIL>(\\'/chatbot\\', methods=[\"POST\"])\\<EMAIL>(\"10 per minute\")\\n# Code for: def process_repository():\\n\\<EMAIL>(\"/get\", methods=[\"POST\"])\\<EMAIL>(\"30 per minute\")\\n# Code for: def chat():\\n\\<EMAIL>(\"/get_current_repo\", methods=[\"GET\"])\\n# Code for: def get_current_repo():\\n\\<EMAIL>(\"/clear_cache\", methods=[\"POST\"])\\n# Code for: def clear_cache():\\n\\nif __name__ == \\'__main__\\':\\n    app.run(host=\"0.0.0.0\", port=8080, debug=True)'),\n", " Document(metadata={'source': 'test\\\\python\\\\helper.py', 'content_type': 'functions_classes', 'language': 'python'}, page_content='def repo_ingestion(repo_url):\\n    try:\\n        os.makedirs(\"repo\", exist_ok=True)\\n        repo_path = \"repo/\"\\n        Repo.clone_from(repo_url, to_path=repo_path)\\n    except Exception as e:\\n        logging.error(f\"Failed to clone repository: {str(e)}\")\\n        raise'),\n", " Document(metadata={'source': 'test\\\\python\\\\helper.py', 'content_type': 'functions_classes', 'language': 'python'}, page_content='def load_repo(repo_path):\\n    try:\\n        loader = GenericLoader.from_filesystem(repo_path,\\n                                        glob = \"**/*\",\\n                                       suffixes=[\".py\"],\\n                                       parser = LanguageParser(language=Language.PYTHON, parser_threshold=500)\\n                                        )\\n    except Exception as e:\\n        logging.error(f\"Failed to clone repository: {str(e)}\")\\n        raise\\n    \\n    documents = loader.load()\\n\\n    return documents'),\n", " Document(metadata={'source': 'test\\\\python\\\\helper.py', 'content_type': 'functions_classes', 'language': 'python'}, page_content='def text_splitter(documents):\\n    try:\\n        documents_splitter = RecursiveCharacterTextSplitter.from_language(language = Language.PYTHON,\\n                                                             chunk_size = 2000,\\n                                                             chunk_overlap = 200)\\n    except Exception as e:\\n        logging.error(f\"Failed to clone repository: {str(e)}\")\\n        raise\\n    \\n    text_chunks = documents_splitter.split_documents(documents)\\n\\n    return text_chunks'),\n", " Document(metadata={'source': 'test\\\\python\\\\helper.py', 'content_type': 'functions_classes', 'language': 'python'}, page_content='def load_embedding():\\n    try:\\n        embeddings = GoogleGenerativeAIEmbeddings(\\n            model=\"models/embedding-001\",\\n            google_api_key=GOOGLE_API_KEY,\\n            task_type=\"retrieval_query\"\\n        )\\n    except Exception as e:\\n        logging.error(f\"Failed to load embeddings: {str(e)}\")\\n        raise\\n    \\n    return embeddings'),\n", " Document(metadata={'source': 'test\\\\python\\\\helper.py', 'content_type': 'simplified_code', 'language': 'python'}, page_content='import os\\nimport logging\\nfrom git import Repo\\nfrom langchain_community.document_loaders.generic import GenericLoader\\nfrom langchain_community.document_loaders.parsers.language.language_parser import LanguageParser\\nfrom langchain.text_splitter import Language\\nfrom langchain.text_splitter import RecursiveCharacterTextSplitter\\nfrom langchain_google_genai import GoogleGenerativeAIEmbeddings\\n\\nfrom dotenv import load_dotenv\\nload_dotenv()\\n\\nGOOGLE_API_KEY = os.environ.get(\\'GOOGLE_API_KEY\\')\\nos.environ[\"GOOGLE_API_KEY\"] = GOOGLE_API_KEY\\n\\n\\n\\n#clone any github repositories \\n# Code for: def repo_ingestion(repo_url):\\n\\n\\n\\n\\n#Loading repositories as documents\\n# Code for: def load_repo(repo_path):\\n\\n\\n\\n\\n#Creating text chunks \\n# Code for: def text_splitter(documents):\\n\\n\\n\\n#loading embeddings model\\n# Code for: def load_embedding():'),\n", " Document(metadata={'source': 'test\\\\java\\\\SecondDataSourceProperties.java', 'language': 'java'}, page_content='package com.morganstanley.loanApp.configuration;\\n\\nimport jakarta.annotation.PostConstruct;\\nimport org.springframework.boot.context.properties.ConfigurationProperties;\\nimport org.springframework.stereotype.Component;\\n\\n@Component\\n@ConfigurationProperties(prefix = \"sp.second-datasource\")\\npublic class SecondDataSourceProperties {\\n    public String getUrl() {\\n        return url;\\n    }\\n\\n    public void setUrl(String url) {\\n        this.url = url;\\n    }\\n\\n    public String getUsername() {\\n        return username;\\n    }\\n\\n    public void setUsername(String username) {\\n        this.username = username;\\n    }\\n\\n    public String getPassword() {\\n        return password;\\n    }\\n\\n    public void setPassword(String password) {\\n        this.password = password;\\n    }\\n\\n    public String getDriverClassName() {\\n        return driverClassName;\\n    }\\n\\n    public void setDriverClassName(String driverClassName) {\\n        this.driverClassName = driverClassName;\\n    }\\n\\n    private String url;\\n    private String username;\\n    private String password;\\n    private String driverClassName;\\n    @PostConstruct\\n    public void printProps() {\\n        System.out.println(\"Secondary DS Props:\");\\n        System.out.println(\"URL: \" + url);\\n        System.out.println(\"Username: \" + username);\\n        System.out.println(\"Driver: \" + driverClassName);\\n    }\\n\\n\\n}'),\n", " Document(metadata={'source': 'test\\\\java\\\\StoredProcDBConfig.java', 'language': 'java'}, page_content='package com.morganstanley.loanApp.configuration;\\nimport javax.sql.DataSource;\\n\\nimport com.zaxxer.hikari.HikariDataSource;\\nimport org.springframework.beans.factory.annotation.Qualifier;\\nimport org.springframework.boot.context.properties.ConfigurationProperties;\\nimport org.springframework.boot.jdbc.DataSourceBuilder;\\nimport org.springframework.context.annotation.Bean;\\nimport org.springframework.context.annotation.Configuration;\\nimport org.springframework.context.annotation.Primary;\\nimport org.springframework.jdbc.core.JdbcTemplate;\\n\\n@Configuration\\npublic class StoredProcDBConfig {\\n    @Bean\\n    @Qualifier(\"dataSource\")\\n    @Primary\\n    public DataSource primaryDataSource(PrimaryDataSourceProperties props) {\\n        HikariDataSource ds = new HikariDataSource();\\n        ds.setJdbcUrl(props.getUrl());\\n        ds.setUsername(props.getUsername());\\n        ds.setPassword(props.getPassword());\\n        ds.setDriverClassName(props.getDriverClassName());\\n\\n        System.out.println(\">>> Primary DataSource initialized with URL: \" + props.getUrl());\\n\\n        return ds;\\n    }\\n\\n    @Bean(name = \"storeProcDataSource\")\\n    public DataSource storeProcDataSource(SecondDataSourceProperties props) {\\n        HikariDataSource ds = new HikariDataSource();\\n        ds.setJdbcUrl(props.getUrl());\\n        ds.setUsername(props.getUsername());\\n        ds.setPassword(props.getPassword());\\n        ds.setDriverClassName(props.getDriverClassName());\\n\\n        System.out.println(\">>> Second DataSource initialized with URL: \" + props.getUrl());\\n\\n        return ds;\\n    }\\n\\n    @Bean\\n    public JdbcTemplate storeProcJdbcTemplate(@Qualifier(\"storeProcDataSource\") DataSource ds) {\\n        return new JdbcTemplate(ds);\\n    }\\n}'),\n", " Document(metadata={'source': 'test\\\\java\\\\StoreProcService.java', 'language': 'java'}, page_content='package com.morganstanley.loanApp.service;\\n\\nimport org.springframework.beans.factory.annotation.Autowired;\\nimport org.springframework.beans.factory.annotation.Qualifier;\\nimport org.springframework.jdbc.core.JdbcTemplate;\\nimport org.springframework.stereotype.Service;\\n\\nimport java.sql.CallableStatement;\\nimport java.sql.Connection;\\nimport java.sql.Types;\\n\\n@Service\\npublic class StoreProcService {\\n\\n\\n    @Autowired\\n    @Qualifier(\"storeProcJdbcTemplate\")\\n    private JdbcTemplate storeProcTemplate;\\n\\n\\n\\n\\n    public Integer callStoreProc(String branchName, String branchAddress) {\\n\\n        return storeProcTemplate.execute((Connection conn) -> {\\n            CallableStatement cs = conn.prepareCall(\"{call sp_insert_branch(?, ?, ?)}\");\\n            cs.setString(1, branchName);\\n            cs.setString(2, branchAddress);\\n            cs.registerOutParameter(3, Types.INTEGER);\\n            cs.execute();\\n            return cs.getInt(3);\\n        });\\n    }\\n}'),\n", " Document(metadata={'source': 'test\\\\java\\\\User.java', 'language': 'java'}, page_content='package com.morganstanley.loanApp.model;\\n\\nimport jakarta.persistence.*;\\nimport lombok.Data;\\n\\n@Entity\\n@Table(name = \"users\")\\n@Data\\npublic class User {\\n\\n    @Id\\n    @GeneratedValue(strategy = GenerationType.IDENTITY)\\n    private Long id;\\n\\n    private String name;\\n    private String email;\\n}'),\n", " Document(metadata={'source': 'test\\\\java\\\\UserController.java', 'language': 'java'}, page_content='package com.morganstanley.loanApp.controller;\\n\\nimport com.morganstanley.loanApp.dto.UserLoanRequestDTO;\\nimport com.morganstanley.loanApp.model.User;\\n\\nimport com.morganstanley.loanApp.service.StoreProcService;\\nimport com.morganstanley.loanApp.service.UserService;\\nimport jakarta.validation.Valid;\\nimport org.springframework.beans.factory.annotation.Autowired;\\nimport org.springframework.http.HttpStatus;\\nimport org.springframework.http.ResponseEntity;\\nimport org.springframework.web.bind.annotation.PostMapping;\\nimport org.springframework.web.bind.annotation.RequestBody;\\nimport org.springframework.web.bind.annotation.RequestMapping;\\nimport org.springframework.web.bind.annotation.RestController;\\n\\nimport java.io.*;\\nimport java.util.ArrayList;\\nimport java.util.List;\\n\\n@RestController\\n@RequestMapping(\"/loanApp\")'),\n", " Document(metadata={'source': 'test\\\\java\\\\UserController.java', 'language': 'java'}, page_content='public class UserController {\\n    @Autowired\\n    private UserService userService;\\n\\n    @Autowired\\n    private StoreProcService storeProcService;\\n\\n    @PostMapping(\"/createLoan\")\\n    public ResponseEntity<User> createUserWithLoan(@Valid @RequestBody UserLoanRequestDTO dto) {\\n        User savedUser = userService.createUserWithLoan(dto);\\n        return new ResponseEntity<>(savedUser, HttpStatus.CREATED);\\n    }\\n\\n    @PostMapping(\"/save-from-file\")\\n    public ResponseEntity<String> saveUserLoanFromFile(){\\n        try (InputStream is = getClass().getClassLoader().getResourceAsStream(\"usersData.txt\");\\n             BufferedReader reader = new BufferedReader(new InputStreamReader(is))) {\\n\\n            if (is == null) {\\n                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)\\n                        .body(\"Data file not found\");\\n            }\\n\\n            String line ;\\n            while ((line = reader.readLine()) != null) {\\n                String[] parts = line.split(\",\");\\n                if (parts.length != 4) {\\n                    return ResponseEntity.badRequest()\\n                            .body(\"Invalid file format\");\\n                }\\n\\n\\n                UserLoanRequestDTO userDTO = new UserLoanRequestDTO();\\n                userDTO.setName(parts[0].trim());\\n                userDTO.setEmail(parts[1].trim());\\n                userDTO.setLoanType(parts[2].trim());\\n                userDTO.setAmount(Double.valueOf(parts[3].trim()));\\n\\n                userService.createUserWithLoan(userDTO);\\n            }\\n            return ResponseEntity.ok(\"Loan application saved from flat file\");\\n\\n        } catch (Exception e) {\\n            e.printStackTrace();\\n            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)\\n                    .body(\"Error processing flat file\");\\n        }\\n    }'),\n", " Document(metadata={'source': 'test\\\\java\\\\UserController.java', 'language': 'java'}, page_content='@PostMapping(\"/processStoreProcFromCSV\")\\n    public ResponseEntity<List<String>> readCSVAndCallStoreProc(){\\n        List<String> outputMessages = new ArrayList<>();\\n\\n        try (InputStream is = getClass().getClassLoader().getResourceAsStream(\"branchesData.csv\");\\n             BufferedReader reader = new BufferedReader(new InputStreamReader(is))) {\\n            String line;\\n            boolean isFirstLine = true;\\n\\n            while ((line = reader.readLine()) != null) {\\n                if (isFirstLine) {\\n                    isFirstLine = false; // skip header\\n                    continue;\\n                }\\n\\n                String[] parts = line.split(\",\");\\n                if (parts.length != 2) continue;\\n\\n                String branchName = parts[0].trim();\\n                String branchAddress = parts[1].trim();\\n\\n                Integer result = storeProcService.callStoreProc(branchName, branchAddress);\\n                outputMessages.add(\"Branch Id for \" + branchName + \" is: \" + result);\\n            }\\n\\n            return ResponseEntity.ok(outputMessages);\\n        } catch (IOException | NumberFormatException ex) {\\n            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)\\n                    .body(List.of(\"Error reading file or parsing data: \" + ex.getMessage()));\\n        }\\n    }\\n\\n\\n}'),\n", " Document(metadata={'source': 'test\\\\java\\\\UserLoanRequestDTO.java', 'language': 'java'}, page_content='package com.morganstanley.loanApp.dto;\\n\\nimport jakarta.validation.constraints.Email;\\nimport jakarta.validation.constraints.NotBlank;\\nimport jakarta.validation.constraints.NotNull;\\nimport jakarta.validation.constraints.Positive;\\nimport lombok.Data;\\nimport lombok.Getter;\\nimport lombok.Setter;\\n\\n\\n\\npublic class UserLoanRequestDTO {\\n\\n    public String getName() {\\n        return name;\\n    }\\n\\n    public void setName(String name) {\\n        this.name = name;\\n    }\\n\\n    public String getEmail() {\\n        return email;\\n    }\\n\\n    public void setEmail(String email) {\\n        this.email = email;\\n    }\\n\\n    public String getLoanType() {\\n        return loanType;\\n    }\\n\\n    public void setLoanType(String loanType) {\\n        this.loanType = loanType;\\n    }\\n\\n    public Double getAmount() {\\n        return amount;\\n    }\\n\\n    public void setAmount(Double amount) {\\n        this.amount = amount;\\n    }\\n\\n    @NotBlank(message = \"Name is required\")\\n    private String name;\\n\\n    @Email(message = \"Email should be valid\")\\n    @NotBlank(message = \"Email is required\")\\n    private String email;\\n\\n    @NotBlank(message = \"Loan type is required\")\\n    private String loanType;\\n\\n    @NotNull(message = \"Loan amount is required\")\\n    @Positive(message = \"Loan amount must be positive\")\\n    private Double amount;\\n}'),\n", " Document(metadata={'source': 'test\\\\java\\\\UserRepository.java', 'language': 'java'}, page_content='package com.morganstanley.loanApp.repository;\\n\\nimport com.morganstanley.loanApp.model.User;\\nimport org.springframework.data.jpa.repository.JpaRepository;\\n\\n\\npublic interface UserRepository extends JpaRepository<User, Long> {\\n}'),\n", " Document(metadata={'source': 'test\\\\java\\\\UserService.java', 'language': 'java'}, page_content='package com.morganstanley.loanApp.service;\\n\\nimport com.morganstanley.loanApp.dto.UserLoanRequestDTO;\\nimport com.morganstanley.loanApp.model.LoanDetails;\\nimport com.morganstanley.loanApp.model.User;\\nimport com.morganstanley.loanApp.repository.LoanDetailsRepository;\\nimport com.morganstanley.loanApp.repository.UserRepository;\\nimport org.springframework.beans.factory.annotation.Autowired;\\nimport org.springframework.stereotype.Service;\\n\\n@Service\\npublic class UserService {\\n    @Autowired\\n    private UserRepository userRepository;\\n\\n    @Autowired\\n    private LoanDetailsRepository loanDetailsRepository;\\n\\n    public User createUserWithLoan(UserLoanRequestDTO dto) {\\n        User user = new User();\\n        user.setName(dto.getName());\\n        user.setEmail(dto.getEmail());\\n        user = userRepository.save(user);\\n\\n        LoanDetails loan = new LoanDetails();\\n        loan.setLoanType(dto.getLoanType());\\n        loan.setAmount(dto.getAmount());\\n        loan.setUserId(user.getId());\\n        loanDetailsRepository.save(loan);\\n\\n        return user;\\n    }\\n}')]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["docs"]}, {"cell_type": "code", "execution_count": 9, "id": "0404452f", "metadata": {}, "outputs": [], "source": ["groq_api_key=\"********************************************************\""]}, {"cell_type": "code", "execution_count": 10, "id": "4829d74a", "metadata": {}, "outputs": [], "source": ["system_prompt = \"\"\"\n", "You are a **code and data lineage analysis engine**. From the provided Python or Java code chunk, extract **structured graph triples** in the format:\n", "\n", "[SourceNodeLabel]:SourceNodeName -[RELATION]-> [TargetNodeLabel]:TargetNodeName\n", "\n", "## Context:\n", "- Use the explicit Folder and File info provided above each code chunk.\n", "- Ignore package statements when building Folder nodes.\n", "- If database table names, column names, or API endpoints are detected, include them as nodes for lineage.\n", "- If functions or classes are reading from or writing to database tables or making API calls, capture them in the lineage relationships.\n", "\n", "## Layers to capture:\n", "- Folder -> File\n", "- File -> Class\n", "- Class -> Function/Method\n", "- Function/Method -> Function/Method (CALLS)\n", "- Class -> Class (INHERITS)\n", "- Function/Method -> Table (READS_FROM / WRITES_TO)\n", "- Function/Method -> API (CALLS_API)\n", "\n", "## Node Types:\n", "- Folder\n", "- File\n", "- Class\n", "- Function\n", "- Table\n", "- Column (if explicit)\n", "- API\n", "\n", "## Relationship Types:\n", "- CONTAINS\n", "- DECLARES\n", "- CALLS\n", "- INHERITS\n", "- READS_FROM\n", "- WRITES_TO\n", "- CALLS_API\n", "\n", "## Examples:\n", "\n", "Folder:test/java -[CONTAINS]-> File:SecondDataSourceProperties.java\n", "File:SecondDataSourceProperties.java -[DECLARES]-> Class:SecondDataSourceProperties\n", "Class:SecondDataSourceProperties -[DECLARES]-> Function:getUrl\n", "Function:load_user_data -[READS_FROM]-> Table:users\n", "Function:save_user_data -[WRITES_TO]-> Table:users\n", "Function:fetch_from_service -[CALLS_API]-> API:getUserProfile\n", "\n", "## Instructions:\n", "- Return **only these triples, nothing else**.\n", "- If unsure, prefer to omit rather than hallucinate.\n", "- If code chunk does not involve data sources, return structural triples only.\n", "\n", "\"\"\"\n"]}, {"cell_type": "code", "execution_count": 11, "id": "5aad5f9a", "metadata": {}, "outputs": [], "source": ["from langchain_groq import ChatGroq\n", "from langchain_experimental.graph_transformers import LLMGraphTransformer\n", "\n", "llm = ChatGroq(\n", "    model=\"gemma2-9b-it\",   # or any verified available model\n", "    api_key=groq_api_key)"]}, {"cell_type": "code", "execution_count": 12, "id": "849eb8a5", "metadata": {}, "outputs": [], "source": ["transformer4 = LLMGraphTransformer(\n", "    llm=llm,\n", "    additional_instructions=system_prompt,\n", "    allowed_nodes=[\"Folder\", \"File\", \"Class\", \"Function\", \"Table\", \"Column\", \"API\"],\n", "    allowed_relationships=[\n", "        (\"Folder\", \"CONTAINS\", \"File\"),\n", "        (\"File\", \"DECLARES\", \"Class\"),\n", "        (\"Class\", \"DECLARES\", \"Function\"),\n", "        (\"Function\", \"CALLS\", \"Function\"),\n", "        (\"Class\", \"INHERITS\", \"Class\"),\n", "        (\"Function\", \"READS_FROM\", \"Table\"),\n", "        (\"Function\", \"WRITES_TO\", \"Table\"),\n", "        (\"Function\", \"CALLS_API\", \"API\")\n", "    ],\n", "    strict_mode=True,\n", "    node_properties=False,\n", "    relationship_properties=False\n", ")\n"]}, {"cell_type": "code", "execution_count": 14, "id": "ddfb9e29", "metadata": {}, "outputs": [], "source": ["graph_documents4 = transformer4.convert_to_graph_documents(docs)"]}, {"cell_type": "code", "execution_count": 19, "id": "efeef59a", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Node(id='Cleanup_On_Shutdown', type='Function', properties={}),\n", " Node(id='Db', type='File', properties={}),\n", " Node(id='Chromadb', type='Api', properties={})]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["graph_documents4[0].nodes  # Display first 100 nodes\n"]}, {"cell_type": "code", "execution_count": 21, "id": "7a15e4eb", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Relationship(source=Node(id='Cleanup_On_Shutdown', type='Function', properties={}), target=Node(id='Chromadb', type='Api', properties={}), type='CALLS_API', properties={})]"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["graph_documents4[0].relationships"]}, {"cell_type": "code", "execution_count": 27, "id": "b1abb2da", "metadata": {}, "outputs": [{"data": {"text/plain": ["[GraphDocument(nodes=[Node(id='Folder:Test/Python', type='Folder', properties={}), Node(id='File:App.Py', type='File', properties={}), Node(id='Class:Chromadb', type='Class', properties={}), Node(id='Function:Client', type='Function', properties={}), Node(id='Function:Reset', type='Function', properties={})], relationships=[Relationship(source=Node(id='Folder:Test/Python', type='Folder', properties={}), target=Node(id='File:App.Py', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='File:App.Py', type='File', properties={}), target=Node(id='Class:Chromadb', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Class:Chromadb', type='Class', properties={}), target=Node(id='Function:Client', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Function:Client', type='Function', properties={}), target=Node(id='Function:Reset', type='Function', properties={}), type='CALLS', properties={})], source=Document(metadata={'source': 'test\\\\python\\\\app.py', 'content_type': 'functions_classes', 'language': <Language.PYTHON: 'python'>}, page_content='File Path: test\\\\python\\\\app.py\\nFolder: test\\\\python\\nFile: app.py\\n\\ndef cleanup_on_shutdown():\\n    try:\\n        if os.path.exists(\"db\"):\\n            import chromadb\\n            # Close any open ChromaDB instances\\n            client = chromadb.Client()\\n            client.reset()\\n    except Exception as e:\\n        logger.error(f\"Error during shutdown cleanup: {str(e)}\")')),\n", " GraphDocument(nodes=[Node(id='Folder:Test\\\\Python', type='Folder', properties={}), Node(id='File:App.Py', type='File', properties={}), Node(id='Class:Remove_Readonly', type='Function', properties={})], relationships=[Relationship(source=Node(id='Folder:Test\\\\Python', type='Folder', properties={}), target=Node(id='File:App.Py', type='File', properties={}), type='CONTAINS', properties={})], source=Document(metadata={'source': 'test\\\\python\\\\app.py', 'content_type': 'functions_classes', 'language': <Language.PYTHON: 'python'>}, page_content='File Path: test\\\\python\\\\app.py\\nFolder: test\\\\python\\nFile: app.py\\n\\ndef remove_readonly(func, path, _):\\n    \"\"\"Clear the readonly bit and reattempt the removal\"\"\"\\n    try:\\n        os.chmod(path, stat.S_IWRITE)\\n        func(path)\\n    except Exception as e:\\n        logger.error(f\"Error removing readonly file {path}: {str(e)}\")')),\n", " GraphDocument(nodes=[Node(id='Folder:Test/Python', type='Folder', properties={}), Node(id='File:App.Py', type='File', properties={}), Node(id='Class:Cleanup_Directories', type='Class', properties={}), Node(id='Function:Force_Remove_Readonly', type='Function', properties={}), Node(id='Function:Cleanup_Directories', type='Function', properties={}), Node(id='Function:Sqlite3.Connect', type='Function', properties={}), Node(id='Class:Sqlite3', type='Class', properties={}), Node(id='Function:Gc.Collect', type='Function', properties={}), Node(id='Function:Shutil.Rmtree', type='Function', properties={}), Node(id='Class:Os', type='Class', properties={}), Node(id='Class:Stat', type='Class', properties={}), Node(id='Class:Shutil', type='Class', properties={}), Node(id='Class:Logger', type='Class', properties={}), Node(id='Class:Time', type='Class', properties={}), Node(id='Class:Gc', type='Class', properties={})], relationships=[Relationship(source=Node(id='Folder:Test/Python', type='Folder', properties={}), target=Node(id='File:App.Py', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='File:App.Py', type='File', properties={}), target=Node(id='Class:Cleanup_Directories', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Class:Cleanup_Directories', type='Class', properties={}), target=Node(id='Function:Cleanup_Directories', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Class:Cleanup_Directories', type='Class', properties={}), target=Node(id='Function:Force_Remove_Readonly', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Function:Force_Remove_Readonly', type='Function', properties={}), target=Node(id='Function:Force_Remove_Readonly', type='Function', properties={}), type='CALLS', properties={}), Relationship(source=Node(id='Function:Cleanup_Directories', type='Function', properties={}), target=Node(id='Function:Sqlite3.Connect', type='Function', properties={}), type='CALLS', properties={}), Relationship(source=Node(id='Function:Cleanup_Directories', type='Function', properties={}), target=Node(id='Function:Gc.Collect', type='Function', properties={}), type='CALLS', properties={}), Relationship(source=Node(id='Function:Cleanup_Directories', type='Function', properties={}), target=Node(id='Function:Shutil.Rmtree', type='Function', properties={}), type='CALLS', properties={})], source=Document(metadata={'source': 'test\\\\python\\\\app.py', 'content_type': 'functions_classes', 'language': <Language.PYTHON: 'python'>}, page_content='File Path: test\\\\python\\\\app.py\\nFolder: test\\\\python\\nFile: app.py\\n\\ndef cleanup_directories():\\n    \"\"\"Clean up directories with better error handling for Git repositories\"\"\"\\n    def force_remove_readonly(func, path, _):\\n        try:\\n            os.chmod(path, stat.S_IWRITE)\\n            func(path)\\n        except Exception as e:\\n            logger.warning(f\"Could not remove {path}: {str(e)}\")\\n\\n    try:\\n        # Force close any open database connections\\n        import sqlite3\\n        try:\\n            sqlite3.connect(\\'db/chroma.sqlite3\\').close()\\n        except:\\n            pass\\n\\n        # Force garbage collection to release file handles\\n        import gc\\n        gc.collect()\\n\\n        if os.path.exists(\"repo\"):\\n            logger.info(\"Removing existing repo directory\")\\n            shutil.rmtree(\"repo\", onerror=force_remove_readonly)\\n\\n        if os.path.exists(\"db\"):\\n            logger.info(\"Removing existing db directory\")\\n            # Wait a moment for any file handles to be released\\n            import time\\n            time.sleep(1)\\n            shutil.rmtree(\"db\", onerror=force_remove_readonly)\\n\\n    except Exception as e:\\n        logger.error(f\"Error during cleanup: {str(e)}\")\\n        # Continue execution even if cleanup fails\\n        pass')),\n", " GraphDocument(nodes=[Node(id='Folder:Test\\\\Python', type='Folder', properties={}), Node(id='File:App.Py', type='File', properties={}), Node(id='Class:Validate_Github_Url', type='Function', properties={})], relationships=[Relationship(source=Node(id='Folder:Test\\\\Python', type='Folder', properties={}), target=Node(id='File:App.Py', type='File', properties={}), type='CONTAINS', properties={})], source=Document(metadata={'source': 'test\\\\python\\\\app.py', 'content_type': 'functions_classes', 'language': <Language.PYTHON: 'python'>}, page_content='File Path: test\\\\python\\\\app.py\\nFolder: test\\\\python\\nFile: app.py\\n\\ndef validate_github_url(url):\\n    \"\"\"Validate if the provided URL is a GitHub repository URL.\"\"\"\\n    if not isinstance(url, str):\\n        return False\\n    url = url.lower()\\n    return url.startswith((\\'http://github.com/\\', \\'https://github.com/\\')) and len(url.split(\\'/\\')) >= 5')),\n", " GraphDocument(nodes=[Node(id='File:App.Py', type='File', properties={})], relationships=[], source=Document(metadata={'source': 'test\\\\python\\\\app.py', 'content_type': 'functions_classes', 'language': <Language.PYTHON: 'python'>}, page_content='File Path: test\\\\python\\\\app.py\\nFolder: test\\\\python\\nFile: app.py\\n\\ndef initialize_chat_model():\\n    \"\"\"Initialize the chat model and vector store.\"\"\"\\n    try:\\n        embeddings = load_embedding()\\n        vectordb = Chroma(\\n            persist_directory=\"db\",\\n            embedding_function=embeddings\\n        )\\n        \\n        llm = ChatGoogleGenerativeAI(\\n            model=\"gemini-2.0-flash-lite\",\\n            temperature=0.6,\\n            google_api_key=GOOGLE_API_KEY,\\n            max_output_tokens=2048  # Add token limit\\n        )\\n        \\n        memory = ConversationBufferMemory(\\n            memory_key=\"chat_history\",\\n            return_messages=True,\\n            output_key=\"answer\"  # Specify output key\\n        )\\n        \\n        qa_chain = ConversationalRetrievalChain.from_llm(\\n            llm=llm,\\n            retriever=vectordb.as_retriever(\\n                search_type=\"mmr\",\\n                search_kwargs={\"k\": 4, \"fetch_k\": 20}  # Adjust retrieval parameters\\n            ),\\n            memory=memory,\\n            return_source_documents=True,  # Include source documents\\n            verbose=True\\n        )\\n        return qa_chain\\n    except Exception as e:\\n        logger.error(f\"Error initializing chat model: {str(e)}\")\\n        raise')),\n", " GraphDocument(nodes=[Node(id='Test/Python', type='Folder', properties={}), Node(id='App.Py', type='File', properties={}), Node(id='Index', type='Function', properties={})], relationships=[Relationship(source=Node(id='Test/Python', type='Folder', properties={}), target=Node(id='App.Py', type='File', properties={}), type='CONTAINS', properties={})], source=Document(metadata={'source': 'test\\\\python\\\\app.py', 'content_type': 'functions_classes', 'language': <Language.PYTHON: 'python'>}, page_content=\"File Path: test\\\\python\\\\app.py\\nFolder: test\\\\python\\nFile: app.py\\n\\ndef index():\\n    return render_template('index.html')\")),\n", " GraphDocument(nodes=[Node(id='Folder:Test\\\\Python', type='Folder', properties={}), Node(id='File:App.Py', type='File', properties={}), Node(id='Class:Request', type='Class', properties={}), Node(id='Function:Process_Repository', type='Function', properties={}), Node(id='Function:Validate_Github_Url', type='Function', properties={}), Node(id='Function:Repo_Ingestion', type='Function', properties={}), Node(id='Function:Cleanup_Directories', type='Function', properties={}), Node(id='Function:Os.System', type='Function', properties={}), Node(id='Function:Store_Index.Py', type='File', properties={})], relationships=[Relationship(source=Node(id='Folder:Test\\\\Python', type='Folder', properties={}), target=Node(id='File:App.Py', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='Function:Process_Repository', type='Function', properties={}), target=Node(id='Function:Validate_Github_Url', type='Function', properties={}), type='CALLS', properties={}), Relationship(source=Node(id='Function:Process_Repository', type='Function', properties={}), target=Node(id='Function:Repo_Ingestion', type='Function', properties={}), type='CALLS', properties={}), Relationship(source=Node(id='Function:Process_Repository', type='Function', properties={}), target=Node(id='Function:Cleanup_Directories', type='Function', properties={}), type='CALLS', properties={}), Relationship(source=Node(id='Function:Process_Repository', type='Function', properties={}), target=Node(id='Function:Os.System', type='Function', properties={}), type='CALLS', properties={})], source=Document(metadata={'source': 'test\\\\python\\\\app.py', 'content_type': 'functions_classes', 'language': <Language.PYTHON: 'python'>}, page_content='File Path: test\\\\python\\\\app.py\\nFolder: test\\\\python\\nFile: app.py\\n\\ndef process_repository():\\n    try:\\n        repo_url = request.form.get(\\'question\\')\\n        \\n        if not repo_url:\\n            return jsonify({\"error\": \"Repository URL is required\"}), 400\\n        \\n        if not validate_github_url(repo_url):\\n            return jsonify({\"error\": \"Invalid GitHub repository URL\"}), 400\\n\\n        # Extract repo name from URL\\n        repo_name = repo_url.split(\\'/\\')[-1]\\n        if repo_name.endswith(\\'.git\\'):\\n            repo_name = repo_name[:-4]\\n\\n        # Clear existing repo and db if they exist\\n        cleanup_directories()\\n\\n        # Clone and process the repository\\n        repo_ingestion(repo_url)\\n        \\n        # Initialize the vector store\\n        try:\\n            os.system(\"python store_index.py\")\\n        except Exception as e:\\n            logger.error(f\"Error creating index: {str(e)}\")\\n            return jsonify({\"error\": \"Failed to create search index\"}), 500\\n\\n        return jsonify({\\n            \"success\": True, \\n            \"message\": \"Repository processed successfully\",\\n            \"repo_name\": repo_name\\n        })\\n\\n    except Exception as e:\\n        logger.error(f\"Error processing repository: {str(e)}\")\\n        cleanup_directories()  # Cleanup on error\\n        return jsonify({\"error\": \"Failed to process repository. Please try again.\"}), 500')),\n", " GraphDocument(nodes=[Node(id='Folder:Test/Python', type='Folder', properties={}), Node(id='File:App.Py', type='File', properties={}), Node(id='Class:Chat', type='Class', properties={}), Node(id='Function:Chat', type='Function', properties={})], relationships=[Relationship(source=Node(id='Folder:Test/Python', type='Folder', properties={}), target=Node(id='File:App.Py', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='File:App.Py', type='File', properties={}), target=Node(id='Class:Chat', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Class:Chat', type='Class', properties={}), target=Node(id='Function:Chat', type='Function', properties={}), type='DECLARES', properties={})], source=Document(metadata={'source': 'test\\\\python\\\\app.py', 'content_type': 'functions_classes', 'language': <Language.PYTHON: 'python'>}, page_content='File Path: test\\\\python\\\\app.py\\nFolder: test\\\\python\\nFile: app.py\\n\\ndef chat():\\n    try:\\n        message = request.form.get(\\'msg\\')\\n        \\n        if not message:\\n            return jsonify({\"error\": \"Message is required\"}), 400\\n\\n        if not os.path.exists(\"db\"):\\n            return jsonify({\"error\": \"Please analyze a repository first\"}), 400\\n\\n        qa_chain = initialize_chat_model()\\n        result = qa_chain.invoke({\\n            \"question\": message\\n        })\\n        \\n        # Extract the answer from the result\\n        answer = result.get(\\'answer\\', \\'\\')\\n        if not answer:\\n            return jsonify({\"error\": \"No response generated\"}), 500\\n\\n        # Clean and format the response\\n        answer = answer.strip()\\n        return jsonify({\"answer\": answer})\\n\\n    except Exception as e:\\n        logger.error(f\"Error in chat endpoint: {str(e)}\")\\n        return jsonify({\"error\": \"An error occurred while processing your request\"}), 500')),\n", " GraphDocument(nodes=[Node(id='Folder:Test/Python', type='Folder', properties={}), Node(id='File:App.Py', type='File', properties={}), Node(id='Class:Get_Current_Repo', type='Function', properties={})], relationships=[Relationship(source=Node(id='Folder:Test/Python', type='Folder', properties={}), target=Node(id='File:App.Py', type='File', properties={}), type='CONTAINS', properties={})], source=Document(metadata={'source': 'test\\\\python\\\\app.py', 'content_type': 'functions_classes', 'language': <Language.PYTHON: 'python'>}, page_content='File Path: test\\\\python\\\\app.py\\nFolder: test\\\\python\\nFile: app.py\\n\\ndef get_current_repo():\\n    try:\\n        if os.path.exists(\"repo\"):\\n            # Get the repository name from the cloned URL\\n            with open(\"repo/.git/config\", \"r\") as f:\\n                config = f.read()\\n                # Extract repo name from the URL\\n                for line in config.split(\\'\\\\n\\'):\\n                    if \\'url = \\' in line:\\n                        url = line.strip().split(\\'url = \\')[1]\\n                        repo_name = url.split(\\'/\\')[-1]\\n                        if repo_name.endswith(\\'.git\\'):\\n                            repo_name = repo_name[:-4]\\n                        return jsonify({\"repo_name\": repo_name})\\n        return jsonify({\"repo_name\": None})\\n    except Exception as e:\\n        logger.error(f\"Error getting repo name: {str(e)}\")\\n        return jsonify({\"error\": \"Failed to get repo name\"}), 500')),\n", " GraphDocument(nodes=[Node(id='Folder:Test/Python', type='Folder', properties={}), Node(id='File:App.Py', type='File', properties={})], relationships=[Relationship(source=Node(id='Folder:Test/Python', type='Folder', properties={}), target=Node(id='File:App.Py', type='File', properties={}), type='CONTAINS', properties={})], source=Document(metadata={'source': 'test\\\\python\\\\app.py', 'content_type': 'functions_classes', 'language': <Language.PYTHON: 'python'>}, page_content='File Path: test\\\\python\\\\app.py\\nFolder: test\\\\python\\nFile: app.py\\n\\ndef clear_cache():\\n    try:\\n        def force_remove_readonly(func, path, _):\\n            os.chmod(path, stat.S_IWRITE)\\n            if os.path.exists(path):\\n                func(path)\\n\\n        # Close any open file handles in the db directory\\n        if os.path.exists(\"db\"):\\n            try:\\n                import gc\\n                gc.collect()  # Force garbage collection\\n                shutil.rmtree(\"db\", onerror=force_remove_readonly)\\n            except Exception as e:\\n                logger.warning(f\"Error removing db directory: {str(e)}\")\\n\\n        # Handle Git repository cleanup\\n        if os.path.exists(\"repo\"):\\n            try:\\n                # Force close Git repository\\n                import git\\n                try:\\n                    repo = git.Repo(\"repo\")\\n                    repo.git.gc()\\n                    repo.close()\\n                except:\\n                    pass\\n\\n                # Wait for handles to be released\\n                import time\\n                time.sleep(1)\\n\\n                # Remove read-only attributes recursively\\n                for root, dirs, files in os.walk(\"repo\", topdown=False):\\n                    for name in files:\\n                        try:\\n                            file_path = os.path.join(root, name)\\n                            os.chmod(file_path, stat.S_IWRITE)\\n                        except:\\n                            pass\\n                    for name in dirs:\\n                        try:\\n                            dir_path = os.path.join(root, name)\\n                            os.chmod(dir_path, stat.S_IWRITE)\\n                        except:\\n                            pass\\n\\n                shutil.rmtree(\"repo\", onerror=force_remove_readonly)\\n            except Exception as e:\\n                logger.warning(f\"Error removing repo directory: {str(e)}\")')),\n", " GraphDocument(nodes=[Node(id='File:Shutil.Py', type='File', properties={})], relationships=[], source=Document(metadata={'source': 'test\\\\python\\\\app.py', 'content_type': 'functions_classes', 'language': <Language.PYTHON: 'python'>}, page_content='shutil.rmtree(\"repo\", onerror=force_remove_readonly)\\n            except Exception as e:\\n                logger.warning(f\"Error removing repo directory: {str(e)}\")\\n\\n        return jsonify({\"message\": \"Cache cleared successfully\"}), 200\\n    except Exception as e:\\n        logger.error(f\"Error clearing cache: {str(e)}\")\\n        return jsonify({\"error\": \"Failed to clear cache\"}), 500')),\n", " GraphDocument(nodes=[Node(id='Folder:Test\\\\Python', type='Folder', properties={}), Node(id='File:App.Py', type='File', properties={})], relationships=[Relationship(source=Node(id='Folder:Test\\\\Python', type='Folder', properties={}), target=Node(id='File:App.Py', type='File', properties={}), type='CONTAINS', properties={})], source=Document(metadata={'source': 'test\\\\python\\\\app.py', 'content_type': 'simplified_code', 'language': <Language.PYTHON: 'python'>}, page_content='File Path: test\\\\python\\\\app.py\\nFolder: test\\\\python\\nFile: app.py\\n\\nfrom flask import Flask, render_template, jsonify, request\\nfrom flask_cors import CORS\\nfrom flask_limiter import Limiter\\nfrom flask_limiter.util import get_remote_address\\nimport redis\\nfrom langchain_chroma import Chroma\\nfrom langchain_google_genai import ChatGoogleGenerativeAI\\nfrom langchain.memory import ConversationBufferMemory\\nfrom langchain.chains import ConversationalRetrievalChain\\nfrom src.helper import load_embedding, repo_ingestion\\nfrom dotenv import load_dotenv\\nimport os\\nimport logging\\nimport shutil\\nimport stat\\n\\n# Configure logging\\nlogging.basicConfig(level=logging.INFO)\\nlogger = logging.getLogger(__name__)\\n\\n# Ensure the application properly closes resources on shutdown\\n# Code for: def cleanup_on_shutdown():\\n\\n# Register the cleanup function\\nimport atexit\\natexit.register(cleanup_on_shutdown)\\n\\n# Code for: def remove_readonly(func, path, _):\\n\\n# Code for: def cleanup_directories():\\n\\n# Run cleanup at startup\\ncleanup_directories()\\n\\n# Load environment variables\\nload_dotenv()\\nGOOGLE_API_KEY = os.getenv(\\'GOOGLE_API_KEY\\')\\n\\nif not GOOGLE_API_KEY:\\n    raise ValueError(\"GOOGLE_API_KEY environment variable is not set\")\\n\\n# Initialize Flask app and limiter\\napp = Flask(__name__)\\nCORS(app)\\n\\n# Simple in-memory limiter configuration\\nlimiter = Limiter(\\n    app=app,\\n    key_func=get_remote_address,\\n    default_limits=[\"200 per day\", \"50 per hour\"],\\n    storage_options={\"STORAGE_BACKEND\": \"memory\"}\\n)\\n\\n# Code for: def validate_github_url(url):\\n\\n# Code for: def initialize_chat_model():\\n\\<EMAIL>(\\'/\\')\\n# Code for: def index():\\n\\<EMAIL>(\\'/chatbot\\', methods=[\"POST\"])\\<EMAIL>(\"10 per minute\")\\n# Code for: def process_repository():\\n\\<EMAIL>(\"/get\", methods=[\"POST\"])\\<EMAIL>(\"30 per minute\")\\n# Code for: def chat():\\n\\<EMAIL>(\"/get_current_repo\", methods=[\"GET\"])\\n# Code for: def get_current_repo():\\n\\<EMAIL>(\"/clear_cache\", methods=[\"POST\"])\\n# Code for: def clear_cache():')),\n", " GraphDocument(nodes=[Node(id='Folder:App', type='Folder', properties={})], relationships=[Relationship(source=Node(id='Folder:App', type='Folder', properties={}), target=Node(id='File:App.Py', type='File', properties={}), type='CONTAINS', properties={})], source=Document(metadata={'source': 'test\\\\python\\\\app.py', 'content_type': 'simplified_code', 'language': <Language.PYTHON: 'python'>}, page_content='@app.route(\"/get_current_repo\", methods=[\"GET\"])\\n# Code for: def get_current_repo():\\n\\<EMAIL>(\"/clear_cache\", methods=[\"POST\"])\\n# Code for: def clear_cache():\\n\\nif __name__ == \\'__main__\\':\\n    app.run(host=\"0.0.0.0\", port=8080, debug=True)')),\n", " GraphDocument(nodes=[Node(id='Folder:Test/Python', type='Folder', properties={}), Node(id='File:Helper.Py', type='File', properties={}), Node(id='Class:Repo', type='Class', properties={})], relationships=[Relationship(source=Node(id='Folder:Test/Python', type='Folder', properties={}), target=Node(id='File:Helper.Py', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='File:Helper.Py', type='File', properties={}), target=Node(id='Class:Repo', type='Class', properties={}), type='DECLARES', properties={})], source=Document(metadata={'source': 'test\\\\python\\\\helper.py', 'content_type': 'functions_classes', 'language': <Language.PYTHON: 'python'>}, page_content='File Path: test\\\\python\\\\helper.py\\nFolder: test\\\\python\\nFile: helper.py\\n\\ndef repo_ingestion(repo_url):\\n    try:\\n        os.makedirs(\"repo\", exist_ok=True)\\n        repo_path = \"repo/\"\\n        Repo.clone_from(repo_url, to_path=repo_path)\\n    except Exception as e:\\n        logging.error(f\"Failed to clone repository: {str(e)}\")\\n        raise')),\n", " GraphDocument(nodes=[Node(id='Folder:Test\\\\Python', type='Folder', properties={}), Node(id='File:Helper.Py', type='File', properties={}), Node(id='Class:Genericloader', type='Class', properties={}), Node(id='Class:Languageparser', type='Class', properties={}), Node(id='Class:Language', type='Class', properties={})], relationships=[Relationship(source=Node(id='Folder:Test\\\\Python', type='Folder', properties={}), target=Node(id='File:Helper.Py', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='File:Helper.Py', type='File', properties={}), target=Node(id='Class:Genericloader', type='Class', properties={}), type='DECLARES', properties={})], source=Document(metadata={'source': 'test\\\\python\\\\helper.py', 'content_type': 'functions_classes', 'language': <Language.PYTHON: 'python'>}, page_content='File Path: test\\\\python\\\\helper.py\\nFolder: test\\\\python\\nFile: helper.py\\n\\ndef load_repo(repo_path):\\n    try:\\n        loader = GenericLoader.from_filesystem(repo_path,\\n                                        glob = \"**/*\",\\n                                       suffixes=[\".py\"],\\n                                       parser = LanguageParser(language=Language.PYTHON, parser_threshold=500)\\n                                        )\\n    except Exception as e:\\n        logging.error(f\"Failed to clone repository: {str(e)}\")\\n        raise\\n    \\n    documents = loader.load()\\n\\n    return documents')),\n", " GraphDocument(nodes=[Node(id='Folder:Test\\\\Python', type='Folder', properties={}), Node(id='File:Helper.Py', type='File', properties={}), Node(id='Class:Recursivecharactertextsplitter', type='Class', properties={}), Node(id='Function:From_Language', type='Function', properties={}), Node(id='Function:Split_Documents', type='Function', properties={}), Node(id='Function:Text_Splitter', type='Function', properties={})], relationships=[Relationship(source=Node(id='Folder:Test\\\\Python', type='Folder', properties={}), target=Node(id='File:Helper.Py', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='File:Helper.Py', type='File', properties={}), target=Node(id='Class:Recursivecharactertextsplitter', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Class:Recursivecharactertextsplitter', type='Class', properties={}), target=Node(id='Function:From_Language', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Class:Recursivecharactertextsplitter', type='Class', properties={}), target=Node(id='Function:Split_Documents', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Function:Text_Splitter', type='Function', properties={}), target=Node(id='Function:Split_Documents', type='Function', properties={}), type='CALLS', properties={})], source=Document(metadata={'source': 'test\\\\python\\\\helper.py', 'content_type': 'functions_classes', 'language': <Language.PYTHON: 'python'>}, page_content='File Path: test\\\\python\\\\helper.py\\nFolder: test\\\\python\\nFile: helper.py\\n\\ndef text_splitter(documents):\\n    try:\\n        documents_splitter = RecursiveCharacterTextSplitter.from_language(language = Language.PYTHON,\\n                                                             chunk_size = 2000,\\n                                                             chunk_overlap = 200)\\n    except Exception as e:\\n        logging.error(f\"Failed to clone repository: {str(e)}\")\\n        raise\\n    \\n    text_chunks = documents_splitter.split_documents(documents)\\n\\n    return text_chunks')),\n", " GraphDocument(nodes=[Node(id='Folder:Test/Python', type='Folder', properties={}), Node(id='File:Helper.Py', type='File', properties={}), Node(id='Class:Googlegenerativeaiembeddings', type='Class', properties={}), Node(id='Function:Load_Embedding', type='Function', properties={})], relationships=[Relationship(source=Node(id='Folder:Test/Python', type='Folder', properties={}), target=Node(id='File:Helper.Py', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='File:Helper.Py', type='File', properties={}), target=Node(id='Class:Googlegenerativeaiembeddings', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Class:Googlegenerativeaiembeddings', type='Class', properties={}), target=Node(id='Function:Load_Embedding', type='Function', properties={}), type='DECLARES', properties={})], source=Document(metadata={'source': 'test\\\\python\\\\helper.py', 'content_type': 'functions_classes', 'language': <Language.PYTHON: 'python'>}, page_content='File Path: test\\\\python\\\\helper.py\\nFolder: test\\\\python\\nFile: helper.py\\n\\ndef load_embedding():\\n    try:\\n        embeddings = GoogleGenerativeAIEmbeddings(\\n            model=\"models/embedding-001\",\\n            google_api_key=GOOGLE_API_KEY,\\n            task_type=\"retrieval_query\"\\n        )\\n    except Exception as e:\\n        logging.error(f\"Failed to load embeddings: {str(e)}\")\\n        raise\\n    \\n    return embeddings')),\n", " GraphDocument(nodes=[Node(id='Folder:Test\\\\Python', type='Folder', properties={}), Node(id='File:Helper.Py', type='File', properties={}), Node(id='Class:Os', type='Class', properties={}), Node(id='Class:Logging', type='Class', properties={}), Node(id='Class:Repo', type='Class', properties={}), Node(id='Class:Genericloader', type='Class', properties={}), Node(id='Class:Languageparser', type='Class', properties={}), Node(id='Class:Language', type='Class', properties={}), Node(id='Class:Recursivecharactertextsplitter', type='Class', properties={}), Node(id='Class:Googlegenerativeaiembeddings', type='Class', properties={}), Node(id='Class:Dotenv', type='Class', properties={})], relationships=[Relationship(source=Node(id='Folder:Test\\\\Python', type='Folder', properties={}), target=Node(id='File:Helper.Py', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='File:Helper.Py', type='File', properties={}), target=Node(id='Class:Os', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='File:Helper.Py', type='File', properties={}), target=Node(id='Class:Logging', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='File:Helper.Py', type='File', properties={}), target=Node(id='Class:Repo', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='File:Helper.Py', type='File', properties={}), target=Node(id='Class:Genericloader', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='File:Helper.Py', type='File', properties={}), target=Node(id='Class:Languageparser', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='File:Helper.Py', type='File', properties={}), target=Node(id='Class:Language', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='File:Helper.Py', type='File', properties={}), target=Node(id='Class:Recursivecharactertextsplitter', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='File:Helper.Py', type='File', properties={}), target=Node(id='Class:Googlegenerativeaiembeddings', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='File:Helper.Py', type='File', properties={}), target=Node(id='Class:Dotenv', type='Class', properties={}), type='DECLARES', properties={})], source=Document(metadata={'source': 'test\\\\python\\\\helper.py', 'content_type': 'simplified_code', 'language': <Language.PYTHON: 'python'>}, page_content='File Path: test\\\\python\\\\helper.py\\nFolder: test\\\\python\\nFile: helper.py\\n\\n\\nimport os\\nimport logging\\nfrom git import Repo\\nfrom langchain_community.document_loaders.generic import GenericLoader\\nfrom langchain_community.document_loaders.parsers.language.language_parser import LanguageParser\\nfrom langchain.text_splitter import Language\\nfrom langchain.text_splitter import RecursiveCharacterTextSplitter\\nfrom langchain_google_genai import GoogleGenerativeAIEmbeddings\\n\\nfrom dotenv import load_dotenv\\nload_dotenv()\\n\\nGOOGLE_API_KEY = os.environ.get(\\'GOOGLE_API_KEY\\')\\nos.environ[\"GOOGLE_API_KEY\"] = GOOGLE_API_KEY\\n\\n\\n\\n#clone any github repositories \\n# Code for: def repo_ingestion(repo_url):\\n\\n\\n\\n\\n#Loading repositories as documents\\n# Code for: def load_repo(repo_path):\\n\\n\\n\\n\\n#Creating text chunks \\n# Code for: def text_splitter(documents):\\n\\n\\n\\n#loading embeddings model\\n# Code for: def load_embedding():')),\n", " GraphDocument(nodes=[Node(id='Folder:Test\\\\Java', type='Folder', properties={}), Node(id='File:Seconddatasourceproperties.Java', type='File', properties={}), Node(id='Class:Seconddatasourceproperties', type='Class', properties={}), Node(id='Function:Geturl', type='Function', properties={}), Node(id='Function:Seturl', type='Function', properties={}), Node(id='Function:Getusername', type='Function', properties={}), Node(id='Function:Setusername', type='Function', properties={}), Node(id='Function:Getpassword', type='Function', properties={}), Node(id='Function:Setpassword', type='Function', properties={}), Node(id='Function:Getdriverclassname', type='Function', properties={}), Node(id='Function:Setdriverclassname', type='Function', properties={}), Node(id='Function:Printprops', type='Function', properties={})], relationships=[Relationship(source=Node(id='Folder:Test\\\\Java', type='Folder', properties={}), target=Node(id='File:Seconddatasourceproperties.Java', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='File:Seconddatasourceproperties.Java', type='File', properties={}), target=Node(id='Class:Seconddatasourceproperties', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Class:Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Function:Geturl', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Class:Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Function:Seturl', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Class:Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Function:Getusername', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Class:Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Function:Setusername', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Class:Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Function:Getpassword', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Class:Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Function:Setpassword', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Class:Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Function:Getdriverclassname', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Class:Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Function:Setdriverclassname', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Class:Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Function:Printprops', type='Function', properties={}), type='DECLARES', properties={})], source=Document(metadata={'source': 'test\\\\java\\\\SecondDataSourceProperties.java'}, page_content='File Path: test\\\\java\\\\SecondDataSourceProperties.java\\nFolder: test\\\\java\\nFile: SecondDataSourceProperties.java\\n\\npackage com.morganstanley.loanApp.configuration;\\n\\nimport jakarta.annotation.PostConstruct;\\nimport org.springframework.boot.context.properties.ConfigurationProperties;\\nimport org.springframework.stereotype.Component;\\n\\n@Component\\n@ConfigurationProperties(prefix = \"sp.second-datasource\")\\npublic class SecondDataSourceProperties {\\n    public String getUrl() {\\n        return url;\\n    }\\n\\n    public void setUrl(String url) {\\n        this.url = url;\\n    }\\n\\n    public String getUsername() {\\n        return username;\\n    }\\n\\n    public void setUsername(String username) {\\n        this.username = username;\\n    }\\n\\n    public String getPassword() {\\n        return password;\\n    }\\n\\n    public void setPassword(String password) {\\n        this.password = password;\\n    }\\n\\n    public String getDriverClassName() {\\n        return driverClassName;\\n    }\\n\\n    public void setDriverClassName(String driverClassName) {\\n        this.driverClassName = driverClassName;\\n    }\\n\\n    private String url;\\n    private String username;\\n    private String password;\\n    private String driverClassName;\\n    @PostConstruct\\n    public void printProps() {\\n        System.out.println(\"Secondary DS Props:\");\\n        System.out.println(\"URL: \" + url);\\n        System.out.println(\"Username: \" + username);\\n        System.out.println(\"Driver: \" + driverClassName);\\n    }\\n\\n\\n}')),\n", " GraphDocument(nodes=[Node(id='Folder:Test\\\\Java', type='Folder', properties={}), Node(id='File:Storedprocdbconfig.Java', type='File', properties={}), Node(id='Class:Storedprocdbconfig', type='Class', properties={}), Node(id='Class:Primarydatasourceproperties', type='Class', properties={}), Node(id='Class:Seconddatasourceproperties', type='Class', properties={}), Node(id='Class:Datasource', type='Class', properties={}), Node(id='Class:Hikaridatasource', type='Class', properties={}), Node(id='Function:Primarydatasource', type='Function', properties={}), Node(id='Function:Storeprocdatasource', type='Function', properties={}), Node(id='Function:Storeprocjdbctemplate', type='Function', properties={})], relationships=[Relationship(source=Node(id='Folder:Test\\\\Java', type='Folder', properties={}), target=Node(id='File:Storedprocdbconfig.Java', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='File:Storedprocdbconfig.Java', type='File', properties={}), target=Node(id='Class:Storedprocdbconfig', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Class:Storedprocdbconfig', type='Class', properties={}), target=Node(id='Function:Primarydatasource', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Class:Storedprocdbconfig', type='Class', properties={}), target=Node(id='Function:Storeprocdatasource', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Class:Storedprocdbconfig', type='Class', properties={}), target=Node(id='Function:Storeprocjdbctemplate', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Class:Hikaridatasource', type='Class', properties={}), target=Node(id='Class:Datasource', type='Class', properties={}), type='INHERITS', properties={})], source=Document(metadata={'source': 'test\\\\java\\\\StoredProcDBConfig.java'}, page_content='File Path: test\\\\java\\\\StoredProcDBConfig.java\\nFolder: test\\\\java\\nFile: StoredProcDBConfig.java\\n\\n\\npackage com.morganstanley.loanApp.configuration;\\nimport javax.sql.DataSource;\\n\\nimport com.zaxxer.hikari.HikariDataSource;\\nimport org.springframework.beans.factory.annotation.Qualifier;\\nimport org.springframework.boot.context.properties.ConfigurationProperties;\\nimport org.springframework.boot.jdbc.DataSourceBuilder;\\nimport org.springframework.context.annotation.Bean;\\nimport org.springframework.context.annotation.Configuration;\\nimport org.springframework.context.annotation.Primary;\\nimport org.springframework.jdbc.core.JdbcTemplate;\\n\\n@Configuration\\npublic class StoredProcDBConfig {\\n    @Bean\\n    @Qualifier(\"dataSource\")\\n    @Primary\\n    public DataSource primaryDataSource(PrimaryDataSourceProperties props) {\\n        HikariDataSource ds = new HikariDataSource();\\n        ds.setJdbcUrl(props.getUrl());\\n        ds.setUsername(props.getUsername());\\n        ds.setPassword(props.getPassword());\\n        ds.setDriverClassName(props.getDriverClassName());\\n\\n        System.out.println(\">>> Primary DataSource initialized with URL: \" + props.getUrl());\\n\\n        return ds;\\n    }\\n\\n    @Bean(name = \"storeProcDataSource\")\\n    public DataSource storeProcDataSource(SecondDataSourceProperties props) {\\n        HikariDataSource ds = new HikariDataSource();\\n        ds.setJdbcUrl(props.getUrl());\\n        ds.setUsername(props.getUsername());\\n        ds.setPassword(props.getPassword());\\n        ds.setDriverClassName(props.getDriverClassName());\\n\\n        System.out.println(\">>> Second DataSource initialized with URL: \" + props.getUrl());\\n\\n        return ds;\\n    }\\n\\n    @Bean\\n    public JdbcTemplate storeProcJdbcTemplate(@Qualifier(\"storeProcDataSource\") DataSource ds) {\\n        return new JdbcTemplate(ds);\\n    }\\n}')),\n", " GraphDocument(nodes=[Node(id='Folder:Test/Java', type='Folder', properties={}), Node(id='File:Storeprocservice.Java', type='File', properties={}), Node(id='Class:Storeprocservice', type='Class', properties={}), Node(id='Function:Callstoreproc', type='Function', properties={})], relationships=[Relationship(source=Node(id='Folder:Test/Java', type='Folder', properties={}), target=Node(id='File:Storeprocservice.Java', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='File:Storeprocservice.Java', type='File', properties={}), target=Node(id='Class:Storeprocservice', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Class:Storeprocservice', type='Class', properties={}), target=Node(id='Function:Callstoreproc', type='Function', properties={}), type='DECLARES', properties={})], source=Document(metadata={'source': 'test\\\\java\\\\StoreProcService.java'}, page_content='File Path: test\\\\java\\\\StoreProcService.java\\nFolder: test\\\\java\\nFile: StoreProcService.java\\n\\n\\npackage com.morganstanley.loanApp.service;\\n\\nimport org.springframework.beans.factory.annotation.Autowired;\\nimport org.springframework.beans.factory.annotation.Qualifier;\\nimport org.springframework.jdbc.core.JdbcTemplate;\\nimport org.springframework.stereotype.Service;\\n\\nimport java.sql.CallableStatement;\\nimport java.sql.Connection;\\nimport java.sql.Types;\\n\\n@Service\\npublic class StoreProcService {\\n\\n\\n    @Autowired\\n    @Qualifier(\"storeProcJdbcTemplate\")\\n    private JdbcTemplate storeProcTemplate;\\n\\n\\n\\n\\n    public Integer callStoreProc(String branchName, String branchAddress) {\\n\\n        return storeProcTemplate.execute((Connection conn) -> {\\n            CallableStatement cs = conn.prepareCall(\"{call sp_insert_branch(?, ?, ?)}\");\\n            cs.setString(1, branchName);\\n            cs.setString(2, branchAddress);\\n            cs.registerOutParameter(3, Types.INTEGER);\\n            cs.execute();\\n            return cs.getInt(3);\\n        });\\n    }\\n}')),\n", " GraphDocument(nodes=[Node(id='Test/Java', type='Folder', properties={}), Node(id='User.Java', type='File', properties={}), Node(id='User', type='Class', properties={})], relationships=[Relationship(source=Node(id='Test/Java', type='Folder', properties={}), target=Node(id='User.Java', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='User.Java', type='File', properties={}), target=Node(id='User', type='Class', properties={}), type='DECLARES', properties={})], source=Document(metadata={'source': 'test\\\\java\\\\User.java'}, page_content='File Path: test\\\\java\\\\User.java\\nFolder: test\\\\java\\nFile: User.java\\n\\npackage com.morganstanley.loanApp.model;\\n\\nimport jakarta.persistence.*;\\nimport lombok.Data;\\n\\n@Entity\\n@Table(name = \"users\")\\n@Data\\npublic class User {\\n\\n    @Id\\n    @GeneratedValue(strategy = GenerationType.IDENTITY)\\n    private Long id;\\n\\n    private String name;\\n    private String email;\\n}')),\n", " GraphDocument(nodes=[Node(id='Test/Java', type='Folder', properties={}), Node(id='Usercontroller.Java', type='File', properties={}), Node(id='Com.Morganstanley.Loanapp.Controller.Usercontroller', type='Class', properties={})], relationships=[Relationship(source=Node(id='Test/Java', type='Folder', properties={}), target=Node(id='Usercontroller.Java', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='Usercontroller.Java', type='File', properties={}), target=Node(id='Com.Morganstanley.Loanapp.Controller.Usercontroller', type='Class', properties={}), type='DECLARES', properties={})], source=Document(metadata={'source': 'test\\\\java\\\\UserController.java'}, page_content='File Path: test\\\\java\\\\UserController.java\\nFolder: test\\\\java\\nFile: UserController.java\\n\\npackage com.morganstanley.loanApp.controller;\\n\\nimport com.morganstanley.loanApp.dto.UserLoanRequestDTO;\\nimport com.morganstanley.loanApp.model.User;\\n\\nimport com.morganstanley.loanApp.service.StoreProcService;\\nimport com.morganstanley.loanApp.service.UserService;\\nimport jakarta.validation.Valid;\\nimport org.springframework.beans.factory.annotation.Autowired;\\nimport org.springframework.http.HttpStatus;\\nimport org.springframework.http.ResponseEntity;\\nimport org.springframework.web.bind.annotation.PostMapping;\\nimport org.springframework.web.bind.annotation.RequestBody;\\nimport org.springframework.web.bind.annotation.RequestMapping;\\nimport org.springframework.web.bind.annotation.RestController;\\n\\nimport java.io.*;\\nimport java.util.ArrayList;\\nimport java.util.List;\\n\\n@RestController\\n@RequestMapping(\"/loanApp\")\\npublic class UserController {\\n    @Autowired\\n    private UserService userService;\\n\\n    @Autowired\\n    private StoreProcService storeProcService;\\n\\n    @PostMapping(\"/createLoan\")\\n    public ResponseEntity<User> createUserWithLoan(@Valid @RequestBody UserLoanRequestDTO dto) {\\n        User savedUser = userService.createUserWithLoan(dto);\\n        return new ResponseEntity<>(savedUser, HttpStatus.CREATED);\\n    }\\n\\n    @PostMapping(\"/save-from-file\")\\n    public ResponseEntity<String> saveUserLoanFromFile(){\\n        try (InputStream is = getClass().getClassLoader().getResourceAsStream(\"usersData.txt\");\\n             BufferedReader reader = new BufferedReader(new InputStreamReader(is))) {\\n\\n            if (is == null) {\\n                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)\\n                        .body(\"Data file not found\");\\n            }')),\n", " GraphDocument(nodes=[Node(id='File:Userloanrequestprocessor.Java', type='File', properties={})], relationships=[Relationship(source=Node(id='File:Userloanrequestprocessor.Java', type='File', properties={}), target=Node(id='Class:Userloanrequestprocessor', type='Class', properties={}), type='DECLARES', properties={})], source=Document(metadata={'source': 'test\\\\java\\\\UserController.java'}, page_content='if (is == null) {\\n                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)\\n                        .body(\"Data file not found\");\\n            }\\n\\n            String line ;\\n            while ((line = reader.readLine()) != null) {\\n                String[] parts = line.split(\",\");\\n                if (parts.length != 4) {\\n                    return ResponseEntity.badRequest()\\n                            .body(\"Invalid file format\");\\n                }\\n\\n\\n                UserLoanRequestDTO userDTO = new UserLoanRequestDTO();\\n                userDTO.setName(parts[0].trim());\\n                userDTO.setEmail(parts[1].trim());\\n                userDTO.setLoanType(parts[2].trim());\\n                userDTO.setAmount(Double.valueOf(parts[3].trim()));\\n\\n                userService.createUserWithLoan(userDTO);\\n            }\\n            return ResponseEntity.ok(\"Loan application saved from flat file\");\\n\\n        } catch (Exception e) {\\n            e.printStackTrace();\\n            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)\\n                    .body(\"Error processing flat file\");\\n        }\\n    }\\n\\n\\n    @PostMapping(\"/processStoreProcFromCSV\")\\n    public ResponseEntity<List<String>> readCSVAndCallStoreProc(){\\n        List<String> outputMessages = new ArrayList<>();\\n\\n        try (InputStream is = getClass().getClassLoader().getResourceAsStream(\"branchesData.csv\");\\n             BufferedReader reader = new BufferedReader(new InputStreamReader(is))) {\\n            String line;\\n            boolean isFirstLine = true;\\n\\n            while ((line = reader.readLine()) != null) {\\n                if (isFirstLine) {\\n                    isFirstLine = false; // skip header\\n                    continue;\\n                }\\n\\n                String[] parts = line.split(\",\");\\n                if (parts.length != 2) continue;\\n\\n                String branchName = parts[0].trim();\\n                String branchAddress = parts[1].trim();')),\n", " GraphDocument(nodes=[Node(id='Folder:Test', type='Folder', properties={}), Node(id='File:Controller.Java', type='File', properties={}), Node(id='Class:Storeproccontroller', type='Class', properties={}), Node(id='Function:Callstoreproc', type='Function', properties={}), Node(id='Function:Geturl', type='Function', properties={})], relationships=[Relationship(source=Node(id='Folder:Test', type='Folder', properties={}), target=Node(id='File:Controller.Java', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='File:Controller.Java', type='File', properties={}), target=Node(id='Class:Storeproccontroller', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Class:Storeproccontroller', type='Class', properties={}), target=Node(id='Function:Callstoreproc', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Function:Callstoreproc', type='Function', properties={}), target=Node(id='Function:Geturl', type='Function', properties={}), type='CALLS', properties={})], source=Document(metadata={'source': 'test\\\\java\\\\UserController.java'}, page_content='String branchName = parts[0].trim();\\n                String branchAddress = parts[1].trim();\\n\\n                Integer result = storeProcService.callStoreProc(branchName, branchAddress);\\n                outputMessages.add(\"Branch Id for \" + branchName + \" is: \" + result);\\n            }\\n\\n            return ResponseEntity.ok(outputMessages);\\n        } catch (IOException | NumberFormatException ex) {\\n            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)\\n                    .body(List.of(\"Error reading file or parsing data: \" + ex.getMessage()));\\n        }\\n    }\\n\\n\\n}')),\n", " GraphDocument(nodes=[Node(id='Folder:Test\\\\Java', type='Folder', properties={}), Node(id='File:Userloanrequestdto.Java', type='File', properties={}), Node(id='Class:Userloanrequestdto', type='Class', properties={})], relationships=[Relationship(source=Node(id='Folder:Test\\\\Java', type='Folder', properties={}), target=Node(id='File:Userloanrequestdto.Java', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='File:Userloanrequestdto.Java', type='File', properties={}), target=Node(id='Class:Userloanrequestdto', type='Class', properties={}), type='DECLARES', properties={})], source=Document(metadata={'source': 'test\\\\java\\\\UserLoanRequestDTO.java'}, page_content='File Path: test\\\\java\\\\UserLoanRequestDTO.java\\nFolder: test\\\\java\\nFile: UserLoanRequestDTO.java\\n\\npackage com.morganstanley.loanApp.dto;\\n\\nimport jakarta.validation.constraints.Email;\\nimport jakarta.validation.constraints.NotBlank;\\nimport jakarta.validation.constraints.NotNull;\\nimport jakarta.validation.constraints.Positive;\\nimport lombok.Data;\\nimport lombok.Getter;\\nimport lombok.Setter;\\n\\n\\n\\npublic class UserLoanRequestDTO {\\n\\n    public String getName() {\\n        return name;\\n    }\\n\\n    public void setName(String name) {\\n        this.name = name;\\n    }\\n\\n    public String getEmail() {\\n        return email;\\n    }\\n\\n    public void setEmail(String email) {\\n        this.email = email;\\n    }\\n\\n    public String getLoanType() {\\n        return loanType;\\n    }\\n\\n    public void setLoanType(String loanType) {\\n        this.loanType = loanType;\\n    }\\n\\n    public Double getAmount() {\\n        return amount;\\n    }\\n\\n    public void setAmount(Double amount) {\\n        this.amount = amount;\\n    }\\n\\n    @NotBlank(message = \"Name is required\")\\n    private String name;\\n\\n    @Email(message = \"Email should be valid\")\\n    @NotBlank(message = \"Email is required\")\\n    private String email;\\n\\n    @NotBlank(message = \"Loan type is required\")\\n    private String loanType;\\n\\n    @NotNull(message = \"Loan amount is required\")\\n    @Positive(message = \"Loan amount must be positive\")\\n    private Double amount;\\n}')),\n", " GraphDocument(nodes=[Node(id='Userrepository.Java', type='File', properties={})], relationships=[Relationship(source=Node(id='Userrepository.Java', type='File', properties={}), target=Node(id='Userrepository', type='Class', properties={}), type='DECLARES', properties={})], source=Document(metadata={'source': 'test\\\\java\\\\UserRepository.java'}, page_content='File Path: test\\\\java\\\\UserRepository.java\\nFolder: test\\\\java\\nFile: UserRepository.java\\n\\npackage com.morganstanley.loanApp.repository;\\n\\nimport com.morganstanley.loanApp.model.User;\\nimport org.springframework.data.jpa.repository.JpaRepository;\\n\\n\\npublic interface UserRepository extends JpaRepository<User, Long> {\\n}')),\n", " GraphDocument(nodes=[Node(id='Userservice.Java', type='File', properties={})], relationships=[Relationship(source=Node(id='Userservice.Java', type='File', properties={}), target=Node(id='Com.Morganstanley.Loanapp.Service.Userservice', type='Class', properties={}), type='DECLARES', properties={})], source=Document(metadata={'source': 'test\\\\java\\\\UserService.java'}, page_content='File Path: test\\\\java\\\\UserService.java\\nFolder: test\\\\java\\nFile: UserService.java\\n\\npackage com.morganstanley.loanApp.service;\\n\\nimport com.morganstanley.loanApp.dto.UserLoanRequestDTO;\\nimport com.morganstanley.loanApp.model.LoanDetails;\\nimport com.morganstanley.loanApp.model.User;\\nimport com.morganstanley.loanApp.repository.LoanDetailsRepository;\\nimport com.morganstanley.loanApp.repository.UserRepository;\\nimport org.springframework.beans.factory.annotation.Autowired;\\nimport org.springframework.stereotype.Service;\\n\\n@Service\\npublic class UserService {\\n    @Autowired\\n    private UserRepository userRepository;\\n\\n    @Autowired\\n    private LoanDetailsRepository loanDetailsRepository;\\n\\n    public User createUserWithLoan(UserLoanRequestDTO dto) {\\n        User user = new User();\\n        user.setName(dto.getName());\\n        user.setEmail(dto.getEmail());\\n        user = userRepository.save(user);\\n\\n        LoanDetails loan = new LoanDetails();\\n        loan.setLoanType(dto.getLoanType());\\n        loan.setAmount(dto.getAmount());\\n        loan.setUserId(user.getId());\\n        loanDetailsRepository.save(loan);\\n\\n        return user;\\n    }\\n}'))]"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["graph_documents4"]}, {"cell_type": "code", "execution_count": 22, "id": "2d51671e", "metadata": {}, "outputs": [], "source": ["import pickle\n", "\n", "with open(\"graph_documents4.pkl\", \"wb\") as f:\n", "    pickle.dump(graph_documents4, f)"]}, {"cell_type": "code", "execution_count": null, "id": "ada634a8", "metadata": {}, "outputs": [], "source": ["with open(\"graph_documents4.pkl\", \"rb\") as f:\n", "    graph_documents4 = pickle.load(f)"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}