/**
 * 
 */
/*
 * package com.bolt.dashboard.response;
 * 
 * import java.net.URI; import java.net.URISyntaxException;
 * 
 * import javax.ws.rs.client.Client; import javax.ws.rs.client.ClientBuilder;
 * import javax.ws.rs.client.Invocation; import javax.ws.rs.client.WebTarget;
 * import javax.ws.rs.core.MediaType; import javax.ws.rs.core.Response;
 * 
 * import org.glassfish.jersey.client.authentication.HttpAuthenticationFeature;
 * 
 *//**
   * <AUTHOR>
   *
   *//*
     * public class Authentication { public static Auth AUTHNTICATION ; public
     * static Response authenticationOfservice(String username,String password){
     * return Auth.authenticate(username, password, new Auth()); }
     * 
     * }
     * 
     * final class Auth{ Response response = null; boolean authenticationStatus=
     * false; public Auth(){
     * 
     * }
     * 
     * public static Response authenticate(String username,String password,Auth
     * auth){ HttpAuthenticationFeature feature =
     * HttpAuthenticationFeature.basic(username, password); final Client client
     * = ClientBuilder.newClient(); client.register(feature); try { WebTarget
     * webTarget = client.target(new
     * URI("http://localhost:8081/api/tfsbuild?proName=BOLT_Dashboard"));
     * Invocation.Builder invocationBuilder =
     * webTarget.request(MediaType.APPLICATION_JSON); auth.response =
     * invocationBuilder.get(); } catch (URISyntaxException e) { 
     * Auto-generated catch block e.printStackTrace(); }
     * 
     * return auth.response; } }
     */