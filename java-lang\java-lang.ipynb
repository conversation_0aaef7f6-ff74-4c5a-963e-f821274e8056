{"cells": [{"cell_type": "code", "execution_count": 26, "id": "72f9b1e7", "metadata": {}, "outputs": [], "source": ["# ===================== IMPORTS =====================\n", "import os\n", "import re\n", "import javalang\n", "from collections import defaultdict\n", "import json\n", "from neo4j import GraphDatabase\n", "from langchain_openai import AzureChatOpenAI\n", "from langchain.chains import GraphCypherQAChain\n", "from langchain_community.graphs import Neo4jGraph"]}, {"cell_type": "code", "execution_count": 45, "id": "baebfa84", "metadata": {}, "outputs": [], "source": ["import os\n", "import re\n", "import javalang\n", "import json\n", "from collections import defaultdict\n", "\n", "# ===================== HELPER FUNCTIONS =====================\n", "\n", "def normalize_node(node_type, full_name, file_path, class_name=None, method_name=None):\n", "    \"\"\"Create standardized node metadata with proper names for Neo4j.\"\"\"\n", "    short_name = os.path.basename(full_name) if node_type in (\"file\", \"folder\") else full_name.split(\".\")[-1]\n", "    if node_type == \"file\" and short_name.endswith(\".java\"):\n", "        short_name = short_name[:-5]  # remove .java\n", "\n", "    return {\n", "        \"id\": f\"{node_type}:{full_name}\",\n", "        \"type\": node_type,\n", "        \"name\": short_name,\n", "        \"full_name\": full_name,\n", "        \"file_path\": file_path,\n", "        \"class_name\": class_name,\n", "        \"method_name\": method_name\n", "    }\n", "\n", "def add_node(nodes, node_type, full_name, file_path, class_name=None, method_name=None):\n", "    node_id = f\"{node_type}:{full_name}\"\n", "    if node_id not in nodes:\n", "        nodes[node_id] = normalize_node(node_type, full_name, file_path, class_name, method_name)\n", "    return node_id\n", "\n", "def add_relation(relations, src, rel, dst):\n", "    if src and dst:\n", "        relations.add((src, rel, dst))\n", "\n", "def extract_db_table_usage(code):\n", "    tables = set()\n", "    for m in re.finditer(r\"\\bFROM\\s+(\\w+)|\\bINTO\\s+(\\w+)|\\bUPDATE\\s+(\\w+)\", code, flags=re.IGNORECASE):\n", "        table = next(g for g in m.groups() if g)\n", "        if table:\n", "            tables.add(table)\n", "    return tables\n", "\n", "def extract_api_calls(code):\n", "    apis = set()\n", "    for m in re.finditer(r'@(?:GetMapping|PostMapping|PutMapping|DeleteMapping)\\(\"([^\"]+)\"\\)', code):\n", "        apis.add(m.group(1))\n", "    for m in re.finditer(r'[\"\\'](https?://[^\\s\"\\']+)[\"\\']', code):\n", "        apis.add(m.group(1))\n", "    return apis\n", "\n", "# ===================== MAIN EXTRACTION FUNCTION =====================\n", "\n", "def extract_relations_v12(project_path):\n", "    nodes = {}\n", "    relations = set()\n", "    collection_entities = {}\n", "    class_uses_classes = defaultdict(set)\n", "\n", "    # ========== PASS 0: Folder-to-Folder and Folder-to-File ==========\n", "    java_files = []\n", "    for root, dirs, files in os.walk(project_path):\n", "        rel_root = os.path.relpath(root, project_path)\n", "        folder_name = rel_root if rel_root != \".\" else os.path.basename(project_path)\n", "        folder_node = add_node(nodes, \"folder\", folder_name, root)\n", "\n", "        # folder → subfolder\n", "        for d in dirs:\n", "            subfolder_rel = os.path.relpath(os.path.join(root, d), project_path)\n", "            subfolder_node = add_node(nodes, \"folder\", subfolder_rel, os.path.join(root, d))\n", "            add_relation(relations, folder_node, \"contains\", subfolder_node)\n", "\n", "        # folder → file\n", "        for file in files:\n", "            if file.endswith(\".java\"):\n", "                file_rel = os.path.relpath(os.path.join(root, file), project_path)\n", "                file_node = add_node(nodes, \"file\", file_rel, os.path.join(root, file))\n", "                add_relation(relations, folder_node, \"contains\", file_node)\n", "                java_files.append(os.path.join(root, file))\n", "\n", "    # ========== PASS 1: Parse all Java files ==========\n", "    parsed_files = {}\n", "    for file_path in java_files:\n", "        with open(file_path, \"r\", encoding=\"utf-8\") as f:\n", "            content = f.read()\n", "            try:\n", "                parsed_files[file_path] = (content, javalang.parse.parse(content))\n", "            except javalang.parser.JavaSyntaxError:\n", "                continue\n", "\n", "    # ========== PASS 2: Detect MongoDB @Document collections ==========\n", "    for file_path, (code, tree) in parsed_files.items():\n", "        package_name = tree.package.name if tree.package else None\n", "        for type_decl in tree.types:\n", "            if isinstance(type_decl, javalang.tree.ClassDeclaration):\n", "                full_class_name = f\"{package_name}.{type_decl.name}\" if package_name else type_decl.name\n", "                for annotation in type_decl.annotations:\n", "                    if annotation.name == \"Document\":\n", "                        for pair in annotation.element:\n", "                            if pair.name == \"collection\":\n", "                                collection = pair.value.value\n", "                                collection_entities[full_class_name] = collection\n", "                                add_node(nodes, \"class\", full_class_name, file_path)\n", "                                add_node(nodes, \"collection\", collection, file_path)\n", "                                add_relation(relations, f\"class:{full_class_name}\", \"mapped_to_collection\", f\"collection:{collection}\")\n", "\n", "    # ========== PASS 3: Class, Method, Variable & Flow Extraction ==========\n", "    for file_path, (code, tree) in parsed_files.items():\n", "        rel_path = os.path.relpath(file_path, project_path)\n", "        file_node = f\"file:{rel_path}\"\n", "\n", "        # File-level DB tables and APIs\n", "        for t in extract_db_table_usage(code):\n", "            table_node = add_node(nodes, \"table\", t, file_path)\n", "            add_relation(relations, file_node, \"uses_table\", table_node)\n", "\n", "        for api in extract_api_calls(code):\n", "            api_node = add_node(nodes, \"api\", api, file_path)\n", "            add_relation(relations, file_node, \"calls_api\", api_node)\n", "\n", "        # Imports for type resolution\n", "        import_map = {imp.path.split('.')[-1]: imp.path for imp in tree.imports if imp.path and not imp.wildcard}\n", "        package_name = tree.package.name if tree.package else None\n", "\n", "        for type_decl in tree.types:\n", "            if not isinstance(type_decl, (javalang.tree.ClassDeclaration, javalang.tree.InterfaceDeclaration)):\n", "                continue\n", "\n", "            decl_type = \"class\" if isinstance(type_decl, javalang.tree.ClassDeclaration) else \"interface\"\n", "            full_decl_name = f\"{package_name}.{type_decl.name}\" if package_name else type_decl.name\n", "            class_node = add_node(nodes, decl_type, full_decl_name, file_path)\n", "            add_relation(relations, file_node, \"declares\", class_node)\n", "\n", "            # Extends / Implements\n", "            if type_decl.extends:\n", "                base = type_decl.extends\n", "                base_name = base.name if hasattr(base, \"name\") else str(base)\n", "                parent_id = add_node(nodes, \"class\", import_map.get(base_name, base_name), file_path)\n", "                add_relation(relations, class_node, \"extends\", parent_id)\n", "\n", "            if getattr(type_decl, \"implements\", None):\n", "                for impl in type_decl.implements:\n", "                    parent_id = add_node(nodes, \"interface\", import_map.get(impl.name, impl.name), file_path)\n", "                    add_relation(relations, class_node, \"implements\", parent_id)\n", "\n", "            # Fields → variables\n", "            for field in getattr(type_decl, \"fields\", []):\n", "                for decl in field.declarators:\n", "                    var_full = f\"{full_decl_name}.{decl.name}\"\n", "                    var_node = add_node(nodes, \"variable\", var_full, file_path, class_name=full_decl_name)\n", "                    add_relation(relations, class_node, \"has_variable\", var_node)\n", "\n", "            # Methods → deep variable & flow\n", "            for method in getattr(type_decl, \"methods\", []):\n", "                method_full = f\"{full_decl_name}.{method.name}\"\n", "                method_node = add_node(nodes, \"method\", method_full, file_path, class_name=full_decl_name)\n", "                add_relation(relations, class_node, \"has_method\", method_node)\n", "\n", "                declared_var_types = {}\n", "\n", "                # Parameters\n", "                for param in method.parameters:\n", "                    param_full = f\"{method_full}.{param.name}\"\n", "                    param_node = add_node(nodes, \"variable\", param_full, file_path, class_name=full_decl_name, method_name=method.name)\n", "                    add_relation(relations, method_node, \"has_parameter\", param_node)\n", "                    declared_var_types[param.name] = param.type.name if hasattr(param.type, \"name\") else None\n", "\n", "                # Return variable\n", "                if method.return_type:\n", "                    return_full = f\"{method_full}.return\"\n", "                    return_node = add_node(nodes, \"variable\", return_full, file_path, class_name=full_decl_name, method_name=method.name)\n", "                    add_relation(relations, method_node, \"returns\", return_node)\n", "\n", "                if not method.body:\n", "                    continue\n", "\n", "                for path, node in method:\n", "                    # Local variable\n", "                    if isinstance(node, javalang.tree.LocalVariableDeclaration):\n", "                        for decl in node.declarators:\n", "                            var_name = decl.name\n", "                            var_full = f\"{full_decl_name}.{method.name}.{var_name}\"\n", "                            var_node = add_node(nodes, \"variable\", var_full, file_path, class_name=full_decl_name, method_name=method.name)\n", "                            add_relation(relations, method_node, \"uses\", var_node)\n", "                            if hasattr(node.type, \"name\"):\n", "                                declared_var_types[var_name] = node.type.name\n", "\n", "                    # Method call\n", "                    elif isinstance(node, javalang.tree.MethodInvocation):\n", "                        called_method = f\"method:{full_decl_name}.{node.member}\"\n", "                        if node.qualifier and node.qualifier in declared_var_types:\n", "                            imp_class = import_map.get(declared_var_types[node.qualifier], declared_var_types[node.qualifier])\n", "                            called_method = f\"method:{imp_class}.{node.member}\"\n", "                        elif node.qualifier:\n", "                            called_method = f\"method:{node.qualifier}.{node.member}\"\n", "\n", "                        add_relation(relations, method_node, \"calls\", called_method)\n", "\n", "                        for arg in node.arguments:\n", "                            if isinstance(arg, javalang.tree.MemberReference):\n", "                                var_node = f\"variable:{full_decl_name}.{method.name}.{arg.member}\"\n", "                                add_relation(relations, called_method, \"uses\", var_node)\n", "\n", "                    # Assignment → variable transformation\n", "                    elif isinstance(node, javalang.tree.Assignment):\n", "                        left = getattr(node, \"expressionl\", None) or getattr(node, \"left\", None)\n", "                        right = node.value\n", "                        if isinstance(left, javalang.tree.MemberReference):\n", "                            left_var = f\"variable:{full_decl_name}.{method.name}.{left.member}\"\n", "                            add_relation(relations, method_node, \"declares\", left_var)\n", "\n", "                            # flows_to\n", "                            if isinstance(right, javalang.tree.MemberReference):\n", "                                right_var = f\"variable:{full_decl_name}.{method.name}.{right.member}\"\n", "                                add_relation(relations, right_var, \"flows_to\", left_var)\n", "\n", "                            elif isinstance(right, javalang.tree.MethodInvocation):\n", "                                called_method = f\"method:{full_decl_name}.{right.member}\"\n", "                                add_relation(relations, called_method, \"assigns\", left_var)\n", "                                add_relation(relations, method_node, \"calls\", called_method)\n", "\n", "    return {\n", "        \"nodes\": list(nodes.values()),\n", "        \"relations\": [list(r) for r in relations]\n", "    }\n"]}, {"cell_type": "code", "execution_count": 46, "id": "9d58a9a8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Graph JSON saved: graph_v12.json, Nodes=5514, Relations=12332\n"]}], "source": ["# ===================== EXTRACT & SAVE =====================\n", "\n", "project_path = \"OneInsights\"  # <-- your project path\n", "json_path = \"graph_v12.json\"\n", "\n", "graph_data = extract_relations_v12(project_path)\n", "with open(json_path, \"w\") as f:\n", "    json.dump(graph_data, f, indent=2)\n", "print(f\"Graph JSON saved: {json_path}, Nodes={len(graph_data['nodes'])}, Relations={len(graph_data['relations'])}\")\n", "\n"]}, {"cell_type": "code", "execution_count": 47, "id": "836b4a92", "metadata": {}, "outputs": [], "source": ["from neo4j import GraphDatabase\n", "import json\n", "\n", "def load_graph_to_neo4j(json_path, uri, user, password, db=\"neo4j\", wipe_existing=True):\n", "    with open(json_path, \"r\", encoding=\"utf-8\") as f:\n", "        data = json.load(f)\n", "\n", "    nodes = data[\"nodes\"]\n", "    relations = data[\"relations\"]\n", "    driver = GraphDatabase.driver(uri, auth=(user, password))\n", "\n", "    def clear_db(tx):\n", "        tx.run(\"MATCH (n) DETACH DELETE n\")\n", "\n", "    def upload(tx):\n", "        for node in nodes:\n", "            label = node[\"type\"].capitalize()\n", "            tx.run(f\"\"\"\n", "                MERGE (n:{label} {{id: $id}})\n", "                SET n += $props\n", "            \"\"\", id=node[\"id\"], props=node)\n", "\n", "        for src_id, rel_type, dst_id in relations:\n", "            tx.run(f\"\"\"\n", "                MATCH (a {{id: $src}})\n", "                MATCH (b {{id: $dst}})\n", "                MERGE (a)-[r:`{rel_type}`]->(b)\n", "            \"\"\", src=src_id, dst=dst_id)\n", "\n", "    with driver.session(database=db) as session:\n", "        if wipe_existing:\n", "            session.execute_write(clear_db)\n", "        session.execute_write(upload)\n", "\n", "    driver.close()\n", "    print(f\"✅ Graph uploaded to Neo4j: {len(nodes)} nodes, {len(relations)} relationships\")\n"]}, {"cell_type": "code", "execution_count": 48, "id": "2e136acb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Graph uploaded to Neo4j: 5514 nodes, 12332 relationships\n"]}], "source": ["load_graph_to_neo4j(\"graph_v12.json\", NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD)\n"]}, {"cell_type": "code", "execution_count": null, "id": "ad175b26", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Graph imported to Neo4j successfully!\n"]}], "source": ["# Neo4j Configuration\n", "NEO4J_URI = \"bolt://localhost:7687\"\n", "NEO4J_USER = \"neo4j\"\n", "NEO4J_PASSWORD = \"Test@7889\"\n", "NEO4J_DB = \"final\"\n"]}, {"cell_type": "code", "execution_count": 32, "id": "b95c3389", "metadata": {}, "outputs": [], "source": ["\n", "\n", "# ===================== CONNECT LANGCHAIN =====================\n", "llm = AzureChatOpenAI(\n", "    api_key=\"********************************\",\n", "    azure_endpoint=\"https://azureopenaibrsc.openai.azure.com/\",\n", "    azure_deployment=\"gpt-4o\",\n", "    api_version=\"2024-12-01-preview\"\n", ")\n", "\n", "graph = Neo4jGraph(\n", "    url=NEO4J_URI,\n", "    username=NEO4J_USER,\n", "    password=NEO4J_PASSWORD,\n", "    database=NEO4J_DB\n", ")"]}, {"cell_type": "code", "execution_count": 33, "id": "adbbaf24", "metadata": {}, "outputs": [], "source": ["chain = GraphCypherQAChain.from_llm(llm=llm, graph=graph, verbose=True, allow_dangerous_requests=True)"]}, {"cell_type": "code", "execution_count": 34, "id": "1381d479", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new GraphCypherQAChain chain...\u001b[0m\n", "Generated Cypher:\n", "\u001b[32;1m\u001b[1;3mcypher\n", "MATCH (c:Class)\n", "WITH count(c) AS classCount\n", "MATCH (m:Method)\n", "RETURN classCount AS numberOfClasses, count(m) AS numberOfMethods\n", "\u001b[0m\n", "Full Context:\n", "\u001b[32;1m\u001b[1;3m[{'numberOfClasses': 73, 'numberOfMethods': 1128}]\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "{'query': 'how many classes and methods we have?', 'result': 'We have 73 classes and 1128 methods.'}\n"]}], "source": ["response = chain.invoke({\"query\": \"how many classes and methods we have?\"})\n", "print(response)\n"]}, {"cell_type": "code", "execution_count": 35, "id": "8b829f6e", "metadata": {}, "outputs": [], "source": ["from langchain.prompts import PromptTemplate\n", "\n", "cypher_prompt = PromptTemplate(\n", "    template=\"\"\"You are an expert Neo4j Cypher generator.\n", "\n", "Task:\n", "- Translate the user question into a single valid Cypher query.\n", "- Output ONLY the Cypher query, nothing else.\n", "- Do not include explanations or natural language.\n", "- Alias nodes with lowercase variables.\n", "- Use DISTINCT on variables, not labels.\n", "- Return results with meaningful aliases.\n", "\n", "Question: {question}\n", "Cypher:\"\"\",\n", "    input_variables=[\"question\"]\n", ")\n"]}, {"cell_type": "code", "execution_count": 36, "id": "d5c36f14", "metadata": {}, "outputs": [], "source": ["qa_chain = GraphCypherQAChain.from_llm(\n", "    llm=llm,\n", "    graph=graph,\n", "    verbose=True,\n", "    allow_dangerous_requests=True,\n", "    cypher_prompt=cypher_prompt,\n", "    return_intermediate_steps=True\n", ")\n"]}, {"cell_type": "code", "execution_count": 37, "id": "63b467e6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new GraphCypherQAChain chain...\u001b[0m\n", "Generated Cypher:\n", "\u001b[32;1m\u001b[1;3mcypher\n", "MATCH (c:Class)\n", "RETURN COUNT(DISTINCT c) AS totalClasses\n", "\u001b[0m\n", "Full Context:\n", "\u001b[32;1m\u001b[1;3m[{'totalClasses': 73}]\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "Generated Cypher:\n", " cypher\n", "MATCH (c:Class)\n", "RETURN COUNT(DISTINCT c) AS totalClasses\n", "\n", "We have 73 classes.\n", "\n", "\n", "\u001b[1m> Entering new GraphCypherQAChain chain...\u001b[0m\n", "Generated Cypher:\n", "\u001b[32;1m\u001b[1;3mcypher\n", "MATCH (c:Class)\n", "RETURN DISTINCT c.name AS className\n", "LIMIT 5\n", "\u001b[0m\n", "Full Context:\n", "\u001b[32;1m\u001b[1;3m[{'className': 'ALMConfiguration'}, {'className': 'ChangeHistoryModel'}, {'className': 'ConfigurationSetting'}, {'className': 'IterationModel'}, {'className': 'IterationOutModel'}]\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "Generated Cypher:\n", " cypher\n", "MATCH (c:Class)\n", "RETURN DISTINCT c.name AS className\n", "LIMIT 5\n", "\n", "ALMConfiguration, ChangeHistoryModel, ConfigurationSetting, IterationModel, IterationOutModel.\n"]}], "source": ["from langchain.memory import ConversationBufferMemory\n", "def ask_question(question):\n", "    inputs = {\n", "        \"query\": question,\n", "        \"chat_history\": memory.load_memory_variables({})[\"chat_history\"]\n", "    }\n", "    result = qa_chain.invoke(inputs)\n", "\n", "    # ✅ Save only string output to memory\n", "    memory.save_context(\n", "        {\"query\": question},\n", "        {\"result\": result[\"result\"]}  \n", "    )\n", "\n", "    cypher = result[\"intermediate_steps\"][0][\"query\"]\n", "    print(\"Generated Cypher:\\n\", cypher)\n", "    return result[\"result\"]\n", "\n", "print(ask_question(\"How many classes do we have?\"))\n", "print(ask_question(\"List first 5 class names\"))\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}