package com.bolt.dashboard.core.config;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperationContext;
import org.springframework.data.mongodb.core.aggregation.MatchOperation;
import org.springframework.data.mongodb.core.aggregation.SortOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.CriteriaDefinition;
import org.springframework.data.mongodb.core.query.Query;

import com.bolt.dashboard.circleci.CircleCIClientImplementation;
import com.bolt.dashboard.core.model.EffortHistoryModel;
import com.bolt.dashboard.core.model.IterationModel;
import com.bolt.dashboard.core.model.IterationOutModel;
import com.bolt.dashboard.core.model.MetricsModel;
import com.bolt.dashboard.core.model.MonogOutMetrics;
import com.bolt.dashboard.core.model.TransitionModel;
import com.bolt.dashboard.core.repository.EffortHistoryRepo;
import com.bolt.dashboard.core.repository.IterationRepo;
import com.bolt.dashboard.core.repository.MetricRepo;
import com.bolt.dashboard.core.repository.ProjectIterationRepo;
import com.bolt.dashboard.core.repository.TransitionRepo;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;

public class MongoAggregate {
	public MongoTemplate template = null;
	private static final Logger LOGGER = LogManager.getLogger(MongoAggregate.class);
	private MetricRepo metricRepo;
	private EffortHistoryRepo effortHistoryRepo;
	public MongoAggregate() {
		try {
			template = DataConfig.getInstance().mongoTemplate();
			metricRepo = DataConfig.getContext().getBean(MetricRepo.class);
			//effortHistoryRepo = DataConfig.getContext().getBean(EffortHistoryRepo.class);
		} catch (Exception e) {
			LOGGER.error(e.getMessage());
		}
	}

	public List<MonogOutMetrics> aggregateMetrics(String projName, String almType) {

		LOGGER.info("Aggregating Data");
		MetricRepo metricRepo = DataConfig.getContext().getBean(MetricRepo.class);
		TransitionRepo transitionRepo = DataConfig.getContext().getBean(TransitionRepo.class);
		EffortHistoryRepo effortHistoryRepo = DataConfig.getContext().getBean(EffortHistoryRepo.class);
		IterationRepo iterationRepo = DataConfig.getContext().getBean(IterationRepo.class);

		List<MetricsModel> metricList = metricRepo.findByPNameAndPAlmType(projName, almType);
		List<TransitionModel> transitionList = transitionRepo.findByPName(projName);
		Map<String, List<TransitionModel>> transitionGrouped = new HashMap<>();

		for (TransitionModel p : transitionList) {
			if (!transitionGrouped.containsKey(p.getwId())) {
				transitionGrouped.put(p.getwId(), new ArrayList<>());
			}
			transitionGrouped.get(p.getwId()).add(p);
		}

		List<EffortHistoryModel> effortHistoryList = effortHistoryRepo.findByPName(projName);
		Map<String,List<EffortHistoryModel>> effortsGrouped = new HashMap<>();
			for(EffortHistoryModel p : effortHistoryList){
				if(!effortsGrouped.containsKey(p.getwId())){
					effortsGrouped.put(p.getwId(), new ArrayList<>());                
				}
				effortsGrouped.get(p.getwId()).add(p);
			}

		Iterator<MetricsModel> metricitr = metricList.iterator();
		List<MonogOutMetrics> aggregatedMetricList = new ArrayList<MonogOutMetrics>();
		while (metricitr.hasNext()) {
			MetricsModel m = metricitr.next();
			MonogOutMetrics agr = new MonogOutMetrics();
			BeanUtils.copyProperties(m, agr);
			agr.setTransitions(transitionGrouped.get(m.getwId()));
			agr.setEfforts(effortsGrouped.get(m.getwId()));
			aggregatedMetricList.add(agr);

		}
		LOGGER.info("Aggregating result: " + aggregatedMetricList.size());
//		Collections.sort(aggregatedMetricList, new Comparator<MonogOutMetrics>() {
//			@Override
//			public int compare(MonogOutMetrics p1, MonogOutMetrics p2) {
//				if (p1.getAcFeatureId() == null && p1.getAcFeatureId() == null)
//					return 0;
//				else if (p1.getAcFeatureId() == null) {
//					return 1;
//				}
//
//				else if (p2.getAcFeatureId() == null) {
//					return -1;
//				}
//
//				return p1.getAcFeatureId().compareTo(p2.getAcFeatureId());
//
//			}
//		});
		return aggregatedMetricList;
	}


	public void aggregate(String projName, String almType) {
//		MatchOperation filterSName = Aggregation.match(new Criteria("pName").is(projName).and("pAlmType").is(almType));
//		AggregationOperation conEffort = getLookUp("EffortHistory", "wId", "wId", "efforts");
//		AggregationOperation conTrans = getLookUp("Transition", "wId", "wId", "transitions");
//		AggregationOperation out = getOutOperation("AggTempCollection");
//		Aggregation mtrAggr = Aggregation.newAggregation(filterSName, conEffort, conTrans, out);
//		template.aggregate(mtrAggr, "Metrics", MonogOutMetrics.class);
//
//		AggregationOperation conMtr = getLookUp("AggTempCollection", "sName", "sName", "metrics");
//		Aggregation ItrAggr = Aggregation.newAggregation(filterSName, conMtr);
//		List<IterationOutModel> toSaveData = template.aggregate(ItrAggr, "Iterations", IterationOutModel.class)
//				.getMappedResults();
//
//		Query query = new Query();
//		query.addCriteria(Criteria.where("pName").is(projName).and("pAlmType").is(almType));
//		if (template.count(query, "Author") > 0) {
//			template.remove(query, "Author");
//		}
//		// List<IterationOutModel> toSaveData=
//		// template.findAll(IterationOutModel.class, "AggTempCollection");
//		DataConfig.getContext().getBean(ProjectIterationRepo.class).save(toSaveData);
//
//		// removing temp collection
//		template.dropCollection("AggTempCollection");


		LOGGER.info("Aggregating Data");
		MetricRepo metricRepo = DataConfig.getContext().getBean(MetricRepo.class);
		TransitionRepo transitionRepo = DataConfig.getContext().getBean(TransitionRepo.class);
		EffortHistoryRepo effortHistoryRepo = DataConfig.getContext().getBean(EffortHistoryRepo.class);
		IterationRepo iterationRepo = DataConfig.getContext().getBean(IterationRepo.class);

		List<MetricsModel> metricList = metricRepo.findByPNameAndPAlmType(projName, almType);
		List<TransitionModel> transitionList = transitionRepo.findByPName(projName);
		Map<String, List<TransitionModel>> transitionGrouped = new HashMap<>();

		for (TransitionModel p : transitionList) {
			if (!transitionGrouped.containsKey(p.getwId())) {
				transitionGrouped.put(p.getwId(), new ArrayList<>());
			}
			transitionGrouped.get(p.getwId()).add(p);
		}

		List<EffortHistoryModel> effortHistoryList = effortHistoryRepo.findByPName(projName);
		Map<String,List<EffortHistoryModel>> effortsGrouped = new HashMap<>();
			for(EffortHistoryModel p : effortHistoryList){
				if(!effortsGrouped.containsKey(p.getwId())){
					effortsGrouped.put(p.getwId(), new ArrayList<>());                
				}
				effortsGrouped.get(p.getwId()).add(p);
			}

		Iterator<MetricsModel> metricitr = metricList.iterator();
		List<MonogOutMetrics> aggregatedMetricList = new ArrayList<MonogOutMetrics>();
		while (metricitr.hasNext()) {
			MetricsModel m = metricitr.next();
			MonogOutMetrics agr = new MonogOutMetrics();
			BeanUtils.copyProperties(m, agr);
			agr.setTransitions(transitionGrouped.get(m.getwId()));
			agr.setEfforts(effortsGrouped.get(m.getwId()));
			aggregatedMetricList.add(agr);

		}

		Map<String, List<MonogOutMetrics>> aggregatedMetricListGrouped = new HashMap<>();
		for (MonogOutMetrics p : aggregatedMetricList) {
			if (!aggregatedMetricListGrouped.containsKey(p.getsName())) {
				aggregatedMetricListGrouped.put(p.getsName(), new ArrayList<>());
			}
			aggregatedMetricListGrouped.get(p.getsName()).add(p);

		}

		List<IterationModel> iterationList = iterationRepo.findByPName(projName);
		Iterator<IterationModel> iterationirt = iterationList.iterator();

		List<IterationOutModel> aggreagation = new ArrayList<IterationOutModel>();
		while (iterationirt.hasNext()) {
			IterationModel iteraiotn = iterationirt.next();
			IterationOutModel agr = new IterationOutModel();
			BeanUtils.copyProperties(iteraiotn, agr);
			agr.setMetrics(aggregatedMetricListGrouped.get(iteraiotn.getsName()));
			aggreagation.add(agr);
		}

		Iterator<IterationOutModel> aggregationitr = aggreagation.iterator();

		Query query = new Query();
		query.addCriteria(Criteria.where("pName").is(projName).and("pAlmType").is(almType));
		if (template.count(query, "Author") > 0) {
			template.remove(query, "Author");
		}
		while (aggregationitr.hasNext()) {
			DataConfig.getContext().getBean(ProjectIterationRepo.class).save(aggregationitr.next());
		}
	}

	public AggregationOperation getLookUp(String to, String lId, String fId, String fld) {
		DBObject opr = (DBObject) new BasicDBObject("$lookup",
				new BasicDBObject("from", to).append("localField", lId).append("foreignField", fId).append("as", fld));
		return new AggregationOperation() {
			@Override
			public DBObject toDBObject(AggregationOperationContext arg0) {
				return opr;
			}
		};
	}

	public AggregationOperation getOutOperation(String collectionName) {
		DBObject opr = (DBObject) new BasicDBObject("$out", collectionName);
		return new AggregationOperation() {
			@Override
			public DBObject toDBObject(AggregationOperationContext arg0) {
				return opr;
			}
		};
	}

	public List<MonogOutMetrics> getMetric(String projName, String wId) {
		MatchOperation filterSName = null;
		if (wId == null) {
			filterSName = Aggregation.match(new Criteria("pName").is(projName));
		} else {
			filterSName = Aggregation.match(new Criteria("wId").is(wId));
		}
		AggregationOperation conEffort = getLookUp("EffortHistory", "wId", "wId", "efforts");
		LOGGER.info("EffortHistory");
		AggregationOperation conTrans = getLookUp("Transition", "wId", "wId", "transitions");
		LOGGER.info("Transition");
		Aggregation mtrAggr = Aggregation.newAggregation(filterSName, conEffort, conTrans);
		LOGGER.info("mtrAggr");

		List<MonogOutMetrics> mon = template.aggregate(mtrAggr, "Metrics", MonogOutMetrics.class).getMappedResults();
		LOGGER.info("mtrAggr" + mon.size());
		return mon;
	}

	public List<IterationOutModel> getCurrentItr(String projName, String almType, List<String> states) {
		MatchOperation filterSName = Aggregation
				.match(new Criteria("pName").is(projName).and("state").in(states).and("pAlmType").is(almType));
		SortOperation sort = Aggregation.sort(Sort.Direction.DESC, "endDate");
		Aggregation mtrAggr = Aggregation.newAggregation(filterSName, sort);
		return template.aggregate(mtrAggr, "Author", IterationOutModel.class).getMappedResults();
	}

	public void deleteIssues(List<String> widList, String projName) {

		Query query = new Query();
		query.addCriteria(Criteria.where("wId").nin(widList).and("pName").is(projName));
		template.remove(query, "Metrics");
		template.remove(query, "Transition");
		template.remove(query, "EffortHistory");
		template.remove(query, "ChangeItems");
		LOGGER.info("Issues Deleted");

	}

	public long getTotalSprintCount(String pName, String almType) {
		Query query = new Query();
		query.addCriteria(
				Criteria.where("pAlmType").is(almType).and("pName").is(pName).and("state").in("ACTIVE", "CLOSED","active","Active","Closed"));
		return template.count(query, "Author");
	}

	public long getBuildsCount(String projectName) {

		Query query = new Query();
		query.addCriteria(Criteria.where("name").is(projectName));
		return template.count(query, "Build");
	}

	public void updateComponentForTaskAndSubtasks(String pName) {
		LOGGER.info("Updateing components for taks and subtaks");

		List<MetricsModel> allStories = metricRepo.findByPNameAndType(pName, "Story");
		for (MetricsModel story : allStories) {
			if (story.getComponents() != null && story.getComponents().size() > 0
					&& (story.getSubtaskList() != null || story.getTaskList() != null)) {
				String componentName = story.getComponents().get(0);
				List<String> subtaskList = story.getSubtaskList();
				List<String> taskList = story.getTaskList();
				if (subtaskList != null) {
					for (String subtask : subtaskList) {
						MetricsModel issue = metricRepo.findByPNameAndWId(pName, subtask);
						if (issue != null) {
							if (issue.getComponents() != null) {
								issue.getComponents().clear();
							} else {
								issue.setComponents(new ArrayList<String>());
							}

							issue.getComponents().add(componentName);
							metricRepo.save(issue);
						}
					}
				}

				if (taskList != null) {
					for (String task : taskList) {
						MetricsModel issue = metricRepo.findByPNameAndWId(pName, task);

						if (issue != null) {
							metricRepo.delete(issue);
							if (issue.getComponents() != null) {
								issue.getComponents().clear();
							} else {
								issue.setComponents(new ArrayList<String>());
							}

							issue.getComponents().add(componentName);

							metricRepo.save(issue);
						}
					}
				}

			}
		}
		// comment below line while commiting jar
		// aggregate(pName,"JIRA");

	}

}
