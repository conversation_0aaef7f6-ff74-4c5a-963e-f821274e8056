package com.morganstanley.loanApp.controller;

import com.morganstanley.loanApp.dto.UserLoanRequestDTO;
import com.morganstanley.loanApp.model.User;

import com.morganstanley.loanApp.service.StoreProcService;
import com.morganstanley.loanApp.service.UserService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/loanApp")
public class UserController {
    @Autowired
    private UserService userService;

    @Autowired
    private StoreProcService storeProcService;

    @PostMapping("/createLoan")
    public ResponseEntity<User> createUserWithLoan(@Valid @RequestBody UserLoanRequestDTO dto) {
        User savedUser = userService.createUserWithLoan(dto);
        return new ResponseEntity<>(savedUser, HttpStatus.CREATED);
    }

    @PostMapping("/save-from-file")
    public ResponseEntity<String> saveUserLoanFromFile(){
        try (InputStream is = getClass().getClassLoader().getResourceAsStream("usersData.txt");
             BufferedReader reader = new BufferedReader(new InputStreamReader(is))) {

            if (is == null) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body("Data file not found");
            }

            String line ;
            while ((line = reader.readLine()) != null) {
                String[] parts = line.split(",");
                if (parts.length != 4) {
                    return ResponseEntity.badRequest()
                            .body("Invalid file format");
                }


                UserLoanRequestDTO userDTO = new UserLoanRequestDTO();
                userDTO.setName(parts[0].trim());
                userDTO.setEmail(parts[1].trim());
                userDTO.setLoanType(parts[2].trim());
                userDTO.setAmount(Double.valueOf(parts[3].trim()));

                userService.createUserWithLoan(userDTO);
            }
            return ResponseEntity.ok("Loan application saved from flat file");

        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error processing flat file");
        }
    }


    @PostMapping("/processStoreProcFromCSV")
    public ResponseEntity<List<String>> readCSVAndCallStoreProc(){
        List<String> outputMessages = new ArrayList<>();

        try (InputStream is = getClass().getClassLoader().getResourceAsStream("branchesData.csv");
             BufferedReader reader = new BufferedReader(new InputStreamReader(is))) {
            String line;
            boolean isFirstLine = true;

            while ((line = reader.readLine()) != null) {
                if (isFirstLine) {
                    isFirstLine = false; // skip header
                    continue;
                }

                String[] parts = line.split(",");
                if (parts.length != 2) continue;

                String branchName = parts[0].trim();
                String branchAddress = parts[1].trim();

                Integer result = storeProcService.callStoreProc(branchName, branchAddress);
                outputMessages.add("Branch Id for " + branchName + " is: " + result);
            }

            return ResponseEntity.ok(outputMessages);
        } catch (IOException | NumberFormatException ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(List.of("Error reading file or parsing data: " + ex.getMessage()));
        }
    }


}

