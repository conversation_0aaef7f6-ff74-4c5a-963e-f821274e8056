package com.bolt.dashboard.util;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class EncryptionDecryptionAES {

	private static final Logger LOGGER = LogManager.getLogger(EncryptionDecryptionAES.class);

	private static final Charset UTF_8 = StandardCharsets.UTF_8;

	private EncryptionDecryptionAES() {
	}

	public static String encrypt(String data, String secret) {
		try {
			byte[] pText = new byte[20];
			if (data != null)
				pText = data.getBytes(UTF_8);

			if (pText.length > 0)
				return EncryptorAesGcmPassword.encrypt(pText, secret);

		} catch (Exception e) {

			LOGGER.info("Problem In encryption");
			LOGGER.info(e.getMessage());
		}
		return "";
	}

	public static String decrypt(String encryptedData, String secretKey) {
		try {
			if (encryptedData != null && !encryptedData.trim().isEmpty())
				return EncryptorAesGcmPassword.decrypt(encryptedData, secretKey);

		} catch (Exception e) {
			LOGGER.info("Problem In Decryption");
			LOGGER.error(e.getMessage());
		}
		return "";
	}

}