
package com.morganstanley.loanApp.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.Types;

@Service
public class StoreProcService {


    @Autowired
    @Qualifier("storeProcJdbcTemplate")
    private JdbcTemplate storeProcTemplate;




    public Integer callStoreProc(String branchName, String branchAddress) {

        return storeProcTemplate.execute((Connection conn) -> {
            CallableStatement cs = conn.prepareCall("{call sp_insert_branch(?, ?, ?)}");
            cs.setString(1, branchName);
            cs.setString(2, branchAddress);
            cs.registerOutParameter(3, Types.INTEGER);
            cs.execute();
            return cs.getInt(3);
        });
    }
}

