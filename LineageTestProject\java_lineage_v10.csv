source_node,source_type,destination_node,destination_type,relationship
LineageTestProject,Folder,AppOne,Folder,CONTAINS
AppOne,Folder,UserService,File,CONTAINS
AppOne,Folder,UserUtils,File,CONTAINS
LineageTestProject,Folder,AppTwo,Folder,CONTAINS
AppTwo,Folder,OrderService,File,CONTAINS
AppTwo,Folder,OrderUtils,File,CONTAINS
UserService,File,UserService,Class,DECLARES
UserUtils,File,UserUtils,Class,DECLARES
OrderService,File,OrderService,Class,DECLARES
OrderUtils,File,OrderUtils,Class,DECLARES
UserService,Class,Get /Get/{Id},Endpoint,DECLARES
UserService,Class,Post /Create,Endpoint,DECLARES
UserService,Class,Get /User,Endpoint,DECLARES
UserService,Class,Userrepository,Variable,HAS_FIELD
UserService,Class,createUser,Method,DECLARES
createUser,Method,User,Variable,USES
UserService,Class,getUser,Method,DECLARES
createUser,Method,User,Table,WRITES_TO
getUser,Method,User,Table,READS_FROM
UserUtils,Class,convertDtoToEntity,Method,DECLARES
convertDtoToEntity,Method,User,Variable,USES
UserUtils,Class,generateUserCode,Method,DECLARES
generateUserCode,Method,Base,Variable,USES
generateUserCode,Method,Code,Variable,USES
OrderService,Class,Post /Create,Endpoint,DECLARES
OrderService,Class,Get /Order,Endpoint,DECLARES
OrderService,Class,Orderrepository,Variable,HAS_FIELD
OrderService,Class,createOrder,Method,DECLARES
createOrder,Method,Order,Variable,USES
createOrder,Method,User,Variable,USES
createOrder,Method,Usercode,Variable,USES
OrderUtils,Class,convertDtoToEntity,Method,DECLARES
convertDtoToEntity,Method,Order,Variable,USES
OrderUtils,Class,calculateTotal,Method,DECLARES
calculateTotal,Method,Total,Variable,USES
calculateTotal,Method,Taxedtotal,Variable,USES
Userdto,Variable,User,Variable,TRANSFORMS_TO
User,Variable,Return (Createuser),Variable,FLOWS_TO
Id,Variable,Return (Getuser),Variable,FLOWS_TO
UserService,Class,Post:/User/Create,Endpoint,EXPOSES
UserService,Class,Get:/User/Get/{Id},Endpoint,EXPOSES
createUser,Method,Post:/User/Create,Endpoint,MAPS_TO
getUser,Method,Get:/User/Get/{Id},Endpoint,MAPS_TO
Post:/User/Create,Endpoint,Userdto,Variable,ACCEPTS
Post:/User/Create,Endpoint,User,Variable,RETURNS
Get:/User/Get/{Id},Endpoint,Id,Variable,ACCEPTS
Get:/User/Get/{Id},Endpoint,User,Variable,RETURNS
createUser,Method,convertDtoToEntity,Method,CALLS
createUser,Method,Save,Method,CALLS
getUser,Method,Findbyid,Method,CALLS
Name,Variable,Base,Variable,FLOWS_TO
Email,Variable,Base,Variable,FLOWS_TO
Base,Variable,Code,Variable,TRANSFORMS_TO
Code,Variable,Output,Variable,PRODUCES
convertDtoToEntity,Method,User,Variable,PRODUCES
generateUserCode,Method,Code,Variable,PRODUCES
Orderdto,Variable,Order,Variable,TRANSFORMS_TO
Pending,Variable,Status,Variable,FLOWS_TO
User,Variable,Usercode,Variable,FLOWS_TO
Orderrepository,Method,Order,Table,WRITES_TO
OrderService,Class,Post:/Order/Create,Endpoint,EXPOSES
createOrder,Method,Post:/Order/Create,Endpoint,MAPS_TO
Post:/Order/Create,Endpoint,Orderdto,Variable,ACCEPTS
Post:/Order/Create,Endpoint,Order,Variable,RETURNS
createOrder,Method,convertDtoToEntity,Method,CALLS
createOrder,Method,Setstatus,Method,CALLS
createOrder,Method,getUser,Method,CALLS
createOrder,Method,generateUserCode,Method,CALLS
createOrder,Method,Setusercode,Method,CALLS
createOrder,Method,Orderrepository,Method,CALLS
generateUserCode,Method,Externalservice,Externalservice,INVOKES
Price,Variable,Total,Variable,FLOWS_TO
Quantity,Variable,Total,Variable,FLOWS_TO
Total,Variable,Taxedtotal,Variable,TRANSFORMS_TO
Taxedtotal,Variable,Output,Method,PRODUCES
