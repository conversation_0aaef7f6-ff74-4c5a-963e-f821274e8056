{"cells": [{"cell_type": "code", "execution_count": 3, "id": "89043924", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello! I'm just a virtual assistant, so I don't have feelings, but thank you for asking. 😊 How can I help you today?\n"]}], "source": ["from langchain_openai import AzureChatOpenAI\n", "\n", "llm = AzureChatOpenAI(\n", "    api_key=\"********************************\",\n", "    azure_endpoint=\"https://azureopenaibrsc.openai.azure.com/\",\n", "    azure_deployment=\"gpt-4o\",\n", "    api_version=\"2024-12-01-preview\"\n", ")\n", "\n", "response = llm.invoke(\"Hello, how are you?\")\n", "print(response.content)\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}