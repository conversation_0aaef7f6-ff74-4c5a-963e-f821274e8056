package com.morganstanley.loanApp.configuration;

import jakarta.annotation.PostConstruct;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "spring.datasource")
public class PrimaryDataSourceProperties {
    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getDriverClassName() {
        return driverClassName;
    }

    public void setDriverClassName(String driverClassName) {
        this.driverClassName = driverClassName;
    }

    private String url;
    private String username;
    private String password;
    private String driverClassName;
    @PostConstruct
    public void printProps() {
        System.out.println("Primary DS Props:");
        System.out.println("URL: " + url);
        System.out.println("Username: " + username);
        System.out.println("Driver: " + driverClassName);
    }


}
