{"cells": [{"cell_type": "code", "execution_count": 1, "id": "c8a83223", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Shaik\\sample\\venv\\Lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["from pathlib import Path\n", "from langchain_community.document_loaders import TextLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter, Language\n", "from langchain_google_genai import ChatGoogleGenerativeAI\n", "from langchain_experimental.graph_transformers import LLMGraphTransformer\n", "from langchain_community.graphs import Neo4jGraph"]}, {"cell_type": "code", "execution_count": 2, "id": "dcd74230", "metadata": {}, "outputs": [], "source": ["NEO4J_URI=\"neo4j://127.0.0.1:7687\"\n", "NEO4J_USERNAME=\"neo4j\"\n", "NEO4J_PASSWORD=\"Test@7889\"\n", "NEO4J_DATABASE=\"test\""]}, {"cell_type": "code", "execution_count": 3, "id": "62ba0f8b", "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ[\"NEO4J_URI\"]=NEO4J_URI\n", "os.environ[\"NEO4J_USERNAME\"]=NEO4J_USERNAME\n", "os.environ[\"NEO4J_PASSWORD\"]=NEO4J_PASSWORD\n", "os.environ[\"NEO4J_DATABASE\"]=NEO4J_DATABASE"]}, {"cell_type": "code", "execution_count": 4, "id": "71243943", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25308\\3861085308.py:1: LangChainDeprecationWarning: The class `Neo4jGraph` was deprecated in LangChain 0.3.8 and will be removed in 1.0. An updated version of the class exists in the :class:`~langchain-neo4j package and should be used instead. To use it run `pip install -U :class:`~langchain-neo4j` and import as `from :class:`~langchain_neo4j import Neo4jGraph``.\n", "  graph=Neo4jGraph(\n"]}], "source": ["graph=Neo4jGraph(\n", "    url=NEO4J_URI,\n", "    username=NEO4J_USERNAME,\n", "    password=NEO4J_PASSWORD,\n", "    database=NEO4J_DATABASE\n", ")"]}, {"cell_type": "code", "execution_count": 23, "id": "e6f2a294", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Folder ➔ File hierarchy (lowercase) ingested into Neo4j.\n"]}], "source": ["# 1️⃣ Folder ➔ File hierarchy ingestion (normalized lowercase)\n", "base_path = Path(\"C:/Shaik/sample/java\")\n", "created_folders = set()\n", "\n", "for file_path in base_path.rglob(\"*.java\"):\n", "    relative_path = file_path.relative_to(base_path)\n", "    parts = relative_path.parts\n", "    parent_folder = base_path.name.lower()\n", "    parent_folder_path = \"\"\n", "\n", "    if parent_folder not in created_folders:\n", "        graph.query(\"MERGE (f:Folder {name: $name})\", {\"name\": parent_folder})\n", "        created_folders.add(parent_folder)\n", "\n", "    for part in parts[:-1]:\n", "        part_lower = part.lower()\n", "        parent_folder_path = str(Path(parent_folder_path) / part_lower) if parent_folder_path else part_lower\n", "        if parent_folder_path not in created_folders:\n", "            graph.query(\"MERGE (f:Folder {name: $name})\", {\"name\": parent_folder_path})\n", "            created_folders.add(parent_folder_path)\n", "        graph.query(\"\"\"\n", "            MATCH (p:Folder {name: $parent_name}), (c:Folder {name: $child_name})\n", "            MERGE (p)-[:CONTAINS]->(c)\n", "        \"\"\", {\"parent_name\": parent_folder, \"child_name\": parent_folder_path})\n", "        parent_folder = parent_folder_path\n", "\n", "    file_name = parts[-1].lower()\n", "    graph.query(\"MERGE (f:File {name: $name})\", {\"name\": file_name})\n", "    graph.query(\"\"\"\n", "        MATCH (p:Folder {name: $parent_name}), (f:File {name: $file_name})\n", "        MERGE (p)-[:CONTAINS]->(f)\n", "    \"\"\", {\"parent_name\": parent_folder, \"file_name\": file_name})\n", "\n", "print(\"✅ Folder ➔ File hierarchy (lowercase) ingested into Neo4j.\")"]}, {"cell_type": "code", "execution_count": 6, "id": "90b6ed9f", "metadata": {}, "outputs": [], "source": ["# 2️⃣ Load Java files for LLM\n", "java_docs = []\n", "for path in base_path.rglob(\"*.java\"):\n", "    loader = TextLoader(str(path))\n", "    loaded_docs = loader.load()\n", "    for doc in loaded_docs:\n", "        doc.metadata[\"language\"] = \"java\"\n", "        doc.metadata[\"source\"] = str(path)\n", "        doc.metadata[\"source_file_name\"] = path.name.lower()\n", "    java_docs.extend(loaded_docs)"]}, {"cell_type": "code", "execution_count": 7, "id": "322d05dc", "metadata": {}, "outputs": [], "source": ["# 3️⃣ Chunk Java code (language-aware)\n", "splitter_java = RecursiveCharacterTextSplitter.from_language(\n", "    language=Language.JAVA,\n", "    chunk_size=2000,\n", "    chunk_overlap=200\n", ")"]}, {"cell_type": "code", "execution_count": 8, "id": "b2285499", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Prepared 14 Java chunks with lowercased file metadata.\n"]}], "source": ["split_docs = []\n", "for doc in java_docs:\n", "    chunks = splitter_java.split_documents([doc])\n", "    for idx, chunk in enumerate(chunks):\n", "        chunk.metadata[\"chunk_index\"] = idx\n", "        chunk.metadata[\"language\"] = \"java\"\n", "        chunk.metadata[\"source\"] = doc.metadata[\"source\"]\n", "        chunk.metadata[\"source_file_name\"] = doc.metadata[\"source_file_name\"]\n", "        split_docs.append(chunk)\n", "\n", "print(f\"✅ Prepared {len(split_docs)} Java chunks with lowercased file metadata.\")"]}, {"cell_type": "code", "execution_count": 9, "id": "5ba97dcd", "metadata": {}, "outputs": [], "source": ["google_api_key = \"AIzaSyDY2qPx7-wQk90KjsCnnFgc88Qzb1lV5b0\""]}, {"cell_type": "code", "execution_count": 10, "id": "36166326", "metadata": {}, "outputs": [], "source": ["from langchain_google_genai import ChatGoogleGenerativeAI\n", "llm = ChatGoogleGenerativeAI(\n", "    model=\"gemini-2.5-pro\",\n", "    temperature=0,\n", "    google_api_key=google_api_key)"]}, {"cell_type": "code", "execution_count": 11, "id": "53b43efc", "metadata": {}, "outputs": [], "source": ["# 5️⃣ System Prompt\n", "system_prompt = \"\"\"\n", "You are a **code and data lineage analysis engine**. From the provided Java code chunk, extract **structured graph triples** in the format:\n", "\n", "[SourceNodeLabel]:SourceNodeName -[RELATION]-> [TargetNodeLabel]:TargetNodeName\n", "\n", "## Context:\n", "- Use the explicit Folder and File info provided in metadata.\n", "- Ignore package statements when building Folder nodes.\n", "- If database table names, column names, or API endpoints are detected, include them as nodes for lineage.\n", "- If functions or classes are reading from or writing to database tables or making API calls, capture them in the lineage relationships.\n", "\n", "## Layers to capture:\n", "- File -> Class\n", "- Class -> Function/Method\n", "- Function/Method -> Function/Method (CALLS)\n", "- Class -> Class (INHERITS)\n", "- Function/Method -> Table (READS_FROM / WRITES_TO)\n", "- Function/Method -> API (CALLS_API)\n", "\n", "## Node Types:\n", "- File\n", "- Class\n", "- Function\n", "- Table\n", "- Column\n", "- API\n", "\n", "## Relationship Types:\n", "- DECLARES\n", "- CALLS\n", "- INHERITS\n", "- READS_FROM\n", "- WRITES_TO\n", "- CALLS_API\n", "\n", "## Instructions:\n", "- Use lowercase consistently for all node names/ids to match pre-existing graph nodes.\n", "- Return **only these triples, nothing else**.\n", "- If unsure, prefer to omit rather than hallucinate.\n", "- If code chunk does not involve data sources, return structural triples only.\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 12, "id": "6c418c99", "metadata": {}, "outputs": [], "source": ["# 6️⃣ LLMGraphTransformer\n", "transformer = LLMGraphTransformer(\n", "    llm=llm,\n", "    additional_instructions=system_prompt,\n", "    allowed_nodes=[\"File\", \"Class\", \"Function\", \"Table\", \"Column\", \"API\"],\n", "    allowed_relationships=[\n", "        (\"File\", \"DECLARES\", \"Class\"),\n", "        (\"Class\", \"DECLARES\", \"Function\"),\n", "        (\"Function\", \"CALLS\", \"Function\"),\n", "        (\"Class\", \"INHERITS\", \"Class\"),\n", "        (\"Function\", \"READS_FROM\", \"Table\"),\n", "        (\"Function\", \"WRITES_TO\", \"Table\"),\n", "        (\"Function\", \"CALLS_API\", \"API\"),\n", "    ],\n", "    strict_mode=True,\n", "    node_properties=False,\n", "    relationship_properties=False,\n", ")"]}, {"cell_type": "code", "execution_count": 13, "id": "7cc442e7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Extracted 14 graph documents using Gemini.\n"]}], "source": ["# 7️⃣ Run LLM graph extraction\n", "graph_documents = transformer.convert_to_graph_documents(split_docs)\n", "print(f\"✅ Extracted {len(graph_documents)} graph documents using Gemini.\")"]}, {"cell_type": "code", "execution_count": 24, "id": "2126904d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Merging LLM relationships into Neo4j: 100%|██████████| 14/14 [00:01<00:00,  7.33it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ LLM relationships merged into existing Folder ➔ File graph cleanly without duplicating File nodes.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["from tqdm import tqdm\n", "\n", "for gd in tqdm(graph_documents, desc=\"Merging LLM relationships into Neo4j\"):\n", "    # 1️⃣ Create/merge nodes\n", "    for node in gd.nodes:\n", "        node_name = node.id.strip().lower()\n", "        node_type = node.type\n", "\n", "        # Files already exist in Stage 1, do not recreate\n", "        if node_type == \"File\":\n", "            continue\n", "\n", "        # Create or match other nodes\n", "        graph.query(\n", "            f\"\"\"\n", "            MERGE (n:{node_type} {{name: $name}})\n", "            \"\"\",\n", "            {\"name\": node_name}\n", "        )\n", "\n", "    # 2️⃣ Create/merge relationships\n", "    for rel in gd.relationships:\n", "        source_name = rel.source.id.strip().lower()\n", "        target_name = rel.target.id.strip().lower()\n", "        rel_type = rel.type\n", "\n", "        source_type = rel.source.type\n", "        target_type = rel.target.type\n", "\n", "        # Build and run relationship merge query\n", "        query = f\"\"\"\n", "        MATCH (s:{source_type} {{name: $source_name}})\n", "        MATCH (t:{target_type} {{name: $target_name}})\n", "        MERGE (s)-[r:{rel_type}]->(t)\n", "        \"\"\"\n", "        graph.query(\n", "            query,\n", "            {\"source_name\": source_name, \"target_name\": target_name}\n", "        )\n", "\n", "print(\"✅ LLM relationships merged into existing Folder ➔ File graph cleanly without duplicating File nodes.\")\n"]}, {"cell_type": "code", "execution_count": 25, "id": "d7bebff5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ All nodes and relationships deleted from Neo4j.\n"]}], "source": ["graph.query(\"MATCH (n) DETACH DELETE n\")\n", "print(\"✅ All nodes and relationships deleted from Neo4j.\")"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}