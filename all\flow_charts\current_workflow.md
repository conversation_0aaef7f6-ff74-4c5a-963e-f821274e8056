
```mermaid
flowchart TD
    A["Java Source Code"] --> B["Stage 1: Structural Analysis<br/>os.walk + Python"]
    B --> C["Extract Folder Hierarchy"]

    A --> D["Stage 2: AST Parsing<br/>Tree-sitter + Regex"]
    D --> E["Extract AST: df_ast + Metadata"]

    C --> F["Stage 3: LLM Enhancement"]
    E --> F

    F --> G["Language Based Chunk Splitting"]
    G --> H{"For Each Chunk"}
    H --> I["Build Enhanced Prompt<br/>AST as Context"]
    I --> J["Extract Relationships"]
    J --> K{"More Chunks?"}
    K -- Yes --> H
    K -- No --> L["Combine Results<br/>df_llm_lineage"]

    L --> M["Stage 4: Data Processing<br/>Deduplication + Cleaning"]
    
    M --> Q["Stage 5: Graph Insertion<br/>Neo4j + Cypher"]
    Q --> R(["Interactive Knowledge View"])

    %% Styling
    classDef normalProcess fill:#e0f7fa,stroke:#006064,stroke-width:2px,color:#004d40
    classDef llmHighlight fill:#ffe0b2,stroke:#ef6c00,stroke-width:2px,color:#4e342e
    classDef chunkProcess fill:#f8bbd0,stroke:#c2185b,stroke-width:2px,color:#4a148c
    classDef startEnd fill:#d1c4e9,stroke:#512da8,stroke-width:3px,color:#1a237e
    classDef outputBox fill:#c8e6c9,stroke:#2e7d32,stroke-width:2px,color:#1b5e20

    class A,B,D,F,M,O,Q normalProcess
    class C,E,L,N,P outputBox
    class G,H,I,J,K chunkProcess
    class R startEnd

```

