package com.bolt.dashboard.service;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.group;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.unwind;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;
import java.util.Set;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.stream.Collectors;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.config.MongoAggregate;
import com.bolt.dashboard.core.model.ALMConfiguration;
import com.bolt.dashboard.core.model.ChangeHistoryModel;
import com.bolt.dashboard.core.model.ComponentBurnDown;
import com.bolt.dashboard.core.model.ComponentGroomingTable;
import com.bolt.dashboard.core.model.ComponentIssueBreakup;
import com.bolt.dashboard.core.model.ComponentSprintWiseStories;
import com.bolt.dashboard.core.model.ComponentStoryAgeing;
import com.bolt.dashboard.core.model.ComponentStoryProgress;
import com.bolt.dashboard.core.model.ComponentTaskRisk;
import com.bolt.dashboard.core.model.ComponentVelocityList;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.model.DefectInsightData;
import com.bolt.dashboard.core.model.EffortHistoryModel;
import com.bolt.dashboard.core.model.IssueList;
import com.bolt.dashboard.core.model.IterationModel;
import com.bolt.dashboard.core.model.IterationOutModel;
import com.bolt.dashboard.core.model.MetricsModel;
import com.bolt.dashboard.core.model.MonogOutMetrics;
import com.bolt.dashboard.core.model.ProjectModel;
import com.bolt.dashboard.core.model.ReleaseDetails;
import com.bolt.dashboard.core.model.ScoreCardSprintData;
import com.bolt.dashboard.core.model.TransitionModel;
import com.bolt.dashboard.core.model.VelocityList;
import com.bolt.dashboard.core.repository.ALMConfigRepo;
import com.bolt.dashboard.core.repository.ChangeHisortyRepo;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.core.repository.EffortHistoryRepo;
import com.bolt.dashboard.core.repository.IterationRepo;
import com.bolt.dashboard.core.repository.MetricRepo;
import com.bolt.dashboard.core.repository.ProjectIterationRepo;
import com.bolt.dashboard.core.repository.ProjectRepo;
import com.bolt.dashboard.core.repository.ReleaseRepo;
import com.bolt.dashboard.core.repository.TransitionRepo;
import com.bolt.dashboard.jira.ChartCalculations;
import com.bolt.dashboard.jira.JIRAApplication;
import com.bolt.dashboard.jira.JiraExceptions;
import com.bolt.dashboard.util.BacklogCalculation;
import com.bolt.dashboard.util.DefectBacklog;
import com.bolt.dashboard.util.DefectCalculations;
import com.bolt.dashboard.util.DefectDensity;
import com.bolt.dashboard.util.DefectParetoModel;
import com.bolt.dashboard.util.DefectProductionSlippage;
import com.bolt.dashboard.util.ProjectHomeCalculation;
import com.bolt.dashboard.util.SprintProgress;
import com.bolt.dashboard.util.SprintProgressCalculations;
import com.bolt.dashboard.util.VelocityCalculations;
import com.google.common.collect.Lists;

@Service
public class ALMServiceImplementation implements AlmService {

	private TransitionRepo transitionRepo;
	private EffortHistoryRepo effortHistoryRepo;
	private ChangeHisortyRepo changeHisortyRepo;
	private ProjectIterationRepo authorRepo;
	private ProjectRepo projectRepo;
	private ReleaseRepo releaseRepo;
	private IterationRepo iterationRepo;
	private MetricRepo metricRepo;
	private ALMConfigRepo almConfigRepo;
	private MongoAggregate agg = null;
	MongoTemplate mongoTemplate = null;
	
//	@Autowired
//    GuavaCacheManager cacheManager;
	
	List<IterationOutModel> workingBacklog = new ArrayList<IterationOutModel>();
	List<IterationOutModel> workingSprints = new ArrayList<IterationOutModel>();
	List<String> velocityFields;
	List<String> closestates;
	List<String> taskNames;
	ALMConfiguration almConfig;
	VelocityList vlist;
	ArrayList<IssueList> issueList;
	List<String> wIdArr;
	double tempSpRefined = 0;
	double tempSpRemoved = 0;
	double finalStoriesCommited = 0;
	double storiesCompleted = 0;
	double defetcsCompleted = 0;
	ArrayList<IssueList> refinedIssuList;
	ArrayList<IssueList> removedIssuList;
	private static final Logger LOGGER = LogManager.getLogger(ALMServiceImplementation.class);

	private ChartCalculations chartService;

	@Autowired
	public ALMServiceImplementation(TransitionRepo transitionRepo, ProjectIterationRepo authorRepo,
			EffortHistoryRepo effortHistoryRepo, ChangeHisortyRepo changeHisortyRepo, ProjectRepo projectRepo,
			ReleaseRepo repo, IterationRepo iterationRepo, MetricRepo metricRepo, ALMConfigRepo almConfig) {
		this.transitionRepo = transitionRepo;
		this.effortHistoryRepo = effortHistoryRepo;
		this.changeHisortyRepo = changeHisortyRepo;
		this.authorRepo = authorRepo;
		this.projectRepo = projectRepo;
		this.releaseRepo = repo;
		this.agg = new MongoAggregate();
		this.iterationRepo = iterationRepo;
		this.metricRepo = metricRepo;
		this.agg = new MongoAggregate();
		this.almConfigRepo = almConfig;
		chartService = new ChartCalculations();

		try {
			mongoTemplate = DataConfig.getInstance().mongoTemplate();
		} catch (Exception e) {
			LOGGER.info(e);
		}

	}
	
//	public void evictAllCaches(String cacheName) {
//		cacheManager.getCache(cacheName).clear();
//	}
	
	@Override
@Cacheable(value="ALMService", key ="'getMetricDetails'+#wId", cacheManager="timeoutCacheManager")
	public MonogOutMetrics getMetricDetails(String wId) {
		MonogOutMetrics result = agg.getMetric(null, wId).get(0);
		return result;
	}

	@Override
@Cacheable(value="ALMService", key ="'getMetricDetails'+#pName", cacheManager="timeoutCacheManager")
	public List<MonogOutMetrics> getAllMetrics(String pName) {
		List<MonogOutMetrics> result = agg.getMetric(pName, null);
		return result;
	}

	@Override
@Cacheable(value="ALMService", key ="'getChangesItems'+#wId", cacheManager="timeoutCacheManager")
	public List<ChangeHistoryModel> getChangesItems(String wId) {
		List<ChangeHistoryModel> result = Lists.newArrayList(changeHisortyRepo.findByWId(wId));
		return result;
	}

	@Override
@Cacheable(value="ALMService", key ="'getTransitionsData'+#wId", cacheManager="timeoutCacheManager")
	public List<TransitionModel> getTransitionsData(String wId) {
		return Lists.newArrayList(transitionRepo.findByWId(wId));
		
	}

	@Override
@Cacheable(value="ALMService", key ="'getIterationData'+#pName+#itrName+#almType", cacheManager="timeoutCacheManager")
	public IterationOutModel getIterationData(String pName, String itrName, String almType) {
		return authorRepo.findByPNameAndSNameAndPAlmType(pName, itrName, almType);
	
	}

	@Override
@Cacheable(value="ALMService", key ="'getEffortData'+#wId", cacheManager="timeoutCacheManager")
	public List<EffortHistoryModel> getEffortData(String wId) {
		return effortHistoryRepo.findByWId(wId);
		
	}

	@Override
@Cacheable(value="ALMService", key ="'getProjectDetials'+#pName+#almType", cacheManager="timeoutCacheManager")
	public List<IterationOutModel> getProjectDetails(String pName, String almType) {
		String almTy = getAlmType(pName);
		return populateAuthor(pName, almTy);

	}

	@Override
@Cacheable(value="ALMService", key ="'getFeatureMetrics'+#projectName+#almType", cacheManager="timeoutCacheManager")
	public List<MonogOutMetrics> getFeatureMetrics(String pName, String almType) {
		return agg.aggregateMetrics(pName, almType);
		 
	}

	public List<IterationOutModel> populateAuthor(String pName, String almType) {
		List<IterationOutModel> dataModel = authorRepo.findByPNameAndPAlmType(pName, almType);
		if (dataModel == null || dataModel.isEmpty()) {
			agg.aggregate(pName, almType);
			dataModel = authorRepo.findByPNameAndPAlmType(pName, almType);
		}
		return dataModel;
	}

	@Override
@Cacheable(value="ALMService", key ="'getCurrentIter'+#pName+#almType", cacheManager="timeoutCacheManager")
	public List<IterationOutModel> getCrtItr(String pName, String almType) {
		
		List<IterationOutModel> dataList = agg.getCurrentItr(pName, almType, ConstantVariable.activeStates);
		if (dataList.isEmpty()) {
			dataList = new ArrayList<>();
		
			List<IterationOutModel> data = agg.getCurrentItr(pName, almType, ConstantVariable.closedStates);
			if (!data.isEmpty()) {
				dataList.add(data.get(0));
			}
		}
		return dataList;
	}

	@Override
@Cacheable(value="ALMService", key ="'getDefectCount'+#pName+#almType", cacheManager="timeoutCacheManager")
	public ProjectModel getDefectCounts(String pName, String almType) {
		return projectRepo.findByProjectNameAndAlmType(pName, almType);
		

	}

	@Override
@Cacheable(value="ALMService", key ="'getRelease'+#projectName", cacheManager="timeoutCacheManager")
	public ReleaseDetails getRelease(String projectName) {
		List<ReleaseDetails> releases = releaseRepo.findByProjectName(projectName);
		return releases.get(0);
	}

	@Override
@Cacheable(value="ALMService", key ="'getUnReleaseData'+#projectName+#almType", cacheManager="timeoutCacheManager")
	public List<MetricsModel> getUnReleaseData(String projectName, String almType) {

		Query query = new Query();

		query.addCriteria(Criteria.where("targetRelease").exists(true)
				.andOperator(Criteria.where(ConstantVariable.pNameConst).is(projectName), Criteria.where(ConstantVariable.pAlmTypeConst).is(almType)));

		List<MetricsModel> response = new ArrayList<>();
		response = mongoTemplate.find(query, MetricsModel.class, ConstantVariable.mixedCaseMetricsConst);

		return response;
	}

	@Override
@Cacheable(value="ALMService", key ="'getDefects'+#projectName+#almType+#defect", cacheManager="timeoutCacheManager")
	public List<MetricsModel> getDefects(String projectName, String almType, String defect) {
		Query query = new Query();
		query.addCriteria(Criteria.where(ConstantVariable.pNameConst).is(projectName).and(ConstantVariable.pAlmTypeConst).is(almType).and("type").is(defect));
		return mongoTemplate.find(query, MetricsModel.class, ConstantVariable.mixedCaseMetricsConst);
		
	}

	@Override
@Cacheable(value="ALMService", key ="'getProdDefects'+#projectName+#almType+#defect+#whereFound+#whenFound", cacheManager="timeoutCacheManager")
	public List<MetricsModel> getProdDefects(String projectName, String almType, String defect, String whereFound,
			String whenFound) {
		Query query = new Query();
		query.addCriteria(Criteria.where(ConstantVariable.pNameConst).is(projectName).and(ConstantVariable
				.pAlmTypeConst).is(almType).and("type").is(defect)
				.orOperator(Criteria.where("whereFound").is(whereFound), Criteria.where("whenFound").is(whenFound)));
	
		return mongoTemplate.find(query, MetricsModel.class, ConstantVariable.mixedCaseMetricsConst);
		
	}

	@Override
@Cacheable(value="ALMService", key ="'getDateIterations'+#projectName+#almType+#startDate+#endDate", cacheManager="timeoutCacheManager")
	public List<IterationOutModel> getDateIterations(String projectName, String almType, long startDate, long endDate) {
		Query query = new Query();
		query.addCriteria(Criteria.where(ConstantVariable.pNameConst).is(projectName).and(ConstantVariable.pAlmTypeConst)
				.is(almType).and("stDate")
				.gte(startDate).and("endDate").lte(endDate));
		List<IterationOutModel> response = new ArrayList<>();
		response = mongoTemplate.find(query, IterationOutModel.class, "Iterations");
		return response;
	}

	@Override
@Cacheable(value="ALMService", key ="'getSlaData'+#projectName+#almType+#createDate", cacheManager="timeoutCacheManager")
	public List<IterationOutModel> getSlaData(String projectName, String almType, long createDate) {
		List<IterationOutModel> data = null;
		Aggregation aggregation = newAggregation(match(Criteria.where(ConstantVariable.pNameConst).is(projectName)), unwind(ConstantVariable.lowerCaseMetricsConst),
				match(Criteria.where("metrics.createDate").gt(createDate)),
				group("_id").addToSet("$metrics").as(ConstantVariable.lowerCaseMetricsConst));

		try {
			data = DataConfig.getInstance().mongoTemplate().aggregate(aggregation, "Author", IterationOutModel.class)
					.getMappedResults();
		} catch (Exception e) {
			
			LOGGER.error(e.getCause());
		}

		/*
		 * MatchOperation filterSName = Aggregation.match(new
		 * Criteria("pName").is(projectName)); UnwindOperation unwinnd =
		 * Aggregation.unwind("metrices"); MatchOperation metricSearch =
		 * Aggregation.match(new Criteria("metrices.createDate").gt(createDate));
		 * GroupOperation group = Aggregation.group("metrices"); String
		 * GroupOperationBuilder = null; GroupOperation groupAppend =
		 * group.addToSet("$metrics"); Aggregation mtrAggr =
		 * Aggregation.newAggregation(filterSName, unwinnd, metricSearch, groupAppend);
		 * try { data = DataConfig.getInstance().mongoTemplate().aggregate(mtrAggr,
		 * "Author", MonogOutMetrics.class) .getMappedResults(); } catch (Exception e) {
		 *  e.printStackTrace(); }
		 */

		/*
		 * Query query = new Query();
		 * query.addCriteria(Criteria.where("pName").is(projectName).and(
		 * "pAlmType").is(almType).and("createDate").gt(createDate)); List<MetricsModel>
		 * response = new ArrayList<>(); response = mongoTemplate.find(query,
		 * MetricsModel.class, "Metrics");
		 */
		return data;
	}

	// Verizon : Filter based on assigned members
	@Override
@Cacheable(value="ALMService", key ="'getAssigneeIssues'+#projectName+#almType+#members", cacheManager="timeoutCacheManager")
	public List<IterationOutModel> getAssigneeIssues(String projectName, String almType, String[] members) {
		List<IterationOutModel> data = null;
		Aggregation aggregation = newAggregation(match(Criteria.where(ConstantVariable.pNameConst).is(projectName)), unwind(ConstantVariable.lowerCaseMetricsConst),
				match(Criteria.where("metrics.assgnTo").in(members)),
				group("_id").first("$sName").as("sName").first("$sId").as("sId").first("$stDate").as("stDate")
						.first("$endDate").as("endDate").first("$completedDate").as("completedDate").first("$velocity")
						.as("velocity").first("$teamSize").as("teamSize").first("$techDebt").as("techDebt")
						.first("$totalStoryPoints").as("totalStoryPoints").first("$totOpenDefetct").as("totOpenDefetct")
						.first("$totDefects").as("totDefects").first("$totClosedDefects").as("totClosedDefects")
						.first("$state").as("state").first("$pName").as(ConstantVariable.pNameConst).first("$pAlmType").as(ConstantVariable.pAlmTypeConst)
						.addToSet("$metrics").as(ConstantVariable.lowerCaseMetricsConst));

		try {
			data = DataConfig.getInstance().mongoTemplate().aggregate(aggregation, "Author", IterationOutModel.class)
					.getMappedResults();
		} catch (Exception e) {
			LOGGER.error(e.getMessage());
		}
		return data;
	}

	@Override
//	@CacheEvict(value="ALMService", allEntries=true)
	public String delDuplicate(String projName) {
		
		try {
			return (new JIRAApplication().deleteJiraIssues(projName));
		} catch (JiraExceptions e) {
			
			LOGGER.error(e.getMessage());
			return "Failed";
		}

	}

	@Override
@Cacheable(value="ALMService", key ="'getCurrentProjectDetials'+#pName+#almType", cacheManager="timeoutCacheManager")
	public Map<Integer, List<IterationOutModel>> getCurrentProjectDetails(String pName, String almType) {
		
		MongoAggregate mongoAggr = new MongoAggregate();
		int totalCount = (int) mongoAggr.getTotalSprintCount(pName, almType);
		Map<Integer, List<IterationOutModel>> outMap = new HashMap<>();
		List<IterationOutModel> currentAuthor = authorRepo.findByPNameAndPAlmTypeAndState(pName, almType, ConstantVariable.upperCaseActiveConst);
		if (currentAuthor.isEmpty()) {
			IterationOutModel temp = authorRepo.findOneByPNameAndPAlmTypeOrderByEndDateDesc(pName, almType);
			currentAuthor.add(temp);
		}
		outMap.put(totalCount, currentAuthor);

		return outMap;
	}

	@Override
//	@CacheEvict(value="ALMService", allEntries=true)
	public String delAllIssues(String projName) {
		
		// List<TransitionModel> transitions=transitionRepo.findByPName(projName);
		// if(!transitions.isEmpty())
		transitionRepo.delete(transitionRepo.findByPName(projName));

		effortHistoryRepo.delete(effortHistoryRepo.findByPName(projName));
		changeHisortyRepo.delete(changeHisortyRepo.findByPName(projName));
		authorRepo.delete(authorRepo.findByPName(projName));

		iterationRepo.delete(iterationRepo.findByPName(projName));

		metricRepo.delete(metricRepo.findByPName(projName));
		releaseRepo.delete(releaseRepo.findByProjectName(projName));
		projectRepo.delete(projectRepo.findByProjectName(projName));
		LOGGER.info("Collections deleted for Project " + projName);
		return "Collections deleted for Project " + projName;

	}

	@Override
@Cacheable(value="ALMService", key ="'getAllTransitions'+#pName", cacheManager="timeoutCacheManager")
	public List<TransitionModel> getAllTransitions(String pName) {
		
		List<TransitionModel> result = transitionRepo.findByPName(pName);
		return result;
	}

	@Override
	public List<ComponentVelocityList> getComponentVelocity(String projectName, boolean flag) {
		List<ComponentVelocityList> response = new ArrayList<ComponentVelocityList>();
		List<IterationOutModel> authorList = authorRepo.findByPName(projectName);
		Collections.sort(authorList, new SprintComparatort());

		List<ScoreCardSprintData> v = getVelocityChart(authorList, projectName);
		ComponentVelocityList c = new ComponentVelocityList();
		c.setComponent("All");
		c.setVelocityList(v);
		response.add(c);
		if (flag) {
			ArrayList<String> componentList = getComponentList(authorList);

			for (String component : componentList) {
				List<IterationOutModel> tempAuthorList = new ArrayList<IterationOutModel>();
				for (IterationOutModel auth : authorList) {

					IterationOutModel author = new IterationOutModel();
					BeanUtils.copyProperties(auth, author);

					List<MonogOutMetrics> metrics = new ArrayList<MonogOutMetrics>();
					if (auth.getMetrics() != null)
						for (MonogOutMetrics m : auth.getMetrics()) {
							if (m.getComponents() != null && m.getComponents().get(0) != null
									&& m.getComponents().get(0).equals(component)) {

								metrics.add(m);

							}
						}
					if (metrics.size() > 0) {
						author.setMetrics(metrics);
						tempAuthorList.add(author);
					}

				}
				Collections.sort(tempAuthorList, new SprintComparatort());
				v = getVelocityChart(tempAuthorList, projectName);
				c = new ComponentVelocityList();
				c.setComponent(component);
				c.setVelocityList(v);
				response.add(c);

			}
		}
		return response;
	}

	@Override
	public List<ComponentSprintWiseStories> getComponentsSprint(String projectName, boolean flag) {
		List<ComponentSprintWiseStories> response = new ArrayList<ComponentSprintWiseStories>();

		List<IterationModel> sprints = iterationRepo.findByPName(projectName);
		List<MetricsModel> metricList = metricRepo.findByPNameAndType(projectName, "Story");
		List<TransitionModel> transitionList = (List<TransitionModel>) transitionRepo.findAll();
		List<IterationOutModel> authorList = authorRepo.findByPName(projectName);
		Collections.sort(authorList, new SprintComparatort());

		List<IterationOutModel> itr = getSprintWiseStories(projectName, metricList, transitionList, authorList);
		ComponentSprintWiseStories c = new ComponentSprintWiseStories();
		c.setComponent("All");
		c.setIterationOutModel(itr);
		response.add(c);
		if (flag) {
			ArrayList<String> componentList = getComponentList(authorList);

			for (String component : componentList) {
				List<IterationOutModel> tempAuthorList = new ArrayList<IterationOutModel>();
				List<MetricsModel> allMetricsOfComponet = new ArrayList<MetricsModel>();
				for (IterationOutModel auth : authorList) {

					IterationOutModel author = new IterationOutModel();
					BeanUtils.copyProperties(auth, author);

					List<MonogOutMetrics> metrics = new ArrayList<MonogOutMetrics>();
					if (auth.getMetrics() != null)
						for (MonogOutMetrics m : auth.getMetrics()) {
							if (m.getComponents() != null && m.getComponents().get(0) != null
									&& m.getComponents().get(0).equals(component)) {

								metrics.add(m);
								MetricsModel temp = new MetricsModel();
								BeanUtils.copyProperties(m, temp);
								allMetricsOfComponet.add(temp);

							}
						}
					if (metrics.size() > 0) {
						author.setMetrics(metrics);
						tempAuthorList.add(author);
					}

				}
				Collections.sort(tempAuthorList, new SprintComparatort());
				// if(component.equals("NP-01 Service Transparency-Consumer")) {
				// System.out.println("sdsdsd");
				// }
				itr = getSprintWiseStories(projectName, allMetricsOfComponet, transitionList, tempAuthorList);
				c = new ComponentSprintWiseStories();
				c.setComponent(component);
				c.setIterationOutModel(itr);
				response.add(c);

			}
		}
		return response;
	}

	@Override
	public Map<String, List> getIssueHierarchy(String projectName) {
		

		List<MetricsModel> allMetrics = metricRepo.findByPName(projectName);
		List<IterationOutModel> authorList = authorRepo.findByPName(projectName);
		ArrayList<String> componentList = getComponentList(authorList);

		Map<String, List> resp = new IssuesHierarchy().getHierarchyData(allMetrics);
		return resp;
	}

	public ArrayList<String> getComponentList(List<IterationOutModel> authorList) {

		ArrayList<String> componentList = new ArrayList<String>();
		for (IterationOutModel auth : authorList) {

			if (auth.getMetrics() != null) {
				for (MonogOutMetrics m : auth.getMetrics()) {
					if (m.getComponents() != null && m.getComponents().get(0) != null) {
						String component = m.getComponents().get(0);
						if (componentList.indexOf(component) < 0) {
							componentList.add(component);
						}
					}
				}
			}
		}
		return componentList;
	}

	@Override
	public Map<String, Map> getComponentWiseIssueHierarchy(String projectName) {

		List<IterationOutModel> authorList = authorRepo.findByPName(projectName);
		List<MetricsModel> metrics = metricRepo.findByPName(projectName);
		ArrayList<String> componentList = getComponentList(authorList);

		Map<String, Map> response = new HashMap<String, Map>();

		Map<String, List> resp = new IssuesHierarchy().getHierarchyData(metrics);
		response.put("All", resp);

		componentList.forEach(component -> {
			List<MetricsModel> filteredMetrics = metrics.stream().filter(m -> {
				if (m != null && m.getComponents() != null && m.getComponents().size() > 0
						&& m.getComponents().get(0).equalsIgnoreCase(component)) {
					return true;
				} else {
					return false;
				}
			}).collect(Collectors.toList());
			Map<String, List> resp1 = new IssuesHierarchy().getHierarchyData(filteredMetrics);
			response.put(component, resp1);

		});

		return response;
	}

	@Override
	public List<String> getComponents(String projectName, boolean b) {
		List<IterationOutModel> authorList = authorRepo.findByPName(projectName);
		List<String> copmonents = getComponentList(authorList);
		Collections.sort(copmonents);
		return copmonents;
	}

	public List<IterationOutModel> getSprintWiseStories(String projectName, List<MetricsModel> metricList,
			List<TransitionModel> transitionList, List<IterationOutModel> sprints) {

		almConfig = almConfigRepo.findByProjectName(projectName).get(0);
		List<String> closestates = Arrays.asList(almConfig.getCloseState());

		Map<String, List<TransitionModel>> transitionGrouped = new HashMap<>();

		for (TransitionModel p : transitionList) {
			if (!transitionGrouped.containsKey(p.getwId())) {
				transitionGrouped.put(p.getwId(), new ArrayList<>());
			}
			transitionGrouped.get(p.getwId()).add(p);
		}

		Iterator<MetricsModel> metricitr = metricList.iterator();
		List<MonogOutMetrics> aggregatedMetricList = new ArrayList<MonogOutMetrics>();
		while (metricitr.hasNext()) {
			MetricsModel m = metricitr.next();
			MonogOutMetrics agr = new MonogOutMetrics();
			BeanUtils.copyProperties(m, agr);
			agr.setTransitions(transitionGrouped.get(m.getwId()));
			aggregatedMetricList.add(agr);

		}

		List<IterationOutModel> sprintwise = new ArrayList<IterationOutModel>();

		ListIterator<IterationOutModel> spIterator = sprints.listIterator();
		ArrayList<String> wIdArr = new ArrayList<String>();
		while (spIterator.hasNext()) {

			int tempStory = 0;
			IterationOutModel sprint = spIterator.next();
			IterationOutModel sprintAndstories = new IterationOutModel();
			List<MonogOutMetrics> spStories = new ArrayList<MonogOutMetrics>();
			if (null != sprint.getStDate() && sprint.getStDate() != 0 && sprint.getsId() != 0) {

				sprintAndstories.setsName(sprint.getsName());
				sprintAndstories.setsId(sprint.getsId());
				sprintAndstories.setStDate(sprint.getStDate());

				if (0 != sprint.getCompletedDate()) {
					sprintAndstories.setEndDate(sprint.getCompletedDate());
				} else {
					sprintAndstories.setEndDate(sprint.getEndDate());
				}

				// if (sprint.getsName().equalsIgnoreCase("NP PI-2.1")) {
				// System.out.println("dsfsdfsd");
				// }
				Iterator<MonogOutMetrics> stIterator = aggregatedMetricList.iterator();
				while (stIterator.hasNext()) {
					MonogOutMetrics story = stIterator.next();

					long endDate;
					if (sprint.getState().toLowerCase().contains(ConstantVariable.lowerCaseActiveConst)) {
						endDate = new Date().getTime();
					} else {

						endDate = sprint.getEndDate();

					}
					// if (story.getwId().equalsIgnoreCase("NTSBW-875")) {
					// System.out.println(story.getwId());
					// }
					if (null != story.getAllocatedDate()
							&& story.getType().toLowerCase().equals(almConfig.getStoryName().toLowerCase())) {
						ArrayList<Long> dates = new ArrayList<Long>(story.getAllocatedDate().keySet());
						ArrayList<String> values = new ArrayList<String>(story.getAllocatedDate().values());
						for (int m = dates.size() - 1; m >= 0; m--) {
							if (values.get(m).contains(sprint.getsId() + "")) {
								if (m == dates.size() - 1 && dates.get(m) < sprint.getStDate()) {
									MonogOutMetrics s = new MonogOutMetrics();
									BeanUtils.copyProperties(story, s);
									spStories.add(s);
								} else if (dates.get(m) < sprint.getStDate() && dates.get(m + 1) > sprint.getStDate()
										&& dates.get(m + 1) > endDate) {
									MonogOutMetrics s = new MonogOutMetrics();
									spStories.add(s);
								}
							}
						}

						long completedDate;
						if (sprint.getState().toLowerCase().contains(ConstantVariable.lowerCaseActiveConst)) {
							completedDate = new Date().getTime();
						} else {
							if (sprint.getCompletedDate() != 0) {
								completedDate = sprint.getCompletedDate();
							} else {
								completedDate = sprint.getEndDate();
							}

						}

						if (null != story.getAllocatedDate() && wIdArr.indexOf(story.getwId()) <= -1) {

							for (int i = 0; i < dates.size(); i++) {
								List<String> dateMapValues = Arrays.asList(values.get(i).split(","));
								if (i == dates.size() - 1) {
									if (dates.get(i) < completedDate
											&& dateMapValues.indexOf(sprint.getsId() + "") > -1) {
										String wid = story.getwId();
										List<TransitionModel> tr = story.getTransitions();
										if (tr != null && tr.size() > 0) {
											Collections.sort(tr, new TransitionComparator());
										}
										TransitionModel trans = filterTrans(story.getTransitions(), completedDate);
										if (closestates.indexOf(trans.getCrState()) > -1
												&& trans.getMdfDate() < completedDate
												&& trans.getMdfDate() > sprint.getStDate()
												&& wIdArr.indexOf(story.getwId()) <= -1) {
											tempStory++;
											wIdArr.add(story.getwId());
											// if(sprint.getsId() == 351) {
											// System.out.println(story.getwId());
											// }

										}
									}
								} else {
									if (dates.get(i) < completedDate && dates.get(i + 1) > completedDate
											&& dateMapValues.indexOf(String.valueOf(sprint.getsId())) > -1) {
										String wid = story.getwId();
										List<TransitionModel> tr = story.getTransitions();
										if (tr != null && tr.size() > 0) {
											Collections.sort(tr, new TransitionComparator());
										}

										TransitionModel trans = filterTrans(story.getTransitions(), completedDate);
										if (closestates.indexOf(trans.getCrState()) > -1
												&& trans.getMdfDate() < completedDate
												&& trans.getMdfDate() > sprint.getStDate()
												&& wIdArr.indexOf(story.getwId()) <= -1) {
											tempStory++;
											wIdArr.add(story.getwId());

										}
									}
								}

							}
						}
					}

				}

			}
			sprintAndstories.setMetrics(spStories);
			sprintAndstories.setClosedStories(tempStory);
			sprintwise.add(sprintAndstories);

		}
		return sprintwise;
	}

	TransitionModel filterTrans(List<TransitionModel> tr, long date) {
		List<TransitionModel> newTr = new ArrayList<TransitionModel>();
		if (null != tr) {
			for (int i = 0; i < tr.size(); i++) {
				if (tr.get(i).getMdfDate() < date) {
					newTr.add(tr.get(i));
				}
			}
		}

		if (newTr.size() > 0) {
			return newTr.get(newTr.size() - 1);
		} else {
			return new TransitionModel();
		}
	}

	public List<ScoreCardSprintData> getVelocityChart(List<IterationOutModel> authorList, String projectName) {

		List<ALMConfiguration> almConfigurations = almConfigRepo.findByProjectName(projectName);
		VelocityCalculations velocity = new VelocityCalculations();
		return velocity.calcVelocity(authorList, almConfigurations.get(0));

	}

	public double callSP(String flag, IterationOutModel sp) {
		double tempSp = 0;
		tempSpRefined = 0;
		tempSpRemoved = 0;
		issueList = new ArrayList<IssueList>();

		long date;
		if (flag.equals("start")) {
			date = sp.getStDate();
		} else {
			if (sp.getState().toLowerCase().contains(ConstantVariable.lowerCaseActiveConst)) {
				date = new Date().getTime();
			} else {

				date = sp.getEndDate();

			}

		}
		// if (sp.getsName().equals("NP PI-1.6") ) {
		// System.out.println("dsfsdfsd");
		// }

		for (IterationOutModel it : workingBacklog) {

			ArrayList<String> keys, dateMapValues;
			ArrayList<Long> dateMapKeys, storyPointMapKeys;
			Map storyPointMap;
			Map<Long, String> dateMap;
			if (it.getMetrics() != null)
				for (MonogOutMetrics ele : it.getMetrics()) {
					dateMap = ele.getAllocatedDate();
					dateMapKeys = new ArrayList<Long>(ele.getAllocatedDate().keySet());
					Collections.sort(dateMapKeys);
					// if (ele.getwId().equals("NTSBW-305")) {
					// System.out.println("dfdf");
					// }

					if (ele.getAllocatedDate() != null && (velocityFields.indexOf(ele.getType()) > -1)
							&& ele.getStoryPoints() != null && (vlist.workItemArr.indexOf(ele.getwId()) < 0)) {

						storyPointMapKeys = new ArrayList<Long>(ele.getStoryPoints().keySet());
						storyPointMap = ele.getStoryPoints();
						Collections.sort(storyPointMapKeys);
						for (int i = 0; i < dateMapKeys.size(); i++) {
							List<String> dateValues = Arrays.asList(dateMap.get(dateMapKeys.get(i)).split(","));
							if (i == dateMapKeys.size() - 1 && dateMapKeys.get(i) < date
									&& (dateValues.contains(String.valueOf(sp.getsId())))) {
								tempSp = tempSp
										+ storyLoop(storyPointMapKeys, sp, storyPointMap, issueList, ele, date, false);
								storyLoopRefined(true, storyPointMapKeys, sp, storyPointMap, issueList, ele, date);
							} else if ((dateMapKeys.get(i) < date) && (dateValues.contains(String.valueOf(sp.getsId())))
									&& (dateMapKeys.get(i + 1) > date)) {
								tempSp = tempSp
										+ storyLoop(storyPointMapKeys, sp, storyPointMap, issueList, ele, date, false);
								storyLoopRefined(false, storyPointMapKeys, sp, storyPointMap, issueList, ele, date);
							}

						}
					}

				}

		}
		return tempSp;
	}

	public double calcClosedSP(IterationOutModel sp) {
		double tempSp = 0;
		issueList = new ArrayList<IssueList>();

		long date;

		if (sp.getState().toLowerCase().contains(ConstantVariable.lowerCaseActiveConst)) {
			date = new Date().getTime();
		} else {
			if (sp.getCompletedDate() != 0) {
				date = sp.getCompletedDate();
			} else {
				date = sp.getEndDate();
			}
		}

		wIdArr = new ArrayList<String>();
		for (IterationOutModel it : workingBacklog) {

			ArrayList<String> keys;
			ArrayList<Long> dateMapKeys, storyPointMapKeys;
			Map storyPointMap;
			Map<Long, String> dateMap;
			if (it.getMetrics() != null)
				for (MonogOutMetrics ele : it.getMetrics()) {
					dateMap = ele.getAllocatedDate();
					dateMapKeys = new ArrayList<Long>(ele.getAllocatedDate().keySet());
					Collections.sort(dateMapKeys);
					if (ele.getsId() != 0) {
						// if(ele.getwId().equals("SBW-792")) {
						// System.out.println("dsdsd");
						// }
						if (ele.getAllocatedDate() != null && (velocityFields.indexOf(ele.getType()) > -1)
								&& ele.getStoryPoints() != null) {

							storyPointMapKeys = new ArrayList<Long>(ele.getStoryPoints().keySet());
							storyPointMap = ele.getStoryPoints();

							Collections.sort(storyPointMapKeys);

							for (int i = 0; i < dateMapKeys.size(); i++) {
								List<String> dateValues = Arrays.asList(dateMap.get(dateMapKeys.get(i)).split(","));
								if (i == dateMapKeys.size() - 1 && dateMapKeys.get(i) < date
										&& dateValues.contains(String.valueOf(sp.getsId()))) {
									String wid = ele.getwId();
									TransitionModel trans = filterTrans(ele.getTransitions(), date);
									if (closestates.indexOf(trans.getCrState()) > -1 && trans.getMdfDate() < date
											&& trans.getMdfDate() > sp.getStDate()
											&& trans.getMdfDate() > dateMapKeys.get(i)
											&& wIdArr.indexOf(ele.getwId()) <= -1) {
										wIdArr.add(ele.getwId());
										tempSp = tempSp + storyLoop(storyPointMapKeys, sp, storyPointMap, issueList,
												ele, date, true);

									}

								} else if (dateMapKeys.get(i) < date && dateValues.indexOf(sp.getsId() + "") > -1
										&& dateMapKeys.get(i + 1) > date) {
									String wid = ele.getwId();
									TransitionModel trans = filterTrans(ele.getTransitions(), date);
									if (closestates.indexOf(trans.getCrState()) > -1 && trans.getMdfDate() < date
											&& trans.getMdfDate() > sp.getStDate()
											&& trans.getMdfDate() > dateMapKeys.get(i)
											&& wIdArr.indexOf(ele.getwId()) <= -1) {

										wIdArr.add(ele.getwId());
										tempSp = tempSp + storyLoop(storyPointMapKeys, sp, storyPointMap, issueList,
												ele, date, true);

									}

								}

							}
						}
					}
				}
		}
		return tempSp;
	}

	double storyLoop(List<Long> keys, IterationOutModel it, Map story, List<IssueList> issueList, MonogOutMetrics m,
			long date, boolean colsedflag) {
		double temp = 0;
		for (int i = 0; i < keys.size(); i++) {
			if (keys.get(i) <= date && i == keys.size() - 1) {
				temp = temp + (double) story.get(keys.get(i));
				if (m.getType() != null && m.getType().equalsIgnoreCase(almConfig.getStoryName()) && colsedflag) {
					storiesCompleted++;
				}
				if (m.getType() != null && m.getType().equalsIgnoreCase(almConfig.getDefectName()) && colsedflag) {
					defetcsCompleted++;
				}
				IssueList iss = new IssueList();
				iss.setStoryPoints((double) story.get(keys.get(i)));
				iss.setwId(m.getwId());
				iss.setAssignee(m.getAssgnTo());
				iss.setState(m.getState());
				iss.setType(m.getType());
				iss.setSortId(m.getwId().split("-")[1]);
				issueList.add(iss);
				vlist.workItemArr.add(m.getwId());
			} else if (keys.get(i) <= date && keys.get(i + 1) > date) {
				temp = temp + (double) story.get(keys.get(i));
				if (m.getType() != null && m.getType().equalsIgnoreCase(almConfig.getStoryName()) && colsedflag) {
					storiesCompleted++;
				}
				if (m.getType() != null && m.getType().equalsIgnoreCase(almConfig.getDefectName()) && colsedflag) {
					defetcsCompleted++;
				}
				IssueList iss = new IssueList();
				iss.setStoryPoints((double) story.get(keys.get(i)));
				iss.setwId(m.getwId());
				iss.setAssignee(m.getAssgnTo());
				iss.setState(m.getState());
				iss.setType(m.getType());
				iss.setSortId(m.getwId().split("-")[1]);
				issueList.add(iss);
				vlist.workItemArr.add(m.getwId());
			}
		}
		return temp;
	}

	double storyLoopRefined(boolean isRefined, List<Long> keys, IterationOutModel it, Map story,
			List<IssueList> issueList, MonogOutMetrics m, long date) {
		double temp = 0;

		List<TransitionModel> tr = m.getTransitions();
		if (tr != null) {
			Collections.sort(tr, new TransitionComparator());
		}

		for (int i = 0; i < keys.size(); i++) {
			if (keys.get(i) <= date && i == keys.size() - 1) {

				if (tr != null && tr.size() > 0) {

					if (!tr.get(tr.size() - 1).getCrState().toUpperCase().equals(ConstantVariable.withDrawnConst) && isRefined) {
						tempSpRefined += (double) story.get(keys.get(i));
						if (m.getType().equals(almConfig.getStoryName())) {
							finalStoriesCommited++;
						}

						IssueList issue = new IssueList();
						issue.setStoryPoints((double) story.get(keys.get(i)));
						issue.setwId(m.getwId());
						issue.setAssignee(m.getAssgnTo());
						issue.setState(m.getState());
						issue.setType(m.getType());
						issue.setSortId(m.getwId().split("-")[1]);
						refinedIssuList.add(issue);
						vlist.workItemArr.add(m.getwId());
					} else {
						tempSpRemoved += (double) story.get(keys.get(i));
						if (m.getType().equals(almConfig.getStoryName())) {
							finalStoriesCommited++;
						}
						IssueList issue = new IssueList();
						issue.setStoryPoints((double) story.get(keys.get(i)));
						issue.setwId(m.getwId());
						issue.setAssignee(m.getAssgnTo());
						issue.setState(m.getState());
						issue.setType(m.getType());
						issue.setSortId(m.getwId().split("-")[1]);
						removedIssuList.add(issue);
						vlist.workItemArr.add(m.getwId());
					}
				} else if (m.getsName() != null && vlist.activeSprint != null
						&& vlist.activeSprint.equals(m.getsName())) {
					tempSpRefined += (double) story.get(keys.get(i));
					if (m.getType().equals(almConfig.getStoryName())) {
						finalStoriesCommited++;
					}
					IssueList issue = new IssueList();
					issue.setStoryPoints((double) story.get(keys.get(i)));
					issue.setwId(m.getwId());
					issue.setAssignee(m.getAssgnTo());
					issue.setState(m.getState());
					issue.setType(m.getType());
					issue.setSortId(m.getwId().split("-")[1]);
					refinedIssuList.add(issue);
					vlist.workItemArr.add(m.getwId());
				} else {
					tempSpRemoved += (double) story.get(keys.get(i));
					IssueList issue = new IssueList();
					issue.setStoryPoints((double) story.get(keys.get(i)));
					issue.setwId(m.getwId());
					issue.setAssignee(m.getAssgnTo());
					issue.setState(m.getState());
					issue.setType(m.getType());
					issue.setSortId(m.getwId().split("-")[1]);
					removedIssuList.add(issue);
					vlist.workItemArr.add(m.getwId());
				}
			} else if (keys.get(i) <= date && keys.get(i + 1) > date) {
				if (tr != null && tr.size() > 0) {

					if (!tr.get(tr.size() - 1).getCrState().toUpperCase().equals(ConstantVariable.withDrawnConst)
							&& !m.getState().toUpperCase().equals(ConstantVariable.withDrawnConst) && isRefined) {
						tempSpRefined += (double) story.get(keys.get(i));
						if (m.getType().equals(almConfig.getStoryName())) {
							finalStoriesCommited++;
						}
						IssueList issue = new IssueList();
						issue.setStoryPoints((double) story.get(keys.get(i)));
						issue.setwId(m.getwId());
						issue.setAssignee(m.getAssgnTo());
						issue.setState(m.getState());
						issue.setType(m.getType());
						issue.setSortId(m.getwId().split("-")[1]);
						refinedIssuList.add(issue);
						vlist.workItemArr.add(m.getwId());
					} else {
						tempSpRemoved += (double) story.get(keys.get(i));
						IssueList issue = new IssueList();
						issue.setStoryPoints((double) story.get(keys.get(i)));
						issue.setwId(m.getwId());
						issue.setAssignee(m.getAssgnTo());
						issue.setState(m.getState());
						issue.setType(m.getType());
						issue.setSortId(m.getwId().split("-")[1]);
						removedIssuList.add(issue);
						vlist.workItemArr.add(m.getwId());
					}
				} else if (vlist.activeSprint.equals(m.getsName())) {
					tempSpRefined += (double) story.get(keys.get(i));
					if (m.getType().equals(almConfig.getStoryName())) {
						finalStoriesCommited++;
					}
					IssueList issue = new IssueList();
					issue.setStoryPoints((double) story.get(keys.get(i)));
					issue.setwId(m.getwId());
					issue.setAssignee(m.getAssgnTo());
					issue.setState(m.getState());
					issue.setType(m.getType());
					issue.setSortId(m.getwId().split("-")[1]);
					refinedIssuList.add(issue);
					vlist.workItemArr.add(m.getwId());
				} else {
					tempSpRemoved += (double) story.get(keys.get(i));
					IssueList issue = new IssueList();
					issue.setStoryPoints((double) story.get(keys.get(i)));
					issue.setwId(m.getwId());
					issue.setAssignee(m.getAssgnTo());
					issue.setState(m.getState());
					issue.setType(m.getType());
					issue.setSortId(m.getwId().split("-")[1]);
					removedIssuList.add(issue);
					vlist.workItemArr.add(m.getwId());
				}
			}
		}

		return temp;
	}

	class TransitionComparator implements Comparator {

		@Override
		public int compare(Object o1, Object o2) {
			TransitionModel t1 = (TransitionModel) o1;
			TransitionModel t2 = (TransitionModel) o2;

			if (t1.getMdfDate() != null && t2.getMdfDate() != null) {
				if (t1.getMdfDate().equals(t2.getMdfDate()))
					return 0;
				else if (t1.getMdfDate() > t2.getMdfDate())
					return 1;
				else
					return -1;
			} else if (t1.getMdfDate() == null) {
				return 1;
			} else {
				return -1;
			}

		}

	}

	class SprintComparatort implements Comparator {

		@Override
		public int compare(Object o1, Object o2) {
			IterationOutModel s1 = (IterationOutModel) o1;
			IterationOutModel s2 = (IterationOutModel) o2;

			if (s1.getStDate() != null && s2.getStDate() != null) {
				if (s1.getStDate().equals(s2.getStDate()))
					return 0;
				else if (s1.getStDate() > s2.getStDate())
					return 1;
				else
					return -1;
			} else if (s1.getStDate() == null) {
				return 1;
			} else {
				return -1;
			}

		}

	}
	
	@Override
//	@CacheEvict(value="ALMService", allEntries=true)
	public void updateComponentsOfTaskandSubtask(String projectName) {
		MongoAggregate mongoaggr = new MongoAggregate();
		mongoaggr.updateComponentForTaskAndSubtasks(projectName);

	}

	@Override
@Cacheable(value="ALMService", key ="'getSprintProgressHome'+#projName +#almType", cacheManager="timeoutCacheManager")
	public List<Map<String, List<String>>> getSprintProgressHome(String projName, String almType) {
		
		ProjectHomeCalculation projHomeCalc = new ProjectHomeCalculation();
		List<Map<String, List<String>>> result = projHomeCalc.getSprintProgressData(projName, almType);
		return result;
	}

	@Override
@Cacheable(value="ALMService", key ="'getDefectsSummaryHome'+#projName +#almType", cacheManager="timeoutCacheManager")
	public List<Map<String, String>> getDefectsSummaryHome(String projName, String almType) {
		
		ProjectHomeCalculation projHomeCalc = new ProjectHomeCalculation();
		this.almConfig = this.almConfigRepo.findByProjectName(projName).get(0);
		List<Map<String, String>> result = projHomeCalc.getDefectsSummaryData(projName, almType, this.almConfig);
		return result;
	}

	@Override
@Cacheable(value="ALMService", key ="'getTaskRisk'+#projName +#almType+#storyPointBased", cacheManager="timeoutCacheManager")
	public List<ComponentTaskRisk> getTaskRisk(String projName, String almType, boolean storyPointBased) {
		
		List<ComponentTaskRisk> result = new SprintProgress().getTaskRisk(projName, almType, storyPointBased);
		return result;
	}

	@Override
@Cacheable(value="ALMService", key ="'getActiveSprints'+#projName +#almType", cacheManager="timeoutCacheManager")
	public List<String> getActiveSprints(String projName, String almType) {
		Query query = new Query();
		query.addCriteria(Criteria.where(ConstantVariable.pNameConst).is(projName).and(ConstantVariable.pAlmTypeConst).is(almType).and("state").in(ConstantVariable.activeStates));
		List<IterationModel> iterations = mongoTemplate.find(query, IterationModel.class, "Iterations");
		if (iterations.size() > 0) {
			List<String> response = new ArrayList<String>();
			Iterator<IterationModel> itr = iterations.iterator();
			while (itr.hasNext()) {
				response.add(itr.next().getsName());
			}
			return response;
		}
		return null;

	}

	@Override
@Cacheable(value="ALMService", key ="'getIssueBrakeUp'+#projName +#almType", cacheManager="timeoutCacheManager")
	public List<ComponentIssueBreakup> getIssueBrakeUp(String projName, String almType) {
		SprintProgressCalculations spCalc = new SprintProgressCalculations();
		List<ComponentIssueBreakup> result = spCalc.getissueBreakup(projName, almType);
		return result;
	}

	@Override
@Cacheable(value="ALMService", key ="'getStoryProgress'+#projName +#almType", cacheManager="timeoutCacheManager")
	public List<ComponentStoryProgress> getStoryProgress(String projName, String almType) {
		SprintProgressCalculations spCalc = new SprintProgressCalculations();
		List<ComponentStoryProgress> result = spCalc.getStoryProgress(projName, almType);
		return result;

	}

	@Override
@Cacheable(value="ALMService", key ="'burndownCalculation'+#projName +#almType+#storyPointBased", cacheManager="timeoutCacheManager")
	public List<ComponentBurnDown> burndownCalculation(String projName, String almType, boolean storyPointBased) {
		
		SprintProgress spCalc = new SprintProgress();
		List<ComponentBurnDown> result = spCalc.getBurndown(projName, almType, storyPointBased);
		return result;

	}

	@Override
@Cacheable(value="ALMService", key ="'getDefectInsightData'+#projName +#almType +#compFlag", cacheManager="timeoutCacheManager")
	public Map<String, List<DefectInsightData>> getDefectInsightData(String projName, String almType,boolean compFlag) {
		
		Map<String, List<DefectInsightData>> result = new DefectCalculations().calculateDefectInsightDataComponent(projName, almType,compFlag);
		return result;
	}

	@Override
@Cacheable(value="ALMService", key ="'defectParetoCalculation'+#projName +#almType+#componentBased", cacheManager="timeoutCacheManager")
	public Map<String, List<DefectParetoModel>> defectParetoCalculation(String projName, String almType,
			boolean componentBased) {
		Map<String, List<DefectParetoModel>> result = new DefectCalculations().getParetoDataComp(projName, almType, componentBased);
		return result;
	}

	@Override
@Cacheable(value="ALMService", key ="'getProductionSlippage'+#projName +#almType+#componentBased", cacheManager="timeoutCacheManager")
	public Map<String, List<DefectProductionSlippage>> getProductionSlippage(String projName, String almType,
			boolean componentBased) {
		Map<String, List<DefectProductionSlippage>> result = new DefectCalculations().getDefectProducationSlippageComp(projName, almType, componentBased);
		return result;
	}

	@Override
@Cacheable(value="ALMService", key ="'getDefectDensity'+#projName +#almType+#componentBased", cacheManager="timeoutCacheManager")
	public Map<String, List<DefectDensity>> getDefectDensity(String projName, String almType, boolean componentBased) {
		Map<String, List<DefectDensity>> result = new DefectCalculations().getDefectDensityComp(projName, almType, componentBased);
		return result;
	}

	@Override
@Cacheable(value="ALMService", key ="'getDefectBacklog'+#projName +#almType+#componentBased", cacheManager="timeoutCacheManager")
	public Map<String, DefectBacklog> getDefectBacklog(String projName, String almType, boolean componentBased) {

		Map<String, DefectBacklog> result = new DefectCalculations().getDefectBacklogComponent(projName, almType, componentBased);
		return result;
	}

	@Override
@Cacheable(value="ALMService", key ="'getDefectTrendAndClassification'+#projName +#almType +#componentBased", cacheManager="timeoutCacheManager")
	public Map getDefectTrendAndClassification(String projName, String almType, boolean componentBased) {
		
		Map result = new DefectCalculations().componentSprintDefectTrend(projName, almType,componentBased);
		return result;
	}

	@Override
@Cacheable(value="ALMService", key ="'getDefectClassification'+#projName +#almType", cacheManager="timeoutCacheManager")
	public Map getDefectClassification(String projName, String almType) {
		
		Map result = new DefectCalculations().defectClassification(projName, almType);
		return result;
	}

	@Override
@Cacheable(value="ALMService", key ="'getStoryAgeingData'+#projName+#almType", cacheManager="timeoutCacheManager")
	public List<ComponentStoryAgeing> getStoryAgeingData(String projName, String almType) {
		
		List<ComponentStoryAgeing> result = new BacklogCalculation().caluclateStoryAgeing(projName, almType);
		return result;
	}

	@Override
@Cacheable(value="ALMService", key ="'getGroomingTable'+#projName+#almType", cacheManager="timeoutCacheManager")
	public  List<ComponentGroomingTable> getGroomingTable(String projName, String almType) {
		List<ComponentGroomingTable> result = new BacklogCalculation().calculateGroomingTable(projName, almType);
		return result;
	}

	@Override
@Cacheable(value="ALMService", key ="'getIterations'+#pName", cacheManager="timeoutCacheManager")
	public List<IterationModel> getAllIterations(String pName) {
		
		List<IterationModel> result = iterationRepo.findByPName(pName);
		return result;
	}

	public String getAlmType(String pName) {
		String almType = "";
		ConfigurationSetting config = DataConfig.getContext().getBean(ConfigurationSettingRep.class)
				.findByProjectName(pName).get(0);
		Set<ConfigurationToolInfoMetric> metric = config.getMetrics();
		Iterator iter = metric.iterator();
		while (iter.hasNext()) {
			Object configuration1 = iter.next();
			ConfigurationToolInfoMetric metric1 = (ConfigurationToolInfoMetric) configuration1;
//			if ("Jira".equals(metric1.getToolName())
//					|| ("Jira Defects".equals(metric1.getToolName()) && metric1.getUrl() == "")) {
//				almType = "JIRA";
//				break;
//			} else if ("Jira Defects".equals(metric1.getToolName()) && metric1.getUrl() != "") {
//				almType = "JIRA DEFECTS";
//				break;
//			} else {
//				almType = "TFS";
//			}
			if ("ALM".equals(metric1.getToolType())) {
				almType = metric1.getToolName().toUpperCase();
			}
		}
		return almType;
	}
	@Override
@Cacheable(value="ALMService", key ="'saveEngScore'+#projectName+#month+#engScore", cacheManager="timeoutCacheManager")
	public String saveEngScore(String projectName, String month, double engScore) {
		
		try {
			List<ProjectModel> proj=projectRepo.findByProjectName(projectName);
            SortedMap<String,Double> engScrMap=proj.get(0).getEngScores();
            if(engScrMap==null){
	           engScrMap=new TreeMap<String,Double>();
                }
			     engScrMap.put(month, engScore);
			     proj.get(0).setEngScores(engScrMap);
			    projectRepo.save(proj);
			     return "Success";
		} catch (Exception e) {
			LOGGER.info(e);
			return "Failure";
		}
		
	}

	@Override
	@Cacheable(value="ALMService", key ="'getComponentVelocity'+#projectName", cacheManager="timeoutCacheManager")
	public List<ComponentVelocityList> getComponentVelocityChart(String projectName, boolean b) {
		LOGGER.info("No cache");
		List<ComponentVelocityList> result = chartService.getComponentVelocity(projectName,true);
		return result;
	}

	@Override
	@Cacheable(value="ALMService", key ="'getComponentsSprintStories'+#projectName", cacheManager="timeoutCacheManager")
	public List<ComponentSprintWiseStories> getComponentsSprintStories(String projectName) {
		LOGGER.info("No cache");
		List<ComponentSprintWiseStories> result = chartService.getComponentsSprintStories(projectName);
		return result;
	}

	@Override
@Cacheable(value="ALMService", key ="'getIssueHierarchy'+#projectName", cacheManager="timeoutCacheManager")
	public Map<String, List> getIssueHierarchyChart(String projectName) {
		
		Map<String, List> result = chartService.getIssueHierarchy(projectName);
		return result;
	}

	@Override
	@Cacheable(value="ALMService", key ="'getComponentWiseIssueHierarchy'+#projectName", cacheManager="timeoutCacheManager")
	public Map<String, Map> getComponentWiseIssueHierarchyChart(String projectName) {
		LOGGER.info("No cache");
		Map<String, Map> result = chartService.getComponentWiseIssueHierarchy(projectName);
		return result;
	}


	@Override
@Cacheable(value="ALMService", key ="'getComponents'+#projectName", cacheManager="timeoutCacheManager")
	public List<String> getComponentsChart(String projectName) {
		
		List<String> result = chartService.getComponents(projectName);
		return result;
	}

}
