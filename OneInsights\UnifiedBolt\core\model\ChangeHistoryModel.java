package com.bolt.dashboard.core.model;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "ChangeItems")
public class ChangeHistoryModel extends BaseModel {
    private String wId;
    private String field;
    private String oldValue;
    private String newValue;
    private Long date;
    private String projKey;
    private String pName;

    public String getProjKey() {
	return projKey;
    }

    public void setProjKey(String projKey) {
	this.projKey = projKey;
    }

    public String getProjectName() {
	return pName;
    }

    public void setProjectName(String pName) {
	this.pName = pName;
    }

    public String getwId() {
	return wId;
    }

    public void setwId(String wId) {
	this.wId = wId;
    }

    public String getField() {
	return field;
    }

    public void setField(String field) {
	this.field = field;
    }

    public String getOldValue() {
	return oldValue;
    }

    public void setOldValue(String oldValue) {
	this.oldValue = oldValue;
    }

    public String getNewValue() {
	return newValue;
    }

    public void setNewValue(String newValue) {
	this.newValue = newValue;
    }

    public Long getDate() {
	return date;
    }

    public void setDate(Long date) {
	this.date = date;
    }

}
