{"cells": [{"cell_type": "code", "execution_count": 25, "id": "e28cf0e4", "metadata": {}, "outputs": [], "source": ["NEO4J_URI=\"neo4j://127.0.0.1:7687\"\n", "NEO4J_USERNAME=\"neo4j\"\n", "NEO4J_PASSWORD=\"Thulla@7889\"\n", "NEO4J_DATABASE=\"test\""]}, {"cell_type": "code", "execution_count": 26, "id": "de339d6a", "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ[\"NEO4J_URI\"]=NEO4J_URI\n", "os.environ[\"NEO4J_USERNAME\"]=NEO4J_USERNAME\n", "os.environ[\"NEO4J_PASSWORD\"]=NEO4J_PASSWORD"]}, {"cell_type": "code", "execution_count": 5, "id": "613f81e8", "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "from langchain_community.graphs import Neo4jGraph\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "id": "59fbae07", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Shaik\\sample\\venv\\Lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["from langchain_community.document_loaders import TextLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter, Language\n", "from pathlib import Path\n", "from langchain_google_genai import ChatGoogleGenerativeAI\n", "from langchain_experimental.graph_transformers import LLMGraphTransformer\n", "from langchain_community.document_loaders import TextLoader\n", "from langchain_community.graphs import Neo4jGraph\n", "\n", "import os"]}, {"cell_type": "code", "execution_count": 7, "id": "b5a41f34", "metadata": {}, "outputs": [], "source": ["java_docs = []\n", "for path in Path(\"java\").rglob(\"*.java\"):\n", "    loader = TextLoader(str(path))\n", "    loaded_docs = loader.load()\n", "    for doc in loaded_docs:\n", "        doc.metadata[\"language\"] = \"java\"\n", "        doc.metadata[\"source\"] = str(path)\n", "    java_docs.extend(loaded_docs)"]}, {"cell_type": "code", "execution_count": 8, "id": "209cdd74", "metadata": {}, "outputs": [{"data": {"text/plain": ["12"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["len(java_docs)"]}, {"cell_type": "code", "execution_count": 7, "id": "06922f27", "metadata": {}, "outputs": [{"data": {"text/plain": ["'package com.morganstanley.loanApp;\\n\\nimport jakarta.annotation.PostConstruct;\\nimport org.springframework.beans.factory.annotation.Autowired;\\nimport org.springframework.beans.factory.annotation.Qualifier;\\nimport org.springframework.boot.SpringApplication;\\nimport org.springframework.boot.autoconfigure.SpringBootApplication;\\nimport org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;\\nimport org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;\\nimport org.springframework.boot.context.properties.EnableConfigurationProperties;\\nimport org.springframework.context.ConfigurableApplicationContext;\\nimport org.springframework.core.env.Environment;\\n\\nimport javax.sql.DataSource;\\nimport java.sql.SQLException;\\n\\n@SpringBootApplication\\n@EnableConfigurationProperties\\npublic class LoanAppApplication {\\n\\n\\t@Autowired\\n\\t@Qualifier(\"dataSource\")\\n\\tprivate DataSource dataSource; // primary\\n\\n\\t@Autowired\\n\\t@Qualifier(\"storeProcDataSource\")\\n\\tprivate DataSource secondDataSource;\\n\\n\\n\\t@PostConstruct\\n\\tpublic void printDsInfo() throws SQLException {\\n\\t\\tSystem.out.println(\"Primary DataSource connection URL: \" + dataSource.getConnection().getMetaData().getURL());\\n\\t\\tSystem.out.println(\"Secondary DataSource connection URL: \" + secondDataSource.getConnection().getMetaData().getURL());\\n\\t}\\n\\tpublic static void main(String[] args) {\\n\\t\\t SpringApplication.run(LoanAppApplication.class, args);\\n\\n\\t}\\n\\n}\\n'"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["java_docs[0].page_content"]}, {"cell_type": "code", "execution_count": 9, "id": "8b745c84", "metadata": {}, "outputs": [], "source": ["splitter_java = RecursiveCharacterTextSplitter.from_language(\n", "    language=Language.JAVA,\n", "    chunk_size=2000,\n", "    chunk_overlap=200\n", ")"]}, {"cell_type": "code", "execution_count": 10, "id": "08a137bd", "metadata": {}, "outputs": [], "source": ["split_docs = []\n", "for doc in java_docs:\n", "    chunks = splitter_java.split_documents([doc])\n", "    for idx, chunk in enumerate(chunks):\n", "        chunk.metadata[\"chunk_index\"] = idx\n", "        chunk.metadata[\"language\"] = \"java\"\n", "        chunk.metadata[\"source\"] = doc.metadata[\"source\"]\n", "        split_docs.append(chunk)"]}, {"cell_type": "code", "execution_count": 10, "id": "a309c5d8", "metadata": {}, "outputs": [{"data": {"text/plain": ["14"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["len(split_docs)"]}, {"cell_type": "code", "execution_count": 32, "id": "0c6d40ab", "metadata": {}, "outputs": [{"data": {"text/plain": ["Document(metadata={'source': 'java\\\\LoanAppApplication.java', 'language': 'java', 'chunk_index': 0, 'id': 'c6c7c6ea226a0e818ee10933e884f066'}, page_content='package com.morganstanley.loanApp;\\n\\nimport jakarta.annotation.PostConstruct;\\nimport org.springframework.beans.factory.annotation.Autowired;\\nimport org.springframework.beans.factory.annotation.Qualifier;\\nimport org.springframework.boot.SpringApplication;\\nimport org.springframework.boot.autoconfigure.SpringBootApplication;\\nimport org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;\\nimport org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;\\nimport org.springframework.boot.context.properties.EnableConfigurationProperties;\\nimport org.springframework.context.ConfigurableApplicationContext;\\nimport org.springframework.core.env.Environment;\\n\\nimport javax.sql.DataSource;\\nimport java.sql.SQLException;\\n\\n@SpringBootApplication\\n@EnableConfigurationProperties\\npublic class LoanAppApplication {\\n\\n\\t@Autowired\\n\\t@Qualifier(\"dataSource\")\\n\\tprivate DataSource dataSource; // primary\\n\\n\\t@Autowired\\n\\t@Qualifier(\"storeProcDataSource\")\\n\\tprivate DataSource secondDataSource;\\n\\n\\n\\t@PostConstruct\\n\\tpublic void printDsInfo() throws SQLException {\\n\\t\\tSystem.out.println(\"Primary DataSource connection URL: \" + dataSource.getConnection().getMetaData().getURL());\\n\\t\\tSystem.out.println(\"Secondary DataSource connection URL: \" + secondDataSource.getConnection().getMetaData().getURL());\\n\\t}\\n\\tpublic static void main(String[] args) {\\n\\t\\t SpringApplication.run(LoanAppApplication.class, args);\\n\\n\\t}\\n\\n}')"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["split_docs[0]"]}, {"cell_type": "code", "execution_count": 11, "id": "1b6143d2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total number of chunks: 14\n", "\n", "--- Chunk 1 ---\n", "Metadata: {'source': 'java\\\\LoanAppApplication.java', 'language': 'java', 'chunk_index': 0}\n", "package com.morganstanley.loanApp;\n", "\n", "import jakarta.annotation.PostConstruct;\n", "import org.springframework.beans.factory.annotation.Autowired;\n", "import org.springframework.beans.factory.annotation.Qualifier;\n", "import org.springframework.boot.SpringApplication;\n", "import org.springframework.boot.autoconfigure.SpringBootApplication;\n", "import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguratio \n", "...\n", "\n", "--- Chunk 2 ---\n", "Metadata: {'source': 'java\\\\configuration\\\\PrimaryDataSourceProperties.java', 'language': 'java', 'chunk_index': 0}\n", "package com.morganstanley.loanApp.configuration;\n", "\n", "import jakarta.annotation.PostConstruct;\n", "import org.springframework.boot.context.properties.ConfigurationProperties;\n", "import org.springframework.stereotype.Component;\n", "\n", "@Component\n", "@ConfigurationProperties(prefix = \"spring.datasource\")\n", "public class PrimaryDataSourceProperties {\n", "    public String getUrl() {\n", "        return url;\n", "    }\n", "\n", "    public void se \n", "...\n", "\n", "--- Chunk 3 ---\n", "Metadata: {'source': 'java\\\\configuration\\\\SecondDataSourceProperties.java', 'language': 'java', 'chunk_index': 0}\n", "package com.morganstanley.loanApp.configuration;\n", "\n", "import jakarta.annotation.PostConstruct;\n", "import org.springframework.boot.context.properties.ConfigurationProperties;\n", "import org.springframework.stereotype.Component;\n", "\n", "@Component\n", "@ConfigurationProperties(prefix = \"sp.second-datasource\")\n", "public class SecondDataSourceProperties {\n", "    public String getUrl() {\n", "        return url;\n", "    }\n", "\n", "    public void  \n", "...\n"]}], "source": ["print(f\"Total number of chunks: {len(split_docs)}\")\n", "for i, d in enumerate(split_docs[:3], 1):\n", "    print(f\"\\n--- Chunk {i} ---\")\n", "    print(\"Metadata:\", d.metadata)\n", "    print(d.page_content[:400], \"\\n...\")\n"]}, {"cell_type": "code", "execution_count": 13, "id": "9e4610dd", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(metadata={'source': 'java\\\\LoanAppApplication.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp;\\n\\nimport jakarta.annotation.PostConstruct;\\nimport org.springframework.beans.factory.annotation.Autowired;\\nimport org.springframework.beans.factory.annotation.Qualifier;\\nimport org.springframework.boot.SpringApplication;\\nimport org.springframework.boot.autoconfigure.SpringBootApplication;\\nimport org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;\\nimport org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;\\nimport org.springframework.boot.context.properties.EnableConfigurationProperties;\\nimport org.springframework.context.ConfigurableApplicationContext;\\nimport org.springframework.core.env.Environment;\\n\\nimport javax.sql.DataSource;\\nimport java.sql.SQLException;\\n\\n@SpringBootApplication\\n@EnableConfigurationProperties\\npublic class LoanAppApplication {\\n\\n\\t@Autowired\\n\\t@Qualifier(\"dataSource\")\\n\\tprivate DataSource dataSource; // primary\\n\\n\\t@Autowired\\n\\t@Qualifier(\"storeProcDataSource\")\\n\\tprivate DataSource secondDataSource;\\n\\n\\n\\t@PostConstruct\\n\\tpublic void printDsInfo() throws SQLException {\\n\\t\\tSystem.out.println(\"Primary DataSource connection URL: \" + dataSource.getConnection().getMetaData().getURL());\\n\\t\\tSystem.out.println(\"Secondary DataSource connection URL: \" + secondDataSource.getConnection().getMetaData().getURL());\\n\\t}\\n\\tpublic static void main(String[] args) {\\n\\t\\t SpringApplication.run(LoanAppApplication.class, args);\\n\\n\\t}\\n\\n}'),\n", " Document(metadata={'source': 'java\\\\configuration\\\\PrimaryDataSourceProperties.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp.configuration;\\n\\nimport jakarta.annotation.PostConstruct;\\nimport org.springframework.boot.context.properties.ConfigurationProperties;\\nimport org.springframework.stereotype.Component;\\n\\n@Component\\n@ConfigurationProperties(prefix = \"spring.datasource\")\\npublic class PrimaryDataSourceProperties {\\n    public String getUrl() {\\n        return url;\\n    }\\n\\n    public void setUrl(String url) {\\n        this.url = url;\\n    }\\n\\n    public String getUsername() {\\n        return username;\\n    }\\n\\n    public void setUsername(String username) {\\n        this.username = username;\\n    }\\n\\n    public String getPassword() {\\n        return password;\\n    }\\n\\n    public void setPassword(String password) {\\n        this.password = password;\\n    }\\n\\n    public String getDriverClassName() {\\n        return driverClassName;\\n    }\\n\\n    public void setDriverClassName(String driverClassName) {\\n        this.driverClassName = driverClassName;\\n    }\\n\\n    private String url;\\n    private String username;\\n    private String password;\\n    private String driverClassName;\\n    @PostConstruct\\n    public void printProps() {\\n        System.out.println(\"Primary DS Props:\");\\n        System.out.println(\"URL: \" + url);\\n        System.out.println(\"Username: \" + username);\\n        System.out.println(\"Driver: \" + driverClassName);\\n    }\\n\\n\\n}'),\n", " Document(metadata={'source': 'java\\\\configuration\\\\SecondDataSourceProperties.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp.configuration;\\n\\nimport jakarta.annotation.PostConstruct;\\nimport org.springframework.boot.context.properties.ConfigurationProperties;\\nimport org.springframework.stereotype.Component;\\n\\n@Component\\n@ConfigurationProperties(prefix = \"sp.second-datasource\")\\npublic class SecondDataSourceProperties {\\n    public String getUrl() {\\n        return url;\\n    }\\n\\n    public void setUrl(String url) {\\n        this.url = url;\\n    }\\n\\n    public String getUsername() {\\n        return username;\\n    }\\n\\n    public void setUsername(String username) {\\n        this.username = username;\\n    }\\n\\n    public String getPassword() {\\n        return password;\\n    }\\n\\n    public void setPassword(String password) {\\n        this.password = password;\\n    }\\n\\n    public String getDriverClassName() {\\n        return driverClassName;\\n    }\\n\\n    public void setDriverClassName(String driverClassName) {\\n        this.driverClassName = driverClassName;\\n    }\\n\\n    private String url;\\n    private String username;\\n    private String password;\\n    private String driverClassName;\\n    @PostConstruct\\n    public void printProps() {\\n        System.out.println(\"Secondary DS Props:\");\\n        System.out.println(\"URL: \" + url);\\n        System.out.println(\"Username: \" + username);\\n        System.out.println(\"Driver: \" + driverClassName);\\n    }\\n\\n\\n}'),\n", " Document(metadata={'source': 'java\\\\configuration\\\\StoredProcDBConfig.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp.configuration;\\nimport javax.sql.DataSource;\\n\\nimport com.zaxxer.hikari.HikariDataSource;\\nimport org.springframework.beans.factory.annotation.Qualifier;\\nimport org.springframework.boot.context.properties.ConfigurationProperties;\\nimport org.springframework.boot.jdbc.DataSourceBuilder;\\nimport org.springframework.context.annotation.Bean;\\nimport org.springframework.context.annotation.Configuration;\\nimport org.springframework.context.annotation.Primary;\\nimport org.springframework.jdbc.core.JdbcTemplate;\\n\\n@Configuration\\npublic class StoredProcDBConfig {\\n    @Bean\\n    @Qualifier(\"dataSource\")\\n    @Primary\\n    public DataSource primaryDataSource(PrimaryDataSourceProperties props) {\\n        HikariDataSource ds = new HikariDataSource();\\n        ds.setJdbcUrl(props.getUrl());\\n        ds.setUsername(props.getUsername());\\n        ds.setPassword(props.getPassword());\\n        ds.setDriverClassName(props.getDriverClassName());\\n\\n        System.out.println(\">>> Primary DataSource initialized with URL: \" + props.getUrl());\\n\\n        return ds;\\n    }\\n\\n    @Bean(name = \"storeProcDataSource\")\\n    public DataSource storeProcDataSource(SecondDataSourceProperties props) {\\n        HikariDataSource ds = new HikariDataSource();\\n        ds.setJdbcUrl(props.getUrl());\\n        ds.setUsername(props.getUsername());\\n        ds.setPassword(props.getPassword());\\n        ds.setDriverClassName(props.getDriverClassName());\\n\\n        System.out.println(\">>> Second DataSource initialized with URL: \" + props.getUrl());\\n\\n        return ds;\\n    }\\n\\n    @Bean\\n    public JdbcTemplate storeProcJdbcTemplate(@Qualifier(\"storeProcDataSource\") DataSource ds) {\\n        return new JdbcTemplate(ds);\\n    }\\n}'),\n", " Document(metadata={'source': 'java\\\\controller\\\\UserController.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp.controller;\\n\\nimport com.morganstanley.loanApp.dto.UserLoanRequestDTO;\\nimport com.morganstanley.loanApp.model.User;\\n\\nimport com.morganstanley.loanApp.service.StoreProcService;\\nimport com.morganstanley.loanApp.service.UserService;\\nimport jakarta.validation.Valid;\\nimport org.springframework.beans.factory.annotation.Autowired;\\nimport org.springframework.http.HttpStatus;\\nimport org.springframework.http.ResponseEntity;\\nimport org.springframework.web.bind.annotation.PostMapping;\\nimport org.springframework.web.bind.annotation.RequestBody;\\nimport org.springframework.web.bind.annotation.RequestMapping;\\nimport org.springframework.web.bind.annotation.RestController;\\n\\nimport java.io.*;\\nimport java.util.ArrayList;\\nimport java.util.List;\\n\\n@RestController\\n@RequestMapping(\"/loanApp\")'),\n", " Document(metadata={'source': 'java\\\\controller\\\\UserController.java', 'language': 'java', 'chunk_index': 1}, page_content='public class UserController {\\n    @Autowired\\n    private UserService userService;\\n\\n    @Autowired\\n    private StoreProcService storeProcService;\\n\\n    @PostMapping(\"/createLoan\")\\n    public ResponseEntity<User> createUserWithLoan(@Valid @RequestBody UserLoanRequestDTO dto) {\\n        User savedUser = userService.createUserWithLoan(dto);\\n        return new ResponseEntity<>(savedUser, HttpStatus.CREATED);\\n    }\\n\\n    @PostMapping(\"/save-from-file\")\\n    public ResponseEntity<String> saveUserLoanFromFile(){\\n        try (InputStream is = getClass().getClassLoader().getResourceAsStream(\"usersData.txt\");\\n             BufferedReader reader = new BufferedReader(new InputStreamReader(is))) {\\n\\n            if (is == null) {\\n                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)\\n                        .body(\"Data file not found\");\\n            }\\n\\n            String line ;\\n            while ((line = reader.readLine()) != null) {\\n                String[] parts = line.split(\",\");\\n                if (parts.length != 4) {\\n                    return ResponseEntity.badRequest()\\n                            .body(\"Invalid file format\");\\n                }\\n\\n\\n                UserLoanRequestDTO userDTO = new UserLoanRequestDTO();\\n                userDTO.setName(parts[0].trim());\\n                userDTO.setEmail(parts[1].trim());\\n                userDTO.setLoanType(parts[2].trim());\\n                userDTO.setAmount(Double.valueOf(parts[3].trim()));\\n\\n                userService.createUserWithLoan(userDTO);\\n            }\\n            return ResponseEntity.ok(\"Loan application saved from flat file\");\\n\\n        } catch (Exception e) {\\n            e.printStackTrace();\\n            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)\\n                    .body(\"Error processing flat file\");\\n        }\\n    }'),\n", " Document(metadata={'source': 'java\\\\controller\\\\UserController.java', 'language': 'java', 'chunk_index': 2}, page_content='@PostMapping(\"/processStoreProcFromCSV\")\\n    public ResponseEntity<List<String>> readCSVAndCallStoreProc(){\\n        List<String> outputMessages = new ArrayList<>();\\n\\n        try (InputStream is = getClass().getClassLoader().getResourceAsStream(\"branchesData.csv\");\\n             BufferedReader reader = new BufferedReader(new InputStreamReader(is))) {\\n            String line;\\n            boolean isFirstLine = true;\\n\\n            while ((line = reader.readLine()) != null) {\\n                if (isFirstLine) {\\n                    isFirstLine = false; // skip header\\n                    continue;\\n                }\\n\\n                String[] parts = line.split(\",\");\\n                if (parts.length != 2) continue;\\n\\n                String branchName = parts[0].trim();\\n                String branchAddress = parts[1].trim();\\n\\n                Integer result = storeProcService.callStoreProc(branchName, branchAddress);\\n                outputMessages.add(\"Branch Id for \" + branchName + \" is: \" + result);\\n            }\\n\\n            return ResponseEntity.ok(outputMessages);\\n        } catch (IOException | NumberFormatException ex) {\\n            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)\\n                    .body(List.of(\"Error reading file or parsing data: \" + ex.getMessage()));\\n        }\\n    }\\n\\n\\n}'),\n", " Document(metadata={'source': 'java\\\\dto\\\\UserLoanRequestDTO.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp.dto;\\n\\nimport jakarta.validation.constraints.Email;\\nimport jakarta.validation.constraints.NotBlank;\\nimport jakarta.validation.constraints.NotNull;\\nimport jakarta.validation.constraints.Positive;\\nimport lombok.Data;\\nimport lombok.Getter;\\nimport lombok.Setter;\\n\\n\\n\\npublic class UserLoanRequestDTO {\\n\\n    public String getName() {\\n        return name;\\n    }\\n\\n    public void setName(String name) {\\n        this.name = name;\\n    }\\n\\n    public String getEmail() {\\n        return email;\\n    }\\n\\n    public void setEmail(String email) {\\n        this.email = email;\\n    }\\n\\n    public String getLoanType() {\\n        return loanType;\\n    }\\n\\n    public void setLoanType(String loanType) {\\n        this.loanType = loanType;\\n    }\\n\\n    public Double getAmount() {\\n        return amount;\\n    }\\n\\n    public void setAmount(Double amount) {\\n        this.amount = amount;\\n    }\\n\\n    @NotBlank(message = \"Name is required\")\\n    private String name;\\n\\n    @Email(message = \"Email should be valid\")\\n    @NotBlank(message = \"Email is required\")\\n    private String email;\\n\\n    @NotBlank(message = \"Loan type is required\")\\n    private String loanType;\\n\\n    @NotNull(message = \"Loan amount is required\")\\n    @Positive(message = \"Loan amount must be positive\")\\n    private Double amount;\\n}'),\n", " Document(metadata={'source': 'java\\\\model\\\\LoanDetails.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp.model;\\n\\nimport jakarta.persistence.*;\\nimport lombok.Data;\\n\\n@Entity\\n@Table(name = \"loan_details\")\\n@Data\\npublic class LoanDetails {\\n\\n    @Id\\n    @GeneratedValue(strategy = GenerationType.IDENTITY)\\n    private Long id;\\n\\n    private String loanType;\\n    private Double amount;\\n\\n    @Column(name = \"user_id\")\\n    private Long userId;\\n\\n\\n\\n}'),\n", " Document(metadata={'source': 'java\\\\model\\\\User.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp.model;\\n\\nimport jakarta.persistence.*;\\nimport lombok.Data;\\n\\n@Entity\\n@Table(name = \"users\")\\n@Data\\npublic class User {\\n\\n    @Id\\n    @GeneratedValue(strategy = GenerationType.IDENTITY)\\n    private Long id;\\n\\n    private String name;\\n    private String email;\\n}'),\n", " Document(metadata={'source': 'java\\\\repository\\\\LoanDetailsRepository.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp.repository;\\n\\nimport com.morganstanley.loanApp.model.LoanDetails;\\nimport org.springframework.data.jpa.repository.JpaRepository;\\n\\npublic interface LoanDetailsRepository extends JpaRepository<LoanDetails,Long> {\\n}'),\n", " Document(metadata={'source': 'java\\\\repository\\\\UserRepository.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp.repository;\\n\\nimport com.morganstanley.loanApp.model.User;\\nimport org.springframework.data.jpa.repository.JpaRepository;\\n\\n\\npublic interface UserRepository extends JpaRepository<User, Long> {\\n}'),\n", " Document(metadata={'source': 'java\\\\service\\\\StoreProcService.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp.service;\\n\\nimport org.springframework.beans.factory.annotation.Autowired;\\nimport org.springframework.beans.factory.annotation.Qualifier;\\nimport org.springframework.jdbc.core.JdbcTemplate;\\nimport org.springframework.stereotype.Service;\\n\\nimport java.sql.CallableStatement;\\nimport java.sql.Connection;\\nimport java.sql.Types;\\n\\n@Service\\npublic class StoreProcService {\\n\\n\\n    @Autowired\\n    @Qualifier(\"storeProcJdbcTemplate\")\\n    private JdbcTemplate storeProcTemplate;\\n\\n\\n\\n\\n    public Integer callStoreProc(String branchName, String branchAddress) {\\n\\n        return storeProcTemplate.execute((Connection conn) -> {\\n            CallableStatement cs = conn.prepareCall(\"{call sp_insert_branch(?, ?, ?)}\");\\n            cs.setString(1, branchName);\\n            cs.setString(2, branchAddress);\\n            cs.registerOutParameter(3, Types.INTEGER);\\n            cs.execute();\\n            return cs.getInt(3);\\n        });\\n    }\\n}'),\n", " Document(metadata={'source': 'java\\\\service\\\\UserService.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp.service;\\n\\nimport com.morganstanley.loanApp.dto.UserLoanRequestDTO;\\nimport com.morganstanley.loanApp.model.LoanDetails;\\nimport com.morganstanley.loanApp.model.User;\\nimport com.morganstanley.loanApp.repository.LoanDetailsRepository;\\nimport com.morganstanley.loanApp.repository.UserRepository;\\nimport org.springframework.beans.factory.annotation.Autowired;\\nimport org.springframework.stereotype.Service;\\n\\n@Service\\npublic class UserService {\\n    @Autowired\\n    private UserRepository userRepository;\\n\\n    @Autowired\\n    private LoanDetailsRepository loanDetailsRepository;\\n\\n    public User createUserWithLoan(UserLoanRequestDTO dto) {\\n        User user = new User();\\n        user.setName(dto.getName());\\n        user.setEmail(dto.getEmail());\\n        user = userRepository.save(user);\\n\\n        LoanDetails loan = new LoanDetails();\\n        loan.setLoanType(dto.getLoanType());\\n        loan.setAmount(dto.getAmount());\\n        loan.setUserId(user.getId());\\n        loanDetailsRepository.save(loan);\\n\\n        return user;\\n    }\\n}')]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["split_docs"]}, {"cell_type": "code", "execution_count": null, "id": "3f534a17", "metadata": {}, "outputs": [{"ename": "APIConnectionError", "evalue": "Connection error.", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mUnsupportedProtocol\u001b[39m                       <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\httpx\\_transports\\default.py:101\u001b[39m, in \u001b[36mmap_httpcore_exceptions\u001b[39m\u001b[34m()\u001b[39m\n\u001b[32m    100\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m101\u001b[39m     \u001b[38;5;28;01my<PERSON>\u001b[39;00m\n\u001b[32m    102\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\httpx\\_transports\\default.py:250\u001b[39m, in \u001b[36mHTTPTransport.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    249\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m map_httpcore_exceptions():\n\u001b[32m--> \u001b[39m\u001b[32m250\u001b[39m     resp = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_pool\u001b[49m\u001b[43m.\u001b[49m\u001b[43mhandle_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mreq\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    252\u001b[39m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(resp.stream, typing.Iterable)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\httpcore\\_sync\\connection_pool.py:207\u001b[39m, in \u001b[36mConnectionPool.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    206\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m scheme == \u001b[33m\"\u001b[39m\u001b[33m\"\u001b[39m:\n\u001b[32m--> \u001b[39m\u001b[32m207\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m UnsupportedProtocol(\n\u001b[32m    208\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mRequest URL is missing an \u001b[39m\u001b[33m'\u001b[39m\u001b[33mhttp://\u001b[39m\u001b[33m'\u001b[39m\u001b[33m or \u001b[39m\u001b[33m'\u001b[39m\u001b[33mhttps://\u001b[39m\u001b[33m'\u001b[39m\u001b[33m protocol.\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    209\u001b[39m     )\n\u001b[32m    210\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m scheme \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m (\u001b[33m\"\u001b[39m\u001b[33mhttp\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mhttps\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mws\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mwss\u001b[39m\u001b[33m\"\u001b[39m):\n", "\u001b[31mUnsupportedProtocol\u001b[39m: Request URL is missing an 'http://' or 'https://' protocol.", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[31mUnsupportedProtocol\u001b[39m                       <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\openai\\_base_client.py:972\u001b[39m, in \u001b[36mSyncAPIClient.request\u001b[39m\u001b[34m(self, cast_to, options, stream, stream_cls)\u001b[39m\n\u001b[32m    971\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m972\u001b[39m     response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_client\u001b[49m\u001b[43m.\u001b[49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    973\u001b[39m \u001b[43m        \u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    974\u001b[39m \u001b[43m        \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_should_stream_response_body\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m=\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    975\u001b[39m \u001b[43m        \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    976\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    977\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m httpx.TimeoutException \u001b[38;5;28;01mas\u001b[39;00m err:\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\httpx\\_client.py:914\u001b[39m, in \u001b[36mClient.send\u001b[39m\u001b[34m(self, request, stream, auth, follow_redirects)\u001b[39m\n\u001b[32m    912\u001b[39m auth = \u001b[38;5;28mself\u001b[39m._build_request_auth(request, auth)\n\u001b[32m--> \u001b[39m\u001b[32m914\u001b[39m response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_send_handling_auth\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    915\u001b[39m \u001b[43m    \u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    916\u001b[39m \u001b[43m    \u001b[49m\u001b[43mauth\u001b[49m\u001b[43m=\u001b[49m\u001b[43mauth\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    917\u001b[39m \u001b[43m    \u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m=\u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    918\u001b[39m \u001b[43m    \u001b[49m\u001b[43mhistory\u001b[49m\u001b[43m=\u001b[49m\u001b[43m[\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    919\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    920\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\httpx\\_client.py:942\u001b[39m, in \u001b[36mClient._send_handling_auth\u001b[39m\u001b[34m(self, request, auth, follow_redirects, history)\u001b[39m\n\u001b[32m    941\u001b[39m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m942\u001b[39m     response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_send_handling_redirects\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    943\u001b[39m \u001b[43m        \u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    944\u001b[39m \u001b[43m        \u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m=\u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    945\u001b[39m \u001b[43m        \u001b[49m\u001b[43mhistory\u001b[49m\u001b[43m=\u001b[49m\u001b[43mhistory\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    946\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    947\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\httpx\\_client.py:979\u001b[39m, in \u001b[36mClient._send_handling_redirects\u001b[39m\u001b[34m(self, request, follow_redirects, history)\u001b[39m\n\u001b[32m    977\u001b[39m     hook(request)\n\u001b[32m--> \u001b[39m\u001b[32m979\u001b[39m response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_send_single_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    980\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\httpx\\_client.py:1014\u001b[39m, in \u001b[36mClient._send_single_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m   1013\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m request_context(request=request):\n\u001b[32m-> \u001b[39m\u001b[32m1014\u001b[39m     response = \u001b[43mtransport\u001b[49m\u001b[43m.\u001b[49m\u001b[43mhandle_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1016\u001b[39m \u001b[38;5;28;01<PERSON>ert\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(response.stream, SyncByteStream)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\httpx\\_transports\\default.py:249\u001b[39m, in \u001b[36mHTTPTransport.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    237\u001b[39m req = httpcore.Request(\n\u001b[32m    238\u001b[39m     method=request.method,\n\u001b[32m    239\u001b[39m     url=httpcore.URL(\n\u001b[32m   (...)\u001b[39m\u001b[32m    247\u001b[39m     extensions=request.extensions,\n\u001b[32m    248\u001b[39m )\n\u001b[32m--> \u001b[39m\u001b[32m249\u001b[39m \u001b[43m\u001b[49m\u001b[38;5;28;43;01mwith\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mmap_httpcore_exceptions\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m    250\u001b[39m \u001b[43m    \u001b[49m\u001b[43mresp\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_pool\u001b[49m\u001b[43m.\u001b[49m\u001b[43mhandle_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mreq\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mC:\\Program Files\\Python311\\Lib\\contextlib.py:158\u001b[39m, in \u001b[36m_GeneratorContextManager.__exit__\u001b[39m\u001b[34m(self, typ, value, traceback)\u001b[39m\n\u001b[32m    157\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m158\u001b[39m     \u001b[38;5;28mself\u001b[39m.gen.throw(typ, value, traceback)\n\u001b[32m    159\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mStopIteration\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[32m    160\u001b[39m     \u001b[38;5;66;03m# Suppress StopIteration *unless* it's the same exception that\u001b[39;00m\n\u001b[32m    161\u001b[39m     \u001b[38;5;66;03m# was passed to throw().  This prevents a StopIteration\u001b[39;00m\n\u001b[32m    162\u001b[39m     \u001b[38;5;66;03m# raised inside the \"with\" statement from being suppressed.\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\httpx\\_transports\\default.py:118\u001b[39m, in \u001b[36mmap_httpcore_exceptions\u001b[39m\u001b[34m()\u001b[39m\n\u001b[32m    117\u001b[39m message = \u001b[38;5;28mstr\u001b[39m(exc)\n\u001b[32m--> \u001b[39m\u001b[32m118\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m mapped_exc(message) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mexc\u001b[39;00m\n", "\u001b[31mUnsupportedProtocol\u001b[39m: Request URL is missing an 'http://' or 'https://' protocol.", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[31mAPIConnectionError\u001b[39m                        Traceback (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[18]\u001b[39m\u001b[32m, line 10\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mlangchain_openai\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m AzureChatOpenAI\n\u001b[32m      3\u001b[39m llm = AzureChatOpenAI(\n\u001b[32m      4\u001b[39m     api_key=\u001b[33m\"\u001b[39m\u001b[33mCTW\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m      5\u001b[39m     azure_endpoint=\u001b[33m\"\u001b[39m\u001b[33mhttp/\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m      6\u001b[39m     azure_deployment=\u001b[33m\"\u001b[39m\u001b[33mgpt-4.1-mini\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m      7\u001b[39m     api_version=\u001b[33m\"\u001b[39m\u001b[33m2024-08-01-preview\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m      8\u001b[39m )\n\u001b[32m---> \u001b[39m\u001b[32m10\u001b[39m response = \u001b[43mllm\u001b[49m\u001b[43m.\u001b[49m\u001b[43minvoke\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m<PERSON><PERSON>, how are you?\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m     11\u001b[39m \u001b[38;5;28mprint\u001b[39m(response.content)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py:378\u001b[39m, in \u001b[36mBaseChatModel.invoke\u001b[39m\u001b[34m(self, input, config, stop, **kwargs)\u001b[39m\n\u001b[32m    366\u001b[39m \u001b[38;5;129m@override\u001b[39m\n\u001b[32m    367\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34minvoke\u001b[39m(\n\u001b[32m    368\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m    373\u001b[39m     **kwargs: Any,\n\u001b[32m    374\u001b[39m ) -> BaseMessage:\n\u001b[32m    375\u001b[39m     config = ensure_config(config)\n\u001b[32m    376\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m cast(\n\u001b[32m    377\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mChatGeneration\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m--> \u001b[39m\u001b[32m378\u001b[39m         \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mgenerate_prompt\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    379\u001b[39m \u001b[43m            \u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_convert_input\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    380\u001b[39m \u001b[43m            \u001b[49m\u001b[43mstop\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstop\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    381\u001b[39m \u001b[43m            \u001b[49m\u001b[43mcallbacks\u001b[49m\u001b[43m=\u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mcallbacks\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    382\u001b[39m \u001b[43m            \u001b[49m\u001b[43mtags\u001b[49m\u001b[43m=\u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtags\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    383\u001b[39m \u001b[43m            \u001b[49m\u001b[43mmetadata\u001b[49m\u001b[43m=\u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmetadata\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    384\u001b[39m \u001b[43m            \u001b[49m\u001b[43mrun_name\u001b[49m\u001b[43m=\u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mrun_name\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    385\u001b[39m \u001b[43m            \u001b[49m\u001b[43mrun_id\u001b[49m\u001b[43m=\u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m.\u001b[49m\u001b[43mpop\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mrun_id\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    386\u001b[39m \u001b[43m            \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    387\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m.generations[\u001b[32m0\u001b[39m][\u001b[32m0\u001b[39m],\n\u001b[32m    388\u001b[39m     ).message\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py:963\u001b[39m, in \u001b[36mBaseChatModel.generate_prompt\u001b[39m\u001b[34m(self, prompts, stop, callbacks, **kwargs)\u001b[39m\n\u001b[32m    954\u001b[39m \u001b[38;5;129m@override\u001b[39m\n\u001b[32m    955\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mgenerate_prompt\u001b[39m(\n\u001b[32m    956\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m    960\u001b[39m     **kwargs: Any,\n\u001b[32m    961\u001b[39m ) -> LLMResult:\n\u001b[32m    962\u001b[39m     prompt_messages = [p.to_messages() \u001b[38;5;28;01mfor\u001b[39;00m p \u001b[38;5;129;01min\u001b[39;00m prompts]\n\u001b[32m--> \u001b[39m\u001b[32m963\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mgenerate\u001b[49m\u001b[43m(\u001b[49m\u001b[43mprompt_messages\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstop\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstop\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcallbacks\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcallbacks\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py:782\u001b[39m, in \u001b[36mBaseChatModel.generate\u001b[39m\u001b[34m(self, messages, stop, callbacks, tags, metadata, run_name, run_id, **kwargs)\u001b[39m\n\u001b[32m    779\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m i, m \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28menumerate\u001b[39m(input_messages):\n\u001b[32m    780\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    781\u001b[39m         results.append(\n\u001b[32m--> \u001b[39m\u001b[32m782\u001b[39m             \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_generate_with_cache\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    783\u001b[39m \u001b[43m                \u001b[49m\u001b[43mm\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    784\u001b[39m \u001b[43m                \u001b[49m\u001b[43mstop\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstop\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    785\u001b[39m \u001b[43m                \u001b[49m\u001b[43mrun_manager\u001b[49m\u001b[43m=\u001b[49m\u001b[43mrun_managers\u001b[49m\u001b[43m[\u001b[49m\u001b[43mi\u001b[49m\u001b[43m]\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mrun_managers\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01melse\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m    786\u001b[39m \u001b[43m                \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    787\u001b[39m \u001b[43m            \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    788\u001b[39m         )\n\u001b[32m    789\u001b[39m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m    790\u001b[39m         \u001b[38;5;28;01mif\u001b[39;00m run_managers:\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py:1028\u001b[39m, in \u001b[36mBaseChatModel._generate_with_cache\u001b[39m\u001b[34m(self, messages, stop, run_manager, **kwargs)\u001b[39m\n\u001b[32m   1026\u001b[39m     result = generate_from_stream(\u001b[38;5;28miter\u001b[39m(chunks))\n\u001b[32m   1027\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m inspect.signature(\u001b[38;5;28mself\u001b[39m._generate).parameters.get(\u001b[33m\"\u001b[39m\u001b[33mrun_manager\u001b[39m\u001b[33m\"\u001b[39m):\n\u001b[32m-> \u001b[39m\u001b[32m1028\u001b[39m     result = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_generate\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   1029\u001b[39m \u001b[43m        \u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstop\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstop\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrun_manager\u001b[49m\u001b[43m=\u001b[49m\u001b[43mrun_manager\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\n\u001b[32m   1030\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1031\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m   1032\u001b[39m     result = \u001b[38;5;28mself\u001b[39m._generate(messages, stop=stop, **kwargs)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\langchain_openai\\chat_models\\base.py:1130\u001b[39m, in \u001b[36mBaseChatOpenAI._generate\u001b[39m\u001b[34m(self, messages, stop, run_manager, **kwargs)\u001b[39m\n\u001b[32m   1128\u001b[39m     generation_info = {\u001b[33m\"\u001b[39m\u001b[33mheaders\u001b[39m\u001b[33m\"\u001b[39m: \u001b[38;5;28mdict\u001b[39m(raw_response.headers)}\n\u001b[32m   1129\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m1130\u001b[39m     response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mclient\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcreate\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mpayload\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1131\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._create_chat_result(response, generation_info)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\openai\\_utils\\_utils.py:287\u001b[39m, in \u001b[36mrequired_args.<locals>.inner.<locals>.wrapper\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m    285\u001b[39m             msg = \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mMissing required argument: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mquote(missing[\u001b[32m0\u001b[39m])\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m\n\u001b[32m    286\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(msg)\n\u001b[32m--> \u001b[39m\u001b[32m287\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\openai\\resources\\chat\\completions\\completions.py:1087\u001b[39m, in \u001b[36mCompletions.create\u001b[39m\u001b[34m(self, messages, model, audio, frequency_penalty, function_call, functions, logit_bias, logprobs, max_completion_tokens, max_tokens, metadata, modalities, n, parallel_tool_calls, prediction, presence_penalty, reasoning_effort, response_format, seed, service_tier, stop, store, stream, stream_options, temperature, tool_choice, tools, top_logprobs, top_p, user, web_search_options, extra_headers, extra_query, extra_body, timeout)\u001b[39m\n\u001b[32m   1044\u001b[39m \u001b[38;5;129m@required_args\u001b[39m([\u001b[33m\"\u001b[39m\u001b[33mmessages\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mmodel\u001b[39m\u001b[33m\"\u001b[39m], [\u001b[33m\"\u001b[39m\u001b[33mmessages\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mmodel\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mstream\u001b[39m\u001b[33m\"\u001b[39m])\n\u001b[32m   1045\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mcreate\u001b[39m(\n\u001b[32m   1046\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m   1084\u001b[39m     timeout: \u001b[38;5;28mfloat\u001b[39m | httpx.Timeout | \u001b[38;5;28;01mNone\u001b[39;00m | NotGiven = NOT_GIVEN,\n\u001b[32m   1085\u001b[39m ) -> ChatCompletion | Stream[ChatCompletionChunk]:\n\u001b[32m   1086\u001b[39m     validate_response_format(response_format)\n\u001b[32m-> \u001b[39m\u001b[32m1087\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_post\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   1088\u001b[39m \u001b[43m        \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m/chat/completions\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m   1089\u001b[39m \u001b[43m        \u001b[49m\u001b[43mbody\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmaybe_transform\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   1090\u001b[39m \u001b[43m            \u001b[49m\u001b[43m{\u001b[49m\n\u001b[32m   1091\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmessages\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1092\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmodel\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1093\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43maudio\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43maudio\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1094\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mfrequency_penalty\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mfrequency_penalty\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1095\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mfunction_call\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mfunction_call\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1096\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mfunctions\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mfunctions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1097\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mlogit_bias\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mlogit_bias\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1098\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mlogprobs\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mlogprobs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1099\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmax_completion_tokens\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmax_completion_tokens\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1100\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmax_tokens\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmax_tokens\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1101\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmetadata\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmetadata\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1102\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmodalities\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmodalities\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1103\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mn\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1104\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mparallel_tool_calls\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mparallel_tool_calls\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1105\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mprediction\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mprediction\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1106\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mpresence_penalty\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mpresence_penalty\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1107\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mreasoning_effort\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mreasoning_effort\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1108\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mresponse_format\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mresponse_format\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1109\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mseed\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mseed\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1110\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mservice_tier\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mservice_tier\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1111\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstop\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstop\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1112\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstore\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstore\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1113\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstream\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1114\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstream_options\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1115\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtemperature\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtemperature\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1116\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtool_choice\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtool_choice\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1117\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtools\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtools\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1118\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtop_logprobs\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtop_logprobs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1119\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtop_p\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtop_p\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1120\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43muser\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43muser\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1121\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mweb_search_options\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mweb_search_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1122\u001b[39m \u001b[43m            \u001b[49m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1123\u001b[39m \u001b[43m            \u001b[49m\u001b[43mcompletion_create_params\u001b[49m\u001b[43m.\u001b[49m\u001b[43mCompletionCreateParamsStreaming\u001b[49m\n\u001b[32m   1124\u001b[39m \u001b[43m            \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mstream\u001b[49m\n\u001b[32m   1125\u001b[39m \u001b[43m            \u001b[49m\u001b[38;5;28;43;01melse\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mcompletion_create_params\u001b[49m\u001b[43m.\u001b[49m\u001b[43mCompletionCreateParamsNonStreaming\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1126\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1127\u001b[39m \u001b[43m        \u001b[49m\u001b[43moptions\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmake_request_options\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   1128\u001b[39m \u001b[43m            \u001b[49m\u001b[43mextra_headers\u001b[49m\u001b[43m=\u001b[49m\u001b[43mextra_headers\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mextra_query\u001b[49m\u001b[43m=\u001b[49m\u001b[43mextra_query\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mextra_body\u001b[49m\u001b[43m=\u001b[49m\u001b[43mextra_body\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\n\u001b[32m   1129\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1130\u001b[39m \u001b[43m        \u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m=\u001b[49m\u001b[43mChatCompletion\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1131\u001b[39m \u001b[43m        \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m   1132\u001b[39m \u001b[43m        \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m=\u001b[49m\u001b[43mStream\u001b[49m\u001b[43m[\u001b[49m\u001b[43mChatCompletionChunk\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1133\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\openai\\_base_client.py:1249\u001b[39m, in \u001b[36mSyncAPIClient.post\u001b[39m\u001b[34m(self, path, cast_to, body, options, files, stream, stream_cls)\u001b[39m\n\u001b[32m   1235\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mpost\u001b[39m(\n\u001b[32m   1236\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   1237\u001b[39m     path: \u001b[38;5;28mstr\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m   1244\u001b[39m     stream_cls: \u001b[38;5;28mtype\u001b[39m[_StreamT] | \u001b[38;5;28;01mNone\u001b[39;00m = \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[32m   1245\u001b[39m ) -> ResponseT | _StreamT:\n\u001b[32m   1246\u001b[39m     opts = FinalRequestOptions.construct(\n\u001b[32m   1247\u001b[39m         method=\u001b[33m\"\u001b[39m\u001b[33mpost\u001b[39m\u001b[33m\"\u001b[39m, url=path, json_data=body, files=to_httpx_files(files), **options\n\u001b[32m   1248\u001b[39m     )\n\u001b[32m-> \u001b[39m\u001b[32m1249\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m cast(ResponseT, \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mopts\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m)\u001b[49m)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\openai\\_base_client.py:1004\u001b[39m, in \u001b[36mSyncAPIClient.request\u001b[39m\u001b[34m(self, cast_to, options, stream, stream_cls)\u001b[39m\n\u001b[32m   1001\u001b[39m         \u001b[38;5;28;01mcontinue\u001b[39;00m\n\u001b[32m   1003\u001b[39m     log.debug(\u001b[33m\"\u001b[39m\u001b[33mRaising connection error\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m-> \u001b[39m\u001b[32m1004\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m APIConnectionError(request=request) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01merr\u001b[39;00m\n\u001b[32m   1006\u001b[39m log.debug(\n\u001b[32m   1007\u001b[39m     \u001b[33m'\u001b[39m\u001b[33mHTTP Response: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m \u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;132;01m%i\u001b[39;00m\u001b[33m \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m\"\u001b[39m\u001b[33m \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m'\u001b[39m,\n\u001b[32m   1008\u001b[39m     request.method,\n\u001b[32m   (...)\u001b[39m\u001b[32m   1012\u001b[39m     response.headers,\n\u001b[32m   1013\u001b[39m )\n\u001b[32m   1014\u001b[39m log.debug(\u001b[33m\"\u001b[39m\u001b[33mrequest_id: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m\"\u001b[39m, response.headers.get(\u001b[33m\"\u001b[39m\u001b[33mx-request-id\u001b[39m\u001b[33m\"\u001b[39m))\n", "\u001b[31mAPIConnectionError\u001b[39m: Connection error."]}], "source": ["from langchain_openai import AzureChatOpenAI\n", "\n", "llm = AzureChatOpenAI(\n", "    api_key=\"CTW\",\n", "    azure_endpoint=\"https://ms-data-lineage.openai.azure.com/\",\n", "    azure_deployment=\"gpt-4.1-mini\",\n", "    api_version=\"2024-12-01-preview\"\n", ")\n", "\n", "response = llm.invoke(\"Hello, how are you?\")\n", "print(response.content)"]}, {"cell_type": "code", "execution_count": 13, "id": "35bd0a17", "metadata": {}, "outputs": [], "source": ["\n", "system_prompt = \"\"\"\n", "You are a **code and data lineage analysis engine**. From the provided Python or Java code chunk, extract **structured graph triples** in the format:\n", "\n", "[SourceNodeLabel]:SourceNodeName -[RELATION]-> [TargetNodeLabel]:TargetNodeName\n", "\n", "## Context:\n", "- Use the explicit Folder and File info provided above each code chunk.\n", "- Ignore package statements when building Folder nodes.\n", "- If database table names, column names, or API endpoints are detected, include them as nodes for lineage.\n", "- If functions or classes are reading from or writing to database tables or making API calls, capture them in the lineage relationships.\n", "\n", "## Layers to capture:\n", "- Folder -> File\n", "- File -> Class\n", "- Class -> Function/Method\n", "- Function/Method -> Function/Method (CALLS)\n", "- Class -> Class (INHERITS)\n", "- Function/Method -> Table (READS_FROM / WRITES_TO)\n", "- Function/Method -> API (CALLS_API)\n", "\n", "## Node Types:\n", "- Folder\n", "- File\n", "- Class\n", "- Function\n", "- Table\n", "- Column (if explicit)\n", "- API\n", "\n", "## Relationship Types:\n", "- CONTAINS\n", "- DECLARES\n", "- CALLS\n", "- INHERITS\n", "- READS_FROM\n", "- WRITES_TO\n", "- CALLS_API\n", "\n", "## Examples:\n", "\n", "Folder:test/java -[CONTAINS]-> File:SecondDataSourceProperties.java\n", "File:SecondDataSourceProperties.java -[DECLARES]-> Class:SecondDataSourceProperties\n", "Class:SecondDataSourceProperties -[DECLARES]-> Function:getUrl\n", "Function:load_user_data -[READS_FROM]-> Table:users\n", "Function:save_user_data -[WRITES_TO]-> Table:users\n", "Function:fetch_from_service -[CALLS_API]-> API:getUserProfile\n", "\n", "## Instructions:\n", "- Return **only these triples, nothing else**.\n", "- If unsure, prefer to omit rather than hallucinate.\n", "- If code chunk does not involve data sources, return structural triples only.\n", "\"\"\"\n", "\n"]}, {"cell_type": "code", "execution_count": 14, "id": "33485b6a", "metadata": {}, "outputs": [], "source": ["\n", "transformer = LLMGraphTransformer(\n", "    llm=llm,\n", "    additional_instructions=system_prompt,\n", "    allowed_nodes=[\"Folder\", \"File\", \"Class\", \"Function\", \"Table\", \"Column\", \"API\"],\n", "    allowed_relationships=[\n", "        (\"Folder\", \"CONTAINS\", \"File\"),\n", "        (\"File\", \"DECLARES\", \"Class\"),\n", "        (\"Class\", \"DECLARES\", \"Function\"),\n", "        (\"Function\", \"CALLS\", \"Function\"),\n", "        (\"Class\", \"INHERITS\", \"Class\"),\n", "        (\"Function\", \"READS_FROM\", \"Table\"),\n", "        (\"Function\", \"WRITES_TO\", \"Table\"),\n", "        (\"Function\", \"CALLS_API\", \"API\")\n", "    ],\n", "    strict_mode=True,\n", "    node_properties=False,\n", "    relationship_properties=False\n", ")\n"]}, {"cell_type": "code", "execution_count": 17, "id": "c67096c0", "metadata": {}, "outputs": [{"ename": "BadRequestError", "evalue": "Error code: 400 - {'error': {'code': 'BadRequest', 'message': 'response_format value as json_schema is enabled only for api versions 2024-08-01-preview and later'}}", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mBadRequestError\u001b[39m                           <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[17]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m graph_documents = \u001b[43mtransformer\u001b[49m\u001b[43m.\u001b[49m\u001b[43mconvert_to_graph_documents\u001b[49m\u001b[43m(\u001b[49m\u001b[43msplit_docs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\langchain_experimental\\graph_transformers\\llm.py:932\u001b[39m, in \u001b[36mLLMGraphTransformer.convert_to_graph_documents\u001b[39m\u001b[34m(self, documents, config)\u001b[39m\n\u001b[32m    920\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mconvert_to_graph_documents\u001b[39m(\n\u001b[32m    921\u001b[39m     \u001b[38;5;28mself\u001b[39m, documents: Sequence[Document], config: Optional[RunnableConfig] = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m    922\u001b[39m ) -> List[GraphDocument]:\n\u001b[32m    923\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"Convert a sequence of documents into graph documents.\u001b[39;00m\n\u001b[32m    924\u001b[39m \n\u001b[32m    925\u001b[39m \u001b[33;03m    Args:\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m    930\u001b[39m \u001b[33;03m        Sequence[GraphDocument]: The transformed documents as graphs.\u001b[39;00m\n\u001b[32m    931\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m932\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m[\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mprocess_response\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdocument\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mdocument\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mdocuments\u001b[49m\u001b[43m]\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\langchain_experimental\\graph_transformers\\llm.py:932\u001b[39m, in \u001b[36m<listcomp>\u001b[39m\u001b[34m(.0)\u001b[39m\n\u001b[32m    920\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mconvert_to_graph_documents\u001b[39m(\n\u001b[32m    921\u001b[39m     \u001b[38;5;28mself\u001b[39m, documents: Sequence[Document], config: Optional[RunnableConfig] = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m    922\u001b[39m ) -> List[GraphDocument]:\n\u001b[32m    923\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"Convert a sequence of documents into graph documents.\u001b[39;00m\n\u001b[32m    924\u001b[39m \n\u001b[32m    925\u001b[39m \u001b[33;03m    Args:\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m    930\u001b[39m \u001b[33;03m        Sequence[GraphDocument]: The transformed documents as graphs.\u001b[39;00m\n\u001b[32m    931\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m932\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m [\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mprocess_response\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdocument\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mfor\u001b[39;00m document \u001b[38;5;129;01min\u001b[39;00m documents]\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\langchain_experimental\\graph_transformers\\llm.py:839\u001b[39m, in \u001b[36mLLMGraphTransformer.process_response\u001b[39m\u001b[34m(self, document, config)\u001b[39m\n\u001b[32m    834\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m    835\u001b[39m \u001b[33;03mProcesses a single document, transforming it into a graph document using\u001b[39;00m\n\u001b[32m    836\u001b[39m \u001b[33;03man LLM based on the model's schema and constraints.\u001b[39;00m\n\u001b[32m    837\u001b[39m \u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m    838\u001b[39m text = document.page_content\n\u001b[32m--> \u001b[39m\u001b[32m839\u001b[39m raw_schema = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mchain\u001b[49m\u001b[43m.\u001b[49m\u001b[43minvoke\u001b[49m\u001b[43m(\u001b[49m\u001b[43m{\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43minput\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtext\u001b[49m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m=\u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    840\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m._function_call:\n\u001b[32m    841\u001b[39m     raw_schema = cast(Dict[Any, Any], raw_schema)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\langchain_core\\runnables\\base.py:3047\u001b[39m, in \u001b[36mRunnableSequence.invoke\u001b[39m\u001b[34m(self, input, config, **kwargs)\u001b[39m\n\u001b[32m   3045\u001b[39m                 input_ = context.run(step.invoke, input_, config, **kwargs)\n\u001b[32m   3046\u001b[39m             \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m3047\u001b[39m                 input_ = \u001b[43mcontext\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrun\u001b[49m\u001b[43m(\u001b[49m\u001b[43mstep\u001b[49m\u001b[43m.\u001b[49m\u001b[43minvoke\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43minput_\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   3048\u001b[39m \u001b[38;5;66;03m# finish the root run\u001b[39;00m\n\u001b[32m   3049\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\langchain_core\\runnables\\base.py:3774\u001b[39m, in \u001b[36mRunnableParallel.invoke\u001b[39m\u001b[34m(self, input, config, **kwargs)\u001b[39m\n\u001b[32m   3769\u001b[39m     \u001b[38;5;28;01mwith\u001b[39;00m get_executor_for_config(config) \u001b[38;5;28;01mas\u001b[39;00m executor:\n\u001b[32m   3770\u001b[39m         futures = [\n\u001b[32m   3771\u001b[39m             executor.submit(_invoke_step, step, \u001b[38;5;28minput\u001b[39m, config, key)\n\u001b[32m   3772\u001b[39m             \u001b[38;5;28;01mfor\u001b[39;00m key, step \u001b[38;5;129;01min\u001b[39;00m steps.items()\n\u001b[32m   3773\u001b[39m         ]\n\u001b[32m-> \u001b[39m\u001b[32m3774\u001b[39m         output = \u001b[43m{\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mfuture\u001b[49m\u001b[43m.\u001b[49m\u001b[43mresult\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mkey\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfuture\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mzip\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43msteps\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfutures\u001b[49m\u001b[43m)\u001b[49m\u001b[43m}\u001b[49m\n\u001b[32m   3775\u001b[39m \u001b[38;5;66;03m# finish the root run\u001b[39;00m\n\u001b[32m   3776\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\langchain_core\\runnables\\base.py:3774\u001b[39m, in \u001b[36m<dictcomp>\u001b[39m\u001b[34m(.0)\u001b[39m\n\u001b[32m   3769\u001b[39m     \u001b[38;5;28;01mwith\u001b[39;00m get_executor_for_config(config) \u001b[38;5;28;01mas\u001b[39;00m executor:\n\u001b[32m   3770\u001b[39m         futures = [\n\u001b[32m   3771\u001b[39m             executor.submit(_invoke_step, step, \u001b[38;5;28minput\u001b[39m, config, key)\n\u001b[32m   3772\u001b[39m             \u001b[38;5;28;01mfor\u001b[39;00m key, step \u001b[38;5;129;01min\u001b[39;00m steps.items()\n\u001b[32m   3773\u001b[39m         ]\n\u001b[32m-> \u001b[39m\u001b[32m3774\u001b[39m         output = {key: \u001b[43mfuture\u001b[49m\u001b[43m.\u001b[49m\u001b[43mresult\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mfor\u001b[39;00m key, future \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mzip\u001b[39m(steps, futures)}\n\u001b[32m   3775\u001b[39m \u001b[38;5;66;03m# finish the root run\u001b[39;00m\n\u001b[32m   3776\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n", "\u001b[36mFile \u001b[39m\u001b[32mC:\\Program Files\\Python311\\Lib\\concurrent\\futures\\_base.py:456\u001b[39m, in \u001b[36mFuture.result\u001b[39m\u001b[34m(self, timeout)\u001b[39m\n\u001b[32m    454\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m CancelledError()\n\u001b[32m    455\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28mself\u001b[39m._state == FINISHED:\n\u001b[32m--> \u001b[39m\u001b[32m456\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m__get_result\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    457\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    458\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTimeoutError\u001b[39;00m()\n", "\u001b[36mFile \u001b[39m\u001b[32mC:\\Program Files\\Python311\\Lib\\concurrent\\futures\\_base.py:401\u001b[39m, in \u001b[36mFuture.__get_result\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    399\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m._exception:\n\u001b[32m    400\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m401\u001b[39m         \u001b[38;5;28;01m<PERSON>se\u001b[39;00m \u001b[38;5;28mself\u001b[39m._exception\n\u001b[32m    402\u001b[39m     \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[32m    403\u001b[39m         \u001b[38;5;66;03m# Break a reference cycle with the exception in self._exception\u001b[39;00m\n\u001b[32m    404\u001b[39m         \u001b[38;5;28mself\u001b[39m = \u001b[38;5;28;01mNone\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32mC:\\Program Files\\Python311\\Lib\\concurrent\\futures\\thread.py:58\u001b[39m, in \u001b[36m_WorkItem.run\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m     55\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m\n\u001b[32m     57\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m---> \u001b[39m\u001b[32m58\u001b[39m     result = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mfn\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     59\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[32m     60\u001b[39m     \u001b[38;5;28mself\u001b[39m.future.set_exception(exc)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\langchain_core\\runnables\\base.py:3758\u001b[39m, in \u001b[36mRunnableParallel.invoke.<locals>._invoke_step\u001b[39m\u001b[34m(step, input_, config, key)\u001b[39m\n\u001b[32m   3752\u001b[39m child_config = patch_config(\n\u001b[32m   3753\u001b[39m     config,\n\u001b[32m   3754\u001b[39m     \u001b[38;5;66;03m# mark each step as a child run\u001b[39;00m\n\u001b[32m   3755\u001b[39m     callbacks=run_manager.get_child(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mmap:key:\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mkey\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m),\n\u001b[32m   3756\u001b[39m )\n\u001b[32m   3757\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m set_config_context(child_config) \u001b[38;5;28;01mas\u001b[39;00m context:\n\u001b[32m-> \u001b[39m\u001b[32m3758\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mcontext\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrun\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   3759\u001b[39m \u001b[43m        \u001b[49m\u001b[43mstep\u001b[49m\u001b[43m.\u001b[49m\u001b[43minvoke\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3760\u001b[39m \u001b[43m        \u001b[49m\u001b[43minput_\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3761\u001b[39m \u001b[43m        \u001b[49m\u001b[43mchild_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3762\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\langchain_core\\runnables\\base.py:5431\u001b[39m, in \u001b[36mRunnableBindingBase.invoke\u001b[39m\u001b[34m(self, input, config, **kwargs)\u001b[39m\n\u001b[32m   5424\u001b[39m \u001b[38;5;129m@override\u001b[39m\n\u001b[32m   5425\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34minvoke\u001b[39m(\n\u001b[32m   5426\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m   5429\u001b[39m     **kwargs: Optional[Any],\n\u001b[32m   5430\u001b[39m ) -> Output:\n\u001b[32m-> \u001b[39m\u001b[32m5431\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mbound\u001b[49m\u001b[43m.\u001b[49m\u001b[43minvoke\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   5432\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m   5433\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_merge_configs\u001b[49m\u001b[43m(\u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   5434\u001b[39m \u001b[43m        \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43m{\u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   5435\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py:378\u001b[39m, in \u001b[36mBaseChatModel.invoke\u001b[39m\u001b[34m(self, input, config, stop, **kwargs)\u001b[39m\n\u001b[32m    366\u001b[39m \u001b[38;5;129m@override\u001b[39m\n\u001b[32m    367\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34minvoke\u001b[39m(\n\u001b[32m    368\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m    373\u001b[39m     **kwargs: Any,\n\u001b[32m    374\u001b[39m ) -> BaseMessage:\n\u001b[32m    375\u001b[39m     config = ensure_config(config)\n\u001b[32m    376\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m cast(\n\u001b[32m    377\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mChatGeneration\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m--> \u001b[39m\u001b[32m378\u001b[39m         \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mgenerate_prompt\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    379\u001b[39m \u001b[43m            \u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_convert_input\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    380\u001b[39m \u001b[43m            \u001b[49m\u001b[43mstop\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstop\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    381\u001b[39m \u001b[43m            \u001b[49m\u001b[43mcallbacks\u001b[49m\u001b[43m=\u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mcallbacks\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    382\u001b[39m \u001b[43m            \u001b[49m\u001b[43mtags\u001b[49m\u001b[43m=\u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtags\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    383\u001b[39m \u001b[43m            \u001b[49m\u001b[43mmetadata\u001b[49m\u001b[43m=\u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmetadata\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    384\u001b[39m \u001b[43m            \u001b[49m\u001b[43mrun_name\u001b[49m\u001b[43m=\u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mrun_name\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    385\u001b[39m \u001b[43m            \u001b[49m\u001b[43mrun_id\u001b[49m\u001b[43m=\u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m.\u001b[49m\u001b[43mpop\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mrun_id\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    386\u001b[39m \u001b[43m            \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    387\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m.generations[\u001b[32m0\u001b[39m][\u001b[32m0\u001b[39m],\n\u001b[32m    388\u001b[39m     ).message\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py:963\u001b[39m, in \u001b[36mBaseChatModel.generate_prompt\u001b[39m\u001b[34m(self, prompts, stop, callbacks, **kwargs)\u001b[39m\n\u001b[32m    954\u001b[39m \u001b[38;5;129m@override\u001b[39m\n\u001b[32m    955\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mgenerate_prompt\u001b[39m(\n\u001b[32m    956\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m    960\u001b[39m     **kwargs: Any,\n\u001b[32m    961\u001b[39m ) -> LLMResult:\n\u001b[32m    962\u001b[39m     prompt_messages = [p.to_messages() \u001b[38;5;28;01mfor\u001b[39;00m p \u001b[38;5;129;01min\u001b[39;00m prompts]\n\u001b[32m--> \u001b[39m\u001b[32m963\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mgenerate\u001b[49m\u001b[43m(\u001b[49m\u001b[43mprompt_messages\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstop\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstop\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcallbacks\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcallbacks\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py:782\u001b[39m, in \u001b[36mBaseChatModel.generate\u001b[39m\u001b[34m(self, messages, stop, callbacks, tags, metadata, run_name, run_id, **kwargs)\u001b[39m\n\u001b[32m    779\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m i, m \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28menumerate\u001b[39m(input_messages):\n\u001b[32m    780\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    781\u001b[39m         results.append(\n\u001b[32m--> \u001b[39m\u001b[32m782\u001b[39m             \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_generate_with_cache\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    783\u001b[39m \u001b[43m                \u001b[49m\u001b[43mm\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    784\u001b[39m \u001b[43m                \u001b[49m\u001b[43mstop\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstop\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    785\u001b[39m \u001b[43m                \u001b[49m\u001b[43mrun_manager\u001b[49m\u001b[43m=\u001b[49m\u001b[43mrun_managers\u001b[49m\u001b[43m[\u001b[49m\u001b[43mi\u001b[49m\u001b[43m]\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mrun_managers\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01melse\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m    786\u001b[39m \u001b[43m                \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    787\u001b[39m \u001b[43m            \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    788\u001b[39m         )\n\u001b[32m    789\u001b[39m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m    790\u001b[39m         \u001b[38;5;28;01mif\u001b[39;00m run_managers:\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py:1028\u001b[39m, in \u001b[36mBaseChatModel._generate_with_cache\u001b[39m\u001b[34m(self, messages, stop, run_manager, **kwargs)\u001b[39m\n\u001b[32m   1026\u001b[39m     result = generate_from_stream(\u001b[38;5;28miter\u001b[39m(chunks))\n\u001b[32m   1027\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m inspect.signature(\u001b[38;5;28mself\u001b[39m._generate).parameters.get(\u001b[33m\"\u001b[39m\u001b[33mrun_manager\u001b[39m\u001b[33m\"\u001b[39m):\n\u001b[32m-> \u001b[39m\u001b[32m1028\u001b[39m     result = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_generate\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   1029\u001b[39m \u001b[43m        \u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstop\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstop\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrun_manager\u001b[49m\u001b[43m=\u001b[49m\u001b[43mrun_manager\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\n\u001b[32m   1030\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1031\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m   1032\u001b[39m     result = \u001b[38;5;28mself\u001b[39m._generate(messages, stop=stop, **kwargs)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\langchain_openai\\chat_models\\base.py:1105\u001b[39m, in \u001b[36mBaseChatOpenAI._generate\u001b[39m\u001b[34m(self, messages, stop, run_manager, **kwargs)\u001b[39m\n\u001b[32m   1103\u001b[39m         response = \u001b[38;5;28mself\u001b[39m.root_client.beta.chat.completions.parse(**payload)\n\u001b[32m   1104\u001b[39m     \u001b[38;5;28;01mexcept\u001b[39;00m openai.BadRequestError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m-> \u001b[39m\u001b[32m1105\u001b[39m         \u001b[43m_handle_openai_bad_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43me\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1106\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28mself\u001b[39m._use_responses_api(payload):\n\u001b[32m   1107\u001b[39m     original_schema_obj = kwargs.get(\u001b[33m\"\u001b[39m\u001b[33mresponse_format\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\langchain_openai\\chat_models\\base.py:1103\u001b[39m, in \u001b[36mBaseChatOpenAI._generate\u001b[39m\u001b[34m(self, messages, stop, run_manager, **kwargs)\u001b[39m\n\u001b[32m   1101\u001b[39m payload.pop(\u001b[33m\"\u001b[39m\u001b[33mstream\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m   1102\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m1103\u001b[39m     response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mroot_client\u001b[49m\u001b[43m.\u001b[49m\u001b[43mbeta\u001b[49m\u001b[43m.\u001b[49m\u001b[43mchat\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcompletions\u001b[49m\u001b[43m.\u001b[49m\u001b[43mparse\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mpayload\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1104\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m openai.BadRequestError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m   1105\u001b[39m     _handle_openai_bad_request(e)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\openai\\resources\\chat\\completions\\completions.py:180\u001b[39m, in \u001b[36mCompletions.parse\u001b[39m\u001b[34m(self, messages, model, audio, response_format, frequency_penalty, function_call, functions, logit_bias, logprobs, max_completion_tokens, max_tokens, metadata, modalities, n, parallel_tool_calls, prediction, presence_penalty, reasoning_effort, seed, service_tier, stop, store, stream_options, temperature, tool_choice, tools, top_logprobs, top_p, user, web_search_options, extra_headers, extra_query, extra_body, timeout)\u001b[39m\n\u001b[32m    173\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mparser\u001b[39m(raw_completion: ChatCompletion) -> ParsedChatCompletion[ResponseFormatT]:\n\u001b[32m    174\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m _parse_chat_completion(\n\u001b[32m    175\u001b[39m         response_format=response_format,\n\u001b[32m    176\u001b[39m         chat_completion=raw_completion,\n\u001b[32m    177\u001b[39m         input_tools=tools,\n\u001b[32m    178\u001b[39m     )\n\u001b[32m--> \u001b[39m\u001b[32m180\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_post\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    181\u001b[39m \u001b[43m    \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m/chat/completions\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m    182\u001b[39m \u001b[43m    \u001b[49m\u001b[43mbody\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmaybe_transform\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    183\u001b[39m \u001b[43m        \u001b[49m\u001b[43m{\u001b[49m\n\u001b[32m    184\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmessages\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    185\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmodel\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    186\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43maudio\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43maudio\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    187\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mfrequency_penalty\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mfrequency_penalty\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    188\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mfunction_call\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mfunction_call\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    189\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mfunctions\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mfunctions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    190\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mlogit_bias\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mlogit_bias\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    191\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mlogprobs\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mlogprobs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    192\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmax_completion_tokens\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmax_completion_tokens\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    193\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmax_tokens\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmax_tokens\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    194\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmetadata\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmetadata\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    195\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmodalities\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmodalities\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    196\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mn\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    197\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mparallel_tool_calls\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mparallel_tool_calls\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    198\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mprediction\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mprediction\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    199\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mpresence_penalty\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mpresence_penalty\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    200\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mreasoning_effort\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mreasoning_effort\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    201\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mresponse_format\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43m_type_to_response_format\u001b[49m\u001b[43m(\u001b[49m\u001b[43mresponse_format\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    202\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mseed\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mseed\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    203\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mservice_tier\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mservice_tier\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    204\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstop\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstop\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    205\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstore\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstore\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    206\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstream\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m    207\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstream_options\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    208\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtemperature\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtemperature\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    209\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtool_choice\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtool_choice\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    210\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtools\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtools\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    211\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtop_logprobs\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtop_logprobs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    212\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtop_p\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtop_p\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    213\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43muser\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43muser\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    214\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mweb_search_options\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mweb_search_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    215\u001b[39m \u001b[43m        \u001b[49m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    216\u001b[39m \u001b[43m        \u001b[49m\u001b[43mcompletion_create_params\u001b[49m\u001b[43m.\u001b[49m\u001b[43mCompletionCreateParams\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    217\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    218\u001b[39m \u001b[43m    \u001b[49m\u001b[43moptions\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmake_request_options\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    219\u001b[39m \u001b[43m        \u001b[49m\u001b[43mextra_headers\u001b[49m\u001b[43m=\u001b[49m\u001b[43mextra_headers\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    220\u001b[39m \u001b[43m        \u001b[49m\u001b[43mextra_query\u001b[49m\u001b[43m=\u001b[49m\u001b[43mextra_query\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    221\u001b[39m \u001b[43m        \u001b[49m\u001b[43mextra_body\u001b[49m\u001b[43m=\u001b[49m\u001b[43mextra_body\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    222\u001b[39m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    223\u001b[39m \u001b[43m        \u001b[49m\u001b[43mpost_parser\u001b[49m\u001b[43m=\u001b[49m\u001b[43mparser\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    224\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    225\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;66;43;03m# we turn the `ChatCompletion` instance into a `ParsedChatCompletion`\u001b[39;49;00m\n\u001b[32m    226\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;66;43;03m# in the `parser` function above\u001b[39;49;00m\n\u001b[32m    227\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcast\u001b[49m\u001b[43m(\u001b[49m\u001b[43mType\u001b[49m\u001b[43m[\u001b[49m\u001b[43mParsedChatCompletion\u001b[49m\u001b[43m[\u001b[49m\u001b[43mResponseFormatT\u001b[49m\u001b[43m]\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mChatCompletion\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    228\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m    229\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\openai\\_base_client.py:1249\u001b[39m, in \u001b[36mSyncAPIClient.post\u001b[39m\u001b[34m(self, path, cast_to, body, options, files, stream, stream_cls)\u001b[39m\n\u001b[32m   1235\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mpost\u001b[39m(\n\u001b[32m   1236\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   1237\u001b[39m     path: \u001b[38;5;28mstr\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m   1244\u001b[39m     stream_cls: \u001b[38;5;28mtype\u001b[39m[_StreamT] | \u001b[38;5;28;01mNone\u001b[39;00m = \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[32m   1245\u001b[39m ) -> ResponseT | _StreamT:\n\u001b[32m   1246\u001b[39m     opts = FinalRequestOptions.construct(\n\u001b[32m   1247\u001b[39m         method=\u001b[33m\"\u001b[39m\u001b[33mpost\u001b[39m\u001b[33m\"\u001b[39m, url=path, json_data=body, files=to_httpx_files(files), **options\n\u001b[32m   1248\u001b[39m     )\n\u001b[32m-> \u001b[39m\u001b[32m1249\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m cast(ResponseT, \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mopts\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m)\u001b[49m)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Shaik\\sample\\venv\\Lib\\site-packages\\openai\\_base_client.py:1037\u001b[39m, in \u001b[36mSyncAPIClient.request\u001b[39m\u001b[34m(self, cast_to, options, stream, stream_cls)\u001b[39m\n\u001b[32m   1034\u001b[39m             err.response.read()\n\u001b[32m   1036\u001b[39m         log.debug(\u001b[33m\"\u001b[39m\u001b[33mRe-raising status error\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m-> \u001b[39m\u001b[32m1037\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;28mself\u001b[39m._make_status_error_from_response(err.response) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mN<PERSON>\u001b[39;00m\n\u001b[32m   1039\u001b[39m     \u001b[38;5;28;01mbreak\u001b[39;00m\n\u001b[32m   1041\u001b[39m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m response \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON>one\u001b[39;00m, \u001b[33m\"\u001b[39m\u001b[33mcould not resolve response (should never happen)\u001b[39m\u001b[33m\"\u001b[39m\n", "\u001b[31mBadRequestError\u001b[39m: Error code: 400 - {'error': {'code': 'BadRequest', 'message': 'response_format value as json_schema is enabled only for api versions 2024-08-01-preview and later'}}"]}], "source": ["graph_documents = transformer.convert_to_graph_documents(split_docs)\n"]}, {"cell_type": "markdown", "id": "c1350a73", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 19, "id": "19a64fcc", "metadata": {}, "outputs": [{"data": {"text/plain": ["[GraphDocument(nodes=[Node(id='Com/Morganstanley/Loanapp', type='Folder', properties={}), Node(id='Loanappapplication.Java', type='File', properties={}), Node(id='Loanappapplication', type='Class', properties={}), Node(id='Printdsinfo', type='Function', properties={}), Node(id='Main', type='Function', properties={}), Node(id='Springapplication.Run', type='Function', properties={})], relationships=[Relationship(source=Node(id='Com/Morganstanley/Loanapp', type='Folder', properties={}), target=Node(id='Loanappapplication.Java', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='Loanappapplication.Java', type='File', properties={}), target=Node(id='Loanappapplication', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Loanappapplication', type='Class', properties={}), target=Node(id='Printdsinfo', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Loanappapplication', type='Class', properties={}), target=Node(id='Main', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Main', type='Function', properties={}), target=Node(id='Springapplication.Run', type='Function', properties={}), type='CALLS', properties={})], source=Document(metadata={'source': 'java\\\\LoanAppApplication.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp;\\n\\nimport jakarta.annotation.PostConstruct;\\nimport org.springframework.beans.factory.annotation.Autowired;\\nimport org.springframework.beans.factory.annotation.Qualifier;\\nimport org.springframework.boot.SpringApplication;\\nimport org.springframework.boot.autoconfigure.SpringBootApplication;\\nimport org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;\\nimport org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;\\nimport org.springframework.boot.context.properties.EnableConfigurationProperties;\\nimport org.springframework.context.ConfigurableApplicationContext;\\nimport org.springframework.core.env.Environment;\\n\\nimport javax.sql.DataSource;\\nimport java.sql.SQLException;\\n\\n@SpringBootApplication\\n@EnableConfigurationProperties\\npublic class LoanAppApplication {\\n\\n\\t@Autowired\\n\\t@Qualifier(\"dataSource\")\\n\\tprivate DataSource dataSource; // primary\\n\\n\\t@Autowired\\n\\t@Qualifier(\"storeProcDataSource\")\\n\\tprivate DataSource secondDataSource;\\n\\n\\n\\t@PostConstruct\\n\\tpublic void printDsInfo() throws SQLException {\\n\\t\\tSystem.out.println(\"Primary DataSource connection URL: \" + dataSource.getConnection().getMetaData().getURL());\\n\\t\\tSystem.out.println(\"Secondary DataSource connection URL: \" + secondDataSource.getConnection().getMetaData().getURL());\\n\\t}\\n\\tpublic static void main(String[] args) {\\n\\t\\t SpringApplication.run(LoanAppApplication.class, args);\\n\\n\\t}\\n\\n}')),\n", " GraphDocument(nodes=[Node(id='Com/Morganstanley/Loanapp/Configuration', type='Folder', properties={}), Node(id='Primarydatasourceproperties.Java', type='File', properties={}), Node(id='Primarydatasourceproperties', type='Class', properties={}), Node(id='Geturl', type='Function', properties={}), Node(id='Seturl', type='Function', properties={}), Node(id='Getusername', type='Function', properties={}), Node(id='Setusername', type='Function', properties={}), Node(id='Getpassword', type='Function', properties={}), Node(id='Setpassword', type='Function', properties={}), Node(id='Getdriverclassname', type='Function', properties={}), Node(id='Setdriverclassname', type='Function', properties={}), Node(id='Printprops', type='Function', properties={})], relationships=[Relationship(source=Node(id='Com/Morganstanley/Loanapp/Configuration', type='Folder', properties={}), target=Node(id='Primarydatasourceproperties.Java', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='Primarydatasourceproperties.Java', type='File', properties={}), target=Node(id='Primarydatasourceproperties', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Primarydatasourceproperties', type='Class', properties={}), target=Node(id='Geturl', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Primarydatasourceproperties', type='Class', properties={}), target=Node(id='Seturl', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Primarydatasourceproperties', type='Class', properties={}), target=Node(id='Getusername', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Primarydatasourceproperties', type='Class', properties={}), target=Node(id='Setusername', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Primarydatasourceproperties', type='Class', properties={}), target=Node(id='Getpassword', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Primarydatasourceproperties', type='Class', properties={}), target=Node(id='Setpassword', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Primarydatasourceproperties', type='Class', properties={}), target=Node(id='Getdriverclassname', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Primarydatasourceproperties', type='Class', properties={}), target=Node(id='Setdriverclassname', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Primarydatasourceproperties', type='Class', properties={}), target=Node(id='Printprops', type='Function', properties={}), type='DECLARES', properties={})], source=Document(metadata={'source': 'java\\\\configuration\\\\PrimaryDataSourceProperties.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp.configuration;\\n\\nimport jakarta.annotation.PostConstruct;\\nimport org.springframework.boot.context.properties.ConfigurationProperties;\\nimport org.springframework.stereotype.Component;\\n\\n@Component\\n@ConfigurationProperties(prefix = \"spring.datasource\")\\npublic class PrimaryDataSourceProperties {\\n    public String getUrl() {\\n        return url;\\n    }\\n\\n    public void setUrl(String url) {\\n        this.url = url;\\n    }\\n\\n    public String getUsername() {\\n        return username;\\n    }\\n\\n    public void setUsername(String username) {\\n        this.username = username;\\n    }\\n\\n    public String getPassword() {\\n        return password;\\n    }\\n\\n    public void setPassword(String password) {\\n        this.password = password;\\n    }\\n\\n    public String getDriverClassName() {\\n        return driverClassName;\\n    }\\n\\n    public void setDriverClassName(String driverClassName) {\\n        this.driverClassName = driverClassName;\\n    }\\n\\n    private String url;\\n    private String username;\\n    private String password;\\n    private String driverClassName;\\n    @PostConstruct\\n    public void printProps() {\\n        System.out.println(\"Primary DS Props:\");\\n        System.out.println(\"URL: \" + url);\\n        System.out.println(\"Username: \" + username);\\n        System.out.println(\"Driver: \" + driverClassName);\\n    }\\n\\n\\n}')),\n", " GraphDocument(nodes=[Node(id='Com/Morganstanley/Loanapp/Configuration', type='Folder', properties={}), Node(id='Seconddatasourceproperties.Java', type='File', properties={}), Node(id='Seconddatasourceproperties', type='Class', properties={}), Node(id='Geturl', type='Function', properties={}), Node(id='Seturl', type='Function', properties={}), Node(id='Getusername', type='Function', properties={}), Node(id='Setusername', type='Function', properties={}), Node(id='Getpassword', type='Function', properties={}), Node(id='Setpassword', type='Function', properties={}), Node(id='Getdriverclassname', type='Function', properties={}), Node(id='Setdriverclassname', type='Function', properties={}), Node(id='Printprops', type='Function', properties={})], relationships=[Relationship(source=Node(id='Com/Morganstanley/Loanapp/Configuration', type='Folder', properties={}), target=Node(id='Seconddatasourceproperties.Java', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='Seconddatasourceproperties.Java', type='File', properties={}), target=Node(id='Seconddatasourceproperties', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Geturl', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Seturl', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Getusername', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Setusername', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Getpassword', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Setpassword', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Getdriverclassname', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Setdriverclassname', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Printprops', type='Function', properties={}), type='DECLARES', properties={})], source=Document(metadata={'source': 'java\\\\configuration\\\\SecondDataSourceProperties.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp.configuration;\\n\\nimport jakarta.annotation.PostConstruct;\\nimport org.springframework.boot.context.properties.ConfigurationProperties;\\nimport org.springframework.stereotype.Component;\\n\\n@Component\\n@ConfigurationProperties(prefix = \"sp.second-datasource\")\\npublic class SecondDataSourceProperties {\\n    public String getUrl() {\\n        return url;\\n    }\\n\\n    public void setUrl(String url) {\\n        this.url = url;\\n    }\\n\\n    public String getUsername() {\\n        return username;\\n    }\\n\\n    public void setUsername(String username) {\\n        this.username = username;\\n    }\\n\\n    public String getPassword() {\\n        return password;\\n    }\\n\\n    public void setPassword(String password) {\\n        this.password = password;\\n    }\\n\\n    public String getDriverClassName() {\\n        return driverClassName;\\n    }\\n\\n    public void setDriverClassName(String driverClassName) {\\n        this.driverClassName = driverClassName;\\n    }\\n\\n    private String url;\\n    private String username;\\n    private String password;\\n    private String driverClassName;\\n    @PostConstruct\\n    public void printProps() {\\n        System.out.println(\"Secondary DS Props:\");\\n        System.out.println(\"URL: \" + url);\\n        System.out.println(\"Username: \" + username);\\n        System.out.println(\"Driver: \" + driverClassName);\\n    }\\n\\n\\n}')),\n", " GraphDocument(nodes=[Node(id='Storedprocdbconfig', type='Class', properties={}), Node(id='Primarydatasource', type='Function', properties={}), Node(id='Storeprocdatasource', type='Function', properties={}), Node(id='Storeprocjdbctemplate', type='Function', properties={})], relationships=[Relationship(source=Node(id='Storedprocdbconfig', type='Class', properties={}), target=Node(id='Primarydatasource', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Storedprocdbconfig', type='Class', properties={}), target=Node(id='Storeprocdatasource', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Storedprocdbconfig', type='Class', properties={}), target=Node(id='Storeprocjdbctemplate', type='Function', properties={}), type='DECLARES', properties={})], source=Document(metadata={'source': 'java\\\\configuration\\\\StoredProcDBConfig.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp.configuration;\\nimport javax.sql.DataSource;\\n\\nimport com.zaxxer.hikari.HikariDataSource;\\nimport org.springframework.beans.factory.annotation.Qualifier;\\nimport org.springframework.boot.context.properties.ConfigurationProperties;\\nimport org.springframework.boot.jdbc.DataSourceBuilder;\\nimport org.springframework.context.annotation.Bean;\\nimport org.springframework.context.annotation.Configuration;\\nimport org.springframework.context.annotation.Primary;\\nimport org.springframework.jdbc.core.JdbcTemplate;\\n\\n@Configuration\\npublic class StoredProcDBConfig {\\n    @Bean\\n    @Qualifier(\"dataSource\")\\n    @Primary\\n    public DataSource primaryDataSource(PrimaryDataSourceProperties props) {\\n        HikariDataSource ds = new HikariDataSource();\\n        ds.setJdbcUrl(props.getUrl());\\n        ds.setUsername(props.getUsername());\\n        ds.setPassword(props.getPassword());\\n        ds.setDriverClassName(props.getDriverClassName());\\n\\n        System.out.println(\">>> Primary DataSource initialized with URL: \" + props.getUrl());\\n\\n        return ds;\\n    }\\n\\n    @Bean(name = \"storeProcDataSource\")\\n    public DataSource storeProcDataSource(SecondDataSourceProperties props) {\\n        HikariDataSource ds = new HikariDataSource();\\n        ds.setJdbcUrl(props.getUrl());\\n        ds.setUsername(props.getUsername());\\n        ds.setPassword(props.getPassword());\\n        ds.setDriverClassName(props.getDriverClassName());\\n\\n        System.out.println(\">>> Second DataSource initialized with URL: \" + props.getUrl());\\n\\n        return ds;\\n    }\\n\\n    @Bean\\n    public JdbcTemplate storeProcJdbcTemplate(@Qualifier(\"storeProcDataSource\") DataSource ds) {\\n        return new JdbcTemplate(ds);\\n    }\\n}')),\n", " GraphDocument(nodes=[Node(id='Com/Morganstanley/Loanapp/Controller', type='Folder', properties={}), Node(id='Loanappcontroller.Java', type='File', properties={}), Node(id='Loanappcontroller', type='Class', properties={})], relationships=[Relationship(source=Node(id='Com/Morganstanley/Loanapp/Controller', type='Folder', properties={}), target=Node(id='Loanappcontroller.Java', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='Loanappcontroller.Java', type='File', properties={}), target=Node(id='Loanappcontroller', type='Class', properties={}), type='DECLARES', properties={})], source=Document(metadata={'source': 'java\\\\controller\\\\UserController.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp.controller;\\n\\nimport com.morganstanley.loanApp.dto.UserLoanRequestDTO;\\nimport com.morganstanley.loanApp.model.User;\\n\\nimport com.morganstanley.loanApp.service.StoreProcService;\\nimport com.morganstanley.loanApp.service.UserService;\\nimport jakarta.validation.Valid;\\nimport org.springframework.beans.factory.annotation.Autowired;\\nimport org.springframework.http.HttpStatus;\\nimport org.springframework.http.ResponseEntity;\\nimport org.springframework.web.bind.annotation.PostMapping;\\nimport org.springframework.web.bind.annotation.RequestBody;\\nimport org.springframework.web.bind.annotation.RequestMapping;\\nimport org.springframework.web.bind.annotation.RestController;\\n\\nimport java.io.*;\\nimport java.util.ArrayList;\\nimport java.util.List;\\n\\n@RestController\\n@RequestMapping(\"/loanApp\")')),\n", " GraphDocument(nodes=[Node(id='Usercontroller', type='Class', properties={}), Node(id='Createuserwithloan', type='Function', properties={}), Node(id='Saveuserloanfromfile', type='Function', properties={}), Node(id='/Createloan', type='Api', properties={}), Node(id='/Save-From-File', type='Api', properties={}), Node(id='Userservice.Createuserwithloan', type='Function', properties={})], relationships=[Relationship(source=Node(id='Usercontroller', type='Class', properties={}), target=Node(id='Createuserwithloan', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Usercontroller', type='Class', properties={}), target=Node(id='Saveuserloanfromfile', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Createuserwithloan', type='Function', properties={}), target=Node(id='/Createloan', type='Api', properties={}), type='CALLS_API', properties={}), Relationship(source=Node(id='Saveuserloanfromfile', type='Function', properties={}), target=Node(id='/Save-From-File', type='Api', properties={}), type='CALLS_API', properties={}), Relationship(source=Node(id='Createuserwithloan', type='Function', properties={}), target=Node(id='Userservice.Createuserwithloan', type='Function', properties={}), type='CALLS', properties={}), Relationship(source=Node(id='Saveuserloanfromfile', type='Function', properties={}), target=Node(id='Userservice.Createuserwithloan', type='Function', properties={}), type='CALLS', properties={})], source=Document(metadata={'source': 'java\\\\controller\\\\UserController.java', 'language': 'java', 'chunk_index': 1}, page_content='public class UserController {\\n    @Autowired\\n    private UserService userService;\\n\\n    @Autowired\\n    private StoreProcService storeProcService;\\n\\n    @PostMapping(\"/createLoan\")\\n    public ResponseEntity<User> createUserWithLoan(@Valid @RequestBody UserLoanRequestDTO dto) {\\n        User savedUser = userService.createUserWithLoan(dto);\\n        return new ResponseEntity<>(savedUser, HttpStatus.CREATED);\\n    }\\n\\n    @PostMapping(\"/save-from-file\")\\n    public ResponseEntity<String> saveUserLoanFromFile(){\\n        try (InputStream is = getClass().getClassLoader().getResourceAsStream(\"usersData.txt\");\\n             BufferedReader reader = new BufferedReader(new InputStreamReader(is))) {\\n\\n            if (is == null) {\\n                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)\\n                        .body(\"Data file not found\");\\n            }\\n\\n            String line ;\\n            while ((line = reader.readLine()) != null) {\\n                String[] parts = line.split(\",\");\\n                if (parts.length != 4) {\\n                    return ResponseEntity.badRequest()\\n                            .body(\"Invalid file format\");\\n                }\\n\\n\\n                UserLoanRequestDTO userDTO = new UserLoanRequestDTO();\\n                userDTO.setName(parts[0].trim());\\n                userDTO.setEmail(parts[1].trim());\\n                userDTO.setLoanType(parts[2].trim());\\n                userDTO.setAmount(Double.valueOf(parts[3].trim()));\\n\\n                userService.createUserWithLoan(userDTO);\\n            }\\n            return ResponseEntity.ok(\"Loan application saved from flat file\");\\n\\n        } catch (Exception e) {\\n            e.printStackTrace();\\n            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)\\n                    .body(\"Error processing flat file\");\\n        }\\n    }')),\n", " GraphDocument(nodes=[Node(id='Readcsvandcallstoreproc', type='Function', properties={}), Node(id='Callstoreproc', type='Function', properties={})], relationships=[Relationship(source=Node(id='Readcsvandcallstoreproc', type='Function', properties={}), target=Node(id='Callstoreproc', type='Function', properties={}), type='CALLS', properties={})], source=Document(metadata={'source': 'java\\\\controller\\\\UserController.java', 'language': 'java', 'chunk_index': 2}, page_content='@PostMapping(\"/processStoreProcFromCSV\")\\n    public ResponseEntity<List<String>> readCSVAndCallStoreProc(){\\n        List<String> outputMessages = new ArrayList<>();\\n\\n        try (InputStream is = getClass().getClassLoader().getResourceAsStream(\"branchesData.csv\");\\n             BufferedReader reader = new BufferedReader(new InputStreamReader(is))) {\\n            String line;\\n            boolean isFirstLine = true;\\n\\n            while ((line = reader.readLine()) != null) {\\n                if (isFirstLine) {\\n                    isFirstLine = false; // skip header\\n                    continue;\\n                }\\n\\n                String[] parts = line.split(\",\");\\n                if (parts.length != 2) continue;\\n\\n                String branchName = parts[0].trim();\\n                String branchAddress = parts[1].trim();\\n\\n                Integer result = storeProcService.callStoreProc(branchName, branchAddress);\\n                outputMessages.add(\"Branch Id for \" + branchName + \" is: \" + result);\\n            }\\n\\n            return ResponseEntity.ok(outputMessages);\\n        } catch (IOException | NumberFormatException ex) {\\n            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)\\n                    .body(List.of(\"Error reading file or parsing data: \" + ex.getMessage()));\\n        }\\n    }\\n\\n\\n}')),\n", " GraphDocument(nodes=[Node(id='Com/Morganstanley/Loanapp/Dto', type='Folder', properties={}), Node(id='Userloanrequestdto.Java', type='File', properties={}), Node(id='Userloanrequestdto', type='Class', properties={}), Node(id='Getname', type='Function', properties={}), Node(id='Setname', type='Function', properties={}), Node(id='Getemail', type='Function', properties={}), Node(id='Setemail', type='Function', properties={}), Node(id='Getloantype', type='Function', properties={}), Node(id='Setloantype', type='Function', properties={}), Node(id='Getamount', type='Function', properties={}), Node(id='Setamount', type='Function', properties={})], relationships=[Relationship(source=Node(id='Com/Morganstanley/Loanapp/Dto', type='Folder', properties={}), target=Node(id='Userloanrequestdto.Java', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='Userloanrequestdto.Java', type='File', properties={}), target=Node(id='Userloanrequestdto', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Userloanrequestdto', type='Class', properties={}), target=Node(id='Getname', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Userloanrequestdto', type='Class', properties={}), target=Node(id='Setname', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Userloanrequestdto', type='Class', properties={}), target=Node(id='Getemail', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Userloanrequestdto', type='Class', properties={}), target=Node(id='Setemail', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Userloanrequestdto', type='Class', properties={}), target=Node(id='Getloantype', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Userloanrequestdto', type='Class', properties={}), target=Node(id='Setloantype', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Userloanrequestdto', type='Class', properties={}), target=Node(id='Getamount', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Userloanrequestdto', type='Class', properties={}), target=Node(id='Setamount', type='Function', properties={}), type='DECLARES', properties={})], source=Document(metadata={'source': 'java\\\\dto\\\\UserLoanRequestDTO.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp.dto;\\n\\nimport jakarta.validation.constraints.Email;\\nimport jakarta.validation.constraints.NotBlank;\\nimport jakarta.validation.constraints.NotNull;\\nimport jakarta.validation.constraints.Positive;\\nimport lombok.Data;\\nimport lombok.Getter;\\nimport lombok.Setter;\\n\\n\\n\\npublic class UserLoanRequestDTO {\\n\\n    public String getName() {\\n        return name;\\n    }\\n\\n    public void setName(String name) {\\n        this.name = name;\\n    }\\n\\n    public String getEmail() {\\n        return email;\\n    }\\n\\n    public void setEmail(String email) {\\n        this.email = email;\\n    }\\n\\n    public String getLoanType() {\\n        return loanType;\\n    }\\n\\n    public void setLoanType(String loanType) {\\n        this.loanType = loanType;\\n    }\\n\\n    public Double getAmount() {\\n        return amount;\\n    }\\n\\n    public void setAmount(Double amount) {\\n        this.amount = amount;\\n    }\\n\\n    @NotBlank(message = \"Name is required\")\\n    private String name;\\n\\n    @Email(message = \"Email should be valid\")\\n    @NotBlank(message = \"Email is required\")\\n    private String email;\\n\\n    @NotBlank(message = \"Loan type is required\")\\n    private String loanType;\\n\\n    @NotNull(message = \"Loan amount is required\")\\n    @Positive(message = \"Loan amount must be positive\")\\n    private Double amount;\\n}')),\n", " GraphDocument(nodes=[Node(id='Com/Morganstanley/Loanapp/Model', type='Folder', properties={}), Node(id='Loandetails.Java', type='File', properties={}), Node(id='Loandetails', type='Class', properties={}), Node(id='Loan_Details', type='Table', properties={}), Node(id='Getid', type='Function', properties={}), Node(id='Getloantype', type='Function', properties={}), Node(id='Getamount', type='Function', properties={}), Node(id='Getuserid', type='Function', properties={}), Node(id='Setid', type='Function', properties={}), Node(id='Setloantype', type='Function', properties={}), Node(id='Setamount', type='Function', properties={}), Node(id='Setuserid', type='Function', properties={})], relationships=[Relationship(source=Node(id='Com/Morganstanley/Loanapp/Model', type='Folder', properties={}), target=Node(id='Loandetails.Java', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='Loandetails.Java', type='File', properties={}), target=Node(id='Loandetails', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Loandetails', type='Class', properties={}), target=Node(id='Getid', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Loandetails', type='Class', properties={}), target=Node(id='Getloantype', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Loandetails', type='Class', properties={}), target=Node(id='Getamount', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Loandetails', type='Class', properties={}), target=Node(id='Getuserid', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Loandetails', type='Class', properties={}), target=Node(id='Setid', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Loandetails', type='Class', properties={}), target=Node(id='Setloantype', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Loandetails', type='Class', properties={}), target=Node(id='Setamount', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Loandetails', type='Class', properties={}), target=Node(id='Setuserid', type='Function', properties={}), type='DECLARES', properties={})], source=Document(metadata={'source': 'java\\\\model\\\\LoanDetails.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp.model;\\n\\nimport jakarta.persistence.*;\\nimport lombok.Data;\\n\\n@Entity\\n@Table(name = \"loan_details\")\\n@Data\\npublic class LoanDetails {\\n\\n    @Id\\n    @GeneratedValue(strategy = GenerationType.IDENTITY)\\n    private Long id;\\n\\n    private String loanType;\\n    private Double amount;\\n\\n    @Column(name = \"user_id\")\\n    private Long userId;\\n\\n\\n\\n}')),\n", " GraphDocument(nodes=[Node(id='Com/Morganstanley/Loanapp/Model', type='Folder', properties={}), Node(id='User.Java', type='File', properties={}), Node(id='User', type='Class', properties={}), Node(id='Users', type='Table', properties={})], relationships=[Relationship(source=Node(id='Com/Morganstanley/Loanapp/Model', type='Folder', properties={}), target=Node(id='User.Java', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='User.Java', type='File', properties={}), target=Node(id='User', type='Class', properties={}), type='DECLARES', properties={})], source=Document(metadata={'source': 'java\\\\model\\\\User.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp.model;\\n\\nimport jakarta.persistence.*;\\nimport lombok.Data;\\n\\n@Entity\\n@Table(name = \"users\")\\n@Data\\npublic class User {\\n\\n    @Id\\n    @GeneratedValue(strategy = GenerationType.IDENTITY)\\n    private Long id;\\n\\n    private String name;\\n    private String email;\\n}')),\n", " GraphDocument(nodes=[Node(id='Com/Morganstanley/Loanapp/Repository', type='Folder', properties={}), Node(id='Loandetailsrepository.Java', type='File', properties={}), Node(id='Loandetailsrepository', type='Class', properties={}), Node(id='Jparepository', type='Class', properties={}), Node(id='Save', type='Function', properties={}), Node(id='Findall', type='Function', properties={}), Node(id='Loandetails', type='Table', properties={})], relationships=[Relationship(source=Node(id='Com/Morganstanley/Loanapp/Repository', type='Folder', properties={}), target=Node(id='Loandetailsrepository.Java', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='Loandetailsrepository.Java', type='File', properties={}), target=Node(id='Loandetailsrepository', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Loandetailsrepository', type='Class', properties={}), target=Node(id='Jparepository', type='Class', properties={}), type='INHERITS', properties={}), Relationship(source=Node(id='Loandetailsrepository', type='Class', properties={}), target=Node(id='Save', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Loandetailsrepository', type='Class', properties={}), target=Node(id='Findall', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Save', type='Function', properties={}), target=Node(id='Loandetails', type='Table', properties={}), type='WRITES_TO', properties={}), Relationship(source=Node(id='Findall', type='Function', properties={}), target=Node(id='Loandetails', type='Table', properties={}), type='READS_FROM', properties={})], source=Document(metadata={'source': 'java\\\\repository\\\\LoanDetailsRepository.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp.repository;\\n\\nimport com.morganstanley.loanApp.model.LoanDetails;\\nimport org.springframework.data.jpa.repository.JpaRepository;\\n\\npublic interface LoanDetailsRepository extends JpaRepository<LoanDetails,Long> {\\n}')),\n", " GraphDocument(nodes=[Node(id='Com/Morganstanley/Loanapp/Repository', type='Folder', properties={}), Node(id='Userrepository.Java', type='File', properties={}), Node(id='Userrepository', type='Class', properties={}), Node(id='Jparepository', type='Class', properties={}), Node(id='User', type='Class', properties={})], relationships=[Relationship(source=Node(id='Com/Morganstanley/Loanapp/Repository', type='Folder', properties={}), target=Node(id='Userrepository.Java', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='Userrepository.Java', type='File', properties={}), target=Node(id='Userrepository', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Userrepository', type='Class', properties={}), target=Node(id='Jparepository', type='Class', properties={}), type='INHERITS', properties={})], source=Document(metadata={'source': 'java\\\\repository\\\\UserRepository.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp.repository;\\n\\nimport com.morganstanley.loanApp.model.User;\\nimport org.springframework.data.jpa.repository.JpaRepository;\\n\\n\\npublic interface UserRepository extends JpaRepository<User, Long> {\\n}')),\n", " GraphDocument(nodes=[Node(id='Com/Morganstanley/Loanapp/Service', type='Folder', properties={}), Node(id='Storeprocservice.Java', type='File', properties={}), Node(id='Storeprocservice', type='Class', properties={}), Node(id='Callstoreproc', type='Function', properties={}), Node(id='Branch', type='Table', properties={})], relationships=[Relationship(source=Node(id='Com/Morganstanley/Loanapp/Service', type='Folder', properties={}), target=Node(id='Storeprocservice.Java', type='File', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='Storeprocservice.Java', type='File', properties={}), target=Node(id='Storeprocservice', type='Class', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Storeprocservice', type='Class', properties={}), target=Node(id='Callstoreproc', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Callstoreproc', type='Function', properties={}), target=Node(id='Branch', type='Table', properties={}), type='WRITES_TO', properties={})], source=Document(metadata={'source': 'java\\\\service\\\\StoreProcService.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp.service;\\n\\nimport org.springframework.beans.factory.annotation.Autowired;\\nimport org.springframework.beans.factory.annotation.Qualifier;\\nimport org.springframework.jdbc.core.JdbcTemplate;\\nimport org.springframework.stereotype.Service;\\n\\nimport java.sql.CallableStatement;\\nimport java.sql.Connection;\\nimport java.sql.Types;\\n\\n@Service\\npublic class StoreProcService {\\n\\n\\n    @Autowired\\n    @Qualifier(\"storeProcJdbcTemplate\")\\n    private JdbcTemplate storeProcTemplate;\\n\\n\\n\\n\\n    public Integer callStoreProc(String branchName, String branchAddress) {\\n\\n        return storeProcTemplate.execute((Connection conn) -> {\\n            CallableStatement cs = conn.prepareCall(\"{call sp_insert_branch(?, ?, ?)}\");\\n            cs.setString(1, branchName);\\n            cs.setString(2, branchAddress);\\n            cs.registerOutParameter(3, Types.INTEGER);\\n            cs.execute();\\n            return cs.getInt(3);\\n        });\\n    }\\n}')),\n", " GraphDocument(nodes=[Node(id='Userservice', type='Class', properties={}), Node(id='Createuserwithloan', type='Function', properties={}), Node(id='User', type='Table', properties={}), Node(id='Loandetails', type='Table', properties={})], relationships=[Relationship(source=Node(id='Userservice', type='Class', properties={}), target=Node(id='Createuserwithloan', type='Function', properties={}), type='DECLARES', properties={}), Relationship(source=Node(id='Createuserwithloan', type='Function', properties={}), target=Node(id='User', type='Table', properties={}), type='WRITES_TO', properties={}), Relationship(source=Node(id='Createuserwithloan', type='Function', properties={}), target=Node(id='Loandetails', type='Table', properties={}), type='WRITES_TO', properties={})], source=Document(metadata={'source': 'java\\\\service\\\\UserService.java', 'language': 'java', 'chunk_index': 0}, page_content='package com.morganstanley.loanApp.service;\\n\\nimport com.morganstanley.loanApp.dto.UserLoanRequestDTO;\\nimport com.morganstanley.loanApp.model.LoanDetails;\\nimport com.morganstanley.loanApp.model.User;\\nimport com.morganstanley.loanApp.repository.LoanDetailsRepository;\\nimport com.morganstanley.loanApp.repository.UserRepository;\\nimport org.springframework.beans.factory.annotation.Autowired;\\nimport org.springframework.stereotype.Service;\\n\\n@Service\\npublic class UserService {\\n    @Autowired\\n    private UserRepository userRepository;\\n\\n    @Autowired\\n    private LoanDetailsRepository loanDetailsRepository;\\n\\n    public User createUserWithLoan(UserLoanRequestDTO dto) {\\n        User user = new User();\\n        user.setName(dto.getName());\\n        user.setEmail(dto.getEmail());\\n        user = userRepository.save(user);\\n\\n        LoanDetails loan = new LoanDetails();\\n        loan.setLoanType(dto.getLoanType());\\n        loan.setAmount(dto.getAmount());\\n        loan.setUserId(user.getId());\\n        loanDetailsRepository.save(loan);\\n\\n        return user;\\n    }\\n}'))]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["graph_documents"]}, {"cell_type": "code", "execution_count": null, "id": "a6cd1916", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Code structure and data lineage triples pushed to Neo4j successfully.\n"]}], "source": ["graph.add_graph_documents(graph_documents)\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}