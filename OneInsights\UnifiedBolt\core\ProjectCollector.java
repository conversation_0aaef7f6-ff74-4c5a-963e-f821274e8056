package com.bolt.dashboard.core;

import java.util.Iterator;
import java.util.List;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;

import javax.mail.BodyPart;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Multipart;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;

import com.bolt.dashboard.servicenow.ServiceNowApplication;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.PersistJobDataAfterExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.guava.GuavaCacheManager;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.stereotype.Component;

import com.bolt.dashboard.CodeClimate.CodeClimateApplication;
import com.bolt.dashboard.azureCodeCoverage.AzureCodeCoverageApplication;
import com.bolt.dashboard.azurerepo.AzureRepoApplication;
import com.bolt.dashboard.bitbucketclient.BitBucketApplication;
import com.bolt.dashboard.bitbucketpipeline.BitbucketPipelineApplication;
import com.bolt.dashboard.bitbucketserver.BitBucketServerApplication;
import com.bolt.dashboard.circleci.CircleCIApplication;
import com.bolt.dashboard.codecoverage.CodeCoverageApplication;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.model.MailSetup;
import com.bolt.dashboard.core.model.PortfolioConfig;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.core.repository.MailSetupRepo;
import com.bolt.dashboard.core.repository.PortfolioConfigRepo;
import com.bolt.dashboard.engagementScorecard.EngScorecardApplication;
import com.bolt.dashboard.github.GitHubApplication;
import com.bolt.dashboard.githubaction.GithubActionApplication;
import com.bolt.dashboard.gitlab.GitLabApplication;
import com.bolt.dashboard.gitlabpipeline.GitlabPipelineApplication;
import com.bolt.dashboard.highlight.HighLightClientImplementation;
import com.bolt.dashboard.highlight.HighlightMain;
import com.bolt.dashboard.hpalm.HpALMApplication;
import com.bolt.dashboard.jenkins.JenkinsApplication;
import com.bolt.dashboard.jenkinsPipeline.JenkinsPipelineApplication;
import com.bolt.dashboard.jira.JIRAApplication;
import com.bolt.dashboard.octopusDeploy.OctopusDeployApplication;
import com.bolt.dashboard.projectHealth.ProjectHealthApplication;
import com.bolt.dashboard.projectcoveragedetails.ProjectCoverageApplication;
import com.bolt.dashboard.smarttest.SmartTestApplication;
import com.bolt.dashboard.smarttestdefect.SmartTestDefectApplication;
import com.bolt.dashboard.sonar.SonarApplication;
import com.bolt.dashboard.sprintcomparison.SprintComparisonApplication;
import com.bolt.dashboard.svn.SVNApplication;
import com.bolt.dashboard.teamcity.TeamcityApplication;
import com.bolt.dashboard.teamquality.TeamQualityApplication;
import com.bolt.dashboard.testcollector.TestApplication;
import com.bolt.dashboard.tfsbuild.TFSBuildApplication;
import com.bolt.dashboard.tfscollector.TFSApplication;
import com.bolt.dashboard.tfsversion.TFSVersionControllerApplication;

@PersistJobDataAfterExecution
@DisallowConcurrentExecution
@Component("SonarTaskCollector")
public class ProjectCollector implements Job {
    AnnotationConfigApplicationContext ctx = null;
    private String projectName;
    @SuppressWarnings("unused")
    ExecutorService executor = null;
    ThreadPoolExecutor threadGroup;
    ThreadPoolExecutor childThreadGroup;

    private static final Logger LOG= LogManager.getLogger(ProjectCollector.class);
 
    private boolean mailSent = false;
    int i;

    ConfigurationToolInfoMetric metric1;
    StringBuilder toAddress = new StringBuilder();
    StringBuilder msgBody = new StringBuilder();
    StringBuilder subject = new StringBuilder();
    private int count = 1;
    
//    @Autowired
//    GuavaCacheManager cacheManager;

    public ProjectCollector() {
	if (ctx == null) {
	    ctx = DataConfig.getContext();

	}
	if (executor == null) {
	    executor = Executors.newFixedThreadPool(10);
	    msgBody.append("Hi,\n The BrillioOne.ai insights ran for your Project :" + projectName + ".\n For the following Toolset: \n");
	}
    }

    public ProjectCollector(String projectName) {
	this.projectName = projectName;
	if (executor == null) {
	    executor = Executors.newFixedThreadPool(10);
	    msgBody.append("Hi,\n The BrillioOne.ai insights  ran for your Project :" + projectName + ".\n For the following Toolset: \n");
	}

    }

    public String getProjectName() {
	return projectName;
    }

    public void setProjectName(String projectName) {
	this.projectName = projectName;
    }

    public void printMessage() {
	LOG.info("BOLT");
	LOG.info(".............Since the scheduler ran fine exiting the code............ ");
	LOG.info("        .......................................................        ");
	LOG.info("                 .................................                     ");
	LOG.info("                        ...................                            ");
	LOG.info("                             .....                                     ");
	LOG.info("..................BOLT'S EXECUTION WAS FINE..................");
	LOG.info("..................THANKS FOR USING BOLT......................");
	LOG.info("      ...............................................        ");
	LOG.info("            ..................................               ");
	LOG.info("                   ....................                      ");
	LOG.info("                         .......                             ");

    }

	public void multiTaskThread(String projectName) {

	this.projectName = projectName;
	LOG.info("Inside multiTaskThread......");

	LOG.info("Inside multiTaskThread......");
	PortfolioConfig config = null;
	PortfolioConfigRepo portFolioRepo = ctx.getBean(PortfolioConfigRepo.class);
	List<PortfolioConfig> configs = portFolioRepo.findByProjectName(projectName);
	if (!configs.isEmpty()) {
	    config = configs.get(configs.size() - 1);
	}
	if (config != null && config.getSchedulerEnabled()) {
	    LOG.info("Scheduler enabled :" + config.getSchedulerEnabled());
	    ConfigurationSettingRep configurationRepo = ctx.getBean(ConfigurationSettingRep.class);

	    ConfigurationSetting configurationColection = configurationRepo.findByProjectName(projectName).get(0);
	    Set<ConfigurationToolInfoMetric> listOfTools = configurationColection.getMetrics();

	    Iterator iter = listOfTools.iterator();

	    subject.append("BOLT for " + projectName);
	    LOG.info("The collector is about to run for project " + projectName);
	    for (i = 0; i < listOfTools.size(); i++) {
		while (iter.hasNext()) {
		    Object configuration1 = iter.next();
		    metric1 = (ConfigurationToolInfoMetric) configuration1;
		    LOG.info("tool name  " + metric1.getToolName());
		    makeSwitchCaseCall(metric1.getToolName());

		}
	    }

	    if (i == listOfTools.size()) {

		executor.shutdown();
		LOG.info("executor shutdown.............");
		LOG.warn("executor shutdown.............");
	    }

	    LOG.info("Collector ran for " + projectName);

	    msgBody.append("\n\n Thanks & Regards,\n BOLT");

	    if (mailSent)
		LOG.info("mail has been send");

	    while (threadGroup.getActiveCount() > 0) {
		/*
		 * This while loop is required to restrict the JVM control till
		 * all threads in threadGroup completed their task
		 */

	    }
	    destroy();
	} else {
	    LOG.info("Scheduler is disabled for " + projectName);
	}

    }

    public ThreadPoolExecutor makeSwitchCaseCall(String toolName) {
	threadGroup =(ThreadPoolExecutor)Executors.newCachedThreadPool();
	childThreadGroup = (ThreadPoolExecutor)Executors.newCachedThreadPool();
	switch (toolName) {
	case "Jenkins":
	    jenkins(projectName, toolName);
	    break;

	case "CircleCI":
	    circleCI(projectName, toolName);
	    break;
	case "SonarQube":
	    sonarqube(projectName, toolName);
	    break;
	case "BITBUCKET":
	    bit(projectName, toolName);
	    break;
	case "Code Coverage":
	    codecoverage(projectName, toolName);
	    break;
	case "Jira":
	    jira(projectName, toolName);
	    break;

	case "JIRA-KANBAN":
		jira(projectName, toolName);
		break;
	case "Junit":
	    junit(projectName, toolName);
	    break;
	case "SVN":
	    svn(projectName, toolName);
	    break;
	case "GITHUB":
	    git(projectName, toolName);
	    break;
	case "TeamCity":
	    teamcity(projectName, toolName);
	    break;
	case "TFS Build":
	    tfsbuild(projectName, toolName);
	    break;
	case "TFS":
	    tfsversion(projectName, toolName);
	    break;
	/*
	 * case "TFS ALM": tfsalm(projectName, toolName); break;
	 */

	case "BIT Server":
	    bitserver(projectName, toolName);
	    break;

	case "GITLAB":
	    gitlab(projectName, toolName);
	    break;
	case "hpAlm":
	    hpAlm(projectName, toolName);
	    break;

	case "CodeClimate":
	    codeClimate(projectName, toolName);
	    break;

	case "Octopus Deploy":
	    octopusDeploy(projectName, toolName);
	    break;
	case "BITBUCKET Pipeline":
		bitbucketPipeline(projectName, toolName);
		break;
	case "GITLAB Pipeline":
		gitlabPipeline(projectName, toolName);
		break;
	case "GITLAB CodeCoverage" :
		gitlabCodeCoverage(projectName, toolName);
		break;
		
	case "Jenkins Pipeline":
		jenkinsPipeline(projectName,toolName);
		break;
	case "Azure Board":
		azureBoard(projectName,toolName);
		break;
	case "Azure Repo":
		azureRepo(projectName,toolName);
		break;
	case "Azure CodeCoverage":
		azureCC(projectName,toolName);
		break;
	case "HighLight" :
		highLight(projectName,toolName);
		break;
	case "ServiceNow" :
		servicenow(projectName,toolName);
		break;
	case "Github Action":
		githubAction(projectName, toolName);
		break;
	default:
	    LOG.info("Wrong Input");

	}
	return threadGroup;
    }

	public void githubAction(String projectName, String toolName) {
		childThreadGroup.execute(() -> new GithubActionApplication().githubActionMain(projectName));
		count = count + i;
		msgBody.append(count++ + "\t *" + toolName + "\n");
		LOG.info("GITHUB Action started.....");
	}
	
	private void servicenow(String projectName, String toolName) {
		childThreadGroup.execute(()->new ServiceNowApplication().ServiceNowMain(projectName));
		//new Thread(threadGroup, () -> new ServiceNowApplication().ServiceNowMain(projectName)).start();
		count = count + i;
		msgBody.append(count++ + "\t *" + toolName + "\n");
		LOG.info("ServiceNow collector started.....");
	}

	private void azureBoard(String projectName2, String toolName) {
  		
		childThreadGroup.execute(()->new TFSApplication().tfscollectorMain(projectName2));
      	//new Thread(threadGroup, () -> new TFSApplication().tfscollectorMain(projectName)).start();
      	count = count + i;
      	msgBody.append(count++ + "\t *" + toolName + "\n");
      	LOG.info("Azure Board collector started.....");
  	}
    
    private void azureRepo(String projectName, String toolName) {
  		
    	childThreadGroup.execute(()->new AzureRepoApplication().azureRepoMain(projectName));
      	//new Thread(threadGroup, () -> new AzureRepoApplication().azureRepoMain(projectName)).start();
      	count = count + i;
      	msgBody.append(count++ + "\t *" + toolName + "\n");
      	LOG.info("AzureRepo collector started.....");
  	}
    
    private void azureCC(String projectName, String toolName) {
    	childThreadGroup.execute(()->new AzureCodeCoverageApplication().azureCoverageMain(projectName));
      	//new Thread(threadGroup, () -> new AzureRepoApplication().azureRepoMain(projectName)).start();
      	count = count + i;
      	msgBody.append(count++ + "\t *" + toolName + "\n");
      	LOG.info("Azure Code Coverage collector started.....");
    }
    
    private void highLight(String projectName,String toolName) {
    	childThreadGroup.execute(()->new HighlightMain().highlightMain(projectName));
    	//new Thread(threadGroup,() -> new HighlightMain().highlightMain(projectName)).start();
    	count = count + i;
    	msgBody.append(count++ + "\t *" + toolName + "\n");
    	LOG.info("Highlight collector started....");
    }
    
    private void gitlabCodeCoverage(String projectName2, String toolName) {
		
    	childThreadGroup.execute(()->new ProjectCoverageApplication().projectCoverageMain(projectName2));
    	//new Thread(threadGroup, () -> new ProjectCoverageApplication().projectCoverageMain(projectName2)).start();
      	count = count + i;
      	msgBody.append(count++ + "\t *" + toolName + "\n");
      	LOG.info("Bitbucket Pipeline collector started.....");
	}

	@Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
	multiTaskThread(projectName);
    }

    public void test(String name) {
    	LOG.info("Schedular is running for " + name);
    }
    private void bitbucketPipeline(String projectName2, String toolName) {
  		
    	childThreadGroup.execute(()->new BitbucketPipelineApplication().bitbucketPipelineMain(projectName));
      	//new Thread(threadGroup, () -> new BitbucketPipelineApplication().bitbucketPipelineMain(projectName)).start();
      	count = count + i;
      	msgBody.append(count++ + "\t *" + toolName + "\n");
      	LOG.info("Bitbucket Pipeline collector started.....");
  	}
    
    private void gitlabPipeline(String projectName2, String toolName) {
  		
    	childThreadGroup.execute(()->new GitlabPipelineApplication().gitlabPipelineMain(projectName));
      //	new Thread(threadGroup, () -> new GitlabPipelineApplication().gitlabPipelineMain(projectName)).start();
      	count = count + i;
      	msgBody.append(count++ + "\t *" + toolName + "\n");
      	LOG.info("Gitlab Pipeline collector started.....");
  	}

    public void smarttestdefect(String projectName, String toolName) {
    	childThreadGroup.execute(()->new SmartTestDefectApplication().smartTestMain(projectName));
	//new Thread(threadGroup, () -> new SmartTestDefectApplication().smartTestMain(projectName)).start();
	count = count + i;
	msgBody.append(count++ + "\t *" + toolName + "\n");
	LOG.info("Smart test defect completed.....");
    }

    /*
     * public void projectComparison(String projectName) { new
     * Thread(childThreadGroup, () -> new
     * ProjectComparisonApplication().projectComparisonMain(projectName))
     * .start(); count = count + i; LOG.info("Project Comparison completed....."
     * ); }
     */

    public void smarttest(String projectName, String toolName) {
    	childThreadGroup.execute(()-> new SmartTestApplication().smartTestMain(projectName));
	//new Thread(threadGroup, () -> new SmartTestApplication().smartTestMain(projectName)).start();
	count = count + i;
	msgBody.append(count++ + "\t *" + toolName + "\n");
	LOG.info("Smart test  completed.....");
    }

    public void hpAlm(String projectName, String toolName) {
    	childThreadGroup.execute(()-> new HpALMApplication().hpAlmMain(projectName));
	//new Thread(threadGroup, () -> new HpALMApplication().hpAlmMain(projectName)).start();
	count = count + i;
	msgBody.append(count++ + "\t *" + toolName + "\n");
	LOG.info("HPAlm completed.....");
    }

    public void projectHealth(String projectName) {
    	childThreadGroup.execute(()->new ProjectHealthApplication().projectHealthMain(projectName));
	//new Thread(childThreadGroup, () -> new ProjectHealthApplication().projectHealthMain(projectName)).start();
	count = count + i;
	LOG.info("Project Health completed.....");
    }

    public void sprintComparison(String projectName) {
    	childThreadGroup.execute(()->new SprintComparisonApplication().sprintComparisonMain(projectName));
	//new Thread(childThreadGroup, () -> new SprintComparisonApplication().sprintComparisonMain(projectName)).start();
	count = count + i;
	LOG.info("Sprint comparison completed.....");
    }

    public void circleCI(String projectName, String toolName) {
    	childThreadGroup.execute(()-> new CircleCIApplication().circleCIMain(projectName));
	//new Thread(threadGroup, () -> new CircleCIApplication().circleCIMain(projectName)).start();
	count = count + i;
	msgBody.append(count++ + "\t *" + toolName + "\n");
	LOG.info("Circle CI completed.....");
    }

    public void jenkins(String projectName, String toolName) {
    	childThreadGroup.execute(()-> new JenkinsApplication().jenkinsMain(projectName));
	//new Thread(threadGroup, () -> new JenkinsApplication().jenkinsMain(projectName)).start();
	count = count + i;
	msgBody.append(count++ + "\t *" + toolName + "\n");
	LOG.info("Jenkins completed.....");
    }
    
    public void jenkinsPipeline(String projectName, String toolName) {
    	childThreadGroup.execute(()-> new JenkinsPipelineApplication().jenkinsPipelineMain(projectName));
    	//new Thread(threadGroup, () -> new JenkinsPipelineApplication().jenkinsPipelineMain(projectName)).start();
    	count = count + i;
    	msgBody.append(count++ + "\t *" + toolName + "\n");
    	LOG.info("Jenkins Pipeline completed.....");

    }

    public void jira(String projectName, String toolName) {
    	childThreadGroup.execute(()-> new JIRAApplication().jiraMain(projectName));
	//new Thread(threadGroup, () -> new JIRAApplication().jiraMain(projectName)).start();
	count = count + i;
	msgBody.append(count++ + "\t *" + toolName + "\n");
	
	LOG.info("JIRA collector started.....");
    }
    public void engScorecard(String projectName) {
    	childThreadGroup.execute(()-> new EngScorecardApplication().engScorecardMain(projectName, true));
		//new Thread(childThreadGroup, () -> new EngScorecardApplication().engScorecardMain(projectName, true)).start();
		count = count + i;
		LOG.info("Engscorecard collector Scheduled.....");
	}

    public void sonarqube(String projectName, String toolName) {
    	childThreadGroup.execute(()-> new SonarApplication().sonarMain(projectName));
	//new Thread(threadGroup, () -> new SonarApplication().sonarMain(projectName)).start();
	count = count + i;
	msgBody.append(count++ + "\t *" + toolName + "\n");
	LOG.info("Sonar started.....");
    }

    public void bit(String projectName, String toolName) {
    	childThreadGroup.execute(()-> new BitBucketApplication().bitBucketMain(projectName));
	//new Thread(threadGroup, () -> new BitBucketApplication().bitBucketMain(projectName)).start();
	count = count + i;
	msgBody.append(count++ + "\t *" + toolName + "\n");
	LOG.info("BITBUCKET CLIENT completed.....");
    }

    public void codecoverage(String projectName, String toolName) {
    	childThreadGroup.execute(()-> new CodeCoverageApplication().codeCoverageMain(projectName));
	//new Thread(threadGroup, () -> new CodeCoverageApplication().codeCoverageMain(projectName)).start();
	count = count + i;
	msgBody.append(count++ + "\t *" + toolName + "\n");
	LOG.info("Code Coverage completed.....");
    }

    public void junit(String projectName, String toolName) {
    	childThreadGroup.execute(()-> new TestApplication().testCollectorMain(projectName));
	//new Thread(threadGroup, () -> new TestApplication().testCollectorMain(projectName)).start();
	count = count + i;
	msgBody.append(count++ + "\t *" + toolName + "\n");
	LOG.info("Junit completed.....");
    }

    public void svn(String projectName, String toolName) {
    	childThreadGroup.execute(()->  new SVNApplication().svnMain(projectName));
	//new Thread(threadGroup, () -> new SVNApplication().svnMain(projectName)).start();
	count = count + i;
	msgBody.append(count++ + "\t *" + toolName + "\n");
	LOG.info("SVN completed.....");
    }

    public void git(String projectName, String toolName) {
    	childThreadGroup.execute(()->  new GitHubApplication().gitMain(projectName));
	//new Thread(threadGroup, () -> new GitHubApplication().gitMain(projectName)).start();
	count = count + i;
	msgBody.append(count++ + "\t *" + toolName + "\n");
	LOG.info("GIT HUB completed.....");
    }

    public void teamcity(String projectName, String toolName) {
    	childThreadGroup.execute(()->  new TeamcityApplication().teamCityMain(projectName));
	//new Thread(threadGroup, () -> new TeamcityApplication().teamCityMain(projectName)).start();
	count = count + i;
	msgBody.append(count++ + "\t *" + toolName + "\n");
	LOG.info("Teamcity completed.....");
    }

    public void tfsbuild(String projectName, String toolName) {
    	childThreadGroup.execute(()->  new TFSBuildApplication().tfsbuildMain(projectName));
	//new Thread(threadGroup, () -> new TFSBuildApplication().tfsbuildMain(projectName)).start();
	count = count + i;
	msgBody.append(count++ + "\t *" + toolName + "\n");
	LOG.info("TFS Build completed.....");
    }

    public void tfsversion(String projectName, String toolName) {
    	childThreadGroup.execute(()->  new TFSVersionControllerApplication().tfsversionMain(projectName));
	//new Thread(threadGroup, () -> new TFSVersionControllerApplication().tfsversionMain(projectName)).start();
	count = count + i;
	msgBody.append(count++ + "\t *" + toolName + "\n");
	LOG.info("TFS Version Controller  completed.....");
    }

    /*
     * public void tfsalm(String projectName, String toolName) { new
     * Thread(threadGroup, () -> new
     * TFSApplication().tfscollectorMain(projectName)).start(); count = count +
     * i; msgBody.append(count++ + "\t *" + toolName + "\n"); LOG.info(
     * "TFS ALM completed....."); }
     */

    public void bitserver(String projectName, String toolName) {
    	childThreadGroup.execute(()->  new BitBucketServerApplication().bitBucketServerMain(projectName));
	//new Thread(threadGroup, () -> new BitBucketServerApplication().bitBucketServerMain(projectName)).start();
	count = count + i;
	msgBody.append(count++ + "\t *" + toolName + "\n");
	LOG.info("BITBUCKET SERVER completed.....");
    }

    public void gitlab(String projectName, String toolName) {
    	childThreadGroup.execute(()->   new GitLabApplication().gitLabMain(projectName));
	//new Thread(threadGroup, () -> new GitLabApplication().gitLabMain(projectName)).start();
	count = count + i;
	msgBody.append(count++ + "\t *" + toolName + "\n");
	LOG.info("Git Lab started.....");
    }

    public void codeClimate(String projectName, String toolName) {
    	childThreadGroup.execute(()->   new CodeClimateApplication().codeClimateMain(projectName));
	//new Thread(threadGroup, () -> new CodeClimateApplication().codeClimateMain(projectName)).start();
	count = count + i;
	msgBody.append(count++ + "\t *" + toolName + "\n");
	LOG.info("Code Climate completed.....");
    }

    public void octopusDeploy(String projectName, String toolName) {
    	childThreadGroup.execute(()->  new OctopusDeployApplication().octoMain(projectName));
	//new Thread(threadGroup, () -> new OctopusDeployApplication().octoMain(projectName)).start();
	count = count + i;
	msgBody.append(count++ + "\t *" + toolName + "\n");
	LOG.info("Octopus Deploy completed.....");
    }

    /**
     * This method is written to send mail across the project to intimate that
     * Scheduler for that particluar project ran successfully. This will contain
     * the list of Tools that were executed.
     * 
     * @param toAddress
     *            The receivers mail address
     * @param msgBody
     *            Mail Body.
     * @param subject
     *            Subject of Mail.
     * @return
     * @throws MyOwnRunTimeException
     */
    public boolean sendMail(String toAddress, String msgBody, String subject) {
	String[] smtpFieldsArray = getSMTPInfo();
	Properties props = new Properties();
	props.put(ConstantVariable.MAIL_SMTP_AUTH, smtpFieldsArray[4]);
	props.put(ConstantVariable.MAIL_SMTP_ENABLE, smtpFieldsArray[4]);
	props.put(ConstantVariable.MAIL_SMTP_HOST, smtpFieldsArray[0]);
	props.put(ConstantVariable.MAIL_SMTP_PORT, smtpFieldsArray[1]);
	LOG.info("property file updated.....");
	Session news = Session.getInstance(props, new javax.mail.Authenticator() {
	    public PasswordAuthentication getPasswordAuthentication() {
		return new PasswordAuthentication(smtpFieldsArray[2], smtpFieldsArray[3]);
	    }
	});
	LOG.info("Successfully Authenticated.........");

	try {

	    Message message = new MimeMessage(news);
	    message.setFrom(new InternetAddress(smtpFieldsArray[2]));
	    LOG.info(toAddress);

	    message.setRecipients(Message.RecipientType.BCC, InternetAddress.parse(ConstantVariable.SMTP_TOADDRESS));
	    message.setSubject(subject);

	    // Create the message body part
	    BodyPart messageBodyPart = new MimeBodyPart();
	    messageBodyPart.setText(msgBody);
	    // Create a multipart message for attachment
	    Multipart multipart = new MimeMultipart();

	    // Set text message part
	    multipart.addBodyPart(messageBodyPart);

	    // Send the complete message parts
	    message.setContent(multipart);
	    LOG.info("All datad addd to Mutltipart....");
	    Transport.send(message);

	    LOG.info("Message sent successfully.............");
	    return true;
	} catch (MessagingException e) {
	    LOG.info("Message sending failed............." + e);
	    return false;
	}

    }

    public String[] getSMTPInfo() {
	String[] smtpFieldsArray = new String[6];
	MailSetupRepo repo = ctx.getBean(MailSetupRepo.class);
	List<MailSetup> mailSetupModelList = repo.findAll();
	Iterator<MailSetup> it = mailSetupModelList.iterator();
	while (it.hasNext()) {

	    MailSetup mailSetup = it.next();
	    smtpFieldsArray[0] = mailSetup.getHost();
	    smtpFieldsArray[1] = mailSetup.getPort();
	    smtpFieldsArray[2] = mailSetup.getUserName();
	    smtpFieldsArray[3] = mailSetup.getPassword();
	    smtpFieldsArray[4] = String.valueOf(mailSetup.isStarttls());
	}
	return smtpFieldsArray;

    }

    public void destroy() {
	if (threadGroup.getActiveCount() == 0) {
	    projectHealth(projectName);
	    sprintComparison(projectName);
	    engScorecard(projectName);
	    // projectComparison(projectName);
	    while (childThreadGroup.getActiveCount() > 0) {
		if (childThreadGroup.getActiveCount() == 0) {
		    childThreadGroup.shutdown();
		   // ctx.close();
		}

	    }
	    //Removing all the Cache
	      evictAllCaches(projectName);

	    threadGroup.shutdown();

	}
    }
    @CacheEvict(allEntries=true)
	public void evictAllCaches(String projectName) {
		LOG.info("Removing the Cache" );
	}
}