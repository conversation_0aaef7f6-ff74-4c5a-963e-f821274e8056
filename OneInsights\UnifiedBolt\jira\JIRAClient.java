/**
 * 
 */
package com.bolt.dashboard.jira;

import org.json.simple.parser.ParseException;

import com.bolt.dashboard.core.model.ProjectModel;

/**
 * <AUTHOR>
 *
 */
public interface JIRAClient {
/**
 * get JIRA rest api data and stored in database
 * @throws ParseException 
 * **/
	ProjectModel getALMToolData(String urlAlm, String userName, String password, String projectName, String key,
			String almType,String board) throws JiraExceptions, ParseException;

}
