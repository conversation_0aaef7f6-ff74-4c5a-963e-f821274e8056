package AppOne;

public class UserUtils {

    public static User convertDtoToEntity(UserDto dto) {
        User user = new User();
        user.setName(dto.getName()); // dto.name FLOWS_TO user.name
        user.setEmail(dto.getEmail()); // dto.email FLOWS_TO user.email
        return user;
    }

    public static String generateUserCode(User user) {
        String base = user.getName() + "-" + user.getEmail(); // user.name + user.email FLOWS_TO base
        String code = base.toUpperCase(); // base TRANSFORMS_TO code
        return code; // code PRODUCES output
    }
}