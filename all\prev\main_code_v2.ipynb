{"cells": [{"cell_type": "code", "execution_count": null, "id": "495c50bb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- Document 1 ---\n", "Metadata: {'source': 'LoanAppApplication.java', 'language': <Language.JAVA: 'java'>}\n", "package com.morganstanley.loanApp;\n", "\n", "import jakarta.annotation.PostConstruct;\n", "import org.springframework.beans.factory.annotation.Autowired;\n", "import org.springframework.beans.factory.annotation.Qualifier;\n", "import org.springframework.boot.SpringApplication;\n", "import org.springframework.boot.autoconfigure. ...\n", "\n", "--- Document 2 ---\n", "Metadata: {'source': 'LoanAppApplication.java', 'language': <Language.JAVA: 'java'>}\n", "@SpringBootApplication\n", "@EnableConfigurationProperties\n", "public class LoanAppApplication {\n", "\n", "\t@Autowired\n", "\t@Qualifier(\"dataSource\")\n", "\tprivate DataSource dataSource; // primary\n", "\n", "\t@Autowired\n", "\t@Qualifier(\"storeProcDataSource\")\n", "\tprivate DataSource secondDataSource;\n", "\n", "\n", "\t@PostConstruct\n", "\tpublic void printDsInfo() ...\n", "\n", "--- Document 3 ---\n", "Metadata: {'source': 'LoanDetails.java', 'language': <Language.JAVA: 'java'>}\n", "package com.morganstanley.loanApp.model;\n", "\n", "import jakarta.persistence.*;\n", "import lombok.Data;\n", "\n", "@Entity\n", "@Table(name = \"loan_details\")\n", "@Data\n", "public class LoanDetails {\n", "\n", "    @Id\n", "    @GeneratedValue(strategy = GenerationType.IDENTITY)\n", "    private Long id;\n", "\n", "    private String loanType;\n", "    private Double a ...\n", "\n"]}], "source": ["from langchain_community.document_loaders.generic import GenericLoader\n", "from langchain_community.document_loaders.parsers.language.language_parser import LanguageParser\n", "from langchain.text_splitter import Language\n", "from langchain_core.documents import Document\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "\n", "def load_and_chunk_code_files(chunk_size=1000, chunk_overlap=200): \n", "    # Loader for Java files\n", "    java_loader = GenericLoader.from_filesystem(\n", "        \".\",\n", "        glob=\"**/*.java\",\n", "        parser=LanguageParser(language=Language.JAVA, parser_threshold=500)\n", "    )\n", "    java_documents = java_loader.load()\n", "\n", "    text_splitter = RecursiveCharacterTextSplitter(\n", "        chunk_size=chunk_size,\n", "        chunk_overlap=chunk_overlap\n", "    )\n", "\n", "    chunked_documents = text_splitter.split_documents(java_documents)\n", "\n", "    return chunked_documents\n", "\n", "# Load and chunk\n", "documents = load_and_chunk_code_files()\n", "\n", "# Check results\n", "for i, doc in enumerate(documents[:3], 1):\n", "    print(f\"--- Document {i} ---\")\n", "    print(\"Metadata:\", doc.metadata)\n", "    print(doc.page_content[:300], \"...\\n\")\n"]}, {"cell_type": "code", "execution_count": 4, "id": "95a9e493", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(metadata={'source': 'LoanAppApplication.java', 'language': <Language.JAVA: 'java'>}, page_content='package com.morganstanley.loanApp;\\n\\nimport jakarta.annotation.PostConstruct;\\nimport org.springframework.beans.factory.annotation.Autowired;\\nimport org.springframework.beans.factory.annotation.Qualifier;\\nimport org.springframework.boot.SpringApplication;\\nimport org.springframework.boot.autoconfigure.SpringBootApplication;\\nimport org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;\\nimport org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;\\nimport org.springframework.boot.context.properties.EnableConfigurationProperties;\\nimport org.springframework.context.ConfigurableApplicationContext;\\nimport org.springframework.core.env.Environment;\\n\\nimport javax.sql.DataSource;\\nimport java.sql.SQLException;\\n\\n@SpringBootApplication\\n@EnableConfigurationProperties\\npublic class LoanAppApplication {\\n\\n\\t@Autowired\\n\\t@Qualifier(\"dataSource\")\\n\\tprivate DataSource dataSource; // primary'),\n", " Document(metadata={'source': 'LoanAppApplication.java', 'language': <Language.JAVA: 'java'>}, page_content='@SpringBootApplication\\n@EnableConfigurationProperties\\npublic class LoanAppApplication {\\n\\n\\t@Autowired\\n\\t@Qualifier(\"dataSource\")\\n\\tprivate DataSource dataSource; // primary\\n\\n\\t@Autowired\\n\\t@Qualifier(\"storeProcDataSource\")\\n\\tprivate DataSource secondDataSource;\\n\\n\\n\\t@PostConstruct\\n\\tpublic void printDsInfo() throws SQLException {\\n\\t\\tSystem.out.println(\"Primary DataSource connection URL: \" + dataSource.getConnection().getMetaData().getURL());\\n\\t\\tSystem.out.println(\"Secondary DataSource connection URL: \" + secondDataSource.getConnection().getMetaData().getURL());\\n\\t}\\n\\tpublic static void main(String[] args) {\\n\\t\\t SpringApplication.run(LoanAppApplication.class, args);\\n\\n\\t}\\n\\n}'),\n", " Document(metadata={'source': 'LoanDetails.java', 'language': <Language.JAVA: 'java'>}, page_content='package com.morganstanley.loanApp.model;\\n\\nimport jakarta.persistence.*;\\nimport lombok.Data;\\n\\n@Entity\\n@Table(name = \"loan_details\")\\n@Data\\npublic class LoanDetails {\\n\\n    @Id\\n    @GeneratedValue(strategy = GenerationType.IDENTITY)\\n    private Long id;\\n\\n    private String loanType;\\n    private Double amount;\\n\\n    @Column(name = \"user_id\")\\n    private Long userId;\\n\\n\\n\\n}'),\n", " Document(metadata={'source': 'LoanDetailsRepository.java', 'language': <Language.JAVA: 'java'>}, page_content='package com.morganstanley.loanApp.repository;\\n\\nimport com.morganstanley.loanApp.model.LoanDetails;\\nimport org.springframework.data.jpa.repository.JpaRepository;\\n\\npublic interface LoanDetailsRepository extends JpaRepository<LoanDetails,Long> {\\n}'),\n", " Document(metadata={'source': 'PrimaryDataSourceProperties.java', 'language': <Language.JAVA: 'java'>}, page_content='package com.morganstanley.loanApp.configuration;\\n\\nimport jakarta.annotation.PostConstruct;\\nimport org.springframework.boot.context.properties.ConfigurationProperties;\\nimport org.springframework.stereotype.Component;\\n\\n@Component\\n@ConfigurationProperties(prefix = \"spring.datasource\")\\npublic class PrimaryDataSourceProperties {\\n    public String getUrl() {\\n        return url;\\n    }\\n\\n    public void setUrl(String url) {\\n        this.url = url;\\n    }\\n\\n    public String getUsername() {\\n        return username;\\n    }\\n\\n    public void setUsername(String username) {\\n        this.username = username;\\n    }\\n\\n    public String getPassword() {\\n        return password;\\n    }\\n\\n    public void setPassword(String password) {\\n        this.password = password;\\n    }\\n\\n    public String getDriverClassName() {\\n        return driverClassName;\\n    }\\n\\n    public void setDriverClassName(String driverClassName) {\\n        this.driverClassName = driverClassName;\\n    }'),\n", " Document(metadata={'source': 'PrimaryDataSourceProperties.java', 'language': <Language.JAVA: 'java'>}, page_content='public String getDriverClassName() {\\n        return driverClassName;\\n    }\\n\\n    public void setDriverClassName(String driverClassName) {\\n        this.driverClassName = driverClassName;\\n    }\\n\\n    private String url;\\n    private String username;\\n    private String password;\\n    private String driverClassName;\\n    @PostConstruct\\n    public void printProps() {\\n        System.out.println(\"Primary DS Props:\");\\n        System.out.println(\"URL: \" + url);\\n        System.out.println(\"Username: \" + username);\\n        System.out.println(\"Driver: \" + driverClassName);\\n    }\\n\\n\\n}'),\n", " Document(metadata={'source': 'SecondDataSourceProperties.java', 'language': <Language.JAVA: 'java'>}, page_content='package com.morganstanley.loanApp.configuration;\\n\\nimport jakarta.annotation.PostConstruct;\\nimport org.springframework.boot.context.properties.ConfigurationProperties;\\nimport org.springframework.stereotype.Component;\\n\\n@Component\\n@ConfigurationProperties(prefix = \"sp.second-datasource\")\\npublic class SecondDataSourceProperties {\\n    public String getUrl() {\\n        return url;\\n    }\\n\\n    public void setUrl(String url) {\\n        this.url = url;\\n    }\\n\\n    public String getUsername() {\\n        return username;\\n    }\\n\\n    public void setUsername(String username) {\\n        this.username = username;\\n    }\\n\\n    public String getPassword() {\\n        return password;\\n    }\\n\\n    public void setPassword(String password) {\\n        this.password = password;\\n    }\\n\\n    public String getDriverClassName() {\\n        return driverClassName;\\n    }\\n\\n    public void setDriverClassName(String driverClassName) {\\n        this.driverClassName = driverClassName;\\n    }'),\n", " Document(metadata={'source': 'SecondDataSourceProperties.java', 'language': <Language.JAVA: 'java'>}, page_content='public String getDriverClassName() {\\n        return driverClassName;\\n    }\\n\\n    public void setDriverClassName(String driverClassName) {\\n        this.driverClassName = driverClassName;\\n    }\\n\\n    private String url;\\n    private String username;\\n    private String password;\\n    private String driverClassName;\\n    @PostConstruct\\n    public void printProps() {\\n        System.out.println(\"Secondary DS Props:\");\\n        System.out.println(\"URL: \" + url);\\n        System.out.println(\"Username: \" + username);\\n        System.out.println(\"Driver: \" + driverClassName);\\n    }\\n\\n\\n}'),\n", " Document(metadata={'source': 'StoredProcDBConfig.java', 'language': <Language.JAVA: 'java'>}, page_content='package com.morganstanley.loanApp.configuration;\\nimport javax.sql.DataSource;\\n\\nimport com.zaxxer.hikari.HikariDataSource;\\nimport org.springframework.beans.factory.annotation.Qualifier;\\nimport org.springframework.boot.context.properties.ConfigurationProperties;\\nimport org.springframework.boot.jdbc.DataSourceBuilder;\\nimport org.springframework.context.annotation.Bean;\\nimport org.springframework.context.annotation.Configuration;\\nimport org.springframework.context.annotation.Primary;\\nimport org.springframework.jdbc.core.JdbcTemplate;\\n\\n@Configuration\\npublic class StoredProcDBConfig {\\n    @Bean\\n    @Qualifier(\"dataSource\")\\n    @Primary\\n    public DataSource primaryDataSource(PrimaryDataSourceProperties props) {\\n        HikariDataSource ds = new HikariDataSource();\\n        ds.setJdbcUrl(props.getUrl());\\n        ds.setUsername(props.getUsername());\\n        ds.setPassword(props.getPassword());\\n        ds.setDriverClassName(props.getDriverClassName());'),\n", " Document(metadata={'source': 'StoredProcDBConfig.java', 'language': <Language.JAVA: 'java'>}, page_content='System.out.println(\">>> Primary DataSource initialized with URL: \" + props.getUrl());\\n\\n        return ds;\\n    }\\n\\n    @Bean(name = \"storeProcDataSource\")\\n    public DataSource storeProcDataSource(SecondDataSourceProperties props) {\\n        HikariDataSource ds = new HikariDataSource();\\n        ds.setJdbcUrl(props.getUrl());\\n        ds.setUsername(props.getUsername());\\n        ds.setPassword(props.getPassword());\\n        ds.setDriverClassName(props.getDriverClassName());\\n\\n        System.out.println(\">>> Second DataSource initialized with URL: \" + props.getUrl());\\n\\n        return ds;\\n    }\\n\\n    @Bean\\n    public JdbcTemplate storeProcJdbcTemplate(@Qualifier(\"storeProcDataSource\") DataSource ds) {\\n        return new JdbcTemplate(ds);\\n    }\\n}'),\n", " Document(metadata={'source': 'StoreProcService.java', 'language': <Language.JAVA: 'java'>}, page_content='package com.morganstanley.loanApp.service;\\n\\nimport org.springframework.beans.factory.annotation.Autowired;\\nimport org.springframework.beans.factory.annotation.Qualifier;\\nimport org.springframework.jdbc.core.JdbcTemplate;\\nimport org.springframework.stereotype.Service;\\n\\nimport java.sql.CallableStatement;\\nimport java.sql.Connection;\\nimport java.sql.Types;\\n\\n@Service\\npublic class StoreProcService {\\n\\n\\n    @Autowired\\n    @Qualifier(\"storeProcJdbcTemplate\")\\n    private JdbcTemplate storeProcTemplate;\\n\\n\\n\\n\\n    public Integer callStoreProc(String branchName, String branchAddress) {\\n\\n        return storeProcTemplate.execute((Connection conn) -> {\\n            CallableStatement cs = conn.prepareCall(\"{call sp_insert_branch(?, ?, ?)}\");\\n            cs.setString(1, branchName);\\n            cs.setString(2, branchAddress);\\n            cs.registerOutParameter(3, Types.INTEGER);\\n            cs.execute();\\n            return cs.getInt(3);\\n        });\\n    }\\n}'),\n", " Document(metadata={'source': 'User.java', 'language': <Language.JAVA: 'java'>}, page_content='package com.morganstanley.loanApp.model;\\n\\nimport jakarta.persistence.*;\\nimport lombok.Data;\\n\\n@Entity\\n@Table(name = \"users\")\\n@Data\\npublic class User {\\n\\n    @Id\\n    @GeneratedValue(strategy = GenerationType.IDENTITY)\\n    private Long id;\\n\\n    private String name;\\n    private String email;\\n}'),\n", " Document(metadata={'source': 'UserController.java', 'language': <Language.JAVA: 'java'>}, page_content='package com.morganstanley.loanApp.controller;\\n\\nimport com.morganstanley.loanApp.dto.UserLoanRequestDTO;\\nimport com.morganstanley.loanApp.model.User;\\n\\nimport com.morganstanley.loanApp.service.StoreProcService;\\nimport com.morganstanley.loanApp.service.UserService;\\nimport jakarta.validation.Valid;\\nimport org.springframework.beans.factory.annotation.Autowired;\\nimport org.springframework.http.HttpStatus;\\nimport org.springframework.http.ResponseEntity;\\nimport org.springframework.web.bind.annotation.PostMapping;\\nimport org.springframework.web.bind.annotation.RequestBody;\\nimport org.springframework.web.bind.annotation.RequestMapping;\\nimport org.springframework.web.bind.annotation.RestController;\\n\\nimport java.io.*;\\nimport java.util.ArrayList;\\nimport java.util.List;\\n\\n@RestController\\n@RequestMapping(\"/loanApp\")\\npublic class UserController {\\n    @Autowired\\n    private UserService userService;\\n\\n    @Autowired\\n    private StoreProcService storeProcService;'),\n", " Document(metadata={'source': 'UserController.java', 'language': <Language.JAVA: 'java'>}, page_content='@RestController\\n@RequestMapping(\"/loanApp\")\\npublic class UserController {\\n    @Autowired\\n    private UserService userService;\\n\\n    @Autowired\\n    private StoreProcService storeProcService;\\n\\n    @PostMapping(\"/createLoan\")\\n    public ResponseEntity<User> createUserWithLoan(@Valid @RequestBody UserLoanRequestDTO dto) {\\n        User savedUser = userService.createUserWithLoan(dto);\\n        return new ResponseEntity<>(savedUser, HttpStatus.CREATED);\\n    }\\n\\n    @PostMapping(\"/save-from-file\")\\n    public ResponseEntity<String> saveUserLoanFromFile(){\\n        try (InputStream is = getClass().getClassLoader().getResourceAsStream(\"usersData.txt\");\\n             BufferedReader reader = new BufferedReader(new InputStreamReader(is))) {\\n\\n            if (is == null) {\\n                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)\\n                        .body(\"Data file not found\");\\n            }'),\n", " Document(metadata={'source': 'UserController.java', 'language': <Language.JAVA: 'java'>}, page_content='if (is == null) {\\n                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)\\n                        .body(\"Data file not found\");\\n            }\\n\\n            String line ;\\n            while ((line = reader.readLine()) != null) {\\n                String[] parts = line.split(\",\");\\n                if (parts.length != 4) {\\n                    return ResponseEntity.badRequest()\\n                            .body(\"Invalid file format\");\\n                }\\n\\n\\n                UserLoanRequestDTO userDTO = new UserLoanRequestDTO();\\n                userDTO.setName(parts[0].trim());\\n                userDTO.setEmail(parts[1].trim());\\n                userDTO.setLoanType(parts[2].trim());\\n                userDTO.setAmount(Double.valueOf(parts[3].trim()));\\n\\n                userService.createUserWithLoan(userDTO);\\n            }\\n            return ResponseEntity.ok(\"Loan application saved from flat file\");'),\n", " Document(metadata={'source': 'UserController.java', 'language': <Language.JAVA: 'java'>}, page_content='userService.createUserWithLoan(userDTO);\\n            }\\n            return ResponseEntity.ok(\"Loan application saved from flat file\");\\n\\n        } catch (Exception e) {\\n            e.printStackTrace();\\n            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)\\n                    .body(\"Error processing flat file\");\\n        }\\n    }\\n\\n\\n    @PostMapping(\"/processStoreProcFromCSV\")\\n    public ResponseEntity<List<String>> readCSVAndCallStoreProc(){\\n        List<String> outputMessages = new ArrayList<>();\\n\\n        try (InputStream is = getClass().getClassLoader().getResourceAsStream(\"branchesData.csv\");\\n             BufferedReader reader = new BufferedReader(new InputStreamReader(is))) {\\n            String line;\\n            boolean isFirstLine = true;\\n\\n            while ((line = reader.readLine()) != null) {\\n                if (isFirstLine) {\\n                    isFirstLine = false; // skip header\\n                    continue;\\n                }'),\n", " Document(metadata={'source': 'UserController.java', 'language': <Language.JAVA: 'java'>}, page_content='while ((line = reader.readLine()) != null) {\\n                if (isFirstLine) {\\n                    isFirstLine = false; // skip header\\n                    continue;\\n                }\\n\\n                String[] parts = line.split(\",\");\\n                if (parts.length != 2) continue;\\n\\n                String branchName = parts[0].trim();\\n                String branchAddress = parts[1].trim();\\n\\n                Integer result = storeProcService.callStoreProc(branchName, branchAddress);\\n                outputMessages.add(\"Branch Id for \" + branchName + \" is: \" + result);\\n            }\\n\\n            return ResponseEntity.ok(outputMessages);\\n        } catch (IOException | NumberFormatException ex) {\\n            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)\\n                    .body(List.of(\"Error reading file or parsing data: \" + ex.getMessage()));\\n        }\\n    }\\n\\n\\n}'),\n", " Document(metadata={'source': 'UserLoanRequestDTO.java', 'language': <Language.JAVA: 'java'>}, page_content='package com.morganstanley.loanApp.dto;\\n\\nimport jakarta.validation.constraints.Email;\\nimport jakarta.validation.constraints.NotBlank;\\nimport jakarta.validation.constraints.NotNull;\\nimport jakarta.validation.constraints.Positive;\\nimport lombok.Data;\\nimport lombok.Getter;\\nimport lombok.Setter;\\n\\n\\n\\npublic class UserLoanRequestDTO {\\n\\n    public String getName() {\\n        return name;\\n    }\\n\\n    public void setName(String name) {\\n        this.name = name;\\n    }\\n\\n    public String getEmail() {\\n        return email;\\n    }\\n\\n    public void setEmail(String email) {\\n        this.email = email;\\n    }\\n\\n    public String getLoanType() {\\n        return loanType;\\n    }\\n\\n    public void setLoanType(String loanType) {\\n        this.loanType = loanType;\\n    }\\n\\n    public Double getAmount() {\\n        return amount;\\n    }\\n\\n    public void setAmount(Double amount) {\\n        this.amount = amount;\\n    }\\n\\n    @NotBlank(message = \"Name is required\")\\n    private String name;'),\n", " Document(metadata={'source': 'UserLoanRequestDTO.java', 'language': <Language.JAVA: 'java'>}, page_content='public void setAmount(Double amount) {\\n        this.amount = amount;\\n    }\\n\\n    @NotBlank(message = \"Name is required\")\\n    private String name;\\n\\n    @Email(message = \"Email should be valid\")\\n    @NotBlank(message = \"Email is required\")\\n    private String email;\\n\\n    @NotBlank(message = \"Loan type is required\")\\n    private String loanType;\\n\\n    @NotNull(message = \"Loan amount is required\")\\n    @Positive(message = \"Loan amount must be positive\")\\n    private Double amount;\\n}'),\n", " Document(metadata={'source': 'UserRepository.java', 'language': <Language.JAVA: 'java'>}, page_content='package com.morganstanley.loanApp.repository;\\n\\nimport com.morganstanley.loanApp.model.User;\\nimport org.springframework.data.jpa.repository.JpaRepository;\\n\\n\\npublic interface UserRepository extends JpaRepository<User, Long> {\\n}'),\n", " Document(metadata={'source': 'UserService.java', 'language': <Language.JAVA: 'java'>}, page_content='package com.morganstanley.loanApp.service;\\n\\nimport com.morganstanley.loanApp.dto.UserLoanRequestDTO;\\nimport com.morganstanley.loanApp.model.LoanDetails;\\nimport com.morganstanley.loanApp.model.User;\\nimport com.morganstanley.loanApp.repository.LoanDetailsRepository;\\nimport com.morganstanley.loanApp.repository.UserRepository;\\nimport org.springframework.beans.factory.annotation.Autowired;\\nimport org.springframework.stereotype.Service;\\n\\n@Service\\npublic class UserService {\\n    @Autowired\\n    private UserRepository userRepository;\\n\\n    @Autowired\\n    private LoanDetailsRepository loanDetailsRepository;\\n\\n    public User createUserWithLoan(UserLoanRequestDTO dto) {\\n        User user = new User();\\n        user.setName(dto.getName());\\n        user.setEmail(dto.getEmail());\\n        user = userRepository.save(user);'),\n", " Document(metadata={'source': 'UserService.java', 'language': <Language.JAVA: 'java'>}, page_content='LoanDetails loan = new LoanDetails();\\n        loan.setLoanType(dto.getLoanType());\\n        loan.setAmount(dto.getAmount());\\n        loan.setUserId(user.getId());\\n        loanDetailsRepository.save(loan);\\n\\n        return user;\\n    }\\n}')]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["documents"]}, {"cell_type": "code", "execution_count": 5, "id": "305202c5", "metadata": {}, "outputs": [], "source": ["import pickle\n", "\n", "with open(\"documents.pkl\", \"wb\") as f:\n", "    pickle.dump(documents, f)\n"]}, {"cell_type": "code", "execution_count": null, "id": "0418469c", "metadata": {}, "outputs": [], "source": ["import pickle\n", "\n", "with open(\"documents.pkl\", \"rb\") as f:\n", "    documents = pickle.load(f)"]}, {"cell_type": "code", "execution_count": 6, "id": "384d4df8", "metadata": {}, "outputs": [], "source": ["groq_api_key=\"********************************************************\""]}, {"cell_type": "code", "execution_count": 7, "id": "767418e4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ModelListResponse(data=[Model(id='meta-llama/llama-prompt-guard-2-22m', created=1748632101, object='model', owned_by='Meta', active=True, context_window=512, public_apps=None, max_completion_tokens=512), Model(id='allam-2-7b', created=1737672203, object='model', owned_by='SDAIA', active=True, context_window=4096, public_apps=None, max_completion_tokens=4096), Model(id='whisper-large-v3-turbo', created=1728413088, object='model', owned_by='OpenAI', active=True, context_window=448, public_apps=None, max_completion_tokens=448), Model(id='llama-3.3-70b-versatile', created=1733447754, object='model', owned_by='Meta', active=True, context_window=131072, public_apps=None, max_completion_tokens=32768), Model(id='compound-beta', created=1740880017, object='model', owned_by='Groq', active=True, context_window=131072, public_apps=None, max_completion_tokens=8192), Model(id='llama3-70b-8192', created=1693721698, object='model', owned_by='Meta', active=True, context_window=8192, public_apps=None, max_completion_tokens=8192), Model(id='qwen/qwen3-32b', created=1748396646, object='model', owned_by='Alibaba Cloud', active=True, context_window=131072, public_apps=None, max_completion_tokens=40960), Model(id='meta-llama/llama-guard-4-12b', created=1746743847, object='model', owned_by='Meta', active=True, context_window=131072, public_apps=None, max_completion_tokens=1024), Model(id='meta-llama/llama-4-scout-17b-16e-instruct', created=1743874824, object='model', owned_by='Meta', active=True, context_window=131072, public_apps=None, max_completion_tokens=8192), Model(id='llama3-8b-8192', created=1693721698, object='model', owned_by='Meta', active=True, context_window=8192, public_apps=None, max_completion_tokens=8192), Model(id='mistral-saba-24b', created=1739996492, object='model', owned_by='Mistral AI', active=True, context_window=32768, public_apps=None, max_completion_tokens=32768), Model(id='playai-tts-arabic', created=1740682783, object='model', owned_by='PlayAI', active=True, context_window=8192, public_apps=None, max_completion_tokens=8192), Model(id='llama-3.1-8b-instant', created=1693721698, object='model', owned_by='Meta', active=True, context_window=131072, public_apps=None, max_completion_tokens=131072), Model(id='distil-whisper-large-v3-en', created=1693721698, object='model', owned_by='Hugging Face', active=True, context_window=448, public_apps=None, max_completion_tokens=448), Model(id='meta-llama/llama-prompt-guard-2-86m', created=1748632165, object='model', owned_by='Meta', active=True, context_window=512, public_apps=None, max_completion_tokens=512), Model(id='qwen-qwq-32b', created=1741214760, object='model', owned_by='Alibaba Cloud', active=True, context_window=131072, public_apps=None, max_completion_tokens=131072), Model(id='playai-tts', created=1740682771, object='model', owned_by='PlayAI', active=True, context_window=8192, public_apps=None, max_completion_tokens=8192), Model(id='gemma2-9b-it', created=1693721698, object='model', owned_by='Google', active=True, context_window=8192, public_apps=None, max_completion_tokens=8192), Model(id='meta-llama/llama-4-maverick-17b-128e-instruct', created=1743877158, object='model', owned_by='Meta', active=True, context_window=131072, public_apps=None, max_completion_tokens=8192), Model(id='compound-beta-mini', created=1742953279, object='model', owned_by='Groq', active=True, context_window=131072, public_apps=None, max_completion_tokens=8192), Model(id='deepseek-r1-distill-llama-70b', created=1737924940, object='model', owned_by='DeepSeek / Meta', active=True, context_window=131072, public_apps=None, max_completion_tokens=131072), Model(id='whisper-large-v3', created=1693721698, object='model', owned_by='OpenAI', active=True, context_window=448, public_apps=None, max_completion_tokens=448)], object='list')\n"]}], "source": ["from groq import Groq\n", "client = Groq(api_key=groq_api_key)\n", "print(client.models.list())\n"]}, {"cell_type": "code", "execution_count": 8, "id": "aea34f42", "metadata": {}, "outputs": [], "source": ["from langchain_groq import ChatGroq\n", "from langchain_experimental.graph_transformers import LLMGraphTransformer\n", "\n", "llm = ChatGroq(\n", "    model=\"gemma2-9b-it\",   # or any verified available model\n", "    api_key=groq_api_key\n", ")\n", "\n", "llm_transformer = LLMGraphTransformer(llm=llm)\n", "\n", "graph_documents = llm_transformer.convert_to_graph_documents(documents)\n"]}, {"cell_type": "code", "execution_count": 18, "id": "316ca310", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Node(id='Com.Morganstanley.Loanapp', type='Package', properties={}),\n", " Node(id='Loanappapplication', type='Class', properties={}),\n", " Node(id='Datasource', type='Object', properties={}),\n", " Node(id='Datasource', type='Object', properties={})]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["graph_documents[0].nodes[:5]  # Display first 5 nodes of the first graph document"]}, {"cell_type": "code", "execution_count": 16, "id": "a611b3f0", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Relationship(source=Node(id='Com.Morganstanley.Loanapp', type='Package', properties={}), target=Node(id='Loanappapplication', type='Class', properties={}), type='CONTAINS', properties={}),\n", " Relationship(source=Node(id='Loanappapplication', type='Class', properties={}), target=Node(id='Datasource', type='Object', properties={}), type='HAS_ATTRIBUTE', properties={})]"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["graph_documents[0].relationships[:5]"]}, {"cell_type": "code", "execution_count": 10, "id": "21c745b2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- Graph Document 1 ---\n", "Nodes: [Node(id='Com.Morganstanley.Loanapp', type='Package', properties={}), Node(id='Loanappapplication', type='Class', properties={}), Node(id='Datasource', type='Object', properties={}), Node(id='Datasource', type='Object', properties={})]\n", "Relationships: [Relationship(source=Node(id='Com.Morganstanley.Loanapp', type='Package', properties={}), target=Node(id='Loanappapplication', type='Class', properties={}), type='CONTAINS', properties={}), Relationship(source=Node(id='Loanappapplication', type='Class', properties={}), target=Node(id='Datasource', type='Object', properties={}), type='HAS_ATTRIBUTE', properties={})]\n", "\n", "\n", "--- Graph Document 2 ---\n", "Nodes: [Node(id='Loanappapplication', type='Class', properties={})]\n", "Relationships: [Relationship(source=Node(id='Loanappapplication', type='Class', properties={}), target=Node(id='Datasource', type='Object', properties={}), type='HAS_ATTRIBUTE', properties={}), Relationship(source=Node(id='Loanappapplication', type='Class', properties={}), target=Node(id='Seconddatasource', type='Object', properties={}), type='HAS_ATTRIBUTE', properties={})]\n", "\n", "\n", "--- Graph Document 3 ---\n", "Nodes: [Node(id='Com.Morganstanley.Loanapp.Model.Loandetails', type='Class', properties={})]\n", "Relationships: [Relationship(source=Node(id='Com.Morganstanley.Loanapp.Model.Loandetails', type='Class', properties={}), target=Node(id='Id', type='Property', properties={}), type='HAS_PROPERTY', properties={}), Relationship(source=Node(id='Com.Morganstanley.Loanapp.Model.Loandetails', type='Class', properties={}), target=Node(id='Loantype', type='Property', properties={}), type='HAS_PROPERTY', properties={}), Relationship(source=Node(id='Com.Morganstanley.Loanapp.Model.Loandetails', type='Class', properties={}), target=Node(id='Amount', type='Property', properties={}), type='HAS_PROPERTY', properties={}), Relationship(source=Node(id='Com.Morganstanley.Loanapp.Model.Loandetails', type='Class', properties={}), target=Node(id='Userid', type='Property', properties={}), type='HAS_PROPERTY', properties={})]\n", "\n", "\n", "--- Graph Document 4 ---\n", "Nodes: [Node(id='Com.Morganstanley.Loanapp.Repository', type='Package', properties={}), Node(id='Com.Morganstanley.Loanapp.Model.Loandetails', type='Class', properties={}), Node(id='Org.Springframework.Data.Jpa.Repository.Jparepository', type='Class', properties={})]\n", "Relationships: [Relationship(source=Node(id='Com.Morganstanley.Loanapp.Repository', type='Package', properties={}), target=Node(id='Com.Morganstanley.Loanapp.Model.Loandetails', type='Class', properties={}), type='IMPORTS', properties={}), Relationship(source=Node(id='Com.Morganstanley.Loanapp.Repository', type='Package', properties={}), target=Node(id='Org.Springframework.Data.Jpa.Repository.Jparepository', type='Class', properties={}), type='IMPLEMENTS', properties={})]\n", "\n", "\n", "--- Graph Document 5 ---\n", "Nodes: [Node(id='Com.Morganstanley.Loanapp.Configuration.Primarydatasourceproperties', type='Class', properties={})]\n", "Relationships: [Relationship(source=Node(id='Com.Morganstanley.Loanapp.Configuration.Primarydatasourceproperties', type='Class', properties={}), target=Node(id='String', type='Type', properties={}), type='HAS_PROPERTY', properties={}), Relationship(source=Node(id='Com.Morganstanley.Loanapp.Configuration.Primarydatasourceproperties', type='Class', properties={}), target=Node(id='String', type='Type', properties={}), type='HAS_PROPERTY', properties={}), Relationship(source=Node(id='Com.Morganstanley.Loanapp.Configuration.Primarydatasourceproperties', type='Class', properties={}), target=Node(id='String', type='Type', properties={}), type='HAS_PROPERTY', properties={}), Relationship(source=Node(id='Com.Morganstanley.Loanapp.Configuration.Primarydatasourceproperties', type='Class', properties={}), target=Node(id='String', type='Type', properties={}), type='HAS_PROPERTY', properties={})]\n", "\n", "\n"]}], "source": ["for i in range(5):\n", "    print(f\"--- Graph Document {i+1} ---\")\n", "    print(\"Nodes:\", graph_documents[i].nodes)\n", "    print(\"Relationships:\", graph_documents[i].relationships)\n", "    print(\"\\n\")"]}, {"cell_type": "code", "execution_count": 11, "id": "82c65a4f", "metadata": {}, "outputs": [], "source": ["NEO4J_URI=\"neo4j+s://0c67ecac.databases.neo4j.io\"\n", "NEO4J_USERNAME=\"neo4j\"\n", "NEO4J_PASSWORD=\"oV1snxwd2J3rukMLIBCilEoCbZOX0JAnc1IGR4vTbF0\"\n", "NEO4J_DATABASE=\"neo4j\""]}, {"cell_type": "code", "execution_count": 12, "id": "b3b1d36e", "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ[\"NEO4J_URI\"]=NEO4J_URI\n", "os.environ[\"NEO4J_USERNAME\"]=NEO4J_USERNAME\n", "os.environ[\"NEO4J_PASSWORD\"]=NEO4J_PASSWORD"]}, {"cell_type": "code", "execution_count": null, "id": "49e378d3", "metadata": {}, "outputs": [], "source": ["from langchain_community.graphs import Neo4jGraph\n", "graph=Neo4jGraph(\n", "    url=NEO4J_URI,\n", "    username=NEO4J_USERNAME,\n", "    password=NEO4J_PASSWORD,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7d15c359", "metadata": {}, "outputs": [], "source": ["graph"]}, {"cell_type": "code", "execution_count": null, "id": "e581542d", "metadata": {}, "outputs": [], "source": ["graph.refresh_schema()\n", "print(graph.schema)"]}, {"cell_type": "code", "execution_count": null, "id": "8e31d012", "metadata": {}, "outputs": [], "source": ["graph.add_graph_documents(graph_documents)"]}, {"cell_type": "code", "execution_count": null, "id": "40659e21", "metadata": {}, "outputs": [], "source": ["graph.refresh_schema()\n", "print(graph.schema)"]}, {"cell_type": "code", "execution_count": null, "id": "69f8d6c9", "metadata": {}, "outputs": [], "source": ["from langchain.chains import GraphCypherQAChain\n", "chain=GraphCypherQAChain.from_llm(llm=llm,graph=graph,verbose=True,allow_dangerous_requests=True)\n", "chain"]}, {"cell_type": "code", "execution_count": null, "id": "77e04933", "metadata": {}, "outputs": [], "source": ["response=chain.invoke({\"query\":\"how many classes we have\"})\n", "\n", "response\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}