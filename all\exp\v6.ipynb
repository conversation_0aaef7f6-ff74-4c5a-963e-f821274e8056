{"cells": [{"cell_type": "code", "execution_count": 36, "id": "45117d8d", "metadata": {}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["graph.query(\"MATCH (n) DETACH DELETE n\")"]}, {"cell_type": "code", "execution_count": 39, "id": "afa9ad5b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Stage 0: Extracted total 19 folder-file relationships.\n", "✅ Stage 1: Extracted 244 AST records.\n", "✅ Prepared 262 AST + structure records.\n", "✅ Prepared 14 LLM chunks.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["LLM Extraction: 100%|██████████| 14/14 [08:41<00:00, 37.22s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Stage 2: Collected 133 LLM triples.\n", "✅ Combined final lineage with 256 rows. Pushing to Neo4j...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Pushing to Neo4j: 100%|██████████| 256/256 [00:01<00:00, 129.56it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Full ingestion complete. Open Neo4j Bloom/Browser to visualize your enriched lineage graph.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# ===============================\n", "# Imports & Config\n", "# ===============================\n", "import os\n", "from pathlib import Path\n", "import pandas as pd\n", "from tqdm import tqdm\n", "from tree_sitter import Language, Parser\n", "import tree_sitter_java as tsjava\n", "\n", "from langchain_community.document_loaders import TextLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter, Language as LC_Language\n", "from langchain_google_genai import ChatGoogleGenerativeAI\n", "from langchain_experimental.graph_transformers import LLMGraphTransformer\n", "from langchain_community.graphs import Neo4jGraph\n", "\n", "# ===============================\n", "# Configuration\n", "# ===============================\n", "BASE_PATH = Path(\"C:/Shaik/sample/java\").resolve()\n", "NEO4J_URI = \"bolt://localhost:7687\"\n", "NEO4J_USER = \"neo4j\"\n", "NEO4J_PASSWORD = \"Test@7889\"\n", "NEO4J_DB = \"astllm3\"\n", "google_api_key = \"AIzaSyDlwEbSYmqOoIGbh-Upn2ZGXR3nwAfxcQ0\"\n", "\n", "llm = ChatGoogleGenerativeAI(model=\"gemini-2.5-pro\", temperature=0, google_api_key=google_api_key)\n", "graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)\n", "\n", "JAVA_LANGUAGE = Language(tsjava.language())\n", "parser = Parser(JAVA_LANGUAGE)\n", "\n", "# ===============================\n", "# Stage 0: Extract Folder & File Hierarchy\n", "# ===============================\n", "\n", "def extract_folder_file_hierarchy(base_path: Path):\n", "    all_records = []\n", "    base_folder_name = base_path.name\n", "\n", "    # Root folder node (no relationship)\n", "    all_records.append({\n", "        \"source_node\": None,\n", "        \"source_type\": None,\n", "        \"destination_node\": base_folder_name,\n", "        \"destination_type\": \"Folder\",\n", "        \"relationship\": None,\n", "        \"file_path\": None,\n", "        \"source_properties\": {\"folder_path\": str(base_path)},\n", "        \"relationship_properties\": {}\n", "    })\n", "\n", "    for root, dirs, files in os.walk(base_path):\n", "        root_path = Path(root)\n", "        rel_root = root_path.relative_to(base_path)\n", "        current_folder = rel_root.name if rel_root != Path(\".\") else base_folder_name\n", "        parent_folder = rel_root.parent.name if rel_root != Path(\".\") and rel_root.parent != Path(\".\") else base_folder_name\n", "\n", "        # Add folder-to-folder relationship if not root\n", "        if rel_root != Path(\".\"):\n", "            all_records.append({\n", "                \"source_node\": parent_folder,\n", "                \"source_type\": \"Folder\",\n", "                \"destination_node\": current_folder,\n", "                \"destination_type\": \"Folder\",\n", "                \"relationship\": \"CONTAINS\",\n", "                \"file_path\": None,\n", "                \"source_properties\": {\"folder_path\": str(root_path)},\n", "                \"relationship_properties\": {\"from\": parent_folder, \"to\": current_folder}\n", "            })\n", "\n", "        # Add folder-to-file relationships\n", "        for f in files:\n", "            if f.endswith(\".java\"):\n", "                file_path = root_path / f\n", "                file_rel_path = file_path.relative_to(base_path)\n", "                all_records.append({\n", "                    \"source_node\": current_folder,\n", "                    \"source_type\": \"Folder\",\n", "                    \"destination_node\": f,\n", "                    \"destination_type\": \"File\",\n", "                    \"relationship\": \"CONTAINS\",\n", "                    \"file_path\": str(file_rel_path),\n", "                    \"source_properties\": {\"file_path\": str(file_rel_path)},\n", "                    \"relationship_properties\": {\"from\": current_folder, \"to\": f}\n", "                })\n", "\n", "    return all_records\n", "\n", "all_folder_file_records = extract_folder_file_hierarchy(BASE_PATH)\n", "print(f\"✅ Stage 0: Extracted total {len(all_folder_file_records)} folder-file relationships.\")\n", "\n", "# Convert folder-file records to DataFrame for downstream use\n", "df_folders_files = pd.DataFrame(all_folder_file_records)\n", "\n", "# ===============================\n", "# Stage 1: AST Extraction\n", "# ===============================\n", "\n", "def read_source_code(file_path):\n", "    with open(file_path, \"r\", encoding=\"utf-8\") as f:\n", "        return f.read().encode(\"utf-8\")\n", "\n", "def extract_ast_structure(node, source_code, file_path):\n", "    records = []\n", "    current_class, current_method = None, None\n", "\n", "    def traverse(n):\n", "        nonlocal current_class, current_method\n", "        line_number = n.start_point[0] + 1\n", "\n", "        if n.type == \"class_declaration\":\n", "            for child in n.children:\n", "                if child.type == \"identifier\":\n", "                    class_name = source_code[child.start_byte:child.end_byte].decode()\n", "                    records.append({\n", "                        \"source_node\": os.path.basename(file_path), \"source_type\": \"File\",\n", "                        \"destination_node\": class_name, \"destination_type\": \"Class\",\n", "                        \"relationship\": \"DECLARES\", \"file_path\": file_path,\n", "                        \"relationship_properties\": {\"from\": os.path.basename(file_path), \"to\": class_name},\n", "                        \"source_properties\": {\"defined_in_file\": os.path.basename(file_path), \"line_number\": line_number}\n", "                    })\n", "                    current_class = class_name\n", "            for child in n.children:\n", "                traverse(child)\n", "            current_class = None\n", "\n", "        elif n.type == \"method_declaration\":\n", "            for child in n.children:\n", "                if child.type == \"identifier\":\n", "                    method_name = source_code[child.start_byte:child.end_byte].decode()\n", "                    records.append({\n", "                        \"source_node\": current_class, \"source_type\": \"Class\",\n", "                        \"destination_node\": method_name, \"destination_type\": \"Method\",\n", "                        \"relationship\": \"DECLARES\", \"file_path\": file_path,\n", "                        \"relationship_properties\": {\"from\": current_class, \"to\": method_name},\n", "                        \"source_properties\": {\"defined_in_class\": current_class, \"defined_in_file\": os.path.basename(file_path), \"line_number\": line_number}\n", "                    })\n", "                    current_method = method_name\n", "            for child in n.children:\n", "                traverse(child)\n", "            current_method = None\n", "\n", "        elif n.type == \"method_invocation\":\n", "            for child in n.children:\n", "                if child.type == \"identifier\":\n", "                    called_method = source_code[child.start_byte:child.end_byte].decode()\n", "                    records.append({\n", "                        \"source_node\": current_method, \"source_type\": \"Method\",\n", "                        \"destination_node\": called_method, \"destination_type\": \"Method\",\n", "                        \"relationship\": \"CALLS\", \"file_path\": file_path,\n", "                        \"relationship_properties\": {\"from\": current_method, \"to\": called_method, \"line_number\": line_number},\n", "                        \"source_properties\": {\"in_method\": current_method, \"defined_in_class\": current_class, \"defined_in_file\": os.path.basename(file_path)}\n", "                    })\n", "            for child in n.children:\n", "                traverse(child)\n", "\n", "        elif n.type == \"variable_declarator\":\n", "            for child in n.children:\n", "                if child.type == \"identifier\":\n", "                    var_name = source_code[child.start_byte:child.end_byte].decode()\n", "                    source = current_method or current_class or os.path.basename(file_path)\n", "                    source_type = \"Method\" if current_method else \"Class\" if current_class else \"File\"\n", "                    rel = \"USES\" if current_method else \"HAS_FIELD\" if current_class else \"DECLARES\"\n", "                    records.append({\n", "                        \"source_node\": source, \"source_type\": source_type,\n", "                        \"destination_node\": var_name, \"destination_type\": \"Variable\",\n", "                        \"relationship\": rel, \"file_path\": file_path,\n", "                        \"relationship_properties\": {\"from\": source, \"to\": var_name, \"line_number\": line_number},\n", "                        \"source_properties\": {\"defined_in_class\": current_class, \"defined_in_file\": os.path.basename(file_path), \"line_number\": line_number}\n", "                    })\n", "            for child in n.children:\n", "                traverse(child)\n", "        else:\n", "            for child in n.children:\n", "                traverse(child)\n", "\n", "    traverse(node)\n", "    return records\n", "\n", "ast_records = []\n", "for root, _, files in os.walk(BASE_PATH):\n", "    for file in files:\n", "        if file.endswith(\".java\"):\n", "            fpath = os.path.join(root, file)\n", "            source_code = read_source_code(fpath)\n", "            tree = parser.parse(source_code)\n", "            ast_records.extend(extract_ast_structure(tree.root_node, source_code, fpath))\n", "\n", "print(f\"✅ Stage 1: Extracted {len(ast_records)} AST records.\")\n", "\n", "df_ast = pd.DataFrame(ast_records)\n", "\n", "# ===============================\n", "# Stage 1.5: Combine Folder/File and AST DataFrames\n", "# ===============================\n", "\n", "# Use df_folders_files and df_ast\n", "df_combined_ast = pd.concat([df_folders_files, df_ast], ignore_index=True)\n", "\n", "# Drop rows with missing critical info\n", "df_combined_ast = df_combined_ast.dropna(subset=[\"source_node\", \"destination_node\", \"source_type\", \"destination_type\", \"relationship\"])\n", "\n", "# Normalize key columns to lowercase and strip spaces\n", "for col in [\"source_node\", \"destination_node\", \"source_type\", \"destination_type\", \"relationship\"]:\n", "    df_combined_ast[col] = df_combined_ast[col].astype(str).str.strip().str.lower()\n", "\n", "print(f\"✅ Prepared {len(df_combined_ast)} AST + structure records.\")\n", "\n", "# ===============================\n", "# Stage 2: LLM Extraction\n", "# ===============================\n", "\n", "splitter = RecursiveCharacterTextSplitter.from_language(language=LC_Language.JAVA, chunk_size=2000, chunk_overlap=200)\n", "\n", "java_docs = []\n", "for root, _, files in os.walk(BASE_PATH):\n", "    for file in files:\n", "        if file.endswith(\".java\"):\n", "            loader = TextLoader(os.path.join(root, file))\n", "            java_docs.extend(loader.load())\n", "\n", "split_docs = []\n", "for doc in java_docs:\n", "    split_docs.extend(splitter.split_documents([doc]))\n", "\n", "print(f\"✅ Prepared {len(split_docs)} LLM chunks.\")\n", "\n", "def build_system_prompt(file_path, ast_df):\n", "    ast_subset = ast_df[ast_df[\"file_path\"] == file_path]\n", "    ast_context = \"\"\n", "    for _, row in ast_subset.iterrows():\n", "        ast_context += f\"{row['source_type'].capitalize()}:{row['source_node']} -[{row['relationship'].upper()}]-> {row['destination_type'].capitalize()}:{row['destination_node']}\\n\"\n", "    prompt = f\"\"\"\n", "You are a Java code lineage extraction engine.\n", "\n", "Given the Java code chunk and the following AST context:\n", "{ast_context}\n", "\n", "Extract lineage triples in JSON format:\n", "{{\n", "    \"source_node\": \"...\",\n", "    \"source_type\": \"...\",\n", "    \"destination_node\": \"...\",\n", "    \"destination_type\": \"...\",\n", "    \"relationship\": \"...\",\n", "    \"source_properties\": {{}},\n", "    \"relationship_properties\": {{}}\n", "}}\n", "\n", "Return only JSON arrays without explanation.\n", "\"\"\"\n", "    # No double curly braces replacement needed here because using f-string directly with triple quotes\n", "    return prompt.replace(\"{\", \"{{\").replace(\"}\", \"}}\")\n", "\n", "all_llm_lineage = []\n", "\n", "for chunk in tqdm(split_docs, desc=\"LLM Extraction\"):\n", "    source_file = chunk.metadata.get(\"source\", None)\n", "    system_prompt = build_system_prompt(source_file, df_combined_ast)\n", "\n", "    transformer = LLMGraphTransformer(\n", "        llm=llm,\n", "        additional_instructions=system_prompt,\n", "        allowed_nodes=[\"File\", \"Class\", \"Method\", \"Variable\", \"Table\", \"API\"],\n", "        allowed_relationships=[\n", "            (\"File\", \"DECLARES\", \"Class\"),\n", "            (\"Class\", \"DECLARES\", \"Method\"),\n", "            (\"Method\", \"CALLS\", \"Method\"),\n", "            (\"Class\", \"HAS_FIELD\", \"Variable\"),\n", "            (\"Method\", \"USES\", \"Variable\"),\n", "            (\"Method\", \"READS_FROM\", \"Table\"),\n", "            (\"Method\", \"WRITES_TO\", \"Table\"),\n", "            (\"Method\", \"CALLS_API\", \"API\"),\n", "            (\"Variable\", \"ASSIGNS\", \"Variable\"),\n", "        ],\n", "        strict_mode=True,\n", "        node_properties=True,\n", "        relationship_properties=True,\n", "    )\n", "\n", "    graph_docs = transformer.convert_to_graph_documents([chunk])\n", "    for gd in graph_docs:\n", "        for rel in gd.relationships:\n", "            all_llm_lineage.append({\n", "                \"source_node\": rel.source.id.strip().lower() if rel.source.id else None,\n", "                \"source_type\": rel.source.type.strip().lower() if rel.source.type else None,\n", "                \"destination_node\": rel.target.id.strip().lower() if rel.target.id else None,\n", "                \"destination_type\": rel.target.type.strip().lower() if rel.target.type else None,\n", "                \"relationship\": rel.type.strip().lower() if rel.type else None,\n", "                \"file_path\": source_file,\n", "                \"source_properties\": rel.source.properties if isinstance(rel.source.properties, dict) else {},\n", "                \"relationship_properties\": rel.properties if isinstance(rel.properties, dict) else {},\n", "            })\n", "\n", "print(f\"✅ Stage 2: Collected {len(all_llm_lineage)} LLM triples.\")\n", "\n", "df_llm_lineage = pd.DataFrame(all_llm_lineage)\n", "\n", "# Drop incomplete rows\n", "df_llm_lineage = df_llm_lineage.dropna(subset=[\n", "    \"source_node\", \"source_type\", \"destination_node\", \"destination_type\", \"relationship\"\n", "])\n", "\n", "# Normalize columns\n", "for col in [\"source_node\", \"destination_node\", \"source_type\", \"destination_type\", \"relationship\"]:\n", "    df_llm_lineage[col] = df_llm_lineage[col].astype(str).str.strip().str.lower()\n", "\n", "# ===============================\n", "# Final Combine & Deduplicate\n", "# ===============================\n", "\n", "df_combined = pd.concat([df_combined_ast, df_llm_lineage], ignore_index=True)\n", "\n", "df_combined = df_combined.drop_duplicates(subset=[\n", "    \"source_node\",\n", "    \"source_type\",\n", "    \"destination_node\",\n", "    \"destination_type\",\n", "    \"relationship\",\n", "    \"file_path\"\n", "])\n", "\n", "print(f\"✅ Combined final lineage with {len(df_combined)} rows. Pushing to Neo4j...\")\n", "\n", "# ===============================\n", "# Push to Neo4j\n", "# ===============================\n", "\n", "for idx, row in tqdm(df_combined.iterrows(), total=len(df_combined), desc=\"Pushing to Neo4j\"):\n", "    try:\n", "        source_label = (row[\"source_type\"] or \"Unknown\").capitalize()\n", "        dest_label = (row[\"destination_type\"] or \"Unknown\").capitalize()\n", "        rel_type = (row[\"relationship\"] or \"RELATED\").upper()\n", "\n", "        # Safely get property dicts or empty dicts\n", "        source_props = row.get(\"source_properties\", {})\n", "        if not isinstance(source_props, dict):\n", "            source_props = {}\n", "\n", "        # destination_properties column may not exist or empty in your data, so create empty dict\n", "        destination_props = row.get(\"destination_properties\", {})\n", "        if not isinstance(destination_props, dict):\n", "            destination_props = {}\n", "\n", "        relationship_props = row.get(\"relationship_properties\", {})\n", "        if not isinstance(relationship_props, dict):\n", "            relationship_props = {}\n", "\n", "        graph.query(\n", "            f\"\"\"\n", "            MERGE (s:{source_label} {{name: $source_node}})\n", "            SET s += $source_properties\n", "            MERGE (t:{dest_label} {{name: $destination_node}})\n", "            SET t += $destination_properties\n", "            MERGE (s)-[r:{rel_type}]->(t)\n", "            SET r += $relationship_properties\n", "            \"\"\",\n", "            {\n", "                \"source_node\": row[\"source_node\"],\n", "                \"destination_node\": row[\"destination_node\"],\n", "                \"source_properties\": source_props,\n", "                \"destination_properties\": destination_props,\n", "                \"relationship_properties\": relationship_props,\n", "            }\n", "        )\n", "    except Exception as e:\n", "        print(f\"Error at row {idx}: {e}\")\n", "\n", "print(\"✅ Full ingestion complete. Open Neo4j Bloom/Browser to visualize your enriched lineage graph.\")\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}