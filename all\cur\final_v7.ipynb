{"cells": [{"cell_type": "code", "execution_count": null, "id": "0d635234", "metadata": {}, "outputs": [], "source": ["import os \n", "from pathlib import Path \n", "from tqdm import tqdm \n", "import pandas as pd \n", "import re \n", "from collections import defaultdict \n", " \n", "from tree_sitter import Language, Parser \n", "import tree_sitter_java as tsjava \n", " \n", "from langchain_community.document_loaders import TextLoader \n", "from langchain.text_splitter import RecursiveCharacterTextSplitter, Language as LC_Language \n", "from langchain_google_genai import ChatGoogleGenerativeAI \n", "from langchain_experimental.graph_transformers import LLMGraphTransformer \n", "from langchain_community.graphs import Neo4jGraph \n", " \n", "BASE_PATH = Path(r \"C:/Shaik/sample/GihubActions \") \n", "NEO4J_URI =  \"bolt://localhost:7687 \" \n", "NEO4J_USER =  \"neo4j \" \n", "NEO4J_PASSWORD =  \"Test@7889 \" \n", "NEO4J_DB =  \"githubactions2 \" \n", "GOOGLE_API_KEY =  \"AIzaSyASelgh7zLlDIpbpNyJkgkimlM_ODU86dY \" \n", " \n", "graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB) \n", "JAVA_LANGUAGE = Language(tsjava.language()) \n", "parser = Parser(JAVA_LANGUAGE) \n", " \n", "llm = ChatGoogleGenerativeAI( \n", "  model= \"gemini-2.5-pro \", \n", "  temperature=0, \n", "  google_api_key=GOOGLE_API_KEY \n", ") \n", " \n", "# Node colors configuration \n", "NODE_COLORS = { \n", "  'folder': '#FFB6C1',  # Light Pink \n", "  'file': '#87CEEB',   # Sky Blue \n", "  'class': '#98FB98',   # <PERSON><PERSON> \n", "  'interface': '#DDA0DD', # Plum \n", "  'method': '#F0E68C',  # Khaki \n", "  'variable': '#FFA07A', # <PERSON> Salmon \n", "  'endpoint': '#20B2AA', # Light Sea Green \n", "  'table': '#D2691E'   # Chocolate \n", "} \n", " \n", "# Relationship colors configuration \n", "RELATIONSHIP_COLORS = { \n", "  'contains': '#4169E1',   # Royal Blue \n", "  'declares': '#32CD32',   # <PERSON><PERSON> \n", "  'has_field': '#FF6347',  # <PERSON><PERSON> \n", "  'uses': '#9370DB',     # Medium Purple \n", "  'calls': '#FF1493',    # Deep Pink \n", "  'extends': '#00CED1',   # Dark Turquoise \n", "  'implements': '#FF8C00',  # Dark Orange \n", "  'maps_to': '#8B4513',   # <PERSON><PERSON> \n", "  'reads_from': '#2E8B57',  # Sea Green \n", "  'writes_to': '#B22222'   # Fire Brick \n", "} \n", " \n", "# Common temp variables to filter out \n", "TEMP_VARIABLES = { \n", "  'i', 'j', 'k', 'l', 'm', 'n', 'x', 'y', 'z', \n", "  'temp', 'tmp', 'temporary', 'temp1', 'temp2', \n", "  'count', 'counter', 'index', 'idx', 'iter', \n", "  'result', 'res', 'ret', 'val', 'value', \n", "  'item', 'elem', 'element', 'obj', 'object', \n", "  'str', 'string', 'num', 'number', 'flag', \n", "  'bool', 'boolean', 'arr', 'array', 'list', \n", "  'map', 'set', 'data', 'info', 'param', 'arg' \n", "} \n", " \n", "def to_pascal_case(text): \n", "   \" \" \"Convert text to PascalCase \" \" \" \n", "  if not text: \n", "    return text \n", "  # Remove file extensions first \n", "  text = re.sub(r'  .(java|class)$', '', text, flags=re.IGNORECASE) \n", "  # Split on various delimiters and capitalize each part \n", "  parts = re.split(r'[_  -  s  .]+', text) \n", "  return ''.join(word.capitalize() for word in parts if word) \n", " \n", "def is_temp_variable(var_name): \n", "   \" \" \"Check if variable is a common temp variable \" \" \" \n", "  if not var_name: \n", "    return True \n", "  var_lower = var_name.lower().strip() \n", "  return var_lower in TEMP_VARIABLES or len(var_lower) <= 1"]}, {"cell_type": "code", "execution_count": null, "id": "81bc72fd", "metadata": {}, "outputs": [], "source": ["\n", "# ========== STAGE 1: FOLDER + FILE HIERARCHY ==========\n", "def extract_folder_file_hierarchy(base_path):\n", "  folder_records, file_records = [], []\n", "  base_path = os.path.abspath(base_path)\n", "  base_folder_name = os.path.basename(base_path)\n", "  processed_folders = set()\n", "\n", "  for root, dirs, files in os.walk(base_path):\n", "    rel_root = os.path.relpath(root, base_path)\n", "    parent_folder = base_folder_name if rel_root == '.' else os.path.dirname(rel_root) or base_folder_name\n", "    current_folder = base_folder_name if rel_root == '.' else os.path.basename(rel_root)\n", "\n", "    folder_key = f'{parent_folder}->{current_folder}'\n", "    if folder_key not in processed_folders and parent_folder != current_folder:\n", "      folder_records.append({\n", "        'source_node': parent_folder,\n", "        'source_type': 'folder',\n", "        'destination_node': current_folder,\n", "        'destination_type': 'folder',\n", "        'relationship': 'contains',\n", "        'file_path': None\n", "      })\n", "      processed_folders.add(folder_key)\n", "\n", "    for f in files:\n", "      if f.endswith('.java'):\n", "        file_rel_path = os.path.relpath(os.path.join(root, f), base_path)\n", "        file_records.append({\n", "          'source_node': current_folder,\n", "          'source_type': 'folder',\n", "          'destination_node': f,\n", "          'destination_type': 'file',\n", "          'relationship': 'contains',\n", "          'file_path': file_rel_path\n", "        })\n", "  return folder_records, file_records\n", "\n", "folder_records, file_records = extract_folder_file_hierarchy(BASE_PATH)\n", "df_folders = pd.DataFrame(folder_records)\n", "df_files = pd.DataFrame(file_records)\n", "\n", "print(f'Stage 1 Complete: {len(df_folders)} folder relationships, {len(df_files)} file relationships')"]}, {"cell_type": "code", "execution_count": null, "id": "b09fb3b0", "metadata": {}, "outputs": [], "source": ["\n", "# ========== UTILITY FUNCTIONS ==========\n", "def read_source_code(file_path):\n", "  with open(file_path, 'r', encoding='utf-8') as f:\n", "    return f.read().encode('utf-8')\n", "\n", "def extract_package_and_imports(source_code_str):\n", "  package_pattern = r'package s+([ w .]+);'\n", "  import_pattern = r'import s+([ w .]+);'\n", "  package_match = re.search(package_pattern, source_code_str)\n", "  package_name = package_match.group(1) if package_match else None\n", "  import_matches = re.findall(import_pattern, source_code_str)\n", "  return package_name, import_matches\n", "\n", "def extract_api_endpoints(source_code_str):\n", "  endpoints = []\n", "  # Enhanced patterns to catch more API endpoint variations\n", "  mapping_patterns = {\n", "    'RequestMapping': [\n", "      r'@RequestMapping s* ( s*value s*= s*[\" ']([^\" ']+)[\" ']',\n", "      r'@RequestMapping s* ( s*[\" ']([^\" ']+)[\" ']',\n", "      r'@RequestMapping s* ( s*path s*= s*[\" ']([^\" ']+)[\" ']'\n", "    ],\n", "    'GetMapping': [\n", "      r'@GetMapping s* ( s*[\" ']([^\" ']+)[\" ']',\n", "      r'@GetMapping s* ( s*value s*= s*[\" ']([^\" ']+)[\" ']',\n", "      r'@GetMapping s* ( s*path s*= s*[\" ']([^\" ']+)[\" ']'\n", "    ],\n", "    'PostMapping': [\n", "      r'@PostMapping s* ( s*[\" ']([^\" ']+)[\" ']',\n", "      r'@PostMapping s* ( s*value s*= s*[\" ']([^\" ']+)[\" ']',\n", "      r'@PostMapping s* ( s*path s*= s*[\" ']([^\" ']+)[\" ']'\n", "    ],\n", "    'PutMapping': [\n", "      r'@PutMapping s* ( s*[\" ']([^\" ']+)[\" ']',\n", "      r'@PutMapping s* ( s*value s*= s*[\" ']([^\" ']+)[\" ']'\n", "    ],\n", "    'DeleteMapping': [\n", "      r'@DeleteMapping s* ( s*[\" ']([^\" ']+)[\" ']',\n", "      r'@DeleteMapping s* ( s*value s*= s*[\" ']([^\" ']+)[\" ']'\n", "    ],\n", "    'PatchMapping': [\n", "      r'@PatchMapping s* ( s*[\" ']([^\" ']+)[\" ']',\n", "      r'@PatchMapping s* ( s*value s*= s*[\" ']([^\" ']+)[\" ']'\n", "    ]\n", "  }\n", "  \n", "  for mapping_type, patterns in mapping_patterns.items():\n", "    for pattern in patterns:\n", "      matches = re.findall(pattern, source_code_str, re.MULTILINE | re.DOTALL)\n", "      for match in matches:\n", "        if match.strip(): # Only add non-empty matches\n", "          endpoints.append({\n", "            'type': mapping_type,\n", "            'path': match.strip(),\n", "            'method': mapping_type.replace('Mapping', '').upper() if mapping_type != 'RequestMapping' else 'GET'\n", "          })\n", "  \n", "  # Also look for @RestController and @Controller classes\n", "  controller_pattern = r'@(RestController|Controller)'\n", "  if re.search(controller_pattern, source_code_str):\n", "    # Extract class-level RequestMapping\n", "    class_mapping = re.search(r'@RequestMapping s* ( s*[\" ']([^\" ']+)[\" ']', source_code_str)\n", "    base_path = class_mapping.group(1) if class_mapping else \"\"\n", "    \n", "    # Find methods with mappings\n", "    method_pattern = r'@(GetMapping|PostMapping|PutMapping|DeleteMapping|PatchMapping|RequestMapping) s*(?: ([^)]* ))? s*  s*(?:public|private|protected)? s* w+ s+( w+) s* ('\n", "    method_matches = re.findall(method_pattern, source_code_str, re.MULTILINE)\n", "    \n", "    for mapping_type, method_name in method_matches:\n", "      if base_path:\n", "        full_path = f\"{base_path.rstrip('/')}/{method_name}\"\n", "      else:\n", "        full_path = f\"/{method_name}\"\n", "      \n", "      endpoints.append({\n", "        'type': mapping_type,\n", "        'path': full_path,\n", "        'method': mapping_type.replace('Mapping', '').upper() if mapping_type != 'RequestMapping' else 'GET',\n", "        'method_name': method_name\n", "      })\n", "  \n", "  return endpoints\n", "\n", "def extract_database_entities(source_code_str):\n", "  entities = []\n", "  \n", "  # Enhanced Entity detection\n", "  entity_patterns = [\n", "    r'@Entity s*(?: ([^)]* ))?',\n", "    r'@Table s* ( s*name s*= s*[\" ']([^\" ']+)[\" ']',\n", "    r'@Entity s*  s*@Table s* ( s*name s*= s*[\" ']([^\" ']+)[\" ']'\n", "  ]\n", "  \n", "  for pattern in entity_patterns:\n", "    if re.search(pattern, source_code_str, re.MULTILINE):\n", "      # Extract table name from @Table annotation\n", "      table_matches = re.findall(r'@Table s* ( s*name s*= s*[\" ']([^\" ']+)[\" ']', source_code_str)\n", "      for table_name in table_matches:\n", "        if table_name.strip():\n", "          entities.append({\n", "            'type': 'table',\n", "            'name': table_name.strip()\n", "          })\n", "      \n", "      # If no @Table, try to infer from class name\n", "      if not table_matches:\n", "        class_match = re.search(r'public s+class s+( w+)', source_code_str)\n", "        if class_match:\n", "          class_name = class_match.group(1)\n", "          # Convert CamelCase to snake_case for table name\n", "          table_name = re.sub('([a-z0-9])([A-Z])', r' 1_ 2', class_name).lower()\n", "          entities.append({\n", "            'type': 'table',\n", "            'name': table_name\n", "          })\n", "  \n", "  # Enhanced Query detection\n", "  query_patterns = [\n", "    r'@Query s* ( s*[\" ']([^\" ']*(?:FROM|from) s+([ w]+)[^\" ']*)[\" ']',\n", "    r'@Query s* ( s*value s*= s*[\" ']([^\" ']*(?:FROM|from) s+([ w]+)[^\" ']*)[\" ']',\n", "    r'@Query s* ( s*nativeQuery s*= s*true s*, s*value s*= s*[\" ']([^\" ']*(?:FROM|from) s+([ w]+)[^\" ']*)[\" ']'\n", "  ]\n", "  \n", "  for pattern in query_patterns:\n", "    query_matches = re.findall(pattern, source_code_str, re.MULTILINE | re.IGNORECASE)\n", "    for match in query_matches:\n", "      if isinstance(match, tuple) and len(match) >= 2:\n", "        table_name = match[1].strip()\n", "        if table_name and table_name.lower() not in ['select', 'where', 'order', 'group']:\n", "          entities.append({\n", "            'type': 'table',\n", "            'name': table_name\n", "          })\n", "  \n", "  # Repository pattern detection\n", "  repository_pattern = r'interface s+( w+) s+extends s+.*Repository'\n", "  repo_matches = re.findall(repository_pattern, source_code_str)\n", "  for repo_name in repo_matches:\n", "    # Extract entity name from repository name (e.g., UserRepository -> User)\n", "    entity_name = repo_name.replace('Repository', '')\n", "    if entity_name:\n", "      entities.append({\n", "        'type': 'table',\n", "        'name': entity_name.lower()\n", "      })\n", "  \n", "  return entities"]}, {"cell_type": "code", "execution_count": null, "id": "f5045662", "metadata": {}, "outputs": [], "source": ["\n", "\n", "def extract_interface_extends(source_code_str):\n", "  extends_relationships = []\n", "  \n", "  # Interface extends\n", "  interface_extends_pattern = r'interface s+( w+) s+extends s+([ w<>, s]+)'\n", "  matches = re.findall(interface_extends_pattern, source_code_str)\n", "  for interface_name, extends_clause in matches:\n", "    parent_interfaces = [part.strip().split('<')[0].strip() for part in extends_clause.split(',')]\n", "    for parent in parent_interfaces:\n", "      if parent:\n", "        extends_relationships.append({\n", "          'child_interface': interface_name,\n", "          'parent_interface': parent,\n", "          'full_extends': extends_clause.strip(),\n", "          'type': 'interface_extends'\n", "        })\n", "  \n", "  # Class extends\n", "  class_extends_pattern = r'class s+( w+) s+extends s+([ w<>]+)'\n", "  class_matches = re.findall(class_extends_pattern, source_code_str)\n", "  for child_class, parent_class in class_matches:\n", "    # Clean generic types\n", "    parent_class = re.sub(r'<.*?>', '', parent_class).strip()\n", "    if parent_class:\n", "      extends_relationships.append({\n", "        'child_interface': child_class,\n", "        'parent_interface': parent_class,\n", "        'full_extends': parent_class,\n", "        'type': 'class_extends'\n", "      })\n", "  \n", "  # Class implements\n", "  implements_pattern = r'class s+( w+)(?: s+extends s+ w+)? s+implements s+([ w<>, s]+)'\n", "  impl_matches = re.findall(implements_pattern, source_code_str)\n", "  for class_name, implements_clause in impl_matches:\n", "    interfaces = [part.strip().split('<')[0].strip() for part in implements_clause.split(',')]\n", "    for interface in interfaces:\n", "      if interface:\n", "        extends_relationships.append({\n", "          'child_interface': class_name,\n", "          'parent_interface': interface,\n", "          'full_extends': implements_clause.strip(),\n", "          'type': 'class_implements'\n", "        })\n", "  \n", "  return extends_relationships"]}, {"cell_type": "code", "execution_count": null, "id": "aec994ea", "metadata": {}, "outputs": [], "source": ["\n", "\n", "def build_enhanced_class_registry():\n", "  class_registry = {}\n", "  for root, _, files in os.walk(BASE_PATH):\n", "    for file in files:\n", "      if file.endswith('.java'):\n", "        file_path = os.path.join(root, file)\n", "        try:\n", "          with open(file_path, 'r', encoding='utf-8') as f:\n", "            source_code_str = f.read()\n", "          \n", "          package_name, imports = extract_package_and_imports(source_code_str)\n", "          endpoints = extract_api_endpoints(source_code_str)\n", "          db_entities = extract_database_entities(source_code_str)\n", "          interface_extends = extract_interface_extends(source_code_str)\n", "          \n", "          class_name = file.replace('.java', '')\n", "          fqcn = f'{package_name}.{class_name}' if package_name else class_name\n", "          \n", "          class_registry[class_name] = {\n", "            'fqcn': fqcn,\n", "            'package': package_name,\n", "            'file_path': file_path,\n", "            'imports': imports,\n", "            'endpoints': endpoints,\n", "            'db_entities': db_entities,\n", "            'interface_extends': interface_extends\n", "          }\n", "          \n", "          # Debug output for endpoints and DB entities\n", "          if endpoints:\n", "            print(f\"🔗 Found {len(endpoints)} endpoints in {class_name}\")\n", "          if db_entities:\n", "            print(f\"🗄️ Found {len(db_entities)} DB entities in {class_name}\")\n", "            \n", "        except Exception as e:\n", "          print(f\" Error processing {file}: {e}\")\n", "          continue\n", "  \n", "  return class_registry\n", "\n", "class_registry = build_enhanced_class_registry()\n", "print(f' Class registry built with {len(class_registry)} classes')"]}, {"cell_type": "code", "execution_count": null, "id": "58fb060c", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 2: AST EXTRACTION ==========\n", "\n", "def extract_ast_structure(file_path):\n", "  records = []\n", "  source_code = read_source_code(file_path)\n", "  tree = parser.parse(source_code)\n", "  root_node = tree.root_node\n", "  file_name = os.path.basename(file_path)\n", "\n", "  def clean_node_name(name):\n", "    \"\"\"Clean node names to remove prefixes and suffixes\"\"\"\n", "    if not name:\n", "      return name\n", "    \n", "    # Remove common prefixes\n", "    prefixes_to_remove = ['method:', 'class:', 'variable:', 'field:']\n", "    for prefix in prefixes_to_remove:\n", "      if name.lower().startswith(prefix):\n", "        name = name[len(prefix):]\n", "    \n", "    # Remove file extensions\n", "    name = re.sub(r' .(java|class)$', '', name, flags=re.IGNORECASE)\n", "    \n", "    return name.strip()\n", "\n", "  def traverse(node, parent_type=None, parent_name=None):\n", "    # Handle class declarations\n", "    if node.type == 'class_declaration':\n", "      class_name = None\n", "      for child in node.children:\n", "        if child.type == 'identifier':\n", "          class_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "          \n", "          # File -> Class relationship\n", "          records.append({\n", "            'source_node': file_name,\n", "            'source_type': 'file',\n", "            'destination_node': class_name,\n", "            'destination_type': 'class',\n", "            'relationship': 'declares',\n", "            'file_path': file_path\n", "          })\n", "          \n", "          # Add API endpoints from registry\n", "          class_info = class_registry.get(class_name, {})\n", "          endpoints = class_info.get('endpoints', [])\n", "          for ep in endpoints:\n", "            endpoint_name = f\"{ep['method']} {ep['path']}\"\n", "            records.append({\n", "              'source_node': class_name,\n", "              'source_type': 'class',\n", "              'destination_node': endpoint_name,\n", "              'destination_type': 'endpoint',\n", "              'relationship': 'declares',\n", "              'file_path': file_path\n", "            })\n", "          \n", "          # Add database entities from registry\n", "          db_entities = class_info.get('db_entities', [])\n", "          for entity in db_entities:\n", "            records.append({\n", "              'source_node': class_name,\n", "              'source_type': 'class',\n", "              'destination_node': entity['name'],\n", "              'destination_type': 'table',\n", "              'relationship': 'maps_to',\n", "              'file_path': file_path\n", "            })\n", "          \n", "          # Add extends/implements relationships from registry\n", "          interface_extends = class_info.get('interface_extends', [])\n", "          for ext_rel in interface_extends:\n", "            rel_type = 'extends' if ext_rel.get('type') == 'class_extends' else 'implements'\n", "            records.append({\n", "              'source_node': class_name,\n", "              'source_type': 'class',\n", "              'destination_node': ext_rel['parent_interface'],\n", "              'destination_type': 'interface' if rel_type == 'implements' else 'class',\n", "              'relationship': rel_type,\n", "              'file_path': file_path\n", "            })\n", "          break\n", "      \n", "      # Traverse children with class context\n", "      for child in node.children:\n", "        traverse(child, 'class', class_name)\n", "        \n", "    # Handle interface declarations\n", "    elif node.type == 'interface_declaration':\n", "      interface_name = None\n", "      for child in node.children:\n", "        if child.type == 'identifier':\n", "          interface_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "          records.append({\n", "            'source_node': file_name,\n", "            'source_type': 'file',\n", "            'destination_node': interface_name,\n", "            'destination_type': 'interface',\n", "            'relationship': 'declares',\n", "            'file_path': file_path\n", "          })\n", "          break\n", "      \n", "      # Traverse children with interface context\n", "      for child in node.children:\n", "        traverse(child, 'interface', interface_name)\n", "        \n", "    # Handle method declarations - FIXED HIERARCHY\n", "    elif node.type == 'method_declaration':\n", "      method_name = None\n", "      for child in node.children:\n", "        if child.type == 'identifier':\n", "          method_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "          \n", "          # CORRECT: Class -> Method (not Method -> Class)\n", "          if parent_name and parent_type in ['class', 'interface']:\n", "                 records.append({\n", "                            'source_node': parent_name,\n", "                            'source_type': parent_type,\n", "                            'destination_node': method_name,\n", "                            'destination_type': 'method',\n", "                            'relationship': 'declares',\n", "                            'file_path': file_path\n", "                        })\n", "                    break\n", "            \n", "            # Traverse children with method context\n", "            for child in node.children:\n", "                traverse(child, 'method', method_name)\n", "                \n", "        # Handle field declarations - FIXED HIERARCHY\n", "        elif node.type == 'field_declaration':\n", "            for child in node.children:\n", "                if child.type == 'variable_declarator':\n", "                    for grandchild in child.children:\n", "                        if grandchild.type == 'identifier':\n", "                            field_name = clean_node_name(source_code[grandchild.start_byte:grandchild.end_byte].decode('utf-8'))\n", "                            \n", "                            # CORRECT: Class -> Variable (not Variable -> Class)\n", "                            if parent_name and parent_type == 'class':\n", "                                records.append({\n", "                                    'source_node': parent_name,\n", "                                    'source_type': parent_type,\n", "                                    'destination_node': field_name,\n", "                                    'destination_type': 'variable',\n", "                                    'relationship': 'has_field',\n", "                                    'file_path': file_path\n", "                                })\n", "                                \n", "        # Handle variable usage in methods - FIXED HIERARCHY\n", "        elif node.type in ['assignment_expression', 'variable_declarator'] and parent_type == 'method':\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    var_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "                    if var_name and var_name != 'this' and parent_name:\n", "                        # CORRECT: Method -> Variable (not Variable -> Method)\n", "                        records.append({\n", "                            'source_node': parent_name,\n", "                            'source_type': parent_type,\n", "                            'destination_node': var_name,\n", "                            'destination_type': 'variable',\n", "                            'relationship': 'uses',\n", "                            'file_path': file_path\n", "                        })\n", "                        \n", "        # Handle field access in methods\n", "        elif node.type == 'field_access' and parent_type == 'method':\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    field_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "                    if field_name and field_name != 'this' and parent_name:\n", "                        records.append({\n", "                            'source_node': parent_name,\n", "                            'source_type': parent_type,\n", "                            'destination_node': field_name,\n", "                            'destination_type': 'variable',\n", "                            'relationship': 'uses',\n", "                            'file_path': file_path\n", "                        })\n", "        \n", "        # Handle return statements\n", "        elif node.type == 'return_statement' and parent_type == 'method':\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    var_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "                    if var_name and parent_name:\n", "                        records.append({\n", "                            'source_node': parent_name,\n", "                            'source_type': parent_type,\n", "                            'destination_node': var_name,\n", "                            'destination_type': 'variable',\n", "                            'relationship': 'uses',\n", "                            'file_path': file_path\n", "                        })\n", "        else:\n", "            # Continue traversing for other node types\n", "            for child in node.children:\n", "                traverse(child, parent_type, parent_name)\n", "\n", "    traverse(root_node)\n", "    return records\n", "\n", "ast_records = []\n", "for root, _, files in os.walk(BASE_PATH):\n", "    for file in files:\n", "        if file.endswith('.java'):\n", "            file_path = os.path.join(root, file)\n", "            try:\n", "                ast_records.extend(extract_ast_structure(file_path))\n", "            except Exception as e:\n", "                print(f' Error processing {file}: {e}')\n", "                continue\n", "\n", "df_ast = pd.DataFrame(ast_records)\n", "print(f'Stage 2 Complete: {len(df_ast)} AST relationships extracted')"]}, {"cell_type": "code", "execution_count": null, "id": "3d076121", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 3: LLM EXTRACTION WITH AST CONTEXT ==========\n", "\n", "\n", "def build_enhanced_system_prompt(file_path, ast_df, class_registry):\n", "  ast_subset = ast_df[ast_df['file_path'] == file_path] if len(ast_df) > 0 else pd.DataFrame()\n", "  ast_context = ''\n", "  for _, row in ast_subset.iterrows():\n", "    ast_context += f\"{row['source_type']}:{row['source_node']} -[{row['relationship']}]-> {row['destination_type']}:{row['destination_node']} \"\n", "  \n", "  registry_context = 'Known Classes: '\n", "  for class_name, info in class_registry.items():\n", "    registry_context += f'- {class_name} (FQCN: {info[\"fqcn\"]}) '\n", "    if len(info.get('endpoints', [])) > 0:\n", "      registry_context += f' * {len(info[\"endpoints\"])} API endpoint(s) '\n", "    if len(info.get('db_entities', [])) > 0:\n", "      registry_context += f' * {len(info[\"db_entities\"])} DB entity/entities '\n", "  \n", "  prompt = f\"\"\"\n", "You are a Java code lineage extraction engine. Extract relationships between code entities with STRICT focus on:\n", "\n", "CONTEXT:\n", "{registry_context}\n", "\n", "AST RELATIONSHIPS (FOLLOW THESE PATTERNS EXACTLY):\n", "{ast_context}\n", "\n", "CRITICAL RULES - <PERSON><PERSON><PERSON><PERSON> EXACTLY:\n", "1. Use SIMPLE names only (remove prefixes like \"method:\", \"class:\", etc.)\n", "2. MANDATORY RELATIONSHIP DIRECTIONS (DO NOT REVERSE):\n", "  - file -[declares]-> class\n", "  - class -[declares]-> method \n", "  - class -[has_field]-> variable\n", "  - method -[uses]-> variable\n", "  - class -[declares]-> endpoint\n", "  - class -[maps_to]-> table\n", "3. Extract REST API endpoints as 'endpoint' nodes (GET /api/users, POST /api/data)\n", "4. Extract database tables from @Entity, @Table, @Query annotations\n", "5. Extract interface extends and class implements relationships\n", "6. NEVER create reverse relationships (method->class, variable->method, etc.)\n", "7. Follow the AST RELATIONSHIPS above for correct structure\n", "8. Clean node names (remove \"method:\", \"class:\" prefixes)\n", "\n", "Extract triples in format:\n", "[SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName\n", "\n", "Return ONLY the triples, no explanations.\n", "\"\"\"\n", "  return prompt"]}, {"cell_type": "code", "execution_count": null, "id": "35cf8070", "metadata": {}, "outputs": [], "source": ["\n", "splitter = RecursiveCharacterTextSplitter.from_language(\n", "  language=LC_Language.JAVA,\n", "  chunk_size=4000,\n", "  chunk_overlap=200\n", ")\n", "\n", "java_docs, split_docs = [], []\n", "for root, _, files in os.walk(BASE_PATH):\n", "  for file in files:\n", "    if file.endswith('.java'):\n", "      try:\n", "        loader = TextLoader(os.path.join(root, file))\n", "        java_docs.extend(loader.load())\n", "      except Exception as e:\n", "        print(f\" Error loading {file}: {e}\")\n", "        continue\n", "\n", "for doc in java_docs:\n", "  split_docs.extend(splitter.split_documents([doc]))\n", "\n", "all_llm_lineage = []\n", "for chunk in tqdm(split_docs, desc='Stage 3: Enhanced LLM Extraction'):\n", "  file_path = chunk.metadata.get('source')\n", "  system_prompt = build_enhanced_system_prompt(file_path, df_ast, class_registry)\n", "  \n", "  transformer = LLMGraphTransformer(\n", "    llm=llm,\n", "    additional_instructions=system_prompt,\n", "    allowed_nodes=['file', 'class', 'interface', 'method', 'variable', 'table', 'endpoint'],\n", "    allowed_relationships=[\n", "      ('file', 'declares', 'class'),\n", "      ('file', 'declares', 'interface'),\n", "      ('class', 'declares', 'method'),\n", "      ('interface', 'declares', 'method'),\n", "      ('class', 'declares', 'endpoint'),\n", "      ('method', 'calls', 'method'),\n", "      ('class', 'has_field', 'variable'),\n", "      ('method', 'uses', 'variable'),\n", "      ('class', 'uses', 'class'),\n", "      ('interface', 'extends', 'interface'),\n", "      ('class', 'extends', 'class'),\n", "      ('class', 'implements', 'interface'),\n", "      ('class', 'maps_to', 'table'),\n", "      ('method', 'reads_from', 'table'),\n", "      ('method', 'writes_to', 'table'),\n", "    ],\n", "    strict_mode=True,\n", "    node_properties=False,\n", "    relationship_properties=False,\n", "  )\n", "  \n", "  try:\n", "    graph_docs = transformer.convert_to_graph_documents([chunk])\n", "    for gd in graph_docs:\n", "      for rel in gd.relationships:\n", "        s_node = rel.source.id.strip()\n", "        s_type = rel.source.type.strip().lower()\n", "        t_node = rel.target.id.strip()\n", "        t_type = rel.target.type.strip().lower()\n", "        rel_type = rel.type.strip().lower()\n", "\n", "        def normalize_entity(entity_name, entity_type):\n", "          if not entity_name:\n", "            return entity_name\n", "          \n", "          # Remove prefixes\n", "          prefixes = ['method:', 'class:', 'variable:', 'field:', 'table:', 'endpoint:']\n", "          for prefix in prefixes:\n", "            if entity_name.lower().startswith(prefix):\n", "              entity_name = entity_name[len(prefix):]\n", "          \n", "          # Remove file extensions\n", "          entity_name = re.sub(r' .(java|class)$', '', entity_name, flags=re.IGNORECASE)\n", "          \n", "          # Clean dots for class names\n", "          if entity_type in ['class', 'method'] and '.' in entity_name:\n", "            entity_name = entity_name.split('.')[-1]\n", "          \n", "          # Match with class registry for consistency\n", "          if entity_type == 'class':\n", "            for class_name in class_registry.keys():\n", "              if entity_name.lower() == class_name.lower():\n", "                return class_name.lower()\n", "          \n", "          return entity_name.lower()\n", "\n", "        s_node = normalize_entity(s_node, s_type)\n", "        t_node = normalize_entity(t_node, t_type)\n", "\n", "        # Skip invalid relationships\n", "        if s_node == t_node and s_type == t_type:\n", "          continue\n", "        if not s_node or not t_node:\n", "          continue\n", "        \n", "        # Enforce correct relationship directions\n", "        valid_directions = {\n", "          ('file', 'declares', 'class'),\n", "          ('file', 'declares', 'interface'),\n", "          ('class', 'declares', 'method'),\n", "          ('interface', 'declares', 'method'),\n", "          ('class', 'declares', 'endpoint'),\n", "          ('class', 'has_field', 'variable'),\n", "          ('method', 'uses', 'variable'),\n", "          ('class', 'maps_to', 'table'),\n", "          ('class', 'extends', 'class'),\n", "          ('class', 'implements', 'interface'),\n", "          ('interface', 'extends', 'interface'),\n", "          ('method', 'calls', 'method'),\n", "          ('method', 'reads_from', 'table'),\n", "          ('method', 'writes_to', 'table')\n", "        }\n", "        \n", "        if (s_type, rel_type, t_type) not in valid_directions:\n", "          continue\n", "\n", "        all_llm_lineage.append({\n", "          'source_node': s_node,\n", "          'source_type': s_type,\n", "          'destination_node': t_node,\n", "          'destination_type': t_type,\n", "          'relationship': rel_type,\n", "          'file_path': file_path\n", "        })\n", "  except Exception as e:\n", "    continue\n", "\n", "df_llm_lineage = pd.DataFrame(all_llm_lineage)\n", "print(f' Stage 3 Complete: {len(df_llm_lineage)} LLM relationships extracted')"]}, {"cell_type": "code", "execution_count": null, "id": "699498b4", "metadata": {}, "outputs": [], "source": ["\n", "# ========== STAGE 4: DATA NORMALIZATION AND CLEANING ==========\n", "\n", "# Clean node names function\n", "def clean_dataframe_names(df):\n", "  \"\"\"Clean node names in dataframe\"\"\"\n", "  for col in [\"source_node\", \"destination_node\"]:\n", "    if col in df.columns:\n", "      df[col] = df[col].astype(str).apply(lambda x: re.sub(r'^(method|class|variable|field|table|endpoint):', '', x, flags=re.IGNORECASE))\n", "      df[col] = df[col].apply(lambda x: re.sub(r' .(java|class)$', '', x, flags=re.IGNORECASE))\n", "      df[col] = df[col].str.strip()\n", "  return df\n", "\n", "# Clean all dataframes\n", "df_folders = clean_dataframe_names(df_folders)\n", "df_files = clean_dataframe_names(df_files)\n", "df_llm_lineage = clean_dataframe_names(df_llm_lineage)\n", "\n", "# Normalize dataframes (excluding AST to reduce noise)\n", "for df in [df_folders, df_files, df_llm_lineage]:\n", "  for col in [\"source_node\", \"destination_node\", \"source_type\", \"destination_type\", \"relationship\"]:\n", "    if col in df.columns:\n", "      df[col] = df[col].astype(str).str.strip().str.lower()\n", "\n", "# Combine dataframes (EXCLUDING df_ast to reduce noise)\n", "df_combined = pd.concat([df_folders, df_files, df_llm_lineage], ignore_index=True)\n", "df_combined.drop_duplicates(\n", "  subset=[\"source_node\", \"source_type\", \"destination_node\", \"destination_type\", \"relationship\"],\n", "  inplace=True\n", ")\n", "\n", "# Remove self-references\n", "df_combined = df_combined[\n", "  ~((df_combined['source_node'] == df_combined['destination_node']) &\n", "   (df_combined['source_type'] == df_combined['destination_type']))\n", "]\n", "\n", "# Remove empty/null entries\n", "df_combined = df_combined[\n", "  (df_combined['source_node'].notna()) & (df_combined['destination_node'].notna()) &\n", "  (df_combined['source_node'] != '') & (df_combined['destination_node'] != '') &\n", "  (df_combined['source_node'] != 'none') & (df_combined['destination_node'] != 'none')\n", "]\n", "\n", "print(f' Combined data: {len(df_combined)} relationships after deduplication and cleaning')"]}, {"cell_type": "code", "execution_count": null, "id": "67911c9c", "metadata": {}, "outputs": [], "source": ["\n", "# ========== STAGE 5: FILE VS CLASS CORRECTION ==========\n", "\n", "\n", "# Create a corrected dataframe for insertion\n", "df_corrected = df_combined.copy()\n", "\n", "# Fix File and Class naming for consistency\n", "for idx, row in df_corrected.iterrows():\n", "  source_node = str(row[\"source_node\"]).strip()\n", "  dest_node = str(row[\"destination_node\"]).strip()\n", "  source_type = str(row[\"source_type\"]).strip().lower()\n", "  dest_type = str(row[\"destination_type\"]).strip().lower()\n", "\n", "  # --- File Node Correction ---\n", "  if source_type == \"file\":\n", "    match = df_files[df_files[\"destination_node\"].str.replace(\".java\", \"\", case=False) == source_node.replace(\".java\", \"\")]\n", "    if not match.empty:\n", "      df_corrected.at[idx, \"source_node\"] = match.iloc[0][\"destination_node\"]\n", "\n", "  if dest_type == \"file\":\n", "    match = df_files[df_files[\"destination_node\"].str.replace(\".java\", \"\", case=False) == dest_node.replace(\".java\", \"\")]\n", "    if not match.empty:\n", "      df_corrected.at[idx, \"destination_node\"] = match.iloc[0][\"destination_node\"]\n", "\n", "  # --- Class Node Correction ---\n", "  if source_type == \"class\":\n", "    clean_name = source_node.replace(\".java\", \"\")\n", "    for class_name in class_registry.keys():\n", "      if class_name.lower() == clean_name.lower():\n", "        df_corrected.at[idx, \"source_node\"] = class_name.lower()\n", "        break\n", "\n", "  if dest_type == \"class\":\n", "    clean_name = dest_node.replace(\".java\", \"\")\n", "    for class_name in class_registry.keys():\n", "      if class_name.lower() == clean_name.lower():\n", "        df_corrected.at[idx, \"destination_node\"] = class_name.lower()\n", "        break\n", "      \n", "print(f' Stage 5 Complete: {len(df_corrected)} corrected relationships ready for Neo4j')"]}, {"cell_type": "code", "execution_count": null, "id": "6ec998a3", "metadata": {}, "outputs": [], "source": ["# ========== STAGE 6: NEO4J INSERTION ==========\n", "\n", "# Clear existing data\n", "graph.query(\"MATCH (n) DETACH DELETE n\")\n", "\n", "# Insert clean corrected data\n", "for idx, row in tqdm(df_corrected.iterrows(), total=len(df_corrected), desc=\"Final Neo4j Insertion\"):\n", "  try:\n", "    source_node = str(row[\"source_node\"]).strip()\n", "    dest_node = str(row[\"destination_node\"]).strip()\n", "    source_type = str(row[\"source_type\"]).strip().capitalize()\n", "    dest_type = str(row[\"destination_type\"]).strip().capitalize()\n", "    relationship = str(row[\"relationship\"]).strip().upper()\n", "\n", "    if not all([source_node, dest_node, source_type, dest_type, relationship]):\n", "      continue\n", "\n", "    query = f\"\"\"\n", "    MERGE (s:{source_type} {{name: $source_node}})\n", "    MERGE (t:{dest_type} {{name: $destination_node}})\n", "    MERGE (s)-[:{relationship}]->(t)\n", "    \"\"\"\n", "    graph.query(query, {\n", "      \"source_node\": source_node,\n", "      \"destination_node\": dest_node\n", "    })\n", "  except Exception as e:\n", "    pass # Optionally collect failed rows for review\n", "\n", "print(\" Final consistent data pushed to Neo4j.\")"]}, {"cell_type": "code", "execution_count": null, "id": "b97c40a1", "metadata": {}, "outputs": [], "source": ["\n", "# ========== VALIDATION ==========\n", "def validate_graph_statistics():\n", "  node_count = graph.query(\"MATCH (n) RETURN count(n) as count\")[0][\"count\"]\n", "  rel_count = graph.query(\"MATCH ()-[r]->() RETURN count(r) as count\")[0][\"count\"]\n", "\n", "  print(f\"Total Nodes in Neo4j: {node_count}\")\n", "  print(f\"Total Relationships in Neo4j: {rel_count}\")\n", "  \n", "  # Show breakdown by node type\n", "  node_types = graph.query(\"MATCH (n) RETURN labels(n)[0] as type, count(n) as count ORDER BY count DESC\")\n", "  print(' Node Types:')\n", "  for row in node_types:\n", "    print(f' {row[\"type\"]}: {row[\"count\"]}')\n", "  \n", "  # Show breakdown by relationship type\n", "  rel_types = graph.query(\"MATCH ()-[r]->() RETURN type(r) as type, count(r) as count ORDER BY count DESC\")\n", "  print(' Relationship Types:')\n", "  for row in rel_types:\n", "    print(f' {row[\"type\"]}: {row[\"count\"]}')\n", "  \n", "  # Show sample endpoints and tables\n", "  endpoints = graph.query(\"MATCH (n:Endpoint) RETURN n.name as name LIMIT 5\")\n", "  if endpoints:\n", "    print(' Sample API Endpoints:')\n", "    for ep in endpoints:\n", "      print(f' {ep[\"name\"]}')\n", "  \n", "  tables = graph.query(\"MATCH (n:Table) RETURN n.name as name LIMIT 5\")\n", "  if tables:\n", "    print(' Sample Database Tables:')\n", "    for table in tables:\n", "      print(f' {table[\"name\"]}')\n", "\n", "validate_graph_statistics()\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}