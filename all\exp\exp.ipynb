{"cells": [{"cell_type": "code", "execution_count": 1, "id": "5a10ea05", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Shaik\\sample\\venv\\Lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20076\\844494305.py:28: LangChainDeprecationWarning: The class `Neo4jGraph` was deprecated in LangChain 0.3.8 and will be removed in 1.0. An updated version of the class exists in the :class:`~langchain-neo4j package and should be used instead. To use it run `pip install -U :class:`~langchain-neo4j` and import as `from :class:`~langchain_neo4j import Neo4jGraph``.\n", "  graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)\n"]}], "source": ["# ===============================\n", "# Imports & Config\n", "# ===============================\n", "import os\n", "from pathlib import Path\n", "import pandas as pd\n", "from tqdm import tqdm\n", "from tree_sitter import Language, Parser\n", "import tree_sitter_java as tsjava\n", "\n", "from langchain_community.document_loaders import TextLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter, Language as LC_Language\n", "from langchain_google_genai import ChatGoogleGenerativeAI\n", "from langchain_experimental.graph_transformers import LLMGraphTransformer\n", "from langchain_community.graphs import Neo4jGraph\n", "\n", "# ===============================\n", "# Configuration\n", "# ===============================\n", "BASE_PATH = Path(\"C:/Shaik/sample/java\")\n", "NEO4J_URI = \"bolt://localhost:7687\"\n", "NEO4J_USER = \"neo4j\"\n", "NEO4J_PASSWORD = \"Test@7889\"\n", "NEO4J_DB = \"astllm3\"\n", "#google_api_key = \"AIzaSyDlwEbSYmqOoIGbh-Upn2ZGXR3nwAfxcQ0\"\n", "\n", "#llm = ChatGoogleGenerativeAI(model=\"gemini-2.5-pro\", temperature=0, google_api_key=google_api_key)\n", "graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)\n", "\n", "JAVA_LANGUAGE = Language(tsjava.language())\n", "parser = Parser(JAVA_LANGUAGE)\n"]}, {"cell_type": "code", "execution_count": 2, "id": "d9291560", "metadata": {}, "outputs": [], "source": ["\n", "\n", "def extract_folder_file_hierarchy(base_path: Path):\n", "    all_records = []\n", "    base_folder_name = base_path.name\n", "\n", "    # Root folder node (no relationship)\n", "    all_records.append({\n", "        \"source_node\": None,\n", "        \"source_type\": None,\n", "        \"destination_node\": base_folder_name,\n", "        \"destination_type\": \"Folder\",\n", "        \"relationship\": None,\n", "        \"file_path\": None,\n", "        \"source_properties\": {\"folder_path\": str(base_path)},\n", "        \"relationship_properties\": {}\n", "    })\n", "\n", "    for root, dirs, files in os.walk(base_path):\n", "        root_path = Path(root)\n", "        rel_root = root_path.relative_to(base_path)\n", "        current_folder = rel_root.name if rel_root != Path(\".\") else base_folder_name\n", "        parent_folder = rel_root.parent.name if rel_root != Path(\".\") and rel_root.parent != Path(\".\") else base_folder_name\n", "\n", "        if rel_root != Path(\".\"):\n", "            all_records.append({\n", "                \"source_node\": parent_folder,\n", "                \"source_type\": \"Folder\",\n", "                \"destination_node\": current_folder,\n", "                \"destination_type\": \"Folder\",\n", "                \"relationship\": \"CONTAINS\",\n", "                \"file_path\": None,\n", "                \"source_properties\": {\"folder_path\": str(root_path)},\n", "                \"relationship_properties\": {\"from\": parent_folder, \"to\": current_folder}\n", "            })\n", "\n", "        for f in files:\n", "            if f.endswith(\".java\"):\n", "                file_path = root_path / f\n", "                file_rel_path = file_path.relative_to(base_path)\n", "                all_records.append({\n", "                    \"source_node\": current_folder,\n", "                    \"source_type\": \"Folder\",\n", "                    \"destination_node\": f,\n", "                    \"destination_type\": \"File\",\n", "                    \"relationship\": \"CONTAINS\",\n", "                    \"file_path\": str(file_rel_path),\n", "                    \"source_properties\": {\"file_path\": str(file_rel_path)},\n", "                    \"relationship_properties\": {\"from\": current_folder, \"to\": f}\n", "                })\n", "\n", "    return all_records\n", "\n", "# Extract folder-file records\n", "folder_file_records = extract_folder_file_hierarchy(BASE_PATH)\n", "\n", "# Convert to DataFrame\n", "df_stage0 = pd.DataFrame(folder_file_records)\n", "\n"]}, {"cell_type": "code", "execution_count": 5, "id": "6d31ef03", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Pushing Stage 0 to Neo4j: 100%|██████████| 19/19 [00:00<00:00, 116.91it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Stage 0 ingestion to Neo4j complete. You can now verify in Neo4j Bloom or Browser.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["\n", "\n", "# Normalize labels for Neo4j ingestion (lowercase + strip)\n", "for col in [\"source_node\", \"destination_node\", \"source_type\", \"destination_type\", \"relationship\"]:\n", "    if col in df_stage0.columns:\n", "        df_stage0[col] = df_stage0[col].astype(str).str.strip().str.lower()\n", "\n", "# Push data to Neo4j\n", "for idx, row in tqdm(df_stage0.iterrows(), total=len(df_stage0), desc=\"Pushing Stage 0 to Neo4j\"):\n", "    try:\n", "        # Source or destination label fallback\n", "        source_label = (row[\"source_type\"] if row[\"source_type\"] != 'none' else None)\n", "        dest_label = (row[\"destination_type\"] if row[\"destination_type\"] != 'none' else None)\n", "\n", "        # If no source_node, means root folder node - create only destination node\n", "        if pd.isna(row[\"source_node\"]) or row[\"source_node\"] in [\"none\", \"nan\"]:\n", "            if dest_label is None:\n", "                dest_label = \"Folder\"\n", "            graph.query(\n", "                f\"\"\"\n", "                MERGE (t:{dest_label} {{name: $destination_node}})\n", "                SET t += $source_properties\n", "                \"\"\",\n", "                {\n", "                    \"destination_node\": row[\"destination_node\"],\n", "                    \"source_properties\": row.get(\"source_properties\", {}) if isinstance(row.get(\"source_properties\", {}), dict) else {},\n", "                }\n", "            )\n", "        else:\n", "            # Regular relationship insertion\n", "            graph.query(\n", "                f\"\"\"\n", "                MERGE (s:{source_label} {{name: $source_node}})\n", "                SET s += $source_properties\n", "                MERGE (t:{dest_label} {{name: $destination_node}})\n", "                SET t += $source_properties\n", "                MERGE (s)-[r:{row['relationship'].upper()}]->(t)\n", "                SET r += $relationship_properties\n", "                \"\"\",\n", "                {\n", "                    \"source_node\": row[\"source_node\"],\n", "                    \"destination_node\": row[\"destination_node\"],\n", "                    \"source_properties\": row.get(\"source_properties\", {}) if isinstance(row.get(\"source_properties\", {}), dict) else {},\n", "                    \"relationship_properties\": row.get(\"relationship_properties\", {}) if isinstance(row.get(\"relationship_properties\", {}), dict) else {},\n", "                }\n", "            )\n", "    except Exception as e:\n", "        print(f\"Error pushing row {idx}: {e}\")\n", "\n", "print(\"✅ Stage 0 ingestion to Neo4j complete. You can now verify in Neo4j Bloom or Browser.\")\n"]}, {"cell_type": "code", "execution_count": 7, "id": "1b850427", "metadata": {}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["graph.query(\"MATCH (n) DETACH DELETE n\")"]}, {"cell_type": "code", "execution_count": 6, "id": "cd5df454", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Stage 1: Extracted 244 AST records.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Pushing Stage 1 to Neo4j: 100%|██████████| 244/244 [00:02<00:00, 85.98it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Stage 1 ingestion to Neo4j complete. Verify in Neo4j Bloom or Browser.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["\n", "\n", "def read_source_code(file_path):\n", "    with open(file_path, \"r\", encoding=\"utf-8\") as f:\n", "        return f.read().encode(\"utf-8\")\n", "\n", "def extract_ast_structure(node, source_code, file_path):\n", "    records = []\n", "    current_class, current_method = None, None\n", "\n", "    def traverse(n):\n", "        nonlocal current_class, current_method\n", "        line_number = n.start_point[0] + 1\n", "\n", "        if n.type == \"class_declaration\":\n", "            for child in n.children:\n", "                if child.type == \"identifier\":\n", "                    class_name = source_code[child.start_byte:child.end_byte].decode()\n", "                    records.append({\n", "                        \"source_node\": os.path.basename(file_path), \"source_type\": \"File\",\n", "                        \"destination_node\": class_name, \"destination_type\": \"Class\",\n", "                        \"relationship\": \"DECLARES\", \"file_path\": str(file_path),\n", "                        \"source_properties\": {\"defined_in_file\": os.path.basename(file_path), \"line_number\": line_number},\n", "                        \"relationship_properties\": {\"from\": os.path.basename(file_path), \"to\": class_name}\n", "                    })\n", "                    current_class = class_name\n", "            for child in n.children:\n", "                traverse(child)\n", "            current_class = None\n", "\n", "        elif n.type == \"method_declaration\":\n", "            for child in n.children:\n", "                if child.type == \"identifier\":\n", "                    method_name = source_code[child.start_byte:child.end_byte].decode()\n", "                    records.append({\n", "                        \"source_node\": current_class, \"source_type\": \"Class\",\n", "                        \"destination_node\": method_name, \"destination_type\": \"Method\",\n", "                        \"relationship\": \"DECLARES\", \"file_path\": str(file_path),\n", "                        \"source_properties\": {\"defined_in_class\": current_class, \"defined_in_file\": os.path.basename(file_path), \"line_number\": line_number},\n", "                        \"relationship_properties\": {\"from\": current_class, \"to\": method_name}\n", "                    })\n", "                    current_method = method_name\n", "            for child in n.children:\n", "                traverse(child)\n", "            current_method = None\n", "\n", "        elif n.type == \"method_invocation\":\n", "            for child in n.children:\n", "                if child.type == \"identifier\":\n", "                    called_method = source_code[child.start_byte:child.end_byte].decode()\n", "                    records.append({\n", "                        \"source_node\": current_method, \"source_type\": \"Method\",\n", "                        \"destination_node\": called_method, \"destination_type\": \"Method\",\n", "                        \"relationship\": \"CALLS\", \"file_path\": str(file_path),\n", "                        \"source_properties\": {\"in_method\": current_method, \"defined_in_class\": current_class, \"defined_in_file\": os.path.basename(file_path)},\n", "                        \"relationship_properties\": {\"from\": current_method, \"to\": called_method, \"line_number\": line_number}\n", "                    })\n", "            for child in n.children:\n", "                traverse(child)\n", "\n", "        elif n.type == \"variable_declarator\":\n", "            for child in n.children:\n", "                if child.type == \"identifier\":\n", "                    var_name = source_code[child.start_byte:child.end_byte].decode()\n", "                    source = current_method or current_class or os.path.basename(file_path)\n", "                    source_type = \"Method\" if current_method else \"Class\" if current_class else \"File\"\n", "                    rel = \"USES\" if current_method else \"HAS_FIELD\" if current_class else \"DECLARES\"\n", "                    records.append({\n", "                        \"source_node\": source, \"source_type\": source_type,\n", "                        \"destination_node\": var_name, \"destination_type\": \"Variable\",\n", "                        \"relationship\": rel, \"file_path\": str(file_path),\n", "                        \"source_properties\": {\"defined_in_class\": current_class, \"defined_in_file\": os.path.basename(file_path), \"line_number\": line_number},\n", "                        \"relationship_properties\": {\"from\": source, \"to\": var_name, \"line_number\": line_number}\n", "                    })\n", "            for child in n.children:\n", "                traverse(child)\n", "        else:\n", "            for child in n.children:\n", "                traverse(child)\n", "\n", "    traverse(node)\n", "    return records\n", "\n", "# Collect AST records\n", "ast_records = []\n", "for root, _, files in os.walk(BASE_PATH):\n", "    for file in files:\n", "        if file.endswith(\".java\"):\n", "            fpath = os.path.join(root, file)\n", "            try:\n", "                source_code = read_source_code(fpath)\n", "                tree = parser.parse(source_code)\n", "                ast_records.extend(extract_ast_structure(tree.root_node, source_code, fpath))\n", "            except Exception as e:\n", "                print(f\"Error parsing {fpath}: {e}\")\n", "\n", "print(f\"✅ Stage 1: Extracted {len(ast_records)} AST records.\")\n", "\n", "# Convert to DataFrame and save CSV\n", "df_stage1 = pd.DataFrame(ast_records)\n", "\n", "\n", "# Normalize columns for Neo4j ingestion\n", "for col in [\"source_node\", \"destination_node\", \"source_type\", \"destination_type\", \"relationship\"]:\n", "    if col in df_stage1.columns:\n", "        df_stage1[col] = df_stage1[col].astype(str).str.strip().str.lower()\n", "\n", "# Push AST data to Neo4j\n", "for idx, row in tqdm(df_stage1.iterrows(), total=len(df_stage1), desc=\"Pushing Stage 1 to Neo4j\"):\n", "    try:\n", "        source_label = (row[\"source_type\"] or \"Unknown\").capitalize()\n", "        dest_label = (row[\"destination_type\"] or \"Unknown\").capitalize()\n", "        rel_type = (row[\"relationship\"] or \"RELATED\").upper()\n", "\n", "        source_props = row.get(\"source_properties\", {})\n", "        if not isinstance(source_props, dict):\n", "            source_props = {}\n", "\n", "        relationship_props = row.get(\"relationship_properties\", {})\n", "        if not isinstance(relationship_props, dict):\n", "            relationship_props = {}\n", "\n", "        graph.query(\n", "            f\"\"\"\n", "            MERGE (s:{source_label} {{name: $source_node}})\n", "            SET s += $source_properties\n", "            MERGE (t:{dest_label} {{name: $destination_node}})\n", "            SET t += $source_properties\n", "            MERGE (s)-[r:{rel_type}]->(t)\n", "            SET r += $relationship_properties\n", "            \"\"\",\n", "            {\n", "                \"source_node\": row[\"source_node\"],\n", "                \"destination_node\": row[\"destination_node\"],\n", "                \"source_properties\": source_props,\n", "                \"relationship_properties\": relationship_props,\n", "            }\n", "        )\n", "    except Exception as e:\n", "        print(f\"Error pushing row {idx}: {e}\")\n", "\n", "print(\"✅ Stage 1 ingestion to Neo4j complete. Verify in Neo4j Bloom or Browser.\")\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}