package com.bolt.dashboard.jira;

import java.util.ArrayList;
import java.util.List;

import org.json.simple.JSONArray;
import org.json.simple.JSONObject;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.model.ChangeHistoryModel;
import com.bolt.dashboard.core.model.EffortHistoryModel;

/**
* <AUTHOR>
*
*/
public class EffortAndChangeItemInfo {
      String pName = "";
      String sName = "";

      /**
      * calculate changeset for workItems
      *
      * @param json
      *            as history json from rest api
      * @param key
      *            as workitemid
      * @param cRuns
      *            as last collector run time
      * @param projectName
      *            as projectName
      * @param sprintName
      *            as sprintName
      * @return An List<Object> contains
      *         filteredStatusArray,modifiedDateList,changeLogs and efforts
      */
      @SuppressWarnings("unchecked")
      public List<Object> populateSubArrays(JSONArray json, String key, Long cRuns, String projectName, String sprintName,
                  double origEst,String projKey) {
            pName = projectName;
            sName = sprintName;
            int loggedTime = 0;
            String epicLink = null;
            List<Object> objectList = new ArrayList<>();
            JSONArray filteredStatusArray = new JSONArray();
            List<Long> modifiedDateList = new ArrayList<>();
            List<EffortHistoryModel> efforts = new ArrayList<>();
            List<ChangeHistoryModel> changeLogs = new ArrayList<>();
            
            for (int i = 0; i < json.size(); i++) {
                  boolean timeest = false;
                  boolean timespent = false;
                  long counter = 0;
                  JSONObject object = (JSONObject) json.get(i);
                  Long createdDate = ConstantVariable.timestamp(object.get("created").toString(),projectName);
                  JSONObject author = (JSONObject) object.get("author");
                  cRuns = cRuns == null ? 0 : cRuns;
                  EffortHistoryModel effortModel = new EffortHistoryModel();
                  if (createdDate >= cRuns) {
                        JSONArray itemArray = (JSONArray) object.get("items");
                        for (int k = 0; k < itemArray.size(); k++) {
                              JSONObject obj = (JSONObject) itemArray.get(k);
                              ChangeHistoryModel cm = new ChangeHistoryModel();
                              cm.setDate(createdDate);
                              cm.setwId(key);
                              cm.setProjKey(projKey);
                              cm.setProjectName(projectName);
                              cm.setField(obj.get("field") == null ? null : obj.get("field").toString());
                              cm.setNewValue(obj.get("to") == null ? null : obj.get("to").toString());
                              cm.setOldValue(obj.get("from") == null ? null : obj.get("from").toString());
                              changeLogs.add(cm);

                              if ("timeestimate".equals(obj.get("field"))) {
                                    timeest = true;
                                    int timeEstimate = 0, timeRemainingEstimate = 0;
                                    
                                    	if (obj.get("from") == null ){
                                    								effortModel.setInitialWork(0);
                                    							}
                                    	if (obj.get("from") != null && origEst != 0) {
                                    								timeEstimate = Integer.parseInt(obj.get("from").toString());
                                    								effortModel.setInitialWork(origEst);

                                    							}

                                    if (obj.get("to") != null) {
                                          timeRemainingEstimate = Integer.parseInt(obj.get("to").toString());
                                          effortModel.setRemainingWork(timeRemainingEstimate);
                                          if (timeRemainingEstimate > timeEstimate)
                                                effortModel.setRemainingWorkInc(timeRemainingEstimate -(double)timeEstimate);
                                          else
                                                effortModel.setRemainingWorkDec(timeEstimate - (double)timeRemainingEstimate);

                                    }

                                    if (timeEstimate < timeRemainingEstimate)
                                          counter++;
                                    else if (timeEstimate - loggedTime != timeRemainingEstimate)
                                          counter++;
                              }
                              if ("timespent".equals(obj.get("field"))) {
                                    timespent = true;
                                    int effort = obj.get("from") != null ? Integer.parseInt((String) obj.get("from")) : 0;
                                    loggedTime = Integer.parseInt((String) obj.get("to")) - effort;
                                    effortModel.setTimeSpent(loggedTime);

                              }
                              if ("status".equals(obj.get("field"))) {
                                    filteredStatusArray.add(obj);
                                    modifiedDateList.add(createdDate);
                              }

                              if (obj.get("field").toString().equals("Epic Link"))
                                    epicLink = (String) obj.get("toString");
                        }
                        if (timeest || timespent) {

                              effortModel.setLogDate(createdDate);
                              effortModel.setLoggedBy(author.get("displayName").toString());
                              effortModel.setwId(key);
                              effortModel.setProjKey(projKey);
                              effortModel.setpName(pName);
                              effortModel.setsName(sName);
                              efforts.add(effortModel);
                        }
                        effortModel.setChangeEsmt(counter);
                  }
            }
            objectList.add(filteredStatusArray);
            objectList.add(modifiedDateList);
            objectList.add(changeLogs);
            objectList.add(efforts);
            objectList.add(epicLink);
            return objectList;
      }

      /**
      *
      * */

}
