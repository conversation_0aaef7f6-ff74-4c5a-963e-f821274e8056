package com.morganstanley.loanApp.service;

import com.morganstanley.loanApp.dto.UserLoanRequestDTO;
import com.morganstanley.loanApp.model.LoanDetails;
import com.morganstanley.loanApp.model.User;
import com.morganstanley.loanApp.repository.LoanDetailsRepository;
import com.morganstanley.loanApp.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UserService {
    @Autowired
    private UserRepository userRepository;

    @Autowired
    private LoanDetailsRepository loanDetailsRepository;

    public User createUserWithLoan(UserLoanRequestDTO dto) {
        User user = new User();
        user.setName(dto.getName());
        user.setEmail(dto.getEmail());
        user = userRepository.save(user);

        LoanDetails loan = new LoanDetails();
        loan.setLoanType(dto.getLoanType());
        loan.setAmount(dto.getAmount());
        loan.setUserId(user.getId());
        loanDetailsRepository.save(loan);

        return user;
    }
}
