{"cells": [{"cell_type": "code", "execution_count": 2, "id": "a230b8d0", "metadata": {}, "outputs": [], "source": ["\n", "import os\n", "from langchain_community.document_loaders.generic import GenericLoader\n", "from langchain_community.document_loaders.parsers.language.language_parser import LanguageParser\n", "from langchain.text_splitter import Language, RecursiveCharacterTextSplitter\n", "from langchain_openai import ChatOpenAI\n", "from langchain_experimental.graph_transformers import LLMGraphTransformer\n", "from langchain_community.graphs import Neo4jGraph"]}, {"cell_type": "code", "execution_count": 5, "id": "6d7f4c52", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting tree-sitter\n", "  Using cached tree_sitter-0.24.0-cp311-cp311-win_amd64.whl.metadata (10 kB)\n", "Collecting tree-sitter-languages\n", "  Downloading tree_sitter_languages-1.10.2-cp311-cp311-win_amd64.whl.metadata (11 kB)\n", "Using cached tree_sitter-0.24.0-cp311-cp311-win_amd64.whl (120 kB)\n", "Downloading tree_sitter_languages-1.10.2-cp311-cp311-win_amd64.whl (8.3 MB)\n", "   ---------------------------------------- 0.0/8.3 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/8.3 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/8.3 MB ? eta -:--:--\n", "   --------------- ------------------------ 3.1/8.3 MB 18.5 MB/s eta 0:00:01\n", "   --------------- ------------------------ 3.1/8.3 MB 18.5 MB/s eta 0:00:01\n", "   --------------- ------------------------ 3.1/8.3 MB 18.5 MB/s eta 0:00:01\n", "   -------------------- ------------------- 4.2/8.3 MB 5.6 MB/s eta 0:00:01\n", "   ------------------------------ --------- 6.3/8.3 MB 6.0 MB/s eta 0:00:01\n", "   ------------------------------ --------- 6.3/8.3 MB 6.0 MB/s eta 0:00:01\n", "   ------------------------------------ --- 7.6/8.3 MB 4.8 MB/s eta 0:00:01\n", "   ---------------------------------------- 8.3/8.3 MB 5.1 MB/s eta 0:00:00\n", "Installing collected packages: tree-sitter, tree-sitter-languages\n", "\n", "   -------------------- ------------------- 1/2 [tree-sitter-languages]\n", "   -------------------- ------------------- 1/2 [tree-sitter-languages]\n", "   ---------------------------------------- 2/2 [tree-sitter-languages]\n", "\n", "Successfully installed tree-sitter-0.24.0 tree-sitter-languages-1.10.2\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["%pip install tree-sitter tree-sitter-languages"]}, {"cell_type": "code", "execution_count": 8, "id": "63185b54", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- Document 1 ---\n", "Metadata: {'source': 'test\\\\java\\\\SecondDataSourceProperties.java', 'language': <Language.JAVA: 'java'>}\n", "package com.morganstanley.loanApp.configuration;\n", "\n", "import jakarta.annotation.PostConstruct;\n", "import org.springframework.boot.context.properties.ConfigurationProperties;\n", "import org.springframework.stereotype.Component;\n", "\n", "@Component\n", "@ConfigurationProperties(prefix = \"sp.second-datasource\")\n", "public class S ...\n", "\n", "--- Document 2 ---\n", "Metadata: {'source': 'test\\\\java\\\\SecondDataSourceProperties.java', 'language': <Language.JAVA: 'java'>}\n", "public String getDriverClassName() {\n", "        return driver<PERSON><PERSON><PERSON><PERSON>;\n", "    }\n", "\n", "    public void setDriverClassName(String driverClassName) {\n", "        this.driverClassName = driverClassName;\n", "    }\n", "\n", "    private String url;\n", "    private String username;\n", "    private String password;\n", "    private String driverC ...\n", "\n", "--- Document 3 ---\n", "Metadata: {'source': 'test\\\\java\\\\StoredProcDBConfig.java', 'language': <Language.JAVA: 'java'>}\n", "package com.morganstanley.loanApp.configuration;\n", "import javax.sql.DataSource;\n", "\n", "import com.zaxxer.hikari.HikariDataSource;\n", "import org.springframework.beans.factory.annotation.Qualifier;\n", "import org.springframework.boot.context.properties.ConfigurationProperties;\n", "import org.springframework.boot.jdbc.Da ...\n", "\n"]}], "source": ["from langchain_community.document_loaders.generic import GenericLoader\n", "from langchain_community.document_loaders.parsers.language.language_parser import LanguageParser\n", "from langchain.text_splitter import Language\n", "from langchain_core.documents import Document\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "\n", "def load_and_chunk_code_files(chunk_size=1000, chunk_overlap=200): \n", "    # Loader for Java files\n", "    java_loader = GenericLoader.from_filesystem(\n", "        \".\",\n", "        glob=\"**/*.java\",\n", "        parser=LanguageParser(language=Language.JAVA, parser_threshold=500)\n", "    )\n", "    java_documents = java_loader.load()\n", "\n", "    text_splitter = RecursiveCharacterTextSplitter(\n", "        chunk_size=chunk_size,\n", "        chunk_overlap=chunk_overlap\n", "    )\n", "\n", "    chunked_documents = text_splitter.split_documents(java_documents)\n", "\n", "    return chunked_documents\n", "\n", "# Load and chunk\n", "documents = load_and_chunk_code_files()\n", "\n", "# Check results\n", "for i, doc in enumerate(documents[:3], 1):\n", "    print(f\"--- Document {i} ---\")\n", "    print(\"Metadata:\", doc.metadata)\n", "    print(doc.page_content[:300], \"...\\n\")\n"]}, {"cell_type": "code", "execution_count": 9, "id": "f6dd6c11", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(metadata={'source': 'test\\\\java\\\\SecondDataSourceProperties.java', 'language': <Language.JAVA: 'java'>}, page_content='package com.morganstanley.loanApp.configuration;\\n\\nimport jakarta.annotation.PostConstruct;\\nimport org.springframework.boot.context.properties.ConfigurationProperties;\\nimport org.springframework.stereotype.Component;\\n\\n@Component\\n@ConfigurationProperties(prefix = \"sp.second-datasource\")\\npublic class SecondDataSourceProperties {\\n    public String getUrl() {\\n        return url;\\n    }\\n\\n    public void setUrl(String url) {\\n        this.url = url;\\n    }\\n\\n    public String getUsername() {\\n        return username;\\n    }\\n\\n    public void setUsername(String username) {\\n        this.username = username;\\n    }\\n\\n    public String getPassword() {\\n        return password;\\n    }\\n\\n    public void setPassword(String password) {\\n        this.password = password;\\n    }\\n\\n    public String getDriverClassName() {\\n        return driverClassName;\\n    }\\n\\n    public void setDriverClassName(String driverClassName) {\\n        this.driverClassName = driverClassName;\\n    }'),\n", " Document(metadata={'source': 'test\\\\java\\\\SecondDataSourceProperties.java', 'language': <Language.JAVA: 'java'>}, page_content='public String getDriverClassName() {\\n        return driverClassName;\\n    }\\n\\n    public void setDriverClassName(String driverClassName) {\\n        this.driverClassName = driverClassName;\\n    }\\n\\n    private String url;\\n    private String username;\\n    private String password;\\n    private String driverClassName;\\n    @PostConstruct\\n    public void printProps() {\\n        System.out.println(\"Secondary DS Props:\");\\n        System.out.println(\"URL: \" + url);\\n        System.out.println(\"Username: \" + username);\\n        System.out.println(\"Driver: \" + driverClassName);\\n    }\\n\\n\\n}'),\n", " Document(metadata={'source': 'test\\\\java\\\\StoredProcDBConfig.java', 'language': <Language.JAVA: 'java'>}, page_content='package com.morganstanley.loanApp.configuration;\\nimport javax.sql.DataSource;\\n\\nimport com.zaxxer.hikari.HikariDataSource;\\nimport org.springframework.beans.factory.annotation.Qualifier;\\nimport org.springframework.boot.context.properties.ConfigurationProperties;\\nimport org.springframework.boot.jdbc.DataSourceBuilder;\\nimport org.springframework.context.annotation.Bean;\\nimport org.springframework.context.annotation.Configuration;\\nimport org.springframework.context.annotation.Primary;\\nimport org.springframework.jdbc.core.JdbcTemplate;\\n\\n@Configuration\\npublic class StoredProcDBConfig {\\n    @Bean\\n    @Qualifier(\"dataSource\")\\n    @Primary\\n    public DataSource primaryDataSource(PrimaryDataSourceProperties props) {\\n        HikariDataSource ds = new HikariDataSource();\\n        ds.setJdbcUrl(props.getUrl());\\n        ds.setUsername(props.getUsername());\\n        ds.setPassword(props.getPassword());\\n        ds.setDriverClassName(props.getDriverClassName());'),\n", " Document(metadata={'source': 'test\\\\java\\\\StoredProcDBConfig.java', 'language': <Language.JAVA: 'java'>}, page_content='System.out.println(\">>> Primary DataSource initialized with URL: \" + props.getUrl());\\n\\n        return ds;\\n    }\\n\\n    @Bean(name = \"storeProcDataSource\")\\n    public DataSource storeProcDataSource(SecondDataSourceProperties props) {\\n        HikariDataSource ds = new HikariDataSource();\\n        ds.setJdbcUrl(props.getUrl());\\n        ds.setUsername(props.getUsername());\\n        ds.setPassword(props.getPassword());\\n        ds.setDriverClassName(props.getDriverClassName());\\n\\n        System.out.println(\">>> Second DataSource initialized with URL: \" + props.getUrl());\\n\\n        return ds;\\n    }\\n\\n    @Bean\\n    public JdbcTemplate storeProcJdbcTemplate(@Qualifier(\"storeProcDataSource\") DataSource ds) {\\n        return new JdbcTemplate(ds);\\n    }\\n}'),\n", " Document(metadata={'source': 'test\\\\java\\\\StoreProcService.java', 'language': <Language.JAVA: 'java'>}, page_content='package com.morganstanley.loanApp.service;\\n\\nimport org.springframework.beans.factory.annotation.Autowired;\\nimport org.springframework.beans.factory.annotation.Qualifier;\\nimport org.springframework.jdbc.core.JdbcTemplate;\\nimport org.springframework.stereotype.Service;\\n\\nimport java.sql.CallableStatement;\\nimport java.sql.Connection;\\nimport java.sql.Types;\\n\\n@Service\\npublic class StoreProcService {\\n\\n\\n    @Autowired\\n    @Qualifier(\"storeProcJdbcTemplate\")\\n    private JdbcTemplate storeProcTemplate;\\n\\n\\n\\n\\n    public Integer callStoreProc(String branchName, String branchAddress) {\\n\\n        return storeProcTemplate.execute((Connection conn) -> {\\n            CallableStatement cs = conn.prepareCall(\"{call sp_insert_branch(?, ?, ?)}\");\\n            cs.setString(1, branchName);\\n            cs.setString(2, branchAddress);\\n            cs.registerOutParameter(3, Types.INTEGER);\\n            cs.execute();\\n            return cs.getInt(3);\\n        });\\n    }\\n}'),\n", " Document(metadata={'source': 'test\\\\java\\\\User.java', 'language': <Language.JAVA: 'java'>}, page_content='package com.morganstanley.loanApp.model;\\n\\nimport jakarta.persistence.*;\\nimport lombok.Data;\\n\\n@Entity\\n@Table(name = \"users\")\\n@Data\\npublic class User {\\n\\n    @Id\\n    @GeneratedValue(strategy = GenerationType.IDENTITY)\\n    private Long id;\\n\\n    private String name;\\n    private String email;\\n}'),\n", " Document(metadata={'source': 'test\\\\java\\\\UserController.java', 'language': <Language.JAVA: 'java'>}, page_content='package com.morganstanley.loanApp.controller;\\n\\nimport com.morganstanley.loanApp.dto.UserLoanRequestDTO;\\nimport com.morganstanley.loanApp.model.User;\\n\\nimport com.morganstanley.loanApp.service.StoreProcService;\\nimport com.morganstanley.loanApp.service.UserService;\\nimport jakarta.validation.Valid;\\nimport org.springframework.beans.factory.annotation.Autowired;\\nimport org.springframework.http.HttpStatus;\\nimport org.springframework.http.ResponseEntity;\\nimport org.springframework.web.bind.annotation.PostMapping;\\nimport org.springframework.web.bind.annotation.RequestBody;\\nimport org.springframework.web.bind.annotation.RequestMapping;\\nimport org.springframework.web.bind.annotation.RestController;\\n\\nimport java.io.*;\\nimport java.util.ArrayList;\\nimport java.util.List;\\n\\n@RestController\\n@RequestMapping(\"/loanApp\")\\npublic class UserController {\\n    @Autowired\\n    private UserService userService;\\n\\n    @Autowired\\n    private StoreProcService storeProcService;'),\n", " Document(metadata={'source': 'test\\\\java\\\\UserController.java', 'language': <Language.JAVA: 'java'>}, page_content='@RestController\\n@RequestMapping(\"/loanApp\")\\npublic class UserController {\\n    @Autowired\\n    private UserService userService;\\n\\n    @Autowired\\n    private StoreProcService storeProcService;\\n\\n    @PostMapping(\"/createLoan\")\\n    public ResponseEntity<User> createUserWithLoan(@Valid @RequestBody UserLoanRequestDTO dto) {\\n        User savedUser = userService.createUserWithLoan(dto);\\n        return new ResponseEntity<>(savedUser, HttpStatus.CREATED);\\n    }\\n\\n    @PostMapping(\"/save-from-file\")\\n    public ResponseEntity<String> saveUserLoanFromFile(){\\n        try (InputStream is = getClass().getClassLoader().getResourceAsStream(\"usersData.txt\");\\n             BufferedReader reader = new BufferedReader(new InputStreamReader(is))) {\\n\\n            if (is == null) {\\n                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)\\n                        .body(\"Data file not found\");\\n            }'),\n", " Document(metadata={'source': 'test\\\\java\\\\UserController.java', 'language': <Language.JAVA: 'java'>}, page_content='if (is == null) {\\n                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)\\n                        .body(\"Data file not found\");\\n            }\\n\\n            String line ;\\n            while ((line = reader.readLine()) != null) {\\n                String[] parts = line.split(\",\");\\n                if (parts.length != 4) {\\n                    return ResponseEntity.badRequest()\\n                            .body(\"Invalid file format\");\\n                }\\n\\n\\n                UserLoanRequestDTO userDTO = new UserLoanRequestDTO();\\n                userDTO.setName(parts[0].trim());\\n                userDTO.setEmail(parts[1].trim());\\n                userDTO.setLoanType(parts[2].trim());\\n                userDTO.setAmount(Double.valueOf(parts[3].trim()));\\n\\n                userService.createUserWithLoan(userDTO);\\n            }\\n            return ResponseEntity.ok(\"Loan application saved from flat file\");'),\n", " Document(metadata={'source': 'test\\\\java\\\\UserController.java', 'language': <Language.JAVA: 'java'>}, page_content='userService.createUserWithLoan(userDTO);\\n            }\\n            return ResponseEntity.ok(\"Loan application saved from flat file\");\\n\\n        } catch (Exception e) {\\n            e.printStackTrace();\\n            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)\\n                    .body(\"Error processing flat file\");\\n        }\\n    }\\n\\n\\n    @PostMapping(\"/processStoreProcFromCSV\")\\n    public ResponseEntity<List<String>> readCSVAndCallStoreProc(){\\n        List<String> outputMessages = new ArrayList<>();\\n\\n        try (InputStream is = getClass().getClassLoader().getResourceAsStream(\"branchesData.csv\");\\n             BufferedReader reader = new BufferedReader(new InputStreamReader(is))) {\\n            String line;\\n            boolean isFirstLine = true;\\n\\n            while ((line = reader.readLine()) != null) {\\n                if (isFirstLine) {\\n                    isFirstLine = false; // skip header\\n                    continue;\\n                }'),\n", " Document(metadata={'source': 'test\\\\java\\\\UserController.java', 'language': <Language.JAVA: 'java'>}, page_content='while ((line = reader.readLine()) != null) {\\n                if (isFirstLine) {\\n                    isFirstLine = false; // skip header\\n                    continue;\\n                }\\n\\n                String[] parts = line.split(\",\");\\n                if (parts.length != 2) continue;\\n\\n                String branchName = parts[0].trim();\\n                String branchAddress = parts[1].trim();\\n\\n                Integer result = storeProcService.callStoreProc(branchName, branchAddress);\\n                outputMessages.add(\"Branch Id for \" + branchName + \" is: \" + result);\\n            }\\n\\n            return ResponseEntity.ok(outputMessages);\\n        } catch (IOException | NumberFormatException ex) {\\n            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)\\n                    .body(List.of(\"Error reading file or parsing data: \" + ex.getMessage()));\\n        }\\n    }\\n\\n\\n}'),\n", " Document(metadata={'source': 'test\\\\java\\\\UserLoanRequestDTO.java', 'language': <Language.JAVA: 'java'>}, page_content='package com.morganstanley.loanApp.dto;\\n\\nimport jakarta.validation.constraints.Email;\\nimport jakarta.validation.constraints.NotBlank;\\nimport jakarta.validation.constraints.NotNull;\\nimport jakarta.validation.constraints.Positive;\\nimport lombok.Data;\\nimport lombok.Getter;\\nimport lombok.Setter;\\n\\n\\n\\npublic class UserLoanRequestDTO {\\n\\n    public String getName() {\\n        return name;\\n    }\\n\\n    public void setName(String name) {\\n        this.name = name;\\n    }\\n\\n    public String getEmail() {\\n        return email;\\n    }\\n\\n    public void setEmail(String email) {\\n        this.email = email;\\n    }\\n\\n    public String getLoanType() {\\n        return loanType;\\n    }\\n\\n    public void setLoanType(String loanType) {\\n        this.loanType = loanType;\\n    }\\n\\n    public Double getAmount() {\\n        return amount;\\n    }\\n\\n    public void setAmount(Double amount) {\\n        this.amount = amount;\\n    }\\n\\n    @NotBlank(message = \"Name is required\")\\n    private String name;'),\n", " Document(metadata={'source': 'test\\\\java\\\\UserLoanRequestDTO.java', 'language': <Language.JAVA: 'java'>}, page_content='public void setAmount(Double amount) {\\n        this.amount = amount;\\n    }\\n\\n    @NotBlank(message = \"Name is required\")\\n    private String name;\\n\\n    @Email(message = \"Email should be valid\")\\n    @NotBlank(message = \"Email is required\")\\n    private String email;\\n\\n    @NotBlank(message = \"Loan type is required\")\\n    private String loanType;\\n\\n    @NotNull(message = \"Loan amount is required\")\\n    @Positive(message = \"Loan amount must be positive\")\\n    private Double amount;\\n}'),\n", " Document(metadata={'source': 'test\\\\java\\\\UserRepository.java', 'language': <Language.JAVA: 'java'>}, page_content='package com.morganstanley.loanApp.repository;\\n\\nimport com.morganstanley.loanApp.model.User;\\nimport org.springframework.data.jpa.repository.JpaRepository;\\n\\n\\npublic interface UserRepository extends JpaRepository<User, Long> {\\n}'),\n", " Document(metadata={'source': 'test\\\\java\\\\UserService.java', 'language': <Language.JAVA: 'java'>}, page_content='package com.morganstanley.loanApp.service;\\n\\nimport com.morganstanley.loanApp.dto.UserLoanRequestDTO;\\nimport com.morganstanley.loanApp.model.LoanDetails;\\nimport com.morganstanley.loanApp.model.User;\\nimport com.morganstanley.loanApp.repository.LoanDetailsRepository;\\nimport com.morganstanley.loanApp.repository.UserRepository;\\nimport org.springframework.beans.factory.annotation.Autowired;\\nimport org.springframework.stereotype.Service;\\n\\n@Service\\npublic class UserService {\\n    @Autowired\\n    private UserRepository userRepository;\\n\\n    @Autowired\\n    private LoanDetailsRepository loanDetailsRepository;\\n\\n    public User createUserWithLoan(UserLoanRequestDTO dto) {\\n        User user = new User();\\n        user.setName(dto.getName());\\n        user.setEmail(dto.getEmail());\\n        user = userRepository.save(user);'),\n", " Document(metadata={'source': 'test\\\\java\\\\UserService.java', 'language': <Language.JAVA: 'java'>}, page_content='LoanDetails loan = new LoanDetails();\\n        loan.setLoanType(dto.getLoanType());\\n        loan.setAmount(dto.getAmount());\\n        loan.setUserId(user.getId());\\n        loanDetailsRepository.save(loan);\\n\\n        return user;\\n    }\\n}')]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["documents"]}, {"cell_type": "code", "execution_count": 11, "id": "688395d1", "metadata": {}, "outputs": [], "source": ["groq_api_key=\"********************************************************\""]}, {"cell_type": "code", "execution_count": 16, "id": "413f7366", "metadata": {}, "outputs": [], "source": ["system_prompt=\"\"\"\n", "You are a code analysis engine. From the provided Python or Java code chunk, extract structured graph triples in the format:\n", "\n", "[SourceNodeLabel]:SourceNodeName -[RELATION]-> [TargetNodeLabel]:TargetNodeName\n", "\n", "Layers:\n", "- Folder -> File\n", "- File -> Class\n", "- Class -> Function/Method\n", "- Function/Method -> Function/Method (CALLS)\n", "\n", "Example:\n", "Folder:test/python -[CONTAINS]-> File:example.py\n", "File:example.py -[DECLARES]-> Class:MyClass\n", "Class:MyClass -[DECLARES]-> Function:compute_sum\n", "Function:compute_sum -[CALLS]-> Function:helper\n", "\n", "Return only these triples, nothing else.\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "76469f75", "metadata": {}, "outputs": [], "source": ["from langchain_groq import ChatGroq\n", "from langchain_experimental.graph_transformers import LLMGraphTransformer\n", "\n", "llm = ChatGroq(\n", "    model=\"llama-3.3-70b-versatile\",   # or any verified available model\n", "    api_key=groq_api_key)\n", "\n", "llm_transformer = LLMGraphTransformer(llm=llm,\n", "                                      additional_instructions=system_prompt)\n", "\n", "graph_documents = llm_transformer.convert_to_graph_documents(documents)"]}, {"cell_type": "code", "execution_count": 27, "id": "dc194158", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Node(id='Com.Morganstanley.Loanapp.Configuration', type='Package', properties={}),\n", " Node(id='Seconddatasourceproperties.Java', type='File', properties={}),\n", " Node(id='Seconddatasourceproperties', type='Class', properties={}),\n", " Node(id='Geturl', type='Method', properties={}),\n", " Node(id='Seturl', type='Method', properties={})]"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["graph_documents[0].nodes[:5]"]}, {"cell_type": "code", "execution_count": 28, "id": "81ed99ac", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Relationship(source=Node(id='Com.Morganstanley.Loanapp.Configuration', type='Package', properties={}), target=Node(id='Seconddatasourceproperties.Java', type='File', properties={}), type='CONTAINS', properties={}),\n", " Relationship(source=Node(id='Seconddatasourceproperties.Java', type='File', properties={}), target=Node(id='Seconddatasourceproperties', type='Class', properties={}), type='DECLARES', properties={}),\n", " Relationship(source=Node(id='Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Geturl', type='Method', properties={}), type='DECLARES', properties={}),\n", " Relationship(source=Node(id='Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Seturl', type='Method', properties={}), type='DECLARES', properties={}),\n", " Relationship(source=Node(id='Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Getusername', type='Method', properties={}), type='DECLARES', properties={})]"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["graph_documents[0].relationships[:5]"]}, {"cell_type": "code", "execution_count": 23, "id": "e263f10c", "metadata": {}, "outputs": [], "source": ["llm_transformer2 = LLMGraphTransformer(\n", "    llm=llm,\n", "    allowed_nodes=[\"Folder\", \"File\", \"Class\", \"Function\"],\n", "    allowed_relationships=[\n", "        (\"Folder\", \"CONTAINS\", \"File\"),\n", "        (\"File\", \"DECLARES\", \"Class\"),\n", "        (\"Class\", \"DECLARES\", \"Function\"),\n", "        (\"Function\", \"CALLS\", \"Function\"),\n", "        (\"Class\", \"INHERITS\", \"Class\")\n", "    ],\n", "    strict_mode=True,\n", "    node_properties=False,\n", "    relationship_properties=False,\n", "    ignore_tool_usage=False,\n", "    additional_instructions=system_prompt\n", ")\n", "\n", "graph_documents2 = llm_transformer2.convert_to_graph_documents(documents)"]}, {"cell_type": "code", "execution_count": 25, "id": "0e415390", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Node(id='Com.Morganstanley.Loanapp.Configuration', type='Folder', properties={}),\n", " Node(id='Seconddatasourceproperties.Java', type='File', properties={}),\n", " Node(id='Seconddatasourceproperties', type='Class', properties={}),\n", " Node(id='Geturl', type='Function', properties={}),\n", " Node(id='Seturl', type='Function', properties={})]"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["graph_documents2[0].nodes[:5]"]}, {"cell_type": "code", "execution_count": 26, "id": "2abe5a06", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Relationship(source=Node(id='Com.Morganstanley.Loanapp.Configuration', type='Folder', properties={}), target=Node(id='Seconddatasourceproperties.Java', type='File', properties={}), type='CONTAINS', properties={}),\n", " Relationship(source=Node(id='Seconddatasourceproperties.Java', type='File', properties={}), target=Node(id='Seconddatasourceproperties', type='Class', properties={}), type='DECLARES', properties={}),\n", " Relationship(source=Node(id='Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Geturl', type='Function', properties={}), type='DECLARES', properties={}),\n", " Relationship(source=Node(id='Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Seturl', type='Function', properties={}), type='DECLARES', properties={}),\n", " Relationship(source=Node(id='Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Getusername', type='Function', properties={}), type='DECLARES', properties={})]"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["graph_documents2[0].relationships[:5]"]}, {"cell_type": "code", "execution_count": 30, "id": "53614836", "metadata": {}, "outputs": [], "source": ["\n", "system_prompt3 = \"\"\" You are a code analysis engine. From the provided Python or Java code chunk, extract structured graph triples in the format:\n", "\n", "[SourceNodeLabel]:SourceNodeName -[RELATION]-> [TargetNodeLabel]:TargetNodeName\n", "\n", "Use these consistent node labels: Folder, File, Class, Function.\n", "Use these consistent relationships: CONTAINS, DECLARES, CALLS, INHERITS.\n", "\n", "Layers:\n", "- Folder -> File\n", "- File -> Class\n", "- Class -> Function\n", "- Function -> Function (CALLS)\n", "- Class -> Class (INHERITS)\n", "\n", "If the code contains function calls, extract CALLS edges explicitly.\n", "If the code contains inheritance, extract INHERITS edges explicitly.\n", "\n", "Example:\n", "Folder:test/python -[CONTAINS]-> File:example.py\n", "File:example.py -[DECLARES]-> Class:MyClass\n", "Class:MyClass -[DECLARES]-> Function:compute_sum\n", "Function:compute_sum -[CALLS]-> Function:helper\n", "\n", "Return only these triples, nothing else.\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 31, "id": "6e36c5c3", "metadata": {}, "outputs": [], "source": ["from langchain_experimental.graph_transformers import LLMGraphTransformer\n", "\n", "llm_transformer3 = LLMGraphTransformer(\n", "    llm=llm,\n", "    allowed_nodes=[\n", "        \"Folder\",\n", "        \"File\",\n", "        \"Class\",\n", "        \"Function\"\n", "    ],\n", "    allowed_relationships=[\n", "        (\"Folder\", \"CONTAINS\", \"File\"),\n", "        (\"File\", \"DECLARES\", \"Class\"),\n", "        (\"Class\", \"DECLARES\", \"Function\"),\n", "        (\"Function\", \"CALLS\", \"Function\"),\n", "        (\"Class\", \"INHERITS\", \"Class\")\n", "    ],\n", "    strict_mode=True,\n", "    node_properties=False,\n", "    relationship_properties=False,\n", "    ignore_tool_usage=False,\n", "    additional_instructions=system_prompt3\n", ")\n", "\n", "graph_documents3 = llm_transformer3.convert_to_graph_documents(documents)"]}, {"cell_type": "code", "execution_count": 37, "id": "795da441", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Node(id='Com.Morganstanley.Loanapp.Configuration', type='Folder', properties={}),\n", " Node(id='Seconddatasourceproperties.Java', type='File', properties={}),\n", " Node(id='Seconddatasourceproperties', type='Class', properties={}),\n", " Node(id='Geturl', type='Function', properties={}),\n", " Node(id='Seturl', type='Function', properties={}),\n", " Node(id='Getusername', type='Function', properties={}),\n", " Node(id='Setusername', type='Function', properties={}),\n", " Node(id='Getpassword', type='Function', properties={}),\n", " Node(id='Setpassword', type='Function', properties={}),\n", " Node(id='Getdriverclassname', type='Function', properties={}),\n", " Node(id='Setdriverclassname', type='Function', properties={})]"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["graph_documents3[0].nodes[:20]"]}, {"cell_type": "code", "execution_count": 41, "id": "23b159ca", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Relationship(source=Node(id='Com.Morganstanley.Loanapp.Configuration', type='Folder', properties={}), target=Node(id='Seconddatasourceproperties.Java', type='File', properties={}), type='CONTAINS', properties={}),\n", " Relationship(source=Node(id='Seconddatasourceproperties.Java', type='File', properties={}), target=Node(id='Seconddatasourceproperties', type='Class', properties={}), type='DECLARES', properties={}),\n", " Relationship(source=Node(id='Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Geturl', type='Function', properties={}), type='DECLARES', properties={}),\n", " Relationship(source=Node(id='Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Seturl', type='Function', properties={}), type='DECLARES', properties={}),\n", " Relationship(source=Node(id='Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Getusername', type='Function', properties={}), type='DECLARES', properties={}),\n", " Relationship(source=Node(id='Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Setusername', type='Function', properties={}), type='DECLARES', properties={}),\n", " Relationship(source=Node(id='Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Getpassword', type='Function', properties={}), type='DECLARES', properties={}),\n", " Relationship(source=Node(id='Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Setpassword', type='Function', properties={}), type='DECLARES', properties={}),\n", " Relationship(source=Node(id='Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Getdriverclassname', type='Function', properties={}), type='DECLARES', properties={}),\n", " Relationship(source=Node(id='Seconddatasourceproperties', type='Class', properties={}), target=Node(id='Setdriverclassname', type='Function', properties={}), type='DECLARES', properties={})]"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["graph_documents3[0].relationships[:20]"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}