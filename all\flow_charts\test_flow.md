# test flow in between controller service and repository files 

```mermaid
flowchart TD

    %% === API Endpoints ===
    subgraph "🌐 API Endpoints"
        API1["POST /loanApp/createLoan"]
        API2["POST /loanApp/save-from-file"]
        API3["POST /loanApp/processStoreProcFromCSV"]
    end
    style API1 fill:#bbdefb,stroke:#1565c0,stroke-width:2px
    style API2 fill:#bbdefb,stroke:#1565c0,stroke-width:2px
    style API3 fill:#bbdefb,stroke:#1565c0,stroke-width:2px

    %% === Controller ===
    subgraph "📄 UserController.java"
        CLASS_UC["👑 Class: UserController"]
        UC_M1["📍 createUserWithLoan()"]
        UC_M2["📍 saveUserLoanFromFile()"]
        UC_M3["📍 readCSVAndCallStoreProc()"]
        CLASS_UC --> UC_M1
        CLASS_UC --> UC_M2
        CLASS_UC --> UC_M3
    end
    style CLASS_UC fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    style UC_M1 fill:#fce4ec,stroke:#c2185b,stroke-width:1px
    style UC_M2 fill:#fce4ec,stroke:#c2185b,stroke-width:1px
    style UC_M3 fill:#fce4ec,stroke:#c2185b,stroke-width:1px

    %% === User Service ===
    subgraph "📄 UserService.java"
        CLASS_US["💼 Class: UserService"]
        US_M1["⚙️ createUserWithLoan()"]
        CLASS_US --> US_M1
    end
    style CLASS_US fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    style US_M1 fill:#fff3e0,stroke:#ef6c00,stroke-width:1px

    %% === Store Procedure Service ===
    subgraph "📄 StoreProcService.java"
        CLASS_SP["🛠️ Class: StoreProcService"]
        SP_M1["⚙️ callStoreProc()"]
        CLASS_SP --> SP_M1
    end
    style CLASS_SP fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    style SP_M1 fill:#fff3e0,stroke:#ef6c00,stroke-width:1px

    %% === Repositories ===
    subgraph "📦 Repositories"
        REPO_UR["UserRepository.java\n→ 🧾 Table: User"]
        REPO_LDR["LoanDetailsRepository.java\n→ 🧾 Table: LoanDetails"]
    end
    style REPO_UR fill:#e8f5e9,stroke:#2e7d32,stroke-width:2px
    style REPO_LDR fill:#e8f5e9,stroke:#2e7d32,stroke-width:2px

    %% === Stored Procedure ===
    PROC["🧩 Stored Procedure:\nsp_insert_branch"]
    style PROC fill:#ede7f6,stroke:#5e35b1,stroke-width:2px

    %% === DTO ===
    DTO["📦 DTO:\nUserLoanRequestDTO"]
    style DTO fill:#eceff1,stroke:#607d8b,stroke-width:2px

    %% === Flow Connections ===
    API1 --> UC_M1
    API2 --> UC_M2
    API3 --> UC_M3

    UC_M1 --> US_M1
    UC_M2 --> US_M1
    UC_M3 --> SP_M1

    US_M1 --> REPO_UR
    US_M1 --> REPO_LDR

    SP_M1 --> PROC

    UC_M1 --> DTO
    US_M1 --> DTO


```